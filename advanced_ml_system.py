#!/usr/bin/env python3
"""
Advanced ML System for 90%+ Direction Accuracy
Comprehensive feature engineering and classification-based approach
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
import requests
import time
from datetime import datetime, timedelta
import hmac
import hashlib
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# Binance API configuration
BASE_URL = 'https://fapi.binance.com'
API_KEY = os.getenv('BINANCE_API_KEY', '')
API_SECRET = os.getenv('BINANCE_SECRET_KEY', '')

class AdvancedFeatureEngineering:
    """Advanced feature engineering for maximum predictive power"""
    
    def __init__(self):
        self.feature_names = []
    
    def create_comprehensive_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create comprehensive feature set for ML model"""
        
        # Ensure we have OHLCV data
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in required_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        features_df = df.copy()
        
        # 1. PRICE-BASED FEATURES
        self._add_price_features(features_df)
        
        # 2. TECHNICAL INDICATORS
        self._add_technical_indicators(features_df)
        
        # 3. VOLUME FEATURES
        self._add_volume_features(features_df)
        
        # 4. VOLATILITY FEATURES
        self._add_volatility_features(features_df)
        
        # 5. MOMENTUM FEATURES
        self._add_momentum_features(features_df)
        
        # 6. PATTERN RECOGNITION
        self._add_pattern_features(features_df)
        
        # 7. MARKET STRUCTURE
        self._add_market_structure_features(features_df)
        
        # 8. TIME-BASED FEATURES
        self._add_time_features(features_df)
        
        return features_df
    
    def _add_price_features(self, df: pd.DataFrame):
        """Add price-based features"""
        # Returns
        df['return_1h'] = df['close'].pct_change(1)
        df['return_2h'] = df['close'].pct_change(2)
        df['return_4h'] = df['close'].pct_change(4)
        df['return_8h'] = df['close'].pct_change(8)
        df['return_24h'] = df['close'].pct_change(24)
        
        # Price ratios
        df['high_low_ratio'] = df['high'] / df['low']
        df['close_open_ratio'] = df['close'] / df['open']
        df['high_close_ratio'] = df['high'] / df['close']
        df['low_close_ratio'] = df['low'] / df['close']
        
        # Price position within candle
        df['body_size'] = abs(df['close'] - df['open']) / (df['high'] - df['low'] + 1e-8)
        df['upper_shadow'] = (df['high'] - np.maximum(df['open'], df['close'])) / (df['high'] - df['low'] + 1e-8)
        df['lower_shadow'] = (np.minimum(df['open'], df['close']) - df['low']) / (df['high'] - df['low'] + 1e-8)
    
    def _add_technical_indicators(self, df: pd.DataFrame):
        """Add comprehensive technical indicators using manual calculations"""
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']

        # Moving Averages
        for period in [5, 10, 20, 50, 100, 200]:
            df[f'sma_{period}'] = close.rolling(window=period).mean()
            df[f'ema_{period}'] = close.ewm(span=period).mean()
            df[f'price_sma_{period}_ratio'] = close / df[f'sma_{period}']
            df[f'price_ema_{period}_ratio'] = close / df[f'ema_{period}']

        # MACD
        ema12 = close.ewm(span=12).mean()
        ema26 = close.ewm(span=26).mean()
        df['macd'] = ema12 - ema26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']

        # RSI
        for period in [7, 14, 21, 30]:
            delta = close.diff()
            gain = delta.clip(lower=0)
            loss = (-delta).clip(lower=0)
            avg_gain = gain.rolling(window=period).mean()
            avg_loss = loss.rolling(window=period).mean()
            rs = avg_gain / (avg_loss + 1e-10)
            df[f'rsi_{period}'] = 100 - (100 / (1 + rs))

        # Stochastic Oscillator
        lowest_low = low.rolling(window=14).min()
        highest_high = high.rolling(window=14).max()
        df['stoch_k'] = 100 * (close - lowest_low) / (highest_high - lowest_low + 1e-10)
        df['stoch_d'] = df['stoch_k'].rolling(window=3).mean()

        # Williams %R
        df['williams_r'] = -100 * (highest_high - close) / (highest_high - lowest_low + 1e-10)

        # Bollinger Bands
        df['bb_middle'] = close.rolling(window=20).mean()
        bb_std = close.rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (close - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'] + 1e-10)

        # Average Directional Index (simplified)
        tr = np.maximum(high - low, np.maximum(abs(high - close.shift(1)), abs(low - close.shift(1))))
        df['atr'] = tr.rolling(window=14).mean()

        # Commodity Channel Index (simplified)
        typical_price = (high + low + close) / 3
        df['cci'] = (typical_price - typical_price.rolling(20).mean()) / (0.015 * typical_price.rolling(20).std())
    
    def _add_volume_features(self, df: pd.DataFrame):
        """Add volume-based features"""
        volume = df['volume']
        close = df['close']
        high = df['high']
        low = df['low']

        # On Balance Volume (OBV)
        df['obv'] = (volume * np.where(close > close.shift(1), 1,
                                     np.where(close < close.shift(1), -1, 0))).cumsum()

        # Accumulation/Distribution Line
        clv = ((close - low) - (high - close)) / (high - low + 1e-10)
        df['ad'] = (clv * volume).cumsum()

        # Volume ratios
        for period in [5, 10, 20]:
            df[f'volume_sma_{period}'] = volume.rolling(window=period).mean()
            df[f'volume_ratio_{period}'] = volume / df[f'volume_sma_{period}']

        # Price-Volume relationship
        df['price_volume'] = close * volume
        df['vwap'] = (df['price_volume'].rolling(20).sum() / volume.rolling(20).sum())
        df['price_vwap_ratio'] = close / df['vwap']
    
    def _add_volatility_features(self, df: pd.DataFrame):
        """Add volatility-based features"""
        close = df['close']
        high = df['high']
        low = df['low']

        # True Range
        prev_close = close.shift(1)
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        df['tr'] = np.maximum(tr1, np.maximum(tr2, tr3))

        # Average True Range
        for period in [7, 14, 21]:
            df[f'atr_{period}'] = df['tr'].rolling(window=period).mean()
        df['atr'] = df['atr_14']  # Default ATR

        # Volatility ratios
        df['volatility_5'] = close.rolling(5).std()
        df['volatility_20'] = close.rolling(20).std()
        df['volatility_ratio'] = df['volatility_5'] / (df['volatility_20'] + 1e-10)

        # High-Low spread
        df['hl_spread'] = (high - low) / close
        df['hl_spread_ma'] = df['hl_spread'].rolling(20).mean()
        df['hl_spread_ratio'] = df['hl_spread'] / (df['hl_spread_ma'] + 1e-10)
    
    def _add_momentum_features(self, df: pd.DataFrame):
        """Add momentum-based features"""
        close = df['close']
        high = df['high']
        low = df['low']

        # Rate of Change
        for period in [1, 3, 5, 10, 20]:
            df[f'roc_{period}'] = (close / close.shift(period) - 1) * 100

        # Momentum
        for period in [5, 10, 20]:
            df[f'mom_{period}'] = close - close.shift(period)

        # Price Oscillator (simplified)
        ema12 = close.ewm(span=12).mean()
        ema26 = close.ewm(span=26).mean()
        df['ppo'] = ((ema12 - ema26) / ema26) * 100

        # Ultimate Oscillator (simplified)
        bp = close - np.minimum(low, close.shift(1))
        tr = np.maximum(high, close.shift(1)) - np.minimum(low, close.shift(1))
        avg7 = bp.rolling(7).sum() / tr.rolling(7).sum()
        avg14 = bp.rolling(14).sum() / tr.rolling(14).sum()
        avg28 = bp.rolling(28).sum() / tr.rolling(28).sum()
        df['ultosc'] = 100 * (4 * avg7 + 2 * avg14 + avg28) / 7
    
    def _add_pattern_features(self, df: pd.DataFrame):
        """Add simplified candlestick pattern recognition"""
        open_prices = df['open']
        high = df['high']
        low = df['low']
        close = df['close']

        # Basic candlestick patterns (simplified)
        body = abs(close - open_prices)
        upper_shadow = high - np.maximum(open_prices, close)
        lower_shadow = np.minimum(open_prices, close) - low
        range_hl = high - low

        # Doji pattern
        df['doji'] = (body / (range_hl + 1e-10) < 0.1).astype(int)

        # Hammer pattern
        df['hammer'] = ((lower_shadow > 2 * body) & (upper_shadow < body)).astype(int)

        # Shooting star pattern
        df['shooting_star'] = ((upper_shadow > 2 * body) & (lower_shadow < body)).astype(int)

        # Engulfing patterns
        prev_body = abs(close.shift(1) - open_prices.shift(1))
        df['bullish_engulfing'] = ((close > open_prices) &
                                  (close.shift(1) < open_prices.shift(1)) &
                                  (close > open_prices.shift(1)) &
                                  (open_prices < close.shift(1)) &
                                  (body > prev_body)).astype(int)

        df['bearish_engulfing'] = ((close < open_prices) &
                                  (close.shift(1) > open_prices.shift(1)) &
                                  (close < open_prices.shift(1)) &
                                  (open_prices > close.shift(1)) &
                                  (body > prev_body)).astype(int)

        # Long body patterns
        avg_body = body.rolling(20).mean()
        df['long_body'] = (body > 1.5 * avg_body).astype(int)

        # Gap patterns
        df['gap_up'] = (low > high.shift(1)).astype(int)
        df['gap_down'] = (high < low.shift(1)).astype(int)
    
    def _add_market_structure_features(self, df: pd.DataFrame):
        """Add market structure features"""
        close = df['close'].values
        
        # Support and Resistance levels
        df['pivot'] = (df['high'] + df['low'] + df['close']) / 3
        df['r1'] = 2 * df['pivot'] - df['low']
        df['s1'] = 2 * df['pivot'] - df['high']
        df['r2'] = df['pivot'] + (df['high'] - df['low'])
        df['s2'] = df['pivot'] - (df['high'] - df['low'])
        
        # Distance from pivot levels
        df['dist_pivot'] = (close - df['pivot']) / df['pivot']
        df['dist_r1'] = (close - df['r1']) / df['r1']
        df['dist_s1'] = (close - df['s1']) / df['s1']
        
        # Trend strength
        df['trend_5'] = np.where(close > df['close'].shift(5), 1, -1)
        df['trend_10'] = np.where(close > df['close'].shift(10), 1, -1)
        df['trend_20'] = np.where(close > df['close'].shift(20), 1, -1)
        df['trend_strength'] = df['trend_5'] + df['trend_10'] + df['trend_20']
    
    def _add_time_features(self, df: pd.DataFrame):
        """Add time-based features"""
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
        else:
            df['timestamp'] = df.index
        
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['day_of_month'] = df['timestamp'].dt.day
        df['month'] = df['timestamp'].dt.month
        
        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['dow_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['dow_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)

class AdvancedMLPredictor:
    """Advanced ML system for 90%+ direction accuracy"""
    
    def __init__(self):
        self.feature_engineer = AdvancedFeatureEngineering()
        self.scaler = StandardScaler()
        self.models = {}
        self.feature_importance = {}
        
    def prepare_classification_data(self, df: pd.DataFrame, prediction_horizon: int = 1) -> Tuple[pd.DataFrame, pd.Series]:
        """Prepare data for classification (direction prediction)"""
        
        # Create comprehensive features
        features_df = self.feature_engineer.create_comprehensive_features(df)
        
        # Create target variable (direction)
        # 1 = price goes up, 0 = price goes down
        future_return = features_df['close'].shift(-prediction_horizon) / features_df['close'] - 1
        target = (future_return > 0.001).astype(int)  # 0.1% threshold to avoid noise
        
        # Remove rows with NaN values
        valid_idx = ~(features_df.isnull().any(axis=1) | target.isnull())
        features_df = features_df[valid_idx]
        target = target[valid_idx]
        
        # Select only numeric features for ML
        numeric_features = features_df.select_dtypes(include=[np.number]).columns
        features_df = features_df[numeric_features]
        
        # Remove infinite values
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        features_df = features_df.fillna(method='ffill').fillna(0)
        
        return features_df, target
    
    def train_advanced_ensemble(self, X_train: pd.DataFrame, y_train: pd.Series, 
                              X_val: pd.DataFrame, y_val: pd.Series) -> Dict:
        """Train advanced ensemble of classifiers"""
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        # Individual models with optimized parameters (without XGB/LGB for now)
        models = {
            'rf': RandomForestClassifier(
                n_estimators=300,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            ),
            'gb': GradientBoostingClassifier(
                n_estimators=300,
                max_depth=8,
                learning_rate=0.05,
                subsample=0.8,
                random_state=42
            ),
            'svm': SVC(
                C=1.0,
                kernel='rbf',
                probability=True,
                random_state=42
            ),
            'lr': LogisticRegression(
                C=1.0,
                random_state=42,
                max_iter=1000
            )
        }
        
        # Train individual models
        individual_scores = {}
        for name, model in models.items():
            print(f"Training {name}...")
            
            if name in ['svm', 'lr']:
                model.fit(X_train_scaled, y_train)
                pred = model.predict(X_val_scaled)
            else:
                model.fit(X_train, y_train)
                pred = model.predict(X_val)
            
            score = accuracy_score(y_val, pred)
            individual_scores[name] = score
            self.models[name] = model
            
            print(f"{name} accuracy: {score:.4f}")
        
        # Create weighted ensemble based on validation performance
        weights = {}
        total_score = sum(individual_scores.values())
        for name, score in individual_scores.items():
            weights[name] = score / total_score

        self.weights = weights
        
        return {
            'models': self.models,
            'individual_scores': individual_scores,
            'weights': weights,
            'scaler': self.scaler
        }
    
    def predict_ensemble(self, X: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Make ensemble predictions"""
        X_scaled = self.scaler.transform(X)
        
        predictions = []
        probabilities = []
        
        for name, model in self.models.items():
            if name in ['svm', 'lr']:
                pred = model.predict(X_scaled)
                prob = model.predict_proba(X_scaled)[:, 1]
            else:
                pred = model.predict(X)
                prob = model.predict_proba(X)[:, 1]
            
            predictions.append(pred)
            probabilities.append(prob)
        
        # Weighted ensemble
        ensemble_prob = np.average(probabilities, axis=0, weights=list(self.weights.values()))
        ensemble_pred = (ensemble_prob > 0.5).astype(int)
        
        return ensemble_pred, ensemble_prob

def fetch_binance_data(symbol: str, interval: str, limit: int = 1000) -> pd.DataFrame:
    """Fetch historical data from Binance"""
    try:
        endpoint = '/fapi/v1/klines'
        # Binance has a max limit of 1500 for futures
        limit = min(limit, 1500)
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        response = requests.get(BASE_URL + endpoint, params=params)
        data = response.json()
        
        if isinstance(data, list) and len(data) > 0:
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert to proper data types
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            return df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
        else:
            print(f"Error fetching data: {data}")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"Error fetching Binance data: {e}")
        return pd.DataFrame()

def test_advanced_ml_system():
    """Test the advanced ML system"""
    print("=" * 80)
    print("🚀 TESTING ADVANCED ML SYSTEM FOR 90%+ DIRECTION ACCURACY")
    print("=" * 80)
    
    # Fetch data
    print("📊 Fetching BTC/USDT 1H data...")
    df = fetch_binance_data('BTCUSDT', '1h', 1500)

    if df.empty:
        print("❌ Failed to fetch data")
        return 0.0
    
    print(f"✅ Fetched {len(df)} data points")
    
    # Initialize ML system
    ml_system = AdvancedMLPredictor()
    
    # Prepare data
    print("🔧 Engineering features...")
    X, y = ml_system.prepare_classification_data(df, prediction_horizon=1)
    
    print(f"✅ Created {X.shape[1]} features from {X.shape[0]} samples")
    print(f"📈 Target distribution: {y.value_counts().to_dict()}")
    
    # Split data (time series split)
    split_idx = int(0.8 * len(X))
    X_train, X_val = X.iloc[:split_idx], X.iloc[split_idx:]
    y_train, y_val = y.iloc[:split_idx], y.iloc[split_idx:]
    
    print(f"📊 Training samples: {len(X_train)}, Validation samples: {len(X_val)}")
    
    # Train models
    print("\n🤖 Training advanced ensemble models...")
    results = ml_system.train_advanced_ensemble(X_train, y_train, X_val, y_val)
    
    # Make predictions
    print("\n🎯 Making ensemble predictions...")
    ensemble_pred, ensemble_prob = ml_system.predict_ensemble(X_val)
    
    # Calculate final accuracy
    final_accuracy = accuracy_score(y_val, ensemble_pred)
    
    print(f"\n🏆 FINAL ENSEMBLE ACCURACY: {final_accuracy:.4f} ({final_accuracy*100:.2f}%)")
    
    # Detailed analysis
    print("\n📋 DETAILED CLASSIFICATION REPORT:")
    print(classification_report(y_val, ensemble_pred))
    
    print("\n🎯 CONFUSION MATRIX:")
    print(confusion_matrix(y_val, ensemble_pred))
    
    # Check if we achieved 90%+
    if final_accuracy >= 0.90:
        print(f"\n🎉 SUCCESS! Achieved {final_accuracy*100:.2f}% accuracy (≥90%)")
    else:
        print(f"\n⚠️  Need improvement: {final_accuracy*100:.2f}% accuracy (<90%)")
        print("💡 Suggestions:")
        print("   - Add more external data sources")
        print("   - Implement market regime detection")
        print("   - Use longer historical data")
        print("   - Add sentiment analysis")
    
    return final_accuracy

if __name__ == "__main__":
    accuracy = test_advanced_ml_system()
    print(f"\n🏁 Test completed. Final accuracy: {accuracy*100:.2f}%")
