#!/usr/bin/env python3
"""
Script to add accessSecurity section to all remaining .ts language files
"""

import sys
import os
import json
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_access_security_from_json(lang):
    """Get accessSecurity section from JSON file"""
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if 'accessSecurity' in data:
            return data['accessSecurity']
        else:
            print(f"  [WARNING] accessSecurity section not found in {lang}/common.json")
            return None
    except Exception as e:
        print(f"  [ERROR] Error reading {lang}/common.json: {str(e)}")
        return None

def convert_to_ts_format(access_security_data):
    """Convert JSON data to TypeScript format"""
    lines = ['  "accessSecurity": {']
    
    for key, value in access_security_data.items():
        # Escape quotes in the value
        escaped_value = value.replace('"', '\\"')
        lines.append(f'    "{key}": "{escaped_value}",')
    
    # Remove the last comma
    if lines[-1].endswith(','):
        lines[-1] = lines[-1][:-1]
    
    lines.append('  },')
    
    return '\n'.join(lines)

def add_access_security_to_ts_file(lang):
    """Add accessSecurity section to a .ts file"""
    print(f"  Processing {lang.upper()}:")
    
    # Get accessSecurity data from JSON
    access_security_data = get_access_security_from_json(lang)
    if not access_security_data:
        return False
    
    # Read the .ts file
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"     [ERROR] Error reading {lang}/common.ts: {str(e)}")
        return False
    
    # Check if accessSecurity section already exists
    if '"accessSecurity": {' in content:
        print(f"     [SKIP] accessSecurity section already exists")
        return True
    
    # Find the end of the settings section
    settings_end_pattern = r'(\s+}\s+},\s+"tiers":\s*{)'
    match = re.search(settings_end_pattern, content)
    
    if not match:
        print(f"     [ERROR] Could not find settings section end pattern")
        return False
    
    # Convert accessSecurity data to TypeScript format
    ts_access_security = convert_to_ts_format(access_security_data)
    
    # Insert the accessSecurity section
    replacement = f'    }}\n  }},\n{ts_access_security}\n  "tiers": {{'
    new_content = re.sub(settings_end_pattern, replacement, content)
    
    # Write the updated content back
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"     [SUCCESS] Added accessSecurity section")
        return True
    except Exception as e:
        print(f"     [ERROR] Error writing {lang}/common.ts: {str(e)}")
        return False

def run_script():
    """Run the script to add accessSecurity to all remaining language files"""
    print("ADDING ACCESSSECURITY SECTION TO .TS FILES")
    print("=" * 60)
    
    # Languages to process (excluding en, es, pt which are already done)
    languages = ['ko', 'ja', 'de', 'fr', 'zh']
    
    success_count = 0
    failed_count = 0
    
    for lang in languages:
        try:
            if add_access_security_to_ts_file(lang):
                success_count += 1
            else:
                failed_count += 1
        except Exception as e:
            print(f"  [ERROR] Unexpected error processing {lang}: {str(e)}")
            failed_count += 1
    
    print("\n" + "=" * 60)
    print("SCRIPT SUMMARY")
    print("=" * 60)
    print(f"Languages processed: {len(languages)}")
    print(f"Successful: {success_count}")
    print(f"Failed: {failed_count}")
    
    if failed_count == 0:
        print("\n✅ ALL LANGUAGE FILES UPDATED SUCCESSFULLY!")
        print("✅ AccessSecurity sections added to all .ts files")
        print("\n🎯 NEXT STEPS:")
        print("1. Restart the frontend development server")
        print("2. Clear browser cache if needed")
        print("3. Refresh the Access & Security page")
        print("\n🎉 The page should now display proper translations!")
        return True
    else:
        print(f"\n❌ {failed_count} language file(s) failed to update")
        return False

if __name__ == "__main__":
    success = run_script()
    sys.exit(0 if success else 1)
