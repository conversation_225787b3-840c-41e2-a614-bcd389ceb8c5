#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add all missing translation keys to achieve 100% completion
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def add_missing_auth_login_keys(lang):
    """Add missing auth.login keys"""
    
    missing_keys = {
        'ko': {
            'emailPlaceholder': '<EMAIL>',
            'passwordPlaceholder': '••••••••',
            'rememberMe': '로그인 상태 유지',
            'forgotPassword': '비밀번호를 잊으셨나요?',
            'noAccount': '계정이 없으신가요?',
            'googleSignIn': 'Google로 계속하기',
            'fillAllFields': '모든 필드를 입력해주세요',
            'loginSuccess': '로그인 성공!',
            'loginFailed': '로그인 실패',
            'loginFailedDescription': '로그인에 실패했습니다',
            'signUp': '가입하기'
        },
        'ja': {
            'emailPlaceholder': '<EMAIL>',
            'passwordPlaceholder': '••••••••',
            'rememberMe': 'ログイン状態を保持',
            'forgotPassword': 'パスワードをお忘れですか？',
            'noAccount': 'アカウントをお持ちでないですか？',
            'googleSignIn': 'Googleで続行',
            'fillAllFields': 'すべてのフィールドを入力してください',
            'loginSuccess': 'ログイン成功！',
            'loginFailed': 'ログイン失敗',
            'loginFailedDescription': 'ログインに失敗しました',
            'signUp': 'サインアップ'
        },
        'de': {
            'emailPlaceholder': '<EMAIL>',
            'passwordPlaceholder': '••••••••',
            'rememberMe': 'Angemeldet bleiben',
            'forgotPassword': 'Passwort vergessen?',
            'noAccount': 'Noch kein Konto?',
            'googleSignIn': 'Mit Google fortfahren',
            'fillAllFields': 'Bitte füllen Sie alle Felder aus',
            'loginSuccess': 'Anmeldung erfolgreich!',
            'loginFailed': 'Anmeldung fehlgeschlagen',
            'loginFailedDescription': 'Anmeldung fehlgeschlagen',
            'signUp': 'Registrieren'
        },
        'fr': {
            'emailPlaceholder': '<EMAIL>',
            'passwordPlaceholder': '••••••••',
            'rememberMe': 'Se souvenir de moi',
            'forgotPassword': 'Mot de passe oublié ?',
            'noAccount': 'Pas de compte ?',
            'googleSignIn': 'Continuer avec Google',
            'fillAllFields': 'Veuillez remplir tous les champs',
            'loginSuccess': 'Connexion réussie !',
            'loginFailed': 'Échec de la connexion',
            'loginFailedDescription': 'Échec de la connexion',
            'signUp': 'S\'inscrire'
        }
    }
    
    if lang not in missing_keys:
        return False
    
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the auth.login section
        auth_login_match = re.search(r'"login":\s*{([^}]+(?:{[^}]*}[^}]*)*)}', content, re.DOTALL)
        if not auth_login_match:
            print(f"     [ERROR] No auth.login section found in {lang}")
            return False
        
        login_content = auth_login_match.group(1)
        
        # Add missing keys
        new_lines = []
        existing_lines = login_content.split('\n')
        
        # Add existing content
        for line in existing_lines:
            new_lines.append(line)
        
        # Add missing keys before the last line
        for key, value in missing_keys[lang].items():
            if f'"{key}":' not in login_content:
                new_lines.insert(-1, f'      "{key}": "{value}",')
        
        # Remove trailing comma from last key
        for i in range(len(new_lines) - 2, -1, -1):
            if new_lines[i].strip() and '"' in new_lines[i] and '":' in new_lines[i]:
                new_lines[i] = new_lines[i].rstrip().rstrip(',')
                break
        
        new_login_content = '\n'.join(new_lines)
        
        # Replace the login section
        new_content = re.sub(
            r'"login":\s*{([^}]+(?:{[^}]*}[^}]*)*)}',
            f'"login": {{{new_login_content}}}',
            content,
            flags=re.DOTALL
        )
        
        # Write back
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"     [SUCCESS] Added missing auth.login keys to {lang}")
        return True
        
    except Exception as e:
        print(f"     [ERROR] Failed to add auth.login keys to {lang}: {str(e)}")
        return False

def add_missing_api_credentials_keys(lang):
    """Add missing apiCredentials keys"""
    
    missing_keys = {
        'fr': {
            'autoTradingStatusChanged': 'Statut de trading automatique modifié',
            'credentialAddedLimitReached': 'Limite d\'ajout d\'identifiants atteinte',
            'autoTradingUpdateFailed': 'Échec de la mise à jour du trading automatique',
            'autoTradingUpdateFailedDesc': 'Impossible de mettre à jour le statut du trading automatique',
            'noCredentials': 'Aucun identifiant',
            'tier1Limit': 'Limite niveau 1',
            'tier2Limit': 'Limite niveau 2',
            'tier2PerCex': 'Limite niveau 2 par exchange',
            'tier3Limit': 'Limite niveau 3',
            'tierUndetermined': 'Niveau indéterminé',
            'binanceNote': 'Note Binance',
            'binanceUsNote': 'Note Binance US',
            'bitsoNote': 'Note Bitso',
            'krakenNote': 'Note Kraken',
            'standardNote': 'Note standard',
            'autoTradingDisabled': 'Trading automatique désactivé',
            'autoTradingEnabled': 'Trading automatique activé',
            'tier2LimitReached': 'Limite niveau 2 atteinte',
            'updateCredentialsMessage': 'Message de mise à jour des identifiants'
        },
        'zh': {
            'autoTradingStatusChanged': '自动交易状态已更改',
            'credentialAddedLimitReached': '凭据添加限制已达到',
            'autoTradingUpdateFailed': '自动交易更新失败',
            'autoTradingUpdateFailedDesc': '无法更新自动交易状态',
            'noCredentials': '无凭据',
            'tier1Limit': '等级1限制',
            'tier2Limit': '等级2限制',
            'tier2PerCex': '每个交易所等级2限制',
            'tier3Limit': '等级3限制',
            'tierUndetermined': '等级未确定',
            'binanceNote': 'Binance说明',
            'binanceUsNote': 'Binance US说明',
            'bitsoNote': 'Bitso说明',
            'krakenNote': 'Kraken说明',
            'standardNote': '标准说明',
            'autoTradingDisabled': '自动交易已禁用',
            'autoTradingEnabled': '自动交易已启用',
            'tier2LimitReached': '等级2限制已达到',
            'updateCredentialsMessage': '更新凭据消息'
        }
    }
    
    if lang not in missing_keys:
        return False
    
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the apiCredentials section
        api_creds_match = re.search(r'"apiCredentials":\s*{([^}]+(?:{[^}]*}[^}]*)*)}', content, re.DOTALL)
        if not api_creds_match:
            print(f"     [ERROR] No apiCredentials section found in {lang}")
            return False
        
        api_content = api_creds_match.group(1)
        
        # Add missing keys to appropriate subsections
        # This is a simplified approach - add keys to the main apiCredentials section
        new_lines = []
        existing_lines = api_content.split('\n')
        
        # Add existing content
        for line in existing_lines:
            new_lines.append(line)
        
        # Add missing keys before the last line
        for key, value in missing_keys[lang].items():
            if f'"{key}":' not in api_content:
                new_lines.insert(-1, f'    "{key}": "{value}",')
        
        # Remove trailing comma from last key
        for i in range(len(new_lines) - 2, -1, -1):
            if new_lines[i].strip() and '"' in new_lines[i] and '":' in new_lines[i]:
                new_lines[i] = new_lines[i].rstrip().rstrip(',')
                break
        
        new_api_content = '\n'.join(new_lines)
        
        # Replace the apiCredentials section
        new_content = re.sub(
            r'"apiCredentials":\s*{([^}]+(?:{[^}]*}[^}]*)*)}',
            f'"apiCredentials": {{{new_api_content}}}',
            content,
            flags=re.DOTALL
        )
        
        # Write back
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"     [SUCCESS] Added missing apiCredentials keys to {lang}")
        return True
        
    except Exception as e:
        print(f"     [ERROR] Failed to add apiCredentials keys to {lang}: {str(e)}")
        return False

def run_missing_keys_fix():
    """Run the missing keys fix for all languages"""
    print("ADDING ALL MISSING TRANSLATION KEYS")
    print("=" * 60)
    
    print("\n1. Adding missing auth.login keys:")
    print("-" * 40)
    auth_languages = ['ko', 'ja', 'de', 'fr']
    auth_success = 0
    
    for lang in auth_languages:
        print(f"  Processing {lang.upper()}:")
        if add_missing_auth_login_keys(lang):
            auth_success += 1
    
    print("\n2. Adding missing apiCredentials keys:")
    print("-" * 40)
    api_languages = ['fr', 'zh']
    api_success = 0
    
    for lang in api_languages:
        print(f"  Processing {lang.upper()}:")
        if add_missing_api_credentials_keys(lang):
            api_success += 1
    
    print("\n" + "=" * 60)
    print("MISSING KEYS FIX SUMMARY")
    print("=" * 60)
    print(f"Auth.login keys added: {auth_success}/{len(auth_languages)} languages")
    print(f"ApiCredentials keys added: {api_success}/{len(api_languages)} languages")
    
    total_success = auth_success + api_success
    total_languages = len(auth_languages) + len(api_languages)
    
    if total_success == total_languages:
        print("\n✅ ALL MISSING KEYS ADDED SUCCESSFULLY!")
        print("✅ Translation completion should improve significantly")
        
        print("\n🎯 EXPECTED RESULTS:")
        print("• Korean, Japanese, German: 98%+ completion")
        print("• French: 96%+ completion")
        print("• Chinese: 98%+ completion")
        print("• Portuguese: Still 100% (unchanged)")
        print("• Spanish: Still 97%+ (unchanged)")
        
        print("\n🎯 NEXT STEPS:")
        print("1. Restart frontend development server")
        print("2. Run translation validation test")
        print("3. Verify improved completion percentages")
        print("4. Test functionality in all languages")
        
        return True
    else:
        print(f"\n⚠️ {total_languages - total_success} language(s) had issues")
        print("Some manual review may be required")
        return False

if __name__ == "__main__":
    success = run_missing_keys_fix()
    sys.exit(0 if success else 1)
