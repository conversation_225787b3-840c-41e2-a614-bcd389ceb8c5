#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add deactivated account translations to all language files
"""
import os
import re

# Language files to update (excluding en, es, pt which are already done)
languages = {
    'de': {
        'title': 'Konto deaktiviert',
        'message': 'Ihr Konto wurde deaktiviert und der Zugang zu Handelsfunktionen ist eingeschränkt.',
        'nextSteps': 'Was als nächstes zu tun ist:',
        'step1': 'Kontaktieren Sie unser Support-Team für Hilfe',
        'step2': 'Geben Sie Ihre Kontodaten und den Grund für die Deaktivierung an',
        'step3': 'Warten Sie auf die Kontoüberprüfung und Reaktivierung',
        'emailSupport': 'E-Mail-Support',
        'callSupport': 'Support anrufen',
        'note': 'Hinweis:',
        'noteText': 'Ihre Kontodaten werden gespeichert und bei der Reaktivierung wiederhergestellt. Handelsaktivitäten sind aus Sicherheitsgründen vorübergehend ausgesetzt.'
    },
    'fr': {
        'title': 'Compte désactivé',
        'message': 'Votre compte a été désactivé et l\'accès aux fonctionnalités de trading est restreint.',
        'nextSteps': 'Que faire ensuite:',
        'step1': 'Contactez notre équipe de support pour obtenir de l\'aide',
        'step2': 'Fournissez les détails de votre compte et la raison de la désactivation',
        'step3': 'Attendez la révision du compte et la réactivation',
        'emailSupport': 'Support par e-mail',
        'callSupport': 'Appeler le support',
        'note': 'Note:',
        'noteText': 'Vos données de compte sont préservées et seront restaurées lors de la réactivation. Les activités de trading sont temporairement suspendues pour des raisons de sécurité.'
    },
    'ko': {
        'title': '계정 비활성화됨',
        'message': '귀하의 계정이 비활성화되었으며 거래 기능에 대한 액세스가 제한됩니다.',
        'nextSteps': '다음에 할 일:',
        'step1': '지원팀에 도움을 요청하세요',
        'step2': '계정 세부 정보와 비활성화 사유를 제공하세요',
        'step3': '계정 검토 및 재활성화를 기다리세요',
        'emailSupport': '이메일 지원',
        'callSupport': '지원팀 전화',
        'note': '참고:',
        'noteText': '계정 데이터는 보존되며 재활성화 시 복원됩니다. 보안상의 이유로 거래 활동이 일시적으로 중단됩니다.'
    },
    'ja': {
        'title': 'アカウントが無効化されました',
        'message': 'あなたのアカウントは無効化され、取引機能へのアクセスが制限されています。',
        'nextSteps': '次にすべきこと:',
        'step1': 'サポートチームにお問い合わせください',
        'step2': 'アカウントの詳細と無効化の理由をお知らせください',
        'step3': 'アカウントの審査と再有効化をお待ちください',
        'emailSupport': 'メールサポート',
        'callSupport': 'サポートに電話',
        'note': '注意:',
        'noteText': 'アカウントデータは保持され、再有効化時に復元されます。セキュリティ上の理由により、取引活動は一時的に停止されています。'
    },
    'zh': {
        'title': '账户已停用',
        'message': '您的账户已被停用，交易功能的访问受到限制。',
        'nextSteps': '接下来该做什么：',
        'step1': '联系我们的支持团队寻求帮助',
        'step2': '提供您的账户详细信息和停用原因',
        'step3': '等待账户审核和重新激活',
        'emailSupport': '邮件支持',
        'callSupport': '致电支持',
        'note': '注意：',
        'noteText': '您的账户数据已保留，将在重新激活时恢复。出于安全原因，交易活动暂时暂停。'
    }
}

def add_translations():
    """Add deactivated account translations to language files"""
    base_path = "../frontend/src/i18n/locales"
    
    for lang_code, translations in languages.items():
        file_path = os.path.join(base_path, lang_code, "common.ts")
        
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            continue
        
        try:
            # Read the file
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find the account section and add deactivated translations
            account_pattern = r'(\s*"account":\s*{[^}]*"accountDeleted":\s*"[^"]*",)\s*(\n\s*})'
            
            deactivated_section = f'''
      "deactivated": {{
        "title": "{translations['title']}",
        "message": "{translations['message']}",
        "nextSteps": "{translations['nextSteps']}",
        "step1": "{translations['step1']}",
        "step2": "{translations['step2']}",
        "step3": "{translations['step3']}",
        "emailSupport": "{translations['emailSupport']}",
        "callSupport": "{translations['callSupport']}",
        "note": "{translations['note']}",
        "noteText": "{translations['noteText']}"
      }}'''
            
            replacement = r'\1' + deactivated_section + r',\2'
            
            if re.search(account_pattern, content, re.DOTALL):
                new_content = re.sub(account_pattern, replacement, content, flags=re.DOTALL)
                
                # Write back to file
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"✅ Updated {lang_code}/common.ts")
            else:
                print(f"❌ Could not find account section in {lang_code}/common.ts")
                
        except Exception as e:
            print(f"❌ Error updating {lang_code}/common.ts: {str(e)}")

if __name__ == "__main__":
    print("🌐 Adding deactivated account translations...")
    print("=" * 50)
    add_translations()
    print("\n✅ Translation update complete!")
