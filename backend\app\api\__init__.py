from . import forecast_routes, auth_routes, user_routes, payment_routes, security_routes, trading_routes, trading_container_routes, realtime_routes, nft_routes, referral_routes, solana_routes, admin_routes, support_routes, paper_trading_routes, ip_management_routes, elite_ml_routes

# Export blueprints for registration in __init__.py
blueprints = [
    (forecast_routes.forecast_bp, '/api/forecast'),
    (auth_routes.auth_bp, '/api/auth'),
    (user_routes.user_bp, '/api/users'),
    (payment_routes.payment_bp, '/api/payments'),
    (security_routes.security_bp, '/api/security'),
    (trading_routes.trading_bp, '/api/trading'),
    (trading_container_routes.trading_container_bp, '/api/trading/container'),
    (realtime_routes.realtime_bp, '/api/realtime'),
    (nft_routes.nft_bp, '/api/nft'),
    (referral_routes.referral_bp, '/api/referrals'),
    (solana_routes.solana_bp, '/api/payments/solana'),
    (admin_routes.admin_bp, '/api/admin'),
    (support_routes.support_bp, '/api/support'),
    (paper_trading_routes.paper_trading_bp, '/api/paper-trading'),
    (elite_ml_routes.elite_ml_bp, '/api/elite-ml'),
    (ip_management_routes.ip_mgmt_bp, None)  # Already has /api/admin/ip prefix
]