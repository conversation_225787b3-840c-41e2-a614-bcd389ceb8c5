"""
IP Management API Routes for DeepTrade Admin Dashboard
Handles IP tracking, banning, and monitoring functionality.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import get_jwt, get_jwt_identity
from datetime import datetime, timedelta
import ipaddress

from app.auth.decorators import admin_required
from app.models.ip_tracking import IPAcc<PERSON><PERSON><PERSON>, IPBlacklist, IPRateLimit
from app.models.admin import AdminUser
from app.services.ip_tracking_service import IPTrackingService
from app import db

# Import super_admin_required decorator from admin_routes
def super_admin_required(f):
    """Decorator to require super admin privileges"""
    from functools import wraps
    from flask_jwt_extended import verify_jwt_in_request, get_jwt, get_jwt_identity

    @wraps(f)
    def decorated_function(*args, **kwargs):
        verify_jwt_in_request()
        claims = get_jwt()
        identity = get_jwt_identity()

        # Check for super admin claim
        if not claims.get('is_super_admin', False):
            return jsonify({'message': 'Super admin privileges required', 'error': 'super_admin_required'}), 403

        # Verify admin identity format
        if not identity or not identity.startswith('admin_'):
            return jsonify({'message': 'Invalid admin token', 'error': 'invalid_admin_token'}), 403

        return f(*args, **kwargs)

    return decorated_function

ip_mgmt_bp = Blueprint('ip_management', __name__, url_prefix='/api/admin/ip')


@ip_mgmt_bp.route('/access-logs', methods=['GET'])
@admin_required  # Allow both super admin and limited admin (read-only)
def get_access_logs():
    """Get IP access logs from frontend server only with filtering and pagination. Available to all admin users."""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 50)), 100)
        user_id = request.args.get('user_id')
        admin_id = request.args.get('admin_id')
        ip_address = request.args.get('ip_address')
        days = int(request.args.get('days', 30))
        login_successful = request.args.get('login_successful')

        # Build query - Filter for frontend server logs only (user_id is not null, admin_id is null)
        query = IPAccessLog.query.filter(
            IPAccessLog.user_id.isnot(None),
            IPAccessLog.admin_id.is_(None)
        )
        
        # Apply filters
        if user_id:
            query = query.filter(IPAccessLog.user_id == user_id)
        if admin_id:
            query = query.filter(IPAccessLog.admin_id == admin_id)
        if ip_address:
            query = query.filter(IPAccessLog.ip_address.like(f'%{ip_address}%'))
        if login_successful is not None:
            query = query.filter(IPAccessLog.login_successful == (login_successful.lower() == 'true'))
        
        # Date filter
        if days > 0:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            query = query.filter(IPAccessLog.login_timestamp >= cutoff_date)
        
        # Order and paginate
        query = query.order_by(IPAccessLog.login_timestamp.desc())
        paginated = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'access_logs': [log.to_dict() for log in paginated.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': paginated.total,
                'pages': paginated.pages,
                'has_next': paginated.has_next,
                'has_prev': paginated.has_prev
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting access logs: {str(e)}")
        return jsonify({'error': 'Failed to get access logs'}), 500


@ip_mgmt_bp.route('/blacklist', methods=['GET'])
@admin_required  # Allow both super admin and limited admin (read-only)
def get_ip_blacklist():
    """Get IP blacklist with filtering. Available to all admin users."""
    try:
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 50)), 100)
        is_active = request.args.get('is_active')
        
        query = IPBlacklist.query
        
        if is_active is not None:
            query = query.filter(IPBlacklist.is_active == (is_active.lower() == 'true'))
        
        query = query.order_by(IPBlacklist.banned_at.desc())
        paginated = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'blacklist': [ban.to_dict() for ban in paginated.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': paginated.total,
                'pages': paginated.pages,
                'has_next': paginated.has_next,
                'has_prev': paginated.has_prev
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting IP blacklist: {str(e)}")
        return jsonify({'error': 'Failed to get IP blacklist'}), 500


@ip_mgmt_bp.route('/ban', methods=['POST'])
@super_admin_required  # Require super admin for write operations
def ban_ip_address():
    """Ban an IP address. Requires super admin privileges."""
    try:
        data = request.get_json()
        ip_address = data.get('ip_address', '').strip()
        reason = data.get('reason', '').strip()
        expires_at_str = data.get('expires_at')
        
        if not ip_address or not reason:
            return jsonify({'error': 'IP address and reason are required'}), 400
        
        # Validate IP address
        try:
            ipaddress.ip_address(ip_address)
        except ValueError:
            return jsonify({'error': 'Invalid IP address format'}), 400
        
        # Parse expiration date
        expires_at = None
        if expires_at_str:
            try:
                expires_at = datetime.fromisoformat(expires_at_str.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({'error': 'Invalid expiration date format'}), 400
        
        # Get admin ID from JWT
        claims = get_jwt()
        admin_id = claims.get('admin_id')
        
        # Ban the IP
        ban = IPBlacklist.ban_ip(
            ip_address=ip_address,
            reason=reason,
            banned_by_admin_id=admin_id,
            expires_at=expires_at,
            ban_type='manual'
        )
        
        return jsonify({
            'message': 'IP address banned successfully',
            'ban': ban.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error banning IP: {str(e)}")
        return jsonify({'error': 'Failed to ban IP address'}), 500


@ip_mgmt_bp.route('/unban', methods=['POST'])
@super_admin_required  # Require super admin for write operations
def unban_ip_address():
    """Unban an IP address. Requires super admin privileges."""
    try:
        data = request.get_json()
        ip_address = data.get('ip_address', '').strip()
        
        if not ip_address:
            return jsonify({'error': 'IP address is required'}), 400
        
        # Unban the IP
        ban = IPBlacklist.unban_ip(ip_address)
        
        if not ban:
            return jsonify({'error': 'IP address not found in blacklist'}), 404
        
        return jsonify({
            'message': 'IP address unbanned successfully',
            'ban': ban.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error unbanning IP: {str(e)}")
        return jsonify({'error': 'Failed to unban IP address'}), 500


@ip_mgmt_bp.route('/summary/<ip_address>', methods=['GET'])
@admin_required  # Allow both super admin and limited admin (read-only)
def get_ip_summary(ip_address):
    """Get comprehensive summary for an IP address. Available to all admin users."""
    try:
        summary = IPTrackingService.get_ip_summary(ip_address)
        return jsonify(summary), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting IP summary: {str(e)}")
        return jsonify({'error': 'Failed to get IP summary'}), 500


@ip_mgmt_bp.route('/suspicious', methods=['GET'])
@admin_required  # Allow both super admin and limited admin (read-only)
def get_suspicious_ips():
    """Get list of suspicious IP addresses. Available to all admin users."""
    try:
        days = int(request.args.get('days', 7))
        min_attempts = int(request.args.get('min_attempts', 5))
        
        suspicious_ips = IPTrackingService.get_suspicious_ips(days, min_attempts)
        
        return jsonify({
            'suspicious_ips': suspicious_ips,
            'criteria': {
                'days': days,
                'min_failed_attempts': min_attempts
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting suspicious IPs: {str(e)}")
        return jsonify({'error': 'Failed to get suspicious IPs'}), 500


@ip_mgmt_bp.route('/rate-limits', methods=['GET'])
@admin_required  # Allow both super admin and limited admin (read-only)
def get_rate_limits():
    """Get IP rate limiting status. Available to all admin users."""
    try:
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 50)), 100)
        
        query = IPRateLimit.query.order_by(IPRateLimit.last_attempt_at.desc())
        paginated = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'rate_limits': [limit.to_dict() for limit in paginated.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': paginated.total,
                'pages': paginated.pages,
                'has_next': paginated.has_next,
                'has_prev': paginated.has_prev
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting rate limits: {str(e)}")
        return jsonify({'error': 'Failed to get rate limits'}), 500


@ip_mgmt_bp.route('/rate-limits/reset', methods=['POST'])
@super_admin_required  # Require super admin for write operations
def reset_rate_limit():
    """Reset rate limiting for an IP address. Requires super admin privileges."""
    try:
        data = request.get_json()
        ip_address = data.get('ip_address', '').strip()
        
        if not ip_address:
            return jsonify({'error': 'IP address is required'}), 400
        
        rate_limit = IPRateLimit.reset_rate_limit(ip_address)
        
        return jsonify({
            'message': 'Rate limit reset successfully',
            'rate_limit': rate_limit.to_dict() if rate_limit else None
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error resetting rate limit: {str(e)}")
        return jsonify({'error': 'Failed to reset rate limit'}), 500


@ip_mgmt_bp.route('/stats', methods=['GET'])
@admin_required  # Allow both super admin and limited admin (read-only)
def get_ip_stats():
    """Get IP tracking statistics. Available to all admin users."""
    try:
        days = int(request.args.get('days', 30))
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Total logins
        total_logins = IPAccessLog.query.filter(
            IPAccessLog.login_timestamp >= cutoff_date
        ).count()
        
        # Successful logins
        successful_logins = IPAccessLog.query.filter(
            IPAccessLog.login_timestamp >= cutoff_date,
            IPAccessLog.login_successful == True
        ).count()
        
        # Failed logins
        failed_logins = total_logins - successful_logins
        
        # Unique IPs
        unique_ips = db.session.query(IPAccessLog.ip_address).filter(
            IPAccessLog.login_timestamp >= cutoff_date
        ).distinct().count()
        
        # Active bans
        active_bans = IPBlacklist.query.filter(IPBlacklist.is_active == True).count()
        
        # Rate limited IPs
        rate_limited = IPRateLimit.query.filter(
            IPRateLimit.blocked_until > datetime.utcnow()
        ).count()
        
        return jsonify({
            'period_days': days,
            'total_logins': total_logins,
            'successful_logins': successful_logins,
            'failed_logins': failed_logins,
            'success_rate': round((successful_logins / total_logins * 100) if total_logins > 0 else 0, 2),
            'unique_ips': unique_ips,
            'active_bans': active_bans,
            'rate_limited_ips': rate_limited
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting IP stats: {str(e)}")
        return jsonify({'error': 'Failed to get IP statistics'}), 500


@ip_mgmt_bp.route('/cleanup', methods=['POST'])
@admin_required
def cleanup_old_logs():
    """Clean up old IP access logs."""
    try:
        # Check if user is super admin
        claims = get_jwt()
        if not claims.get('is_super_admin', False):
            return jsonify({'error': 'Super admin privileges required'}), 403
        
        data = request.get_json()
        days_to_keep = int(data.get('days_to_keep', 90))
        
        if days_to_keep < 1:
            return jsonify({'error': 'Must keep at least 1 day of logs'}), 400
        
        deleted_count = IPTrackingService.cleanup_old_logs(days_to_keep)
        
        return jsonify({
            'message': f'Cleaned up {deleted_count} old log entries',
            'deleted_count': deleted_count,
            'days_kept': days_to_keep
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error cleaning up logs: {str(e)}")
        return jsonify({'error': 'Failed to clean up logs'}), 500


@ip_mgmt_bp.route('/admin-connections', methods=['GET'])
@admin_required  # Allow both super admin and limited admin (read-only)
def get_admin_connections():
    """Get admin connection logs with IP information. Available to all admin users."""
    try:
        # Get all admin users with their IP information
        admins = AdminUser.query.filter_by(is_active=True).all()
        admin_connections = []

        for admin in admins:
            # Get recent IP access logs for this admin
            recent_logs = IPAccessLog.query.filter_by(
                admin_id=admin.id,
                login_successful=True
            ).order_by(IPAccessLog.login_timestamp.desc()).limit(5).all()

            # Get unique IPs for this admin
            unique_ips = db.session.query(IPAccessLog.ip_address).filter_by(
                admin_id=admin.id,
                login_successful=True
            ).distinct().all()

            admin_data = {
                'admin_id': admin.id,
                'username': admin.username,
                'is_super_admin': admin.is_super_admin,
                'last_ip_address': admin.last_ip_address,
                'last_login_ip': admin.last_login_ip,
                'ip_login_count': admin.ip_login_count or 0,
                'last_login': admin.last_login.isoformat() if admin.last_login else None,
                'recent_logs': [log.to_dict() for log in recent_logs],
                'unique_ip_count': len(unique_ips),
                'unique_ips': [ip[0] for ip in unique_ips]
            }

            admin_connections.append(admin_data)

        return jsonify({
            'admin_connections': admin_connections
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting admin connections: {str(e)}")
        return jsonify({'error': 'Failed to get admin connections'}), 500
