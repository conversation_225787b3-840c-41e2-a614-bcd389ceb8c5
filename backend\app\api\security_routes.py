"""
Security Routes for DeepTrade Platform
Handles security operations, logs, and monitoring.
"""

from flask import Blueprint, request, jsonify, current_app
from app.auth.decorators import jwt_required
from app.auth.security import SecurityManager
from app.models.user import User
from app.models.security_log import SecurityLog, LoginAttempt
from app import db
from datetime import datetime, timedelta

security_bp = Blueprint('security', __name__, url_prefix='/api/security')

@security_bp.route('/logs', methods=['GET'])
@jwt_required()
def get_security_logs():
    """Get security logs for the authenticated user."""
    try:
        user_id = request.user_id
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get query parameters
        limit = min(int(request.args.get('limit', 50)), 200)
        offset = int(request.args.get('offset', 0))
        event_type = request.args.get('event_type')
        risk_level = request.args.get('risk_level')
        
        # Build query
        query = SecurityLog.query.filter_by(user_id=user_id)
        
        if event_type:
            query = query.filter_by(event_type=event_type)
        if risk_level:
            query = query.filter_by(risk_level=risk_level)
        
        logs = query.order_by(SecurityLog.created_at.desc())\
                   .limit(limit).offset(offset).all()
        
        # Log API access
        SecurityManager.log_api_access(
            user_id=user_id,
            endpoint='GET /api/security/logs',
            ip_address=request.remote_addr
        )
        
        return jsonify({
            'logs': [log.to_dict() for log in logs],
            'total': SecurityLog.query.filter_by(user_id=user_id).count(),
            'limit': limit,
            'offset': offset
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting security logs: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@security_bp.route('/login-attempts', methods=['GET'])
@jwt_required()
def get_login_attempts():
    """Get login attempts for the authenticated user."""
    try:
        user_id = request.user_id
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get query parameters
        limit = min(int(request.args.get('limit', 20)), 100)
        offset = int(request.args.get('offset', 0))
        days = int(request.args.get('days', 30))
        
        # Calculate date range
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Get login attempts
        attempts = LoginAttempt.query.filter(
            LoginAttempt.user_id == user_id,
            LoginAttempt.attempted_at >= cutoff_date
        ).order_by(LoginAttempt.attempted_at.desc())\
         .limit(limit).offset(offset).all()
        
        return jsonify({
            'login_attempts': [attempt.to_dict() for attempt in attempts],
            'total': LoginAttempt.query.filter(
                LoginAttempt.user_id == user_id,
                LoginAttempt.attempted_at >= cutoff_date
            ).count(),
            'limit': limit,
            'offset': offset,
            'days': days
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting login attempts: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@security_bp.route('/sessions', methods=['GET'])
@jwt_required()
def get_active_sessions():
    """Get active sessions for the authenticated user."""
    try:
        user_id = request.user_id
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get recent successful login attempts (proxy for active sessions)
        recent_time = datetime.utcnow() - timedelta(hours=24)
        
        recent_logins = LoginAttempt.query.filter(
            LoginAttempt.user_id == user_id,
            LoginAttempt.success == True,
            LoginAttempt.attempted_at >= recent_time
        ).order_by(LoginAttempt.attempted_at.desc()).all()
        
        # Group by IP address to simulate sessions
        sessions = {}
        for login in recent_logins:
            ip = login.ip_address
            if ip not in sessions:
                sessions[ip] = {
                    'ip_address': ip,
                    'user_agent': login.user_agent,
                    'first_login': login.attempted_at,
                    'last_activity': login.attempted_at,
                    'is_current': ip == request.remote_addr
                }
            else:
                if login.attempted_at > sessions[ip]['last_activity']:
                    sessions[ip]['last_activity'] = login.attempted_at
        
        session_list = list(sessions.values())
        
        return jsonify({
            'active_sessions': session_list,
            'current_ip': request.remote_addr,
            'total_sessions': len(session_list)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting active sessions: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@security_bp.route('/sessions/terminate', methods=['POST'])
@jwt_required()
def terminate_sessions():
    """Terminate sessions (logout from all devices)."""
    try:
        user_id = request.user_id
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        data = request.get_json() or {}
        terminate_all = data.get('all', False)
        ip_address = data.get('ip_address')
        
        if terminate_all:
            # Revoke all tokens (would need JWT blacklist implementation)
            # For now, just log the action
            SecurityManager.log_security_event(
                user_id=user_id,
                event_type='all_sessions_terminated',
                ip_address=request.remote_addr,
                risk_level='medium'
            )
            message = 'All sessions terminated successfully'
        
        elif ip_address:
            # Terminate specific session
            SecurityManager.log_security_event(
                user_id=user_id,
                event_type='session_terminated',
                ip_address=request.remote_addr,
                details={'terminated_ip': ip_address},
                risk_level='low'
            )
            message = f'Session for IP {ip_address} terminated successfully'
        
        else:
            return jsonify({'error': 'Must specify either "all" or "ip_address"'}), 400
        
        return jsonify({'message': message}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error terminating sessions: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@security_bp.route('/suspicious-activity', methods=['GET'])
@jwt_required()
def get_suspicious_activity():
    """Get suspicious activity detection results."""
    try:
        user_id = request.user_id
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Check for various suspicious activities
        suspicious_activities = []
        
        # Check for failed login attempts
        recent_time = datetime.utcnow() - timedelta(hours=1)
        failed_logins = LoginAttempt.query.filter(
            LoginAttempt.user_id == user_id,
            LoginAttempt.success == False,
            LoginAttempt.attempted_at >= recent_time
        ).count()
        
        if failed_logins > 3:
            suspicious_activities.append({
                'type': 'multiple_failed_logins',
                'severity': 'medium',
                'count': failed_logins,
                'description': f'{failed_logins} failed login attempts in the last hour'
            })
        
        # Check for logins from new locations
        recent_logins = LoginAttempt.query.filter(
            LoginAttempt.user_id == user_id,
            LoginAttempt.success == True,
            LoginAttempt.attempted_at >= datetime.utcnow() - timedelta(days=7)
        ).all()
        
        historical_ips = set()
        recent_ips = set()
        
        for login in recent_logins:
            if login.attempted_at >= datetime.utcnow() - timedelta(days=1):
                recent_ips.add(login.ip_address)
            else:
                historical_ips.add(login.ip_address)
        
        new_ips = recent_ips - historical_ips
        if new_ips:
            suspicious_activities.append({
                'type': 'new_location_login',
                'severity': 'low',
                'new_ips': list(new_ips),
                'description': f'Login from {len(new_ips)} new IP address(es)'
            })
        
        # Check for unusual API activity
        api_logs = SecurityLog.query.filter(
            SecurityLog.user_id == user_id,
            SecurityLog.event_type == 'api_access',
            SecurityLog.created_at >= recent_time
        ).count()
        
        if api_logs > 100:  # Arbitrary threshold
            suspicious_activities.append({
                'type': 'high_api_usage',
                'severity': 'low',
                'count': api_logs,
                'description': f'{api_logs} API calls in the last hour'
            })
        
        return jsonify({
            'suspicious_activities': suspicious_activities,
            'risk_score': len(suspicious_activities),
            'last_checked': datetime.utcnow().isoformat()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting suspicious activity: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@security_bp.route('/report-issue', methods=['POST'])
@jwt_required()
def report_security_issue():
    """Report a security issue or concern."""
    try:
        user_id = request.user_id
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        issue_type = data.get('issue_type')
        description = data.get('description')
        
        if not issue_type or not description:
            return jsonify({'error': 'Missing required fields: issue_type, description'}), 400
        
        # Log the security issue report
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='security_issue_reported',
            ip_address=request.remote_addr,
            details={
                'issue_type': issue_type,
                'description': description
            },
            risk_level='high'
        )
        
        # Generate issue ID
        issue_id = f"SEC_{user_id[:8]}_{int(datetime.utcnow().timestamp())}"
        
        return jsonify({
            'message': 'Security issue reported successfully',
            'issue_id': issue_id,
            'status': 'submitted',
            'response_time': '24-48 hours'
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error reporting security issue: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@security_bp.route('/settings', methods=['GET'])
@jwt_required()
def get_security_settings():
    """Get security settings for the user."""
    try:
        user_id = request.user_id
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify({
            'security_settings': {
                'two_fa_enabled': user.two_fa_enabled,
                'email_notifications': user.notification_preferences.get('security_alerts', True) if user.notification_preferences else True,
                'login_notifications': user.notification_preferences.get('login_alerts', True) if user.notification_preferences else True,
                'session_timeout': 3600,  # 1 hour in seconds
                'max_failed_attempts': 5,
                'account_locked': not user.is_active
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting security settings: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@security_bp.route('/settings', methods=['PUT'])
@jwt_required
def update_security_settings():
    """Update security settings for the user."""
    try:
        user_id = request.user_id
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Update notification preferences
        if 'email_notifications' in data or 'login_notifications' in data:
            prefs = user.notification_preferences or {}
            
            if 'email_notifications' in data:
                prefs['security_alerts'] = bool(data['email_notifications'])
            if 'login_notifications' in data:
                prefs['login_alerts'] = bool(data['login_notifications'])
            
            user.notification_preferences = prefs
        
        db.session.commit()
        
        # Log settings update
        SecurityManager.log_security_event(
            user_id=user_id,
            event_type='security_settings_updated',
            ip_address=request.remote_addr,
            details={'updated_settings': list(data.keys())},
            risk_level='low'
        )
        
        return jsonify({'message': 'Security settings updated successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error updating security settings: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500