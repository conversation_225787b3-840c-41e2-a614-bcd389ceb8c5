"""
Solana Payment API Routes
Handles USDT payments on Solana blockchain for profit sharing and membership fees.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from decimal import Decimal
import time
from datetime import datetime, timezone
from app import db
from app.models.user import User
from app.models.user_tier_status import UserTierStatus
from app.models.solana_payment import SolanaPayment, SolanaPaymentType, SolanaPaymentStatus, MembershipBilling
from app.services.solana_service import SolanaService
from app.auth.security import SecurityManager

solana_bp = Blueprint('solana', __name__, url_prefix='/api/payments/solana')


@solana_bp.route('/test', methods=['GET'])
def test_route():
    """Test route to verify blueprint is working."""
    return jsonify({'message': 'Solana blueprint is working!'}), 200





@solana_bp.route('/test-payment', methods=['POST'])
@jwt_required()
def test_payment_creation():
    """Test payment creation without real blockchain transaction."""
    try:
        user_id = get_jwt_identity()
        current_app.logger.info(f"=== TEST PAYMENT CREATION ===")
        current_app.logger.info(f"User ID: {user_id}")

        # Get treasury wallet from environment
        treasury_wallet = current_app.config.get('SOLANA_TREASURY_WALLET')

        # Simulate payment data
        test_data = {
            'payment_type': 'membership_fee',
            'amount': 0.001,
            'wallet_address': treasury_wallet,
            'transaction_signature': f'TEST_SIGNATURE_{int(time.time())}'
        }

        current_app.logger.info(f"Test data: {test_data}")

        # Test the payment creation logic
        payment_type = test_data['payment_type']
        amount = test_data['amount']
        wallet_address = test_data['wallet_address']
        transaction_signature = test_data['transaction_signature']

        # Create or update payment record in database
        current_app.logger.info("Creating test payment record in database...")

        # Check if payment with this transaction signature already exists
        existing_payment = None
        if transaction_signature:
            existing_payment = SolanaPayment.query.filter_by(
                transaction_signature=transaction_signature
            ).first()

        if existing_payment:
            current_app.logger.info(f"Payment with transaction signature {transaction_signature} already exists, updating...")
            payment = existing_payment
            # Update existing payment
            payment.user_id = user_id
            payment.payment_type = SolanaPaymentType(payment_type)
            payment.amount = Decimal(str(amount))
            payment.from_address = wallet_address
            payment.status = SolanaPaymentStatus.CONFIRMED
            payment.processed_at = datetime.now(timezone.utc)
            payment.updated_at = datetime.now(timezone.utc)
        else:
            # Create new payment record
            payment = SolanaPayment(
                user_id=user_id,
                payment_type=SolanaPaymentType(payment_type),
                amount=Decimal(str(amount)),
                to_address=current_app.config.get('SOLANA_TREASURY_WALLET', '9tveNp6FvLn857ZSMNhAJhzG3vDmkDXABgFDdX7iQiPD'),
                from_address=wallet_address,
                transaction_signature=transaction_signature
            )

            # If transaction signature is provided, mark as confirmed
            if transaction_signature:
                payment.status = SolanaPaymentStatus.CONFIRMED
                payment.processed_at = datetime.now(timezone.utc)

            db.session.add(payment)

        db.session.commit()
        current_app.logger.info(f"Test payment {payment.id} processed successfully")

        # Test membership processing
        if payment_type == 'membership_fee':
            from app.services.membership_service import MembershipService
            membership_result = MembershipService.process_successful_payment(
                user_id,
                payment.id
            )
            current_app.logger.info(f"Membership processing result: {membership_result}")

        return jsonify({
            'success': True,
            'message': 'Test payment created successfully',
            'payment': {
                'id': payment.id,
                'amount': float(amount),
                'status': payment.status.value,
                'wallet_address': wallet_address,
                'transaction_signature': transaction_signature,
                'payment_type': payment_type
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"=== TEST PAYMENT ERROR ===")
        current_app.logger.error(f"Error type: {type(e).__name__}")
        current_app.logger.error(f"Error message: {str(e)}")
        import traceback
        current_app.logger.error(f"Full traceback: {traceback.format_exc()}")
        current_app.logger.error(f"=== END TEST PAYMENT ERROR ===")
        db.session.rollback()
        return jsonify({'error': f'Test payment failed: {str(e)}'}), 500


@solana_bp.route('/create-payment', methods=['POST'])
@jwt_required()
def create_solana_payment():
    """Create a new Solana USDT payment request - SIMPLIFIED VERSION."""
    current_app.logger.info("=== CREATE PAYMENT ENDPOINT CALLED (SIMPLIFIED) ===")

    try:
        user_id = get_jwt_identity()
        current_app.logger.info(f"User ID: {user_id}")

        data = request.get_json()
        current_app.logger.info(f"Request data: {data}")

        payment_type = data.get('payment_type')
        amount = data.get('amount')
        wallet_address = data.get('wallet_address')
        transaction_signature = data.get('transaction_signature')
        current_app.logger.info(f"Payment type: {payment_type}, Amount: {amount}, Wallet: {wallet_address}, Signature: {transaction_signature}")

        if not payment_type or not amount or not wallet_address:
            return jsonify({'error': 'Payment type, amount, and wallet address required'}), 400

        if payment_type not in ['profit_share', 'membership_fee', 'referral_payout']:
            return jsonify({'error': 'Invalid payment type'}), 400

        if float(amount) <= 0:
            return jsonify({'error': 'Amount must be greater than 0'}), 400

        # Create payment record using same logic as test endpoint
        current_app.logger.info("Creating payment record in database...")

        # Check if payment with this transaction signature already exists
        existing_payment = None
        if transaction_signature:
            existing_payment = SolanaPayment.query.filter_by(
                transaction_signature=transaction_signature
            ).first()

        if existing_payment:
            current_app.logger.info(f"Payment with transaction signature {transaction_signature} already exists, updating...")
            payment = existing_payment
            # Update existing payment
            payment.user_id = user_id
            payment.payment_type = SolanaPaymentType(payment_type)
            payment.amount = Decimal(str(amount))
            payment.from_address = wallet_address
            payment.status = SolanaPaymentStatus.CONFIRMED
            payment.processed_at = datetime.now(timezone.utc)
            payment.updated_at = datetime.now(timezone.utc)
        else:
            # Create new payment record
            payment = SolanaPayment(
                user_id=user_id,
                payment_type=SolanaPaymentType(payment_type),
                amount=Decimal(str(amount)),
                to_address=current_app.config.get('SOLANA_TREASURY_WALLET', '9tveNp6FvLn857ZSMNhAJhzG3vDmkDXABgFDdX7iQiPD'),
                from_address=wallet_address,
                transaction_signature=transaction_signature
            )

            # If transaction signature is provided, mark as confirmed
            if transaction_signature:
                payment.status = SolanaPaymentStatus.CONFIRMED
                payment.processed_at = datetime.now(timezone.utc)

            db.session.add(payment)

        db.session.commit()
        current_app.logger.info(f"Payment {payment.id} processed successfully")

        # Process membership if needed
        if payment_type == 'membership_fee':
            try:
                from app.services.membership_service import MembershipService
                membership_result = MembershipService.process_successful_payment(
                    user_id,
                    payment.id
                )
                current_app.logger.info(f"Membership processing result: {membership_result}")
            except Exception as membership_error:
                current_app.logger.error(f"Membership processing error: {str(membership_error)}")
                # Don't fail the payment if membership processing fails
                pass

        # Process profit share payment if needed
        elif payment_type == 'profit_share':
            try:
                from app.models.user_tier_status import UserTierStatus
                tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()

                if tier_status and transaction_signature:
                    # Clear debt and re-enable account if disabled
                    tier_status.clear_debt(float(amount))

                    # Update next payday deadline if account was re-enabled
                    if not tier_status.account_disabled and tier_status.profit_share_owed == 0:
                        tier_status.update_payday_deadline()

                    db.session.commit()
                    current_app.logger.info(f"Profit share payment processed for user {user_id}, remaining debt: {tier_status.profit_share_owed}")

            except Exception as profit_share_error:
                current_app.logger.error(f"Profit share processing error: {str(profit_share_error)}")
                # Don't fail the payment if profit share processing fails
                pass

        return jsonify({
            'success': True,
            'message': 'Payment created successfully',
            'payment': {
                'id': payment.id,
                'amount': float(amount),
                'status': payment.status.value,
                'wallet_address': wallet_address,
                'transaction_signature': transaction_signature,
                'payment_type': payment_type
            }
        }), 201

    except Exception as e:
        current_app.logger.error(f"=== PAYMENT CREATION ERROR ===")
        current_app.logger.error(f"Error type: {type(e).__name__}")
        current_app.logger.error(f"Error message: {str(e)}")
        import traceback
        current_app.logger.error(f"Full traceback: {traceback.format_exc()}")
        current_app.logger.error(f"=== END PAYMENT CREATION ERROR ===")
        db.session.rollback()
        return jsonify({'error': f'Failed to create payment: {str(e)}'}), 500



@solana_bp.route('/submit-signature', methods=['POST'])
@jwt_required()
def submit_transaction_signature():
    """Submit transaction signature for payment verification."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        payment_id = data.get('payment_id')
        signature = data.get('signature')
        sender_address = data.get('sender_address')
        
        if not payment_id or not signature:
            return jsonify({'error': 'Payment ID and transaction signature required'}), 400
        
        # Verify payment belongs to user
        payment = SolanaPayment.query.filter_by(id=payment_id, user_id=user_id).first()
        if not payment:
            return jsonify({'error': 'Payment not found'}), 404
        
        # Submit signature using Solana service
        solana_service = SolanaService()
        result = solana_service.submit_payment_signature(payment_id, signature, sender_address)
        
        if result['success']:
            # Log signature submission
            SecurityManager.log_security_event(
                user_id=user_id,
                event_type='solana_signature_submitted',
                ip_address=request.remote_addr,
                details={
                    'payment_id': payment_id,
                    'signature': signature
                },
                risk_level='medium'
            )
            
            return jsonify(result), 200
        else:
            return jsonify({'error': result['error']}), 400
            
    except Exception as e:
        current_app.logger.error(f"Error submitting transaction signature: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@solana_bp.route('/payment/<payment_id>/status', methods=['GET'])
@jwt_required()
def get_payment_status(payment_id):
    """Get status of a Solana payment."""
    try:
        user_id = get_jwt_identity()
        
        # Verify payment belongs to user
        payment = SolanaPayment.query.filter_by(id=payment_id, user_id=user_id).first()
        if not payment:
            return jsonify({'error': 'Payment not found'}), 404
        
        # Monitor payment if it has a signature
        if payment.transaction_signature and payment.status == SolanaPaymentStatus.PENDING:
            solana_service = SolanaService()
            monitoring_result = solana_service.monitor_payment(payment_id)
            
            return jsonify({
                'payment': payment.to_dict(),
                'monitoring_result': monitoring_result
            }), 200
        else:
            return jsonify({
                'payment': payment.to_dict()
            }), 200
            
    except Exception as e:
        current_app.logger.error(f"Error getting payment status: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@solana_bp.route('/payments', methods=['GET'])
@jwt_required()
def get_user_payments():
    """Get user's Solana payment history."""
    try:
        user_id = get_jwt_identity()
        
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)
        payment_type = request.args.get('type')
        status = request.args.get('status')
        
        # Build query
        query = SolanaPayment.query.filter_by(user_id=user_id)
        
        if payment_type:
            try:
                query = query.filter_by(payment_type=SolanaPaymentType(payment_type))
            except ValueError:
                return jsonify({'error': 'Invalid payment type'}), 400
        
        if status:
            try:
                query = query.filter_by(status=SolanaPaymentStatus(status))
            except ValueError:
                return jsonify({'error': 'Invalid status'}), 400
        
        # Get paginated results
        payments = query.order_by(SolanaPayment.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'payments': [p.to_dict() for p in payments.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': payments.total,
                'pages': payments.pages,
                'has_next': payments.has_next,
                'has_prev': payments.has_prev
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting user payments: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@solana_bp.route('/membership/create-billing', methods=['POST'])
@jwt_required()
def create_membership_billing():
    """Create monthly membership billing for Tier 2 users."""
    try:
        user_id = get_jwt_identity()
        # data = request.get_json()  # Not currently used
        
        # Verify user is Tier 2
        tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()
        if not tier_status or tier_status.get_current_tier() != 2:
            return jsonify({'error': 'Only Tier 2 users require membership billing'}), 400
        
        # Get billing details
        from datetime import date, timedelta
        
        billing_start = date.today().replace(day=1)  # First day of current month
        if billing_start.month == 12:
            billing_end = billing_start.replace(year=billing_start.year + 1, month=1) - timedelta(days=1)
        else:
            billing_end = billing_start.replace(month=billing_start.month + 1) - timedelta(days=1)
        
        payment_due = billing_end + timedelta(days=7)  # Due 7 days after month end
        
        # Check if billing already exists for this period
        existing_billing = MembershipBilling.query.filter(
            MembershipBilling.user_id == user_id,  # type: ignore
            MembershipBilling.billing_period_start == billing_start  # type: ignore
        ).first()
        
        if existing_billing:
            return jsonify({'error': 'Billing already exists for this period'}), 400
        
        # Create billing record
        monthly_fee = Decimal('199.00')  # $199 monthly fee for Tier 2
        
        billing = MembershipBilling(
            user_id=user_id,
            billing_period_start=billing_start,
            billing_period_end=billing_end,
            amount=monthly_fee,
            payment_due_date=payment_due
        )
        
        db.session.add(billing)
        db.session.commit()
        
        return jsonify({
            'message': 'Membership billing created successfully',
            'billing': billing.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error creating membership billing: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@solana_bp.route('/membership/billings', methods=['GET'])
@jwt_required()
def get_membership_billings():
    """Get user's membership billing history."""
    try:
        user_id = get_jwt_identity()
        
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)
        
        # Get billings
        billings = MembershipBilling.query.filter_by(user_id=user_id).order_by(
            MembershipBilling.billing_period_start.desc()  # type: ignore
        ).paginate(page=page, per_page=per_page, error_out=False)
        
        # Update overdue status for all billings
        for billing in billings.items:
            billing.check_overdue_status()
        
        db.session.commit()
        
        return jsonify({
            'billings': [b.to_dict() for b in billings.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': billings.total,
                'pages': billings.pages,
                'has_next': billings.has_next,
                'has_prev': billings.has_prev
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting membership billings: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@solana_bp.route('/config', methods=['GET'])
@jwt_required()
def get_solana_config():
    """Get Solana configuration for frontend."""
    try:
        # Get config values from environment variables
        treasury_wallet = current_app.config.get('SOLANA_TREASURY_WALLET')
        payment_token = current_app.config.get('TIER_2_PAYMENT_TOKEN', 'USDT')
        payment_token_mint = current_app.config.get('TIER_2_PAYMENT_TOKEN_MINT', 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB')
        payment_amount = current_app.config.get('TIER_2_PAYMENT_AMOUNT', 199.0)

        if not treasury_wallet:
            return jsonify({'error': 'Treasury wallet not configured. Please set SOLANA_TREASURY_WALLET in environment variables.'}), 500

        config = {
            'treasury_wallet': treasury_wallet,
            'payment_token': payment_token,
            'payment_token_mint': payment_token_mint,
            'payment_amount': payment_amount,
            'network': current_app.config.get('SOLANA_NETWORK', 'mainnet'),
            'explorer_base_url': 'https://explorer.solana.com',
            'required_confirmations': 12,
            'supported_tokens': [payment_token],
            'payment_instructions': {
                'step1': 'Connect your Solana wallet (Phantom, Solflare, etc.)',
                'step2': f'Send {payment_amount} {payment_token} to the treasury wallet: {treasury_wallet}',
                'step3': 'Copy the transaction signature',
                'step4': 'Submit the signature for verification',
                'step5': 'Wait for blockchain confirmation'
            }
        }
        
        return jsonify(config), 200

    except Exception as e:
        current_app.logger.error(f"Error getting Solana config: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@solana_bp.route('/membership/status', methods=['GET'])
@jwt_required()
def get_membership_status():
    """Get detailed membership status for the current user."""
    try:
        user_id = get_jwt_identity()

        from app.services.membership_service import MembershipService

        # Get comprehensive membership summary
        membership_summary = MembershipService.get_membership_summary(user_id)

        if 'error' in membership_summary:
            return jsonify({'error': membership_summary['error']}), 404

        # Get validation status
        validation_result = MembershipService.validate_user_membership(user_id)

        response = {
            'membership_summary': membership_summary,
            'validation': validation_result,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

        return jsonify(response), 200

    except Exception as e:
        current_app.logger.error(f"Error getting membership status: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@solana_bp.route('/membership/validate', methods=['POST'])
@jwt_required()
def validate_membership():
    """Manually validate and update membership status for the current user."""
    try:
        user_id = get_jwt_identity()

        from app.services.membership_service import MembershipService

        # Validate membership
        validation_result = MembershipService.validate_user_membership(user_id)

        # If should downgrade, perform the downgrade
        if validation_result.get('should_downgrade'):
            from app.models.user_tier_status import UserTierStatus
            tier_status = UserTierStatus.query.filter_by(user_id=user_id).first()

            if tier_status and tier_status.tier_2:
                current_app.logger.info(f"Manually downgrading user {user_id} from Tier 2 due to expired membership")
                tier_status.tier_1 = True
                tier_status.tier_2 = False
                tier_status.tier_3 = False
                tier_status.updated_at = datetime.now(timezone.utc)
                db.session.commit()

                validation_result['downgraded'] = True
                validation_result['message'] = 'User downgraded from Tier 2 due to expired membership'

        return jsonify(validation_result), 200

    except Exception as e:
        current_app.logger.error(f"Error validating membership: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
