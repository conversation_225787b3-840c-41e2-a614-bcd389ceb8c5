from datetime import datetime, timedelta
from flask import current_app, jsonify
from flask_jwt_extended import create_access_token, create_refresh_token, get_jwt, get_jwt_identity
import redis
from app.models.user import User

class JWTManager:
    """JWT token management service."""
    
    def __init__(self, redis_client=None):
        """Initialize with graceful Redis fallback and lazy initialization."""
        # Initialize in-memory storage first
        self.using_redis = False
        self.blacklist = set()
        self.user_revocations = {}
        self.user_sessions = {}
        self.active_sessions = {}
        self._provided_redis_client = redis_client
        
        # Initialize attributes that will be set lazily
        self.redis_client = None
        self.access_token_expires = None
        self.refresh_token_expires = None
        self._initialized = False
        
        # Try to initialize immediately if app context is available
        try:
            from flask import has_app_context
            if has_app_context():
                self._do_initialization()
        except ImportError:
            # Flask not available during import, will initialize later
            pass
    
    def _do_initialization(self):
        """Perform the actual initialization when app context is available."""
        if self._initialized:
            return
            
        try:
            if self._provided_redis_client:
                self.redis_client = self._provided_redis_client
                # Test Redis connection
                self.redis_client.ping()
                self.using_redis = True
                current_app.logger.info("[JWT] Using provided Redis client")
            else:
                self.redis_client = redis.from_url(current_app.config['REDIS_URL'])
                # Test Redis connection
                self.redis_client.ping()
                self.using_redis = True
                current_app.logger.info("[JWT] Successfully connected to Redis")
        except Exception as e:
            current_app.logger.warning(f"[JWT] Redis unavailable, using in-memory storage: {str(e)}")
            # Continue with in-memory storage initialized above
        
        # Set default values if config values are not available
        try:
            self.access_token_expires = timedelta(seconds=current_app.config.get('JWT_ACCESS_TOKEN_EXPIRES', 3600))
            self.refresh_token_expires = timedelta(seconds=current_app.config.get('JWT_REFRESH_TOKEN_EXPIRES', 86400))
        except Exception as e:
            current_app.logger.warning(f"[JWT] Config values not available, using defaults: {str(e)}")
            self.access_token_expires = timedelta(seconds=3600)  # 1 hour
            self.refresh_token_expires = timedelta(seconds=86400)  # 24 hours
            
        self._initialized = True
    
    def _ensure_initialized(self):
        """Ensure JWT manager is fully initialized with app context."""
        if not self._initialized:
            self._do_initialization()
    
    def create_tokens_for_user(self, user_id, additional_claims=None):
        """Create access and refresh tokens for user."""
        self._ensure_initialized()
        try:
            current_app.logger.info(f"[JWT Debug] Creating tokens for user {user_id}")
            claims = additional_claims or {}
            
            # Add user-specific claims
            user = User.query.get(user_id)
            if user:
                claims.update({
                    'tier': user.get_tier(),
                    'two_fa_enabled': user.two_fa_enabled,
                    'is_active': user.is_active
                })
            
            # Log token creation time
            current_time = datetime.utcnow().timestamp()
            current_app.logger.info(f"[JWT Debug] Token creation time: {current_time}")
            
            # Create tokens
            access_token = create_access_token(
                identity=user_id,
                expires_delta=self.access_token_expires,
                additional_claims=claims
            )
            
            refresh_token = create_refresh_token(
                identity=user_id,
                expires_delta=self.refresh_token_expires,
                additional_claims=claims
            )
            
            # Track active session
            self.track_active_sessions(
                user_id=user_id,
                token_jti=access_token,
                device_info=claims.get('device_info')
            )
            
            return {
                'access_token': access_token,
                'refresh_token': refresh_token
            }
            
        except Exception as e:
            current_app.logger.error(f"[JWT Debug] Error creating tokens: {str(e)}", exc_info=True)
            raise
    
    def create_2fa_pending_token(self, user_id, session_data=None):
        """Create a temporary token for 2FA pending state."""
        self._ensure_initialized()
        try:
            current_app.logger.info(f"[JWT Debug] Creating 2FA pending token for user {user_id}")
            
            # Create a token that's only valid for a short time (15 minutes)
            expires_delta = timedelta(minutes=15)
            
            # Create token with 2FA pending claim
            token = create_access_token(
                identity=user_id,
                expires_delta=expires_delta,
                additional_claims={
                    '2fa_pending': True,
                    'session_data': session_data or {}
                }
            )
            
            current_app.logger.info(f"[JWT Debug] 2FA pending token created for user {user_id}")
            return token
            
        except Exception as e:
            current_app.logger.error(f"[JWT Debug] Error creating 2FA pending token: {str(e)}", exc_info=True)
            raise
            
    def refresh_access_token(self, refresh_token_identity):
        """Create new access token from refresh token."""
        self._ensure_initialized()
        try:
            current_app.logger.info(f"[JWT Debug] Refreshing access token for user {refresh_token_identity}")
            
            # Create a new access token with the same identity as the refresh token
            access_token = create_access_token(
                identity=refresh_token_identity,
                expires_delta=self.access_token_expires
            )
            
            current_app.logger.info(f"[JWT Debug] Access token refreshed for user {refresh_token_identity}")
            return {
                'access_token': access_token
            }
            
        except Exception as e:
            current_app.logger.error(f"[JWT Debug] Error refreshing access token: {str(e)}", exc_info=True)
            raise
            
    def blacklist_token(self, jti, expires_at=None):
        """Add token to blacklist."""
        self._ensure_initialized()
        try:
            if not expires_at:
                expires_at = datetime.utcnow() + (self.refresh_token_expires or timedelta(days=1))
                
            if self.using_redis and self.redis_client:
                self.redis_client.setex(f"token_blacklist:{jti}", 
                                      int((expires_at - datetime.utcnow()).total_seconds()),
                                      "1")
            else:
                self.blacklist.add(jti)
                
            current_app.logger.info(f"[JWT Debug] Token blacklisted: {jti}")
            
        except Exception as e:
            current_app.logger.error(f"[JWT Debug] Error blacklisting token: {str(e)}", exc_info=True)
            raise
            
    def is_token_blacklisted(self, jti):
        """Check if token is blacklisted."""
        self._ensure_initialized()
        try:
            if self.using_redis and self.redis_client:
                return bool(self.redis_client.exists(f"token_blacklist:{jti}"))
            return jti in self.blacklist
            
        except Exception as e:
            current_app.logger.error(f"[JWT Debug] Error checking token blacklist: {str(e)}", exc_info=True)
            return True  # Fail safe - treat as blacklisted if we can't verify
            
    def revoke_all_user_tokens(self, user_id):
        """Revoke all tokens for a user by storing a revocation timestamp."""
        self._ensure_initialized()
        try:
            now = datetime.utcnow()
            if self.using_redis and self.redis_client:
                self.redis_client.set(f"user_revocation:{user_id}", now.isoformat())
            else:
                self.user_revocations[user_id] = now
                
            current_app.logger.info(f"[JWT Debug] All tokens revoked for user {user_id}")
            
        except Exception as e:
            current_app.logger.error(f"[JWT Debug] Error revoking user tokens: {str(e)}", exc_info=True)
            raise
            
    def clear_user_revocation(self, user_id):
        """Clear revocation timestamp for a user after successful login."""
        self._ensure_initialized()
        try:
            if self.using_redis and self.redis_client:
                self.redis_client.delete(f"user_revocation:{user_id}")
            else:
                self.user_revocations.pop(user_id, None)
                
            current_app.logger.info(f"[JWT Debug] Token revocation cleared for user {user_id}")
            
        except Exception as e:
            current_app.logger.error(f"[JWT Debug] Error clearing user revocation: {str(e)}", exc_info=True)
            raise
            
    def is_user_tokens_revoked(self, user_id, token_issued_at):
        """Check if user tokens were revoked after token was issued."""
        self._ensure_initialized()
        try:
            if self.using_redis and self.redis_client:
                revoked_at = self.redis_client.get(f"user_revocation:{user_id}")
                if revoked_at:
                    if isinstance(revoked_at, bytes):
                        revoked_at = revoked_at.decode('utf-8')
                    revoked_at = datetime.fromisoformat(revoked_at)
                    return revoked_at > datetime.fromtimestamp(token_issued_at)
            else:
                revoked_at = self.user_revocations.get(user_id)
                if revoked_at:
                    return revoked_at > datetime.fromtimestamp(token_issued_at)
                    
            return False
            
        except Exception as e:
            current_app.logger.error(f"[JWT Debug] Error checking user revocation: {str(e)}", exc_info=True)
            return True  # Fail safe - treat as revoked if we can't verify
            
    def is_token_revoked(self, jwt_payload):
        """Check if token is revoked."""
        self._ensure_initialized()
        try:
            user_id = jwt_payload.get('sub')
            token_issued_at = jwt_payload.get('iat')
            
            if not user_id or not token_issued_at:
                return True
                
            # Check if all tokens for this user were revoked after this token was issued
            if self.is_user_tokens_revoked(user_id, token_issued_at):
                current_app.logger.warning(f"[JWT Debug] All tokens revoked for user {user_id}")
                return True
                
            return False
            
        except Exception as e:
            current_app.logger.error(f"[JWT Debug] Error in is_token_revoked: {str(e)}", exc_info=True)
            return False  # Don't revoke on error to avoid false positives
            
            return {
                'access_token': access_token,
                'refresh_token': refresh_token,
                'expires_in': int(self.access_token_expires.total_seconds()) if self.access_token_expires else 3600,
                'token_type': 'Bearer'
            }
        except Exception as e:
            current_app.logger.error(f"[JWT Debug] Error creating tokens: {str(e)}", exc_info=True)
            raise
    
    def create_2fa_pending_token(self, user_id, session_data=None):
        """Create a temporary token for 2FA pending state."""
        claims = {
            'two_fa_pending': True,
            'session_data': session_data or {}
        }
        
        # Short-lived token for 2FA completion (10 minutes)
        token = create_access_token(
            identity=user_id,
            expires_delta=timedelta(minutes=10),
            additional_claims=claims
        )
        
        return token
    
    def refresh_access_token(self, refresh_token_identity):
        """Create new access token from refresh token."""
        self._ensure_initialized()
        # Get user to ensure they're still active
        user = User.query.get(refresh_token_identity)
        if not user or not user.is_active:
            raise ValueError("User not found or inactive")
        
        # Create new access token
        return self.create_tokens_for_user(user.id)
    
    def blacklist_token(self, jti, expires_at=None):
        """Add token to blacklist."""
        self._ensure_initialized()
        if self.using_redis and self.redis_client:
            if expires_at is None:
                expires_at = datetime.utcnow() + (self.access_token_expires or timedelta(hours=1))
            
            # Calculate TTL for Redis
            ttl = int((expires_at - datetime.utcnow()).total_seconds())
            
            # Store in Redis with expiration
            self.redis_client.setex(f"blacklist:{jti}", ttl, "1")
        else:
            # Add to in-memory blacklist
            self.blacklist.add(jti)
    
    def is_token_blacklisted(self, jti):
        """Check if token is blacklisted."""
        self._ensure_initialized()
        if self.using_redis and self.redis_client:
            return self.redis_client.exists(f"blacklist:{jti}")
        return jti in self.blacklist
    
    def revoke_all_user_tokens(self, user_id):
        """Revoke all tokens for a user by storing a revocation timestamp."""
        self._ensure_initialized()
        
        # Add a small buffer (1 second) to ensure any tokens created during logout are also revoked
        revoke_time = datetime.utcnow().timestamp() + 1.0
        
        current_app.logger.info(f"[JWT Debug] Revoking all tokens for user {user_id} at time {revoke_time}")
        
        if self.using_redis and self.redis_client:
            try:
                # Store revocation time in Redis with longer TTL to ensure effectiveness
                ttl = int((self.access_token_expires or timedelta(hours=1)).total_seconds()) * 3  # 3x access token lifetime
                self.redis_client.setex(f"user_revoke:{user_id}", ttl, str(revoke_time))
                current_app.logger.info(f"[JWT Debug] Stored revocation in Redis with TTL: {ttl} seconds")
            except Exception as e:
                current_app.logger.warning(f"[JWT Debug] Redis error in revoke tokens: {str(e)}")
                self.using_redis = False
        
        # Always store in memory as fallback
        self.user_revocations[user_id] = revoke_time
    
    def clear_user_revocation(self, user_id):
        """Clear revocation timestamp for a user after successful login."""
        self._ensure_initialized()
        current_app.logger.info(f"[JWT Debug] Clearing revocation timestamp for user {user_id}")
        
        # Clear from Redis
        if self.using_redis and self.redis_client:
            try:
                deleted = self.redis_client.delete(f"user_revoke:{user_id}")
                if deleted:
                    current_app.logger.info(f"[JWT Debug] Cleared revocation from Redis for user {user_id}")
            except Exception as e:
                current_app.logger.warning(f"[JWT Debug] Redis error clearing revocation: {str(e)}")
        
        # Clear from memory
        if user_id in self.user_revocations:
            del self.user_revocations[user_id]
            current_app.logger.info(f"[JWT Debug] Cleared revocation from memory for user {user_id}")
    
    def is_user_tokens_revoked(self, user_id, token_issued_at):
        """Check if user tokens were revoked after token was issued."""
        self._ensure_initialized()
        current_app.logger.info(f"[JWT Debug] Checking token revocation for user {user_id}, issued at: {token_issued_at}")
        
        # Convert token_issued_at to ensure it's a float
        try:
            if isinstance(token_issued_at, str):
                token_issued_at = float(token_issued_at)
            elif not isinstance(token_issued_at, (int, float)):
                current_app.logger.warning(f"[JWT Debug] Invalid token_issued_at type: {type(token_issued_at)}")
                return False
        except (ValueError, TypeError) as e:
            current_app.logger.warning(f"[JWT Debug] Error converting token_issued_at: {str(e)}")
            return False
        
        # Default to not revoked
        is_revoked = False
        current_time = datetime.utcnow().timestamp()
        
        if self.using_redis and self.redis_client:
            try:
                revoke_time_str = self.redis_client.get(f"user_revoke:{user_id}")
                if revoke_time_str:
                    # Handle different Redis response types
                    if isinstance(revoke_time_str, bytes):
                        revoke_time_str = revoke_time_str.decode('utf-8')
                    elif not isinstance(revoke_time_str, str):
                        revoke_time_str = str(revoke_time_str)
                    
                    try:
                        revoke_time = float(revoke_time_str)
                        current_app.logger.info(f"[JWT Debug] Found revoke time in Redis: {revoke_time}")
                        current_app.logger.info(f"[JWT Debug] Current time: {current_time}")
                        current_app.logger.info(f"[JWT Debug] Token issued at: {token_issued_at}")
                        
                        # Only revoke if the revoke time is valid (not in the future) and token was issued before it
                        if revoke_time <= current_time and token_issued_at < revoke_time:
                            is_revoked = True
                            current_app.logger.warning(f"[JWT Debug] Token revoked - Token issued at {token_issued_at} is before revoke time {revoke_time}")
                        elif revoke_time > current_time:
                            current_app.logger.warning(f"[JWT Debug] Invalid revoke time in future: {revoke_time}, clearing it")
                            # Clear invalid future revoke time
                            self.redis_client.delete(f"user_revoke:{user_id}")
                        else:
                            current_app.logger.info(f"[JWT Debug] Token not revoked - issued after revoke time")
                            
                    except (ValueError, TypeError) as e:
                        current_app.logger.warning(f"[JWT Debug] Invalid revoke time format: {revoke_time_str}, error: {str(e)}")
                        # Clear invalid revoke time
                        self.redis_client.delete(f"user_revoke:{user_id}")
                else:
                    current_app.logger.info("[JWT Debug] No revoke time found in Redis")
            except Exception as e:
                current_app.logger.warning(f"[JWT Debug] Redis error in token revocation check: {str(e)}")
                self.using_redis = False
        
        # Fallback to in-memory if Redis check didn't find anything
        if not is_revoked and user_id in self.user_revocations:
            revoke_time = self.user_revocations[user_id]
            current_app.logger.info(f"[JWT Debug] Found revoke time in memory: {revoke_time}")
            
            # Same validation for in-memory revoke times
            if revoke_time <= current_time and token_issued_at < revoke_time:
                is_revoked = True
                current_app.logger.warning(f"[JWT Debug] Token revoked in memory - Token issued at {token_issued_at} is before revoke time {revoke_time}")
            elif revoke_time > current_time:
                current_app.logger.warning(f"[JWT Debug] Invalid revoke time in future (memory): {revoke_time}, clearing it")
                del self.user_revocations[user_id]
        
        current_app.logger.info(f"[JWT Debug] Token revocation check result: {'REVOKED' if is_revoked else 'NOT REVOKED'}")
        return is_revoked
        
    def is_token_revoked(self, jwt_payload):
        """Check if token is revoked."""
        try:
            # Check if token is blacklisted
            jti = jwt_payload.get('jti')
            if jti and self.is_token_blacklisted(jti):
                current_app.logger.warning(f"[JWT Debug] Token is blacklisted: {jti}")
                return True
                
            # Check if user has revoked all tokens
            user_id = jwt_payload.get('sub')
            iat = jwt_payload.get('iat')
            
            if not user_id or iat is None:
                current_app.logger.warning("[JWT Debug] Missing required token claims (sub or iat)")
                return False  # Don't revoke if claims are missing, let other validators handle it
                
            if self.is_user_tokens_revoked(user_id, iat):
                current_app.logger.warning(f"[JWT Debug] All tokens revoked for user {user_id}")
                return True
                
            return False
            
        except Exception as e:
            current_app.logger.error(f"[JWT Debug] Error in is_token_revoked: {str(e)}", exc_info=True)
            return False  # Don't revoke on error to avoid false positives
    
    def store_user_session(self, user_id, session_data, expires_in=None):
        """Store user session data."""
        self._ensure_initialized()
        if self.using_redis and self.redis_client:
            if expires_in is None:
                expires_in = int((self.access_token_expires or timedelta(hours=1)).total_seconds())
            session_key = f"session:{user_id}"
            self.redis_client.setex(session_key, expires_in, str(session_data))
        else:
            self.user_sessions[user_id] = {
                'data': session_data,
                'expires': datetime.utcnow() + timedelta(seconds=expires_in or (self.access_token_expires.total_seconds() if self.access_token_expires else 3600))
            }
    
    def get_user_session(self, user_id):
        """Get user session data."""
        self._ensure_initialized()
        if self.using_redis and self.redis_client:
            try:
                session_key = f"session:{user_id}"
                session_data = self.redis_client.get(session_key)
                if session_data:
                    if isinstance(session_data, bytes):
                        return session_data.decode('utf-8')
                    elif isinstance(session_data, str):
                        return session_data
                    else:
                        return str(session_data)
            except Exception as e:
                current_app.logger.warning(f"[JWT Debug] Redis error in get session: {str(e)}")
                self.using_redis = False
        
        # Fallback to in-memory
        session = self.user_sessions.get(user_id)
        if session and datetime.utcnow() < session['expires']:
            return str(session['data'])
        return None
    
    def delete_user_session(self, user_id):
        """Delete user session data."""
        if self.using_redis and self.redis_client:
            session_key = f"session:{user_id}"
            self.redis_client.delete(session_key)
        else:
            self.user_sessions.pop(user_id, None)
    
    def track_active_sessions(self, user_id, token_jti, device_info=None):
        """Track active user sessions."""
        session_info = {
            'jti': token_jti,
            'created_at': datetime.utcnow().isoformat(),
            'device_info': device_info or {}
        }
        
        if self.using_redis and self.redis_client:
            # Store in Redis set with expiration
            session_key = f"active_sessions:{user_id}"
            self.redis_client.sadd(session_key, str(session_info))
            self.redis_client.expire(session_key, int((self.refresh_token_expires or timedelta(days=1)).total_seconds()))
        else:
            # Store in memory
            if 'active_sessions' not in self.__dict__:
                self.active_sessions = {}
            if user_id not in self.active_sessions:
                self.active_sessions[user_id] = []
            self.active_sessions[user_id].append(session_info)
    
    def get_active_sessions(self, user_id):
        """Get all active sessions for user."""
        # Use in-memory storage only to avoid Redis type issues
        return self.active_sessions.get(user_id, [])
    
    def remove_session(self, user_id, token_jti):
        """Remove specific session."""
        # Use in-memory storage only to avoid Redis type issues
        if user_id in self.active_sessions:
            self.active_sessions[user_id] = [
                s for s in self.active_sessions[user_id]
                if s.get('jti') != token_jti
            ]
    
    def limit_concurrent_sessions(self, user_id, max_sessions=3):
        """Limit number of concurrent sessions per user."""
        sessions = self.get_active_sessions(user_id)
        
        if len(sessions) >= max_sessions:
            # Remove oldest sessions
            sessions.sort(key=lambda x: x['created_at'])
            
            for old_session in sessions[:-max_sessions+1]:
                # Blacklist old token
                self.blacklist_token(old_session['jti'])
                # Remove from active sessions
                self.remove_session(user_id, old_session['jti'])
    
    @staticmethod
    def validate_token_claims(required_claims=None):
        """Validate JWT token claims."""
        from flask_jwt_extended import verify_jwt_in_request, get_jwt
        
        try:
            verify_jwt_in_request()
            claims = get_jwt()
            
            # Check if token is for 2FA pending
            if claims.get('two_fa_pending'):
                return {'valid': False, 'reason': '2fa_required'}
            
            # Check required claims
            if required_claims:
                for claim in required_claims:
                    if claim not in claims:
                        return {'valid': False, 'reason': f'missing_claim_{claim}'}
            
            return {'valid': True, 'claims': claims}
            
        except Exception as e:
            return {'valid': False, 'reason': str(e)}
    
    def create_password_reset_token(self, user_id):
        """Create token for password reset (not applicable for OAuth but useful for future)."""
        claims = {'password_reset': True}
        
        token = create_access_token(
            identity=user_id,
            expires_delta=timedelta(hours=1),  # 1 hour for password reset
            additional_claims=claims
        )
        
        return token
    
    def create_email_verification_token(self, user_id):
        """Create token for email verification."""
        claims = {'email_verification': True}
        
        token = create_access_token(
            identity=user_id,
            expires_delta=timedelta(hours=24),  # 24 hours for email verification
            additional_claims=claims
        )
        
        return token
    
    def get_token_info(self, token_identity):
        """Get comprehensive token information."""
        user = User.query.get(token_identity)
        
        if not user:
            return None
        
        return {
            'user_id': user.id,
            'email': user.email,
            'full_name': user.full_name,
            'tier': user.get_tier(),
            'two_fa_enabled': user.two_fa_enabled,
            'is_active': user.is_active,
            'subscription': user.get_current_subscription().to_dict() if user.get_current_subscription() else None
        }
    

# Initialize the JWT manager instance with lazy initialization
jwt_manager = JWTManager()