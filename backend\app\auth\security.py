"""
Security Manager for DeepTrade Platform
Handles security-related operations, logging, and monitoring.
"""

import logging
import hashlib
import secrets
from datetime import datetime, timedelta
from flask import request, current_app
from app import db
from app.models.security_log import SecurityLog

class SecurityManager:
    """
    Manages security operations including:
    - Security logging
    - IP tracking
    - Brute force protection
    - Session security
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    @staticmethod
    def log_security_event(user_id, event_type, ip_address=None, user_agent=None, 
                          details=None, risk_level='low'):
        """
        Log a security event to the database.
        
        Args:
            user_id (str): ID of the user involved
            event_type (str): Type of security event
            ip_address (str): IP address of the request
            user_agent (str): User agent string
            details (dict): Additional event details
            risk_level (str): Risk level of the event
        """
        try:
            if not ip_address:
                ip_address = request.remote_addr if request else 'unknown'
            if not user_agent:
                user_agent = request.headers.get('User-Agent', 'unknown') if request else 'unknown'
                
            security_log = SecurityLog(
                user_id=user_id,
                event_type=event_type,
                ip_address=ip_address,
                user_agent=user_agent,
                details=details or {},
                risk_level=risk_level
            )
            
            db.session.add(security_log)
            db.session.commit()
            
        except Exception as e:
            current_app.logger.error(f"Failed to log security event: {str(e)}")
            
    @staticmethod
    def check_brute_force(ip_address, max_attempts=5, time_window=300):
        """
        Check if IP address has exceeded failed login attempts.
        
        Args:
            ip_address (str): IP address to check
            max_attempts (int): Maximum failed attempts allowed
            time_window (int): Time window in seconds
            
        Returns:
            bool: True if IP is blocked due to brute force
        """
        try:
            cutoff_time = datetime.utcnow() - timedelta(seconds=time_window)
            
            failed_attempts = SecurityLog.query.filter(
                SecurityLog.ip_address == ip_address,
                SecurityLog.event_type == 'failed_login',
                SecurityLog.created_at >= cutoff_time
            ).count()
            
            return failed_attempts >= max_attempts
            
        except Exception as e:
            current_app.logger.error(f"Failed to check brute force: {str(e)}")
            return False
            
    @staticmethod
    def hash_sensitive_data(data):
        """
        Hash sensitive data for logging purposes.
        
        Args:
            data (str): Data to hash
            
        Returns:
            str: Hashed data
        """
        if not data:
            return None
            
        return hashlib.sha256(data.encode()).hexdigest()[:16]
        
    @staticmethod
    def generate_secure_token(length=32):
        """
        Generate a cryptographically secure random token.
        
        Args:
            length (int): Length of the token
            
        Returns:
            str: Secure random token
        """
        return secrets.token_urlsafe(length)
        
    @staticmethod
    def validate_ip_address(ip_address):
        """
        Validate and sanitize IP address.
        
        Args:
            ip_address (str): IP address to validate
            
        Returns:
            str: Validated IP address or 'unknown'
        """
        if not ip_address:
            return 'unknown'
            
        # Basic IP validation - you might want to use a proper IP validation library
        parts = ip_address.split('.')
        if len(parts) == 4:
            try:
                for part in parts:
                    if not 0 <= int(part) <= 255:
                        return 'unknown'
                return ip_address
            except ValueError:
                return 'unknown'
        
        # Could be IPv6 or other format
        return ip_address if len(ip_address) <= 45 else 'unknown'
        
    @staticmethod
    def is_suspicious_activity(user_id, activity_type):
        """
        Check if user activity appears suspicious.
        
        Args:
            user_id (str): User ID
            activity_type (str): Type of activity
            
        Returns:
            bool: True if activity appears suspicious
        """
        try:
            # Check recent activity patterns
            recent_time = datetime.utcnow() - timedelta(hours=1)
            
            recent_logs = SecurityLog.query.filter(
                SecurityLog.user_id == user_id,
                SecurityLog.event_type == activity_type,
                SecurityLog.created_at >= recent_time
            ).count()
            
            # Simple threshold - adjust based on activity type
            thresholds = {
                'login_success': 10,
                'password_change': 3,
                'api_access': 100,
                'payment_attempt': 5
            }
            
            threshold = thresholds.get(activity_type, 20)
            return recent_logs > threshold
            
        except Exception as e:
            current_app.logger.error(f"Failed to check suspicious activity: {str(e)}")
            return False
            
    @classmethod
    def log_login_attempt(cls, user_id, success, ip_address=None, user_agent=None):
        """
        Log a login attempt.
        
        Args:
            user_id (str): User ID
            success (bool): Whether login was successful
            ip_address (str): IP address
            user_agent (str): User agent
        """
        event_type = 'login_success' if success else 'failed_login'
        risk_level = 'low' if success else 'medium'
        
        cls.log_security_event(
            user_id=user_id,
            event_type=event_type,
            ip_address=ip_address,
            user_agent=user_agent,
            risk_level=risk_level
        )
        
    @classmethod
    def log_password_change(cls, user_id, ip_address=None):
        """
        Log a password change event.
        
        Args:
            user_id (str): User ID
            ip_address (str): IP address
        """
        cls.log_security_event(
            user_id=user_id,
            event_type='password_change',
            ip_address=ip_address,
            risk_level='medium'
        )
        
    @classmethod
    def log_api_access(cls, user_id, endpoint, ip_address=None):
        """
        Log API access.
        
        Args:
            user_id (str): User ID
            endpoint (str): API endpoint accessed
            ip_address (str): IP address
        """
        cls.log_security_event(
            user_id=user_id,
            event_type='api_access',
            ip_address=ip_address,
            details={'endpoint': endpoint},
            risk_level='low'
        )