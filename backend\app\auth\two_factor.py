import pyotp
import qrcode
import io
import base64
import secrets
import string
from PIL import Image
from flask import current_app
from app.models.user import User, User2FABackupCode, User2FAEmailCode
from app import db
from datetime import datetime, timedelta
import random

class TwoFactorAuth:
    """Two-Factor Authentication service using TOTP."""
    
    @staticmethod
    def generate_secret():
        """Generate a new TOTP secret."""
        return pyotp.random_base32()
    
    @staticmethod
    def generate_backup_codes(count=10):
        """Generate backup codes for account recovery."""
        codes = []
        for _ in range(count):
            # Generate 8-character alphanumeric code
            code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
            codes.append(code)
        return codes
    
    @staticmethod
    def generate_qr_code(user_email, secret, issuer_name="DeepTrade"):
        """Generate QR code for TOTP setup."""
        # Create TOTP URI
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user_email,
            issuer_name=issuer_name
        )
        
        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        # Create QR code image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to base64 string
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        qr_code_data = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{qr_code_data}"
    
    @staticmethod
    def verify_totp_code(secret, code, window=1):
        """Verify TOTP code with time window tolerance."""
        totp = pyotp.TOTP(secret)
        return totp.verify(code, valid_window=window)
    
    @staticmethod
    def setup_2fa_for_user(user_id):
        """Setup 2FA for a user and return secret and QR code."""
        user = User.query.get(user_id)
        if not user:
            raise ValueError("User not found")
        
        # Generate secret
        secret = TwoFactorAuth.generate_secret()
        
        # Generate QR code
        qr_code = TwoFactorAuth.generate_qr_code(user.email, secret)
        
        # Generate backup codes
        backup_codes = TwoFactorAuth.generate_backup_codes()
        
        # Store secret (encrypted) but don't enable 2FA yet
        user.two_fa_secret = user.encrypt_2fa_secret(secret)
        user.two_fa_enabled = False  # Will be enabled after verification
        
        # Clear any existing backup codes
        User2FABackupCode.query.filter_by(user_id=user_id).delete()
        
        # Create new backup codes
        for code in backup_codes:
            backup_code = User2FABackupCode(user_id, code)
            db.session.add(backup_code)
        
        db.session.commit()
        
        return {
            'secret': secret,
            'qr_code': qr_code,
            'backup_codes': backup_codes
        }
    
    @staticmethod
    def enable_2fa_for_user(user_id, verification_code):
        """Enable 2FA for user after verifying setup code."""
        user = User.query.get(user_id)
        if not user:
            raise ValueError("User not found")
        
        if not user.two_fa_secret:
            raise ValueError("2FA not set up for user")
        
        # Get the secret
        secret = user.decrypt_2fa_secret()
        if not secret:
            raise ValueError("Could not decrypt 2FA secret")
        
        # Verify the code
        if not TwoFactorAuth.verify_totp_code(secret, verification_code):
            raise ValueError("Invalid verification code")
        
        # Enable 2FA
        user.two_fa_enabled = True
        db.session.commit()
        
        return True
    
    @staticmethod
    def disable_2fa_for_user(user_id, verification_code):
        """Disable 2FA for user after verification."""
        user = User.query.get(user_id)
        if not user:
            raise ValueError("User not found")
        
        if not user.two_fa_enabled:
            raise ValueError("2FA not enabled for user")
        
        # Get the secret
        secret = user.decrypt_2fa_secret()
        if not secret:
            raise ValueError("Could not decrypt 2FA secret")
        
        # Verify the code
        if not TwoFactorAuth.verify_totp_code(secret, verification_code):
            raise ValueError("Invalid verification code")
        
        # Disable 2FA
        user.disable_2fa()
        db.session.commit()
        
        return True
    
    @staticmethod
    def verify_user_2fa_code(user_id, code):
        """Verify 2FA code for user login."""
        user = User.query.get(user_id)
        if not user:
            return False
        
        if not user.two_fa_enabled:
            return True  # 2FA not enabled, so verification passes
        
        # Get the secret
        secret = user.decrypt_2fa_secret()
        if not secret:
            return False
        
        # First try TOTP code
        if TwoFactorAuth.verify_totp_code(secret, code):
            return True
        
        # Then try backup codes
        return TwoFactorAuth.verify_backup_code(user_id, code)
    
    @staticmethod
    def verify_backup_code(user_id, code):
        """Verify backup code for user."""
        backup_codes = User2FABackupCode.query.filter_by(
            user_id=user_id,
            is_used=False
        ).all()
        
        for backup_code in backup_codes:
            if backup_code.verify_code(code):
                # Mark code as used
                backup_code.use_code()
                db.session.commit()
                return True
        
        return False
    
    @staticmethod
    def regenerate_backup_codes(user_id, verification_code):
        """Regenerate backup codes for user."""
        user = User.query.get(user_id)
        if not user:
            raise ValueError("User not found")
        
        if not user.two_fa_enabled:
            raise ValueError("2FA not enabled for user")
        
        # Verify current 2FA code
        if not TwoFactorAuth.verify_user_2fa_code(user_id, verification_code):
            raise ValueError("Invalid verification code")
        
        # Remove old backup codes
        User2FABackupCode.query.filter_by(user_id=user_id).delete()
        
        # Generate new backup codes
        backup_codes = TwoFactorAuth.generate_backup_codes()
        
        # Store new backup codes
        for code in backup_codes:
            backup_code = User2FABackupCode(user_id, code)
            db.session.add(backup_code)
        
        db.session.commit()
        
        return backup_codes
    
    @staticmethod
    def get_user_2fa_status(user_id):
        """Get 2FA status for user."""
        user = User.query.get(user_id)
        if not user:
            return None
        
        # Count unused backup codes
        unused_backup_codes = User2FABackupCode.query.filter_by(
            user_id=user_id,
            is_used=False
        ).count()
        
        return {
            'enabled': user.two_fa_enabled,
            'setup_completed': user.two_fa_secret is not None,
            'backup_codes_remaining': unused_backup_codes
        }
    
    @staticmethod
    def get_current_totp_code(user_id):
        """Get current TOTP code for user (for testing purposes only)."""
        if not current_app.debug:
            raise ValueError("This method is only available in debug mode")
        
        user = User.query.get(user_id)
        if not user or not user.two_fa_enabled:
            return None
        
        secret = user.decrypt_2fa_secret()
        if not secret:
            return None
        
        totp = pyotp.TOTP(secret)
        return totp.now()

    @staticmethod
    def generate_email_2fa_code():
        """Generate a 6-digit numeric code for email-based 2FA."""
        return ''.join([str(random.randint(0, 9)) for _ in range(6)])

    @staticmethod
    def send_email_2fa_code(user_id):
        """Generate and send 2FA code via email."""
        user = User.query.get(user_id)
        if not user:
            raise ValueError("User not found")

        # Generate 6-digit code
        code = TwoFactorAuth.generate_email_2fa_code()

        # Clear any existing unused codes for this user
        User2FAEmailCode.query.filter_by(user_id=user_id, is_used=False).delete()

        # Create new code record
        email_code = User2FAEmailCode(user_id, code, expires_in_minutes=10)
        db.session.add(email_code)
        db.session.commit()

        # Send email with code
        from app.services.email_service import EmailService
        email_service = EmailService()

        try:
            email_service.send_2fa_code_email(user.email, code)
            return True
        except Exception as e:
            current_app.logger.error(f"Failed to send 2FA code email: {str(e)}")
            return False

    @staticmethod
    def verify_email_2fa_code(user_id, code):
        """Verify email-based 2FA code."""
        # Find the most recent unused code for this user
        email_code = User2FAEmailCode.query.filter_by(
            user_id=user_id,
            is_used=False
        ).order_by(User2FAEmailCode.created_at.desc()).first()

        if not email_code:
            return False

        if email_code.verify_code(code):
            email_code.use_code()
            db.session.commit()
            return True

        return False

    @staticmethod
    def cleanup_expired_email_codes():
        """Clean up expired email 2FA codes."""
        expired_codes = User2FAEmailCode.query.filter(
            User2FAEmailCode.expires_at < datetime.utcnow()
        ).all()

        for code in expired_codes:
            db.session.delete(code)

        db.session.commit()
        return len(expired_codes)