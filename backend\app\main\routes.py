from flask import render_template_string, jsonify, current_app, Response, stream_with_context, request
from . import main_bp
from app.auth.decorators import jwt_required
from app.models.user import User
from app.models.trade import Trade, TradingSession
from app.models.subscription import Subscription
from flask_jwt_extended import get_jwt_identity
import json
import time
from datetime import datetime, timedelta
from app.services.market_data import ml_service

# Admin Panel HTML Template






admin_panel_html = """
<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepTrade Admin Panel</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
        }
    </script>
</head>
<body class="h-full bg-gray-100 dark:bg-gray-900 transition-colors duration-200">
    <!-- Theme Toggle Button (Fixed Position) -->
    <button id="theme-toggle" class="fixed top-4 right-4 z-50 p-3 rounded-lg bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105">
        <!-- Moon Icon (for dark mode) -->
        <svg id="theme-toggle-dark-icon" class="hidden w-5 h-5 text-gray-800 dark:text-gray-200" fill="currentColor" viewBox="0 0 20 20">
            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
        </svg>
        <!-- Sun Icon (for light mode) -->
        <svg id="theme-toggle-light-icon" class="hidden w-5 h-5 text-gray-800 dark:text-gray-200" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
        </svg>
    </button>

    <!-- Login Section -->
    <div id="login-section" class="min-h-screen flex items-center justify-center px-4">
        <div class="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg w-full max-w-md border border-gray-200 dark:border-gray-700">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">DeepTrade Admin</h1>
                <p class="text-gray-600 dark:text-gray-400">Administrative Dashboard</p>
            </div>

            <form id="admin-login-form" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Username</label>
                    <input type="text" id="username" required class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Password</label>
                    <input type="password" id="password" required class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                </div>

                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg transition-colors duration-200">
                    Login
                </button>

                <div id="login-error" class="hidden bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded"></div>
            </form>
        </div>
    </div>

    <!-- Dashboard Section -->
    <div id="dashboard-section" class="hidden min-h-screen">
        <!-- Header -->
        <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 transition-colors duration-200">
            <div class="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6">
                <div class="flex justify-between items-center py-6">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Admin Dashboard</h1>
                        <p class="text-gray-600 dark:text-gray-400 mt-1">DeepTrade System Management</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600 dark:text-gray-300">Welcome, <span id="admin-username" class="font-semibold text-gray-900 dark:text-white"></span></span>
                        <button id="admin-settings-btn" class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 font-medium">
                            Settings
                        </button>
                        <button id="logout-btn" class="bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 font-medium">
                            Logout
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div id="admin-dashboard" class="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-8">
            <!-- Analytics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 transition-colors duration-200">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">Total Users</h3>
                    <p class="text-3xl font-bold text-blue-600 dark:text-blue-400" id="total-users">0</p>
                </div>
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 transition-colors duration-200">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">Active Users</h3>
                    <p class="text-3xl font-bold text-green-600 dark:text-green-400" id="active-users">0</p>
                </div>
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 transition-colors duration-200">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">Tier 2 Users</h3>
                    <p class="text-3xl font-bold text-purple-600 dark:text-purple-400" id="tier-2-users">0</p>
                </div>
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 transition-colors duration-200">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">Tier 3 Users</h3>
                    <p class="text-3xl font-bold text-orange-600 dark:text-orange-400" id="tier-3-users">0</p>
                </div>
            </div>

            <!-- Trading Bot Monitoring -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8 transition-colors duration-200">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">Trading Bot Monitoring</h2>

                <!-- System Health -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-4">System Health</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Overall Status</h4>
                            <p id="bot-overall-status" class="text-lg font-semibold text-gray-900 dark:text-white">Loading...</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Active Containers</h4>
                            <p id="bot-active-containers" class="text-lg font-semibold text-gray-900 dark:text-white">-</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Market Data</h4>
                            <p id="bot-market-data" class="text-lg font-semibold text-gray-900 dark:text-white">-</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">ML Service</h4>
                            <p id="bot-ml-service" class="text-lg font-semibold text-gray-900 dark:text-white">-</p>
                        </div>
                    </div>

                    <!-- Detailed Health Status -->
                    <div id="health-details" class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow mt-4" style="display: none;">
                        <h4 class="text-md font-semibold text-gray-700 dark:text-gray-300 mb-3">Health Check Details</h4>
                        <div id="health-details-content" class="space-y-2">
                            <!-- Health details will be populated here -->
                        </div>
                    </div>

                    <!-- Failed Services Alert -->
                    <div id="failed-services-alert" class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4 mt-4" style="display: none;">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">System Degraded</h3>
                                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                    <p>The following services are experiencing issues:</p>
                                    <ul id="failed-services-list" class="list-disc list-inside mt-1">
                                        <!-- Failed services will be listed here -->
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance (Last 7 Days) -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-4">Performance (Last 7 Days)</h3>
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                            <h4 class="text-sm font-medium text-blue-600 dark:text-blue-400 mb-1">Total Trades</h4>
                            <p id="bot-total-trades" class="text-2xl font-bold text-blue-700 dark:text-blue-300">-</p>
                        </div>
                        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                            <h4 class="text-sm font-medium text-green-600 dark:text-green-400 mb-1">Win Rate</h4>
                            <p id="bot-win-rate" class="text-2xl font-bold text-green-700 dark:text-green-300">-</p>
                        </div>
                        <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
                            <h4 class="text-sm font-medium text-purple-600 dark:text-purple-400 mb-1">Total PnL</h4>
                            <p id="bot-total-pnl" class="text-2xl font-bold text-purple-700 dark:text-purple-300">-</p>
                        </div>
                        <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
                            <h4 class="text-sm font-medium text-yellow-600 dark:text-yellow-400 mb-1">Avg Trades/Day</h4>
                            <p id="bot-avg-trades" class="text-2xl font-bold text-yellow-700 dark:text-yellow-300">-</p>
                        </div>
                        <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                            <h4 class="text-sm font-medium text-red-600 dark:text-red-400 mb-1">Active Alerts</h4>
                            <p id="bot-active-alerts" class="text-2xl font-bold text-red-700 dark:text-red-300">-</p>
                        </div>
                    </div>
                </div>

                <!-- Current Trading Signals -->
                <div class="mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300">Current Trading Signals (BTC/USDT)</h3>
                        <button
                            id="refresh-signals-btn"
                            onclick="refreshTradingSignals()"
                            class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors duration-200 flex items-center space-x-1"
                            title="Refresh trading signals"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            <span>Refresh</span>
                        </button>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600 dark:text-gray-400">Signal Status:</span>
                                <span id="signal-status" class="px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                                    HOLD
                                </span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600 dark:text-gray-400">Confidence:</span>
                                <span id="signal-confidence" class="text-gray-900 dark:text-white font-medium">--</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600 dark:text-gray-400">Current Price:</span>
                                <span id="signal-price" class="text-gray-900 dark:text-white font-medium">--</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600 dark:text-gray-400">Last Update:</span>
                                <span id="signal-timestamp" class="text-gray-900 dark:text-white font-medium">--</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Trading Bot Terminal -->
                <div class="mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300">Trading Bot Live Terminal</h3>
                        <div class="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                            </svg>
                            Newest logs at top
                        </div>
                    </div>
                    <div class="bg-black rounded-lg p-4 font-mono text-sm h-64 overflow-y-auto border border-gray-600">
                        <div class="text-green-400 mb-2">
                            <span class="text-gray-500">[SYSTEM]</span> DeepTrade Trading Bot Terminal v1.0
                        </div>
                        <div class="text-green-400 mb-2">
                            <span class="text-gray-500">[SYSTEM]</span> Initializing monitoring...
                        </div>
                        <div id="terminal-output" class="text-green-400">
                            <!-- Terminal output will be populated here -->
                        </div>
                        <div class="flex items-center mt-2">
                            <span class="text-green-400 mr-2">admin@deeptrade:~$</span>
                            <div class="w-2 h-4 bg-green-400 animate-pulse"></div>
                        </div>
                    </div>
                </div>



                <!-- Trading Alerts -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-4">Trading Alerts</h3>
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Last updated: <span id="alerts-last-updated">Never</span></span>
                            <div class="space-x-2">
                                <button id="refresh-data-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200">
                                    Refresh Data
                                </button>
                                <button id="auto-refresh-toggle" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200">
                                    Auto Refresh: OFF
                                </button>
                            </div>
                        </div>
                        <div id="trading-alerts-content" class="text-gray-600 dark:text-gray-400 text-sm">
                            No recent alerts
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Management -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8 transition-colors duration-200">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">User Management</h2>
                    <div class="flex items-center space-x-4">
                        <button id="refresh-users-btn" class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                            <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Refresh
                        </button>
                        <button id="prune-users-btn" class="bg-amber-600 hover:bg-amber-700 dark:bg-amber-500 dark:hover:bg-amber-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                            <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Prune Unverified
                        </button>
                        <div class="flex items-center space-x-2">
                            <label class="text-sm text-gray-600 dark:text-gray-400">Show:</label>
                            <select id="users-per-page" class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm">
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-16">ID</th>
                                <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Email</th>
                                <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden sm:table-cell">Name</th>
                                <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-16">Tier</th>
                                <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-20">Status</th>
                                <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden lg:table-cell w-24">Created</th>
                                <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-32">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="users-table" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <!-- Users will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <!-- Users Pagination -->
                <div id="users-pagination" class="flex justify-between items-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        <span id="users-pagination-info">Showing 0 of 0 users</span>
                    </div>
                    <div class="flex space-x-2">
                        <button id="users-prev-btn" class="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            Previous
                        </button>
                        <span id="users-page-info" class="px-3 py-1 text-sm text-gray-600 dark:text-gray-400">Page 1 of 1</span>
                        <button id="users-next-btn" class="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            Next
                        </button>
                    </div>
                </div>
            </div>

            <!-- IP Management -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8 transition-colors duration-200">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">IP Address Management</h2>
                    <div class="space-x-2">
                        <button id="refresh-ip-management-btn" class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                            <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Refresh
                        </button>
                        <button id="clear-ip-logs-btn" class="bg-orange-600 hover:bg-orange-700 dark:bg-orange-500 dark:hover:bg-orange-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                            <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Clear Logs
                        </button>
                        <button id="ban-ip-btn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                            <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                            </svg>
                            Ban IP Address
                        </button>
                        <button id="view-suspicious-ips-btn" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                            Suspicious IPs
                        </button>
                    </div>
                </div>

                <!-- IP Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-blue-600 dark:text-blue-400">Total Logins (30d)</h3>
                        <p class="text-2xl font-bold text-blue-900 dark:text-blue-100" id="ip-stats-total-logins">-</p>
                    </div>
                    <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-green-600 dark:text-green-400">Success Rate</h3>
                        <p class="text-2xl font-bold text-green-900 dark:text-green-100" id="ip-stats-success-rate">-</p>
                    </div>
                    <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-red-600 dark:text-red-400">Banned IPs</h3>
                        <p class="text-2xl font-bold text-red-900 dark:text-red-100" id="ip-stats-banned">-</p>
                    </div>
                    <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-yellow-600 dark:text-yellow-400">Rate Limited</h3>
                        <p class="text-2xl font-bold text-yellow-900 dark:text-yellow-100" id="ip-stats-rate-limited">-</p>
                    </div>
                </div>

                <!-- IP Tabs -->
                <div class="border-b border-gray-200 dark:border-gray-700 mb-4">
                    <nav class="-mb-px flex space-x-8">
                        <button id="ip-access-logs-tab" class="py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 dark:text-blue-400">
                            Frontend Access Logs
                        </button>
                        <button id="ip-blacklist-tab" class="py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                            IP Blacklist
                        </button>
                        <button id="ip-rate-limits-tab" class="py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                            Rate Limits
                        </button>
                        <button id="admin-connections-tab" class="py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                            Admin Connections
                        </button>
                    </nav>
                </div>

                <!-- Frontend Access Logs Tab -->
                <div id="ip-access-logs-content" class="space-y-4">
                    <div class="mb-4">
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            <i class="fas fa-info-circle mr-1"></i>
                            Displaying access logs from frontend server only. Admin backend logs are shown in the "Admin Connections" tab.
                            <br>
                            <i class="fas fa-clock mr-1"></i>
                            All timestamps are displayed in GMT-3 timezone.
                        </p>
                    </div>
                    <div class="flex justify-between items-center">
                        <div class="flex space-x-2">
                            <input type="text" id="ip-search" placeholder="Search IP address..." class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm">
                            <select id="ip-filter-success" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm">
                                <option value="">All Attempts</option>
                                <option value="true">Successful Only</option>
                                <option value="false">Failed Only</option>
                            </select>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <label class="text-sm text-gray-600 dark:text-gray-400">Show:</label>
                                <select id="ip-logs-per-page" class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm">
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                            <button id="refresh-ip-logs-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200">
                                Refresh
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">IP Address</th>
                                    <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden sm:table-cell">User/Admin</th>
                                    <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden lg:table-cell">Location</th>
                                    <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-20">Status</th>
                                    <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden md:table-cell">Timestamp</th>
                                    <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="ip-access-logs-table" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <!-- IP access logs will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                    <!-- IP Access Logs Pagination -->
                    <div id="ip-logs-pagination" class="flex justify-between items-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            <span id="ip-logs-pagination-info">Showing 0 of 0 access logs</span>
                        </div>
                        <div class="flex space-x-2">
                            <button id="ip-logs-prev-btn" class="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                Previous
                            </button>
                            <span id="ip-logs-page-info" class="px-3 py-1 text-sm text-gray-600 dark:text-gray-400">Page 1 of 1</span>
                            <button id="ip-logs-next-btn" class="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                Next
                            </button>
                        </div>
                    </div>
                </div>

                <!-- IP Blacklist Tab -->
                <div id="ip-blacklist-content" class="space-y-4 hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">IP Address</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Reason</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Banned By</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Banned At</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Expires</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="ip-blacklist-table" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <!-- IP blacklist will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Rate Limits Tab -->
                <div id="ip-rate-limits-content" class="space-y-4 hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">IP Address</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Attempts</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">First Attempt</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Last Attempt</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Blocked Until</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="ip-rate-limits-table" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <!-- Rate limits will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Admin Connections Tab -->
                <div id="admin-connections-content" class="space-y-4 hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Admin Username</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">IP Address</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Location</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Last Login</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Login Count</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="admin-connections-table" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <!-- Admin connections will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 2FA Reset Requests Management -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8 transition-colors duration-200">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">2FA Reset Requests</h2>
                    <div class="flex items-center space-x-4">
                        <button id="refresh-2fa-requests-btn" class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                            <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Refresh
                        </button>
                    </div>
                </div>

                <!-- 2FA Reset Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-yellow-600 dark:text-yellow-400">Pending Requests</h3>
                        <p class="text-2xl font-bold text-yellow-900 dark:text-yellow-100" id="2fa-stats-pending">-</p>
                    </div>
                    <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-green-600 dark:text-green-400">Approved</h3>
                        <p class="text-2xl font-bold text-green-900 dark:text-green-100" id="2fa-stats-approved">-</p>
                    </div>
                    <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-red-600 dark:text-red-400">Rejected</h3>
                        <p class="text-2xl font-bold text-red-900 dark:text-red-100" id="2fa-stats-rejected">-</p>
                    </div>
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-blue-600 dark:text-blue-400">High Risk</h3>
                        <p class="text-2xl font-bold text-blue-900 dark:text-blue-100" id="2fa-stats-high-risk">-</p>
                    </div>
                </div>

                <!-- 2FA Reset Requests Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Risk Level</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Submitted</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Reason</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="2fa-requests-table" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <!-- 2FA reset requests will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Coupons Management -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8 transition-colors duration-200">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">Coupon Management</h2>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <label class="text-sm text-gray-600 dark:text-gray-400">Show:</label>
                            <select id="coupons-per-page" class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm">
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <button id="refresh-coupons-btn" class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2" onclick="loadCoupons()">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            <span>Refresh</span>
                        </button>
                        <button id="create-coupon-btn" class="bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                            Create New Coupon
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Code</th>
                                <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-16">Tier</th>
                                <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-16 hidden sm:table-cell">Used</th>
                                <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-20 hidden sm:table-cell">Max Uses</th>
                                <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden lg:table-cell">Expires</th>
                                <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-20">Status</th>
                                <th class="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-24">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="coupons-table" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <!-- Coupons will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <!-- Coupons Pagination -->
                <div id="coupons-pagination" class="flex justify-between items-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        <span id="coupons-pagination-info">Showing 0 of 0 coupons</span>
                    </div>
                    <div class="flex space-x-2">
                        <button id="coupons-prev-btn" class="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            Previous
                        </button>
                        <span id="coupons-page-info" class="px-3 py-1 text-sm text-gray-600 dark:text-gray-400">Page 1 of 1</span>
                        <button id="coupons-next-btn" class="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            Next
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="mt-12 text-center text-gray-500 dark:text-gray-400 text-sm border-t border-gray-200 dark:border-gray-700 pt-8">
            <p>DeepTrade Admin Panel - User & System Management</p>
            <p class="mt-2 text-xs">© 2025 DeepTrade. All rights reserved.</p>
        </div>
    </div>

    <!-- User Profile Page -->
    <div id="user-profile-page" class="hidden fixed inset-0 z-50 bg-gray-50 dark:bg-gray-900 overflow-y-auto">
        <!-- Profile Navbar -->
        <nav class="bg-gray-800 dark:bg-gray-900 shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center space-x-4">
                        <button id="back-to-dashboard" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                            ← Back to Dashboard
                        </button>
                        <h1 class="text-xl font-semibold text-white">User Profile</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-300">Admin Panel</span>
                        <button id="profile-theme-toggle" class="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white transition-colors duration-200">
                            <svg class="w-5 h-5 dark:hidden" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                            </svg>
                            <svg class="w-5 h-5 hidden dark:block" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Profile Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <!-- User Info Header -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex justify-between items-start">
                    <div>
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-2" id="profile-user-email">Loading...</h2>
                        <div class="flex items-center space-x-4">
                            <span class="px-3 py-1 rounded-full text-sm font-medium" id="profile-user-status">Status</span>
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium" id="profile-user-tier">Tier 1</span>
                        </div>
                    </div>
                    <button id="delete-user-btn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                        Delete User
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">User ID</h3>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white" id="profile-user-id">-</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</h3>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white" id="profile-user-created">-</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Auto Trading</h3>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white" id="profile-auto-trading">-</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">2FA Status</h3>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white" id="profile-2fa-status">-</p>
                    </div>
                </div>

                <!-- Exchange Connections Section -->
                <div class="mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Exchange Connections</h3>
                    <div id="profile-exchange-connections" class="space-y-2">
                        <p class="text-sm text-gray-500 dark:text-gray-400">Loading exchange information...</p>
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">Total Trades</h3>
                    <p class="text-3xl font-bold text-blue-600 dark:text-blue-400" id="profile-total-trades">0</p>
                </div>
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">Total Profit</h3>
                    <p class="text-3xl font-bold text-green-600 dark:text-green-400" id="profile-total-profit">$0.00</p>
                </div>
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">Profit Share Owed</h3>
                    <p class="text-3xl font-bold text-orange-600 dark:text-orange-400" id="profile-profit-share">$0.00</p>
                </div>
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">Total Payments</h3>
                    <p class="text-3xl font-bold text-purple-600 dark:text-purple-400" id="profile-total-payments">0</p>
                </div>
            </div>

            <!-- Active Trading Container -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">Active Trading Container</h2>
                <div id="profile-trading-container">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Container Status</h4>
                            <p id="container-status" class="text-lg font-semibold text-gray-900 dark:text-white">-</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Current Trade</h4>
                            <p id="container-current-trade" class="text-lg font-semibold text-gray-900 dark:text-white">-</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Risk %</h4>
                            <p id="container-risk" class="text-lg font-semibold text-gray-900 dark:text-white">-</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Leverage</h4>
                            <p id="container-leverage" class="text-lg font-semibold text-gray-900 dark:text-white">-</p>
                        </div>
                    </div>
                    <div class="flex space-x-4">
                        <button id="reset-2fa-btn" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                            Reset 2FA
                        </button>
                        <button id="refresh-container-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                            Refresh Status
                        </button>
                    </div>
                </div>
            </div>

            <!-- Recent Trades -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">Recent Trades (Last 50)</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Symbol</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Side</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Entry Price</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Exit Price</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Quantity</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">PnL</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Entry Time</th>
                            </tr>
                        </thead>
                        <tbody id="profile-trades-table" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <!-- Trades will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Payment History -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">Payment History</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Currency</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Method</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Description</th>
                            </tr>
                        </thead>
                        <tbody id="profile-payments-table" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <!-- Payments will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Delete User Confirmation Modal -->
        <div id="delete-user-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Delete User</h3>
                        </div>
                    </div>

                    <div class="mb-6">
                        <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">
                            Are you sure you want to permanently delete this user and all associated data?
                        </p>
                        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                            <p class="text-sm text-red-800 dark:text-red-200 font-medium">⚠️ This action cannot be undone!</p>
                            <p class="text-xs text-red-600 dark:text-red-300 mt-1">
                                This will permanently delete:
                            </p>
                            <ul class="text-xs text-red-600 dark:text-red-300 mt-1 ml-4 list-disc">
                                <li>User account and profile</li>
                                <li>All trading history and sessions</li>
                                <li>Payment records and subscriptions</li>
                                <li>API credentials and settings</li>
                                <li>All associated data</li>
                            </ul>
                        </div>

                        <div class="mt-4">
                            <p class="text-sm text-gray-600 dark:text-gray-300 mb-2">
                                User Email: <span id="delete-modal-user-email" class="font-medium text-gray-900 dark:text-white"></span>
                            </p>
                            <p class="text-sm text-gray-600 dark:text-gray-300">
                                User ID: <span id="delete-modal-user-id" class="font-mono text-xs text-gray-500 dark:text-gray-400"></span>
                            </p>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button id="cancel-delete-btn" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors duration-200">
                            Cancel
                        </button>
                        <button id="confirm-delete-btn" class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors duration-200">
                            Delete User
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Settings Modal -->
    <div id="admin-settings-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-10 mx-auto p-6 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Admin Settings</h3>
                    <button id="close-settings-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Settings Tabs -->
                <div class="border-b border-gray-200 dark:border-gray-700 mb-4">
                    <nav class="-mb-px flex space-x-4">
                        <button id="password-tab" class="py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 dark:text-blue-400">
                            Change Password
                        </button>
                        <button id="email-tab" class="py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                            Change Email
                        </button>
                        <button id="admin-management-tab" class="py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                            Admin Management
                        </button>
                        <button id="create-admin-tab" class="py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                            Create Admin
                        </button>
                    </nav>
                </div>

                <!-- Change Password Tab -->
                <div id="password-content" class="space-y-4">
                    <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-md p-3 mb-4">
                        <p class="text-sm text-blue-800 dark:text-blue-200">
                            A verification code will be sent to your email address.
                        </p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Password</label>
                        <input type="password" id="new-password" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" placeholder="Enter new password">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Confirm New Password</label>
                        <input type="password" id="confirm-password" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" placeholder="Confirm new password">
                    </div>

                    <!-- Email Verification Section -->
                    <div id="verification-section" class="hidden space-y-3 bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Verification Code</label>
                            <input type="text" id="verification-code" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" placeholder="Enter 6-digit code">
                        </div>
                        <button id="confirm-password-change-btn" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md font-medium transition-colors duration-200">
                            Confirm Password Change
                        </button>
                    </div>

                    <button id="send-verification-btn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md font-medium transition-colors duration-200">
                        Send Verification Code
                    </button>
                </div>

                <!-- Change Email Tab -->
                <div id="email-content" class="space-y-4 hidden">
                    <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-3 mb-4">
                        <p class="text-sm text-yellow-800 dark:text-yellow-200">
                            Verification codes will be sent to both your current and new email addresses.
                        </p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Email Address</label>
                        <input type="email" id="new-email" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" placeholder="Enter new email address">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Confirm New Email</label>
                        <input type="email" id="confirm-email" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" placeholder="Confirm new email address">
                    </div>

                    <!-- Email Verification Section -->
                    <div id="email-verification-section" class="hidden space-y-3 bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Current Email Verification Code</label>
                            <input type="text" id="current-email-code" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" placeholder="Enter code from current email">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Email Verification Code</label>
                            <input type="text" id="new-email-code" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" placeholder="Enter code from new email">
                        </div>
                        <button id="confirm-email-change-btn" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md font-medium transition-colors duration-200">
                            Confirm Email Change
                        </button>
                    </div>

                    <button id="send-email-verification-btn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md font-medium transition-colors duration-200">
                        Send Verification Codes
                    </button>
                </div>

                <!-- Admin Management Tab -->
                <div id="admin-management-content" class="space-y-4 hidden">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-md font-medium text-gray-900 dark:text-white">Admin Accounts</h4>
                        <button id="refresh-admins-btn" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm">
                            Refresh
                        </button>
                    </div>

                    <!-- Admin List -->
                    <div id="admin-list" class="space-y-2 max-h-64 overflow-y-auto">
                        <!-- Admin items will be loaded here -->
                    </div>

                    <!-- Admin Statistics -->
                    <div id="admin-stats" class="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Total Admins:</span>
                                <span id="total-admins" class="font-medium text-gray-900 dark:text-white ml-1">-</span>
                            </div>
                            <div>
                                <span class="text-gray-600 dark:text-gray-400">Super Admins:</span>
                                <span id="super-admins" class="font-medium text-gray-900 dark:text-white ml-1">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Create Admin Tab -->
                <div id="create-admin-content" class="space-y-4 hidden">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email</label>
                        <input type="email" id="admin-email" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Password</label>
                        <input type="password" id="admin-password" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Admin Level</label>
                        <select id="admin-level" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <option value="super_admin">Super Admin</option>
                            <option value="limited_admin">Limited Admin</option>
                        </select>
                    </div>
                    <button id="create-admin-btn" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md font-medium transition-colors duration-200">
                        Create Admin
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Admin Panel -->
    <script>
        let adminToken = null;

        // Theme Management
        function initializeTheme() {
            // Check for saved theme preference or default to light mode
            const savedTheme = localStorage.getItem('admin-theme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

            if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
                document.documentElement.classList.add('dark');
                updateThemeIcons(true);
            } else {
                document.documentElement.classList.remove('dark');
                updateThemeIcons(false);
            }
        }

        function updateThemeIcons(isDark) {
            const darkIcon = document.getElementById('theme-toggle-dark-icon');
            const lightIcon = document.getElementById('theme-toggle-light-icon');

            if (isDark) {
                darkIcon.classList.remove('hidden');
                lightIcon.classList.add('hidden');
            } else {
                darkIcon.classList.add('hidden');
                lightIcon.classList.remove('hidden');
            }
        }

        function toggleTheme() {
            const isDark = document.documentElement.classList.contains('dark');

            if (isDark) {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('admin-theme', 'light');
                updateThemeIcons(false);
            } else {
                document.documentElement.classList.add('dark');
                localStorage.setItem('admin-theme', 'dark');
                updateThemeIcons(true);
            }
        }

        // Initialize theme on page load
        initializeTheme();

        // Theme toggle event listener
        document.getElementById('theme-toggle').addEventListener('click', toggleTheme);

        // Trading Bot Monitoring Variables
        let autoRefreshInterval = null;
        let isAutoRefreshEnabled = false;
        let terminalLines = [];
        let maxTerminalLines = 50;

        // Admin login form handler
        document.getElementById('admin-login-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('login-error');

            try {
                const response = await fetch('/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    adminToken = data.access_token;
                    localStorage.setItem('admin_token', adminToken);
                    document.getElementById('admin-username').textContent = data.admin.username;
                    showDashboard();
                    loadDashboardData();
                } else {
                    errorDiv.textContent = data.message || 'Login failed';
                    errorDiv.classList.remove('hidden');
                }
            } catch (error) {
                errorDiv.textContent = 'Network error. Please try again.';
                errorDiv.classList.remove('hidden');
            }
        });

        // Logout handler
        document.getElementById('logout-btn').addEventListener('click', () => {
            adminToken = null;
            localStorage.removeItem('admin_token');
            showLogin();
        });

        // Trading bot monitoring event listeners
        document.getElementById('refresh-data-btn').addEventListener('click', refreshTradingData);
        document.getElementById('auto-refresh-toggle').addEventListener('click', toggleAutoRefresh);

        // Create coupon button event listener
        document.getElementById('create-coupon-btn').addEventListener('click', showCreateCouponModal);

        // Show dashboard
        function showDashboard() {
            document.getElementById('login-section').classList.add('hidden');
            document.getElementById('dashboard-section').classList.remove('hidden');

            // Apply admin privilege restrictions
            applyAdminPrivilegeRestrictions();

            // Initialize IP management
            initializeIPManagement();
        }

        // Apply UI restrictions based on admin privilege level
        function applyAdminPrivilegeRestrictions() {
            try {
                // Get admin info from token claims
                const tokenPayload = JSON.parse(atob(adminToken.split('.')[1]));
                const isSuperAdmin = tokenPayload.is_super_admin === true;

                console.log('Admin privilege check:', {
                    tokenPayload: tokenPayload,
                    is_super_admin: tokenPayload.is_super_admin,
                    isSuperAdmin: isSuperAdmin,
                    adminToken: adminToken ? adminToken.substring(0, 50) + '...' : 'null'
                });

                if (!isSuperAdmin) {
                    console.log('Applying restrictions for limited admin');
                // Gray out and disable buttons for limited admin

                // Settings button - only super admins can access settings
                const settingsBtn = document.getElementById('admin-settings-btn');
                if (settingsBtn) {
                    settingsBtn.disabled = true;
                    settingsBtn.className = 'bg-gray-400 cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium opacity-50';
                    settingsBtn.title = 'Super admin privileges required';
                }

                // Create coupon button - only super admins can create coupons
                const createCouponBtn = document.getElementById('create-coupon-btn');
                if (createCouponBtn) {
                    createCouponBtn.disabled = true;
                    createCouponBtn.className = 'bg-gray-400 cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium opacity-50';
                    createCouponBtn.title = 'Super admin privileges required';
                }

                // Clear IP logs button - only super admins can clear logs
                const clearLogsBtn = document.getElementById('clear-ip-logs-btn');
                if (clearLogsBtn) {
                    clearLogsBtn.disabled = true;
                    clearLogsBtn.className = 'bg-gray-400 cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium opacity-50';
                    clearLogsBtn.title = 'Super admin privileges required';
                    clearLogsBtn.onclick = function(e) {
                        e.preventDefault();
                        alert('❌ Access denied. Super admin privileges required to clear logs.');
                        return false;
                    };
                }

                // Admin Connections tab - only super admins can view admin connections
                const adminConnectionsTab = document.getElementById('admin-connections-tab');
                if (adminConnectionsTab) {
                    adminConnectionsTab.disabled = true;
                    adminConnectionsTab.className = 'py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-400 cursor-not-allowed opacity-50';
                    adminConnectionsTab.title = 'Super admin privileges required';
                    adminConnectionsTab.onclick = function(e) {
                        e.preventDefault();
                        alert('❌ Access denied. Super admin privileges required to view admin connections.');
                        return false;
                    };
                }
            } else {
                console.log('Super admin privileges detected - no restrictions applied');
            }
            } catch (error) {
                console.error('Error applying admin privilege restrictions:', error);
            }
        }

        // Show login
        function showLogin() {
            document.getElementById('login-section').classList.remove('hidden');
            document.getElementById('dashboard-section').classList.add('hidden');
            document.getElementById('login-error').classList.add('hidden');
        }

        // Load dashboard data
        async function loadDashboardData() {
            console.log('Loading dashboard data...');

            // Simple mode for debugging - only load essential data
            const SIMPLE_MODE = false; // Set to false for full loading

            // Load analytics with error handling
            try {
                console.log('Loading analytics...');
                const analyticsResponse = await fetch('/api/admin/analytics/dashboard', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (analyticsResponse.ok) {
                    const analytics = await analyticsResponse.json();
                    updateAnalytics(analytics);
                    console.log('Analytics loaded successfully');
                } else {
                    console.warn('Analytics failed to load:', analyticsResponse.status);
                }
            } catch (error) {
                console.error('Error loading analytics:', error);
            }

            if (SIMPLE_MODE) {
                console.log('Running in simple mode - loading only essential data');

                // Only load users and coupons in simple mode
                try {
                    console.log('Loading users...');
                    await loadUsers();
                    console.log('Users loaded successfully');
                } catch (error) {
                    console.error('Error loading users:', error);
                }

                try {
                    console.log('Loading coupons...');
                    await loadCoupons();
                    console.log('Coupons loaded successfully');
                } catch (error) {
                    console.error('Error loading coupons:', error);
                }

                console.log('Simple mode loading completed');
                return;
            }

            // Full mode loading
            console.log('Running in full mode - loading all data');

            // Load users with error handling
            try {
                console.log('Loading users...');
                await loadUsers();
                console.log('Users loaded successfully');
            } catch (error) {
                console.error('Error loading users:', error);
            }

            // Load coupons with error handling
            try {
                console.log('Loading coupons...');
                await loadCoupons();
                console.log('Coupons loaded successfully');
            } catch (error) {
                console.error('Error loading coupons:', error);
            }

            // Load trading bot data with error handling (this might be causing issues)
            try {
                console.log('Loading trading bot data...');
                await loadTradingBotData();
                console.log('Trading bot data loaded successfully');
            } catch (error) {
                console.error('Error loading trading bot data (non-critical):', error);
                // Don't let trading bot errors break the dashboard
            }

            // Start terminal monitoring with error handling
            try {
                console.log('Starting terminal monitoring...');
                startTerminalMonitoring();
                console.log('Terminal monitoring started successfully');
            } catch (error) {
                console.error('Error starting terminal monitoring (non-critical):', error);
                // Don't let terminal monitoring errors break the dashboard
            }

            console.log('Dashboard data loading completed');
        }

        // Update analytics display
        function updateAnalytics(data) {
            document.getElementById('total-users').textContent = data.user_stats.total;
            document.getElementById('active-users').textContent = data.user_stats.active;
            document.getElementById('tier-2-users').textContent = data.tier_stats.tier_2;
            document.getElementById('tier-3-users').textContent = data.tier_stats.tier_3;
        }

        // Pagination state
        let currentUsersPage = 1;
        let currentUsersPerPage = 25;
        let currentCouponsPage = 1;
        let currentCouponsPerPage = 25;
        let currentIPLogsPage = 1;
        let currentIPLogsPerPage = 25;

        // Load users table with pagination
        async function loadUsers(page = 1, perPage = 25) {
            try {
                currentUsersPage = page;
                currentUsersPerPage = perPage;

                const response = await fetch(`/api/admin/users?page=${page}&per_page=${perPage}`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateUsersTable(data.users);
                    updateUsersPagination(data.pagination);
                } else if (response.status === 401) {
                    console.error('Unauthorized access to users - token may be invalid');
                    // Don't automatically logout here, let the main token verification handle it
                } else {
                    console.error('Error loading users:', response.status, response.statusText);
                }
            } catch (error) {
                console.error('Error loading users:', error);
            }
        }

        // Update users pagination
        function updateUsersPagination(pagination) {
            const paginationInfo = document.getElementById('users-pagination-info');
            const pageInfo = document.getElementById('users-page-info');
            const prevBtn = document.getElementById('users-prev-btn');
            const nextBtn = document.getElementById('users-next-btn');

            if (pagination) {
                const start = ((pagination.page - 1) * pagination.per_page) + 1;
                const end = Math.min(pagination.page * pagination.per_page, pagination.total);

                paginationInfo.textContent = `Showing ${start}-${end} of ${pagination.total} users`;
                pageInfo.textContent = `Page ${pagination.page} of ${pagination.pages}`;

                prevBtn.disabled = !pagination.has_prev;
                nextBtn.disabled = !pagination.has_next;
            }
        }

        // Update users table
        function updateUsersTable(users) {
            const tbody = document.getElementById('users-table');
            tbody.innerHTML = '';

            // Remove duplicates by grouping users by email and keeping the highest tier
            const uniqueUsers = {};
            users.forEach(user => {
                if (!uniqueUsers[user.email] || user.tier > uniqueUsers[user.email].tier) {
                    uniqueUsers[user.email] = user;
                }
            });

            // Convert back to array and sort by ID
            const deduplicatedUsers = Object.values(uniqueUsers).sort((a, b) => a.id - b.id);

            deduplicatedUsers.forEach(user => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150 cursor-pointer';
                row.onclick = () => showUserProfile(user.id);

                // Check admin privileges for action buttons
                const tokenPayload = JSON.parse(atob(adminToken.split('.')[1]));
                const isSuperAdmin = tokenPayload.is_super_admin || false;

                const toggleButtonClass = isSuperAdmin
                    ? `toggle-user-btn px-3 py-1 text-xs font-medium rounded-lg transition-colors duration-200 ${user.is_active ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-green-600 hover:bg-green-700 text-white'}`
                    : 'toggle-user-btn px-3 py-1 text-xs font-medium rounded-lg bg-gray-400 text-white cursor-not-allowed opacity-50';

                const toggleButtonTitle = isSuperAdmin ? '' : 'title="Super admin privileges required"';
                const toggleButtonDisabled = isSuperAdmin ? '' : 'disabled';

                row.innerHTML = `
                    <td class="px-2 sm:px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${user.id}</td>
                    <td class="px-2 sm:px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 truncate max-w-xs">${user.email}</td>
                    <td class="px-2 sm:px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 hidden sm:table-cell">${user.full_name || user.username || 'N/A'}</td>
                    <td class="px-2 sm:px-4 py-3 whitespace-nowrap">
                        <span class="px-2 py-1 rounded-full text-xs font-medium ${getTierBadgeClass(user.tier)}">
                            ${user.tier}
                        </span>
                    </td>
                    <td class="px-2 sm:px-4 py-3 whitespace-nowrap">
                        <span class="px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(user.status || (user.is_active ? 'active' : 'inactive'))}">
                            ${getStatusText(user.status || (user.is_active ? 'active' : 'inactive'))}
                        </span>
                    </td>
                    <td class="px-2 sm:px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 hidden lg:table-cell">${new Date(user.created_at).toLocaleDateString()}</td>
                    <td class="px-2 sm:px-4 py-3 whitespace-nowrap">
                        <button data-user-id="${user.id}" data-user-active="${user.is_active}" class="${toggleButtonClass}" ${toggleButtonTitle} ${toggleButtonDisabled}>
                            ${user.is_active ? 'Deactivate' : 'Activate'}
                        </button>
                        <button data-user-id="${user.id}" class="view-profile-btn ml-2 px-3 py-1 text-xs font-medium rounded-lg bg-blue-600 hover:bg-blue-700 text-white transition-colors duration-200">
                            View Profile
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Add event listeners for the buttons (using event delegation)
            document.querySelectorAll('.toggle-user-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();

                    // Check admin privileges before allowing action
                    const tokenPayload = JSON.parse(atob(adminToken.split('.')[1]));
                    const isSuperAdmin = tokenPayload.is_super_admin || false;

                    if (!isSuperAdmin) {
                        alert('Super admin privileges required to activate/deactivate users.');
                        return;
                    }

                    const userId = this.getAttribute('data-user-id');
                    const isActive = this.getAttribute('data-user-active') === 'true';
                    toggleUserStatus(userId, isActive);
                });
            });

            document.querySelectorAll('.view-profile-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const userId = this.getAttribute('data-user-id');
                    showUserProfile(userId);
                });
            });
        }

        // Helper function for tier badge classes
        function getTierBadgeClass(tier) {
            switch(tier) {
                case 1: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
                case 2: return 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200';
                case 3: return 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200';
                default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
            }
        }

        // Helper function to get status badge class
        function getStatusBadgeClass(status) {
            switch(status) {
                case 'active': return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200';
                case 'inactive': return 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200';
                case 'unverified': return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200';
                case 'deletion_requested': return 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200';
                default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
            }
        }

        // Helper function to get status text
        function getStatusText(status) {
            switch(status) {
                case 'active': return 'Active';
                case 'inactive': return 'Inactive';
                case 'unverified': return 'Unverified';
                case 'deletion_requested': return 'Deletion Requested';
                default: return 'Unknown';
            }
        }

        // Toggle user status
        async function toggleUserStatus(userId, currentStatus) {
            try {
                const action = currentStatus ? 'deactivate' : 'activate';
                if (!confirm(`Are you sure you want to ${action} this user?`)) {
                    return;
                }

                const response = await fetch(`/api/admin/users/${userId}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        reason: `Admin ${action}d user via toggle button`
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    alert(result.message);
                    loadUsers(); // Reload users table
                } else {
                    const error = await response.json();
                    alert(error.message || `Failed to ${action} user`);
                }
            } catch (error) {
                console.error('Error toggling user status:', error);
                alert('Error toggling user status');
            }
        }

        // Load coupons table with pagination
        async function loadCoupons(page = 1, perPage = 25) {
            try {
                currentCouponsPage = page;
                currentCouponsPerPage = perPage;

                const response = await fetch(`/api/admin/coupons?page=${page}&per_page=${perPage}`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateCouponsTable(data.coupons);
                    updateCouponsPagination(data.pagination);
                } else if (response.status === 401) {
                    console.error('Unauthorized access to coupons - token may be invalid');
                    // Don't automatically logout here, let the main token verification handle it
                } else {
                    console.error('Error loading coupons:', response.status, response.statusText);
                }
            } catch (error) {
                console.error('Error loading coupons:', error);
            }
        }

        // Update coupons pagination
        function updateCouponsPagination(pagination) {
            const paginationInfo = document.getElementById('coupons-pagination-info');
            const pageInfo = document.getElementById('coupons-page-info');
            const prevBtn = document.getElementById('coupons-prev-btn');
            const nextBtn = document.getElementById('coupons-next-btn');

            if (pagination) {
                const start = ((pagination.page - 1) * pagination.per_page) + 1;
                const end = Math.min(pagination.page * pagination.per_page, pagination.total);

                paginationInfo.textContent = `Showing ${start}-${end} of ${pagination.total} coupons`;
                pageInfo.textContent = `Page ${pagination.page} of ${pagination.pages}`;

                prevBtn.disabled = !pagination.has_prev;
                nextBtn.disabled = !pagination.has_next;
            }
        }

        // Update coupons table
        function updateCouponsTable(coupons) {
            const tbody = document.getElementById('coupons-table');
            tbody.innerHTML = '';

            coupons.forEach(coupon => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150';
                const isExpired = new Date(coupon.expiration_date) < new Date();
                const isUsedUp = coupon.usage_count >= coupon.max_uses;

                // Check admin privileges for delete button
                const tokenPayload = JSON.parse(atob(adminToken.split('.')[1]));
                const isSuperAdmin = tokenPayload.is_super_admin || false;

                const deleteButtonClass = isSuperAdmin
                    ? 'px-3 py-1 text-xs font-medium rounded-lg bg-red-600 hover:bg-red-700 text-white transition-colors duration-200'
                    : 'px-3 py-1 text-xs font-medium rounded-lg bg-gray-400 text-white cursor-not-allowed opacity-50';

                const deleteButtonOnClick = isSuperAdmin ? `onclick="deleteCoupon('${coupon.code}')"` : '';
                const deleteButtonTitle = isSuperAdmin ? '' : 'title="Super admin privileges required"';
                const deleteButtonDisabled = isSuperAdmin ? '' : 'disabled';

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900 dark:text-gray-100">${coupon.code}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">Tier ${coupon.tier_level}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${coupon.usage_count}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${coupon.max_uses}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${new Date(coupon.expiration_date).toLocaleDateString()}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-3 py-1 rounded-full text-xs font-medium ${isExpired || isUsedUp ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' : 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'}">
                            ${isExpired ? 'Expired' : isUsedUp ? 'Used Up' : 'Active'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap space-x-2">
                        <button onclick="showCouponDetails('${coupon.code}')" class="px-3 py-1 text-xs font-medium rounded-lg bg-blue-600 hover:bg-blue-700 text-white transition-colors duration-200">
                            View Details
                        </button>
                        <button ${deleteButtonOnClick} class="${deleteButtonClass}" ${deleteButtonTitle} ${deleteButtonDisabled}>
                            Delete
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Delete coupon
        async function deleteCoupon(code) {
            // Check admin privileges
            const tokenPayload = JSON.parse(atob(adminToken.split('.')[1]));
            const isSuperAdmin = tokenPayload.is_super_admin || false;

            if (!isSuperAdmin) {
                alert('Super admin privileges required to delete coupons.');
                return;
            }

            if (!confirm(`Are you sure you want to delete coupon "${code}"?`)) {
                return;
            }

            try {
                const response = await fetch(`/api/admin/coupons/${code}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    loadCoupons(); // Reload coupons table
                }
            } catch (error) {
                console.error('Error deleting coupon:', error);
            }
        }

        // Show coupon details modal
        async function showCouponDetails(couponCode) {
            try {
                const response = await fetch(`/api/admin/coupons/${couponCode}/details`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayCouponDetailsModal(data);
                } else if (response.status === 401) {
                    alert('❌ Unauthorized. Please login again.');
                    showLogin();
                } else {
                    alert('❌ Error loading coupon details');
                }
            } catch (error) {
                console.error('Error loading coupon details:', error);
                alert('❌ Network error while loading coupon details');
            }
        }

        function displayCouponDetailsModal(couponData) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                    <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white">Coupon Details: ${couponData.coupon.code}</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="p-6">
                        <!-- Coupon Information -->
                        <div class="mb-6">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">Coupon Information</h4>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Code</p>
                                    <p class="font-mono font-bold text-gray-900 dark:text-white">${couponData.coupon.code}</p>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Tier Level</p>
                                    <p class="font-bold text-gray-900 dark:text-white">Tier ${couponData.coupon.tier_level}</p>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Usage</p>
                                    <p class="font-bold text-gray-900 dark:text-white">${couponData.coupon.usage_count}/${couponData.coupon.max_uses}</p>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Status</p>
                                    <p class="font-bold ${couponData.coupon.is_active ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}">${couponData.coupon.is_active ? 'Active' : 'Inactive'}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Usage Statistics -->
                        <div class="mb-6">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">Usage Statistics</h4>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                                    <p class="text-sm text-blue-600 dark:text-blue-400">Total Uses</p>
                                    <p class="text-2xl font-bold text-blue-900 dark:text-blue-100">${couponData.usage_stats.total_uses}</p>
                                </div>
                                <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                                    <p class="text-sm text-green-600 dark:text-green-400">Unique IPs</p>
                                    <p class="text-2xl font-bold text-green-900 dark:text-green-100">${couponData.usage_stats.unique_ips}</p>
                                </div>
                                <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                                    <p class="text-sm text-purple-600 dark:text-purple-400">Unique Users</p>
                                    <p class="text-2xl font-bold text-purple-900 dark:text-purple-100">${couponData.usage_stats.unique_users}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Usage Details Table -->
                        <div class="mb-6">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">Usage Details</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">User</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Email</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">IP Address</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Used At</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        ${couponData.usage_details.map(usage => `
                                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">${usage.username || 'N/A'}</td>
                                                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">${usage.email}</td>
                                                <td class="px-4 py-3 text-sm font-mono text-gray-900 dark:text-gray-100">${usage.ip_address || 'N/A'}</td>
                                                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">${new Date(usage.used_at).toLocaleString()}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- IP Usage Summary -->
                        ${couponData.ip_usage && couponData.ip_usage.length > 0 ? `
                        <div class="mb-6">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">IP Usage Summary</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">IP Address</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Usage Count</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">First Used</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Last Used</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        ${couponData.ip_usage.map(ip => `
                                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                                <td class="px-4 py-3 text-sm font-mono text-gray-900 dark:text-gray-100">${ip.ip_address}</td>
                                                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">${ip.usage_count}</td>
                                                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">${new Date(ip.first_used).toLocaleString()}</td>
                                                <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">${new Date(ip.last_used).toLocaleString()}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // Trading Bot Monitoring Functions
        async function loadTradingBotData() {
            try {
                addTerminalLine('SYSTEM', 'Refreshing trading bot data...');

                // Load each component individually with error handling
                const results = await Promise.allSettled([
                    loadSystemHealth(),
                    loadPerformanceData(),
                    loadTradingSignals(),
                    loadTradingAlerts()
                ]);

                // Log results
                results.forEach((result, index) => {
                    const components = ['System Health', 'Performance Data', 'Trading Signals', 'Trading Alerts'];
                    if (result.status === 'fulfilled') {
                        console.log(`${components[index]} loaded successfully`);
                    } else {
                        console.warn(`${components[index]} failed to load:`, result.reason);
                        addTerminalLine('WARNING', `${components[index]} unavailable`);
                    }
                });

                addTerminalLine('SYSTEM', 'Data refresh completed');
            } catch (error) {
                console.error('Error loading trading bot data:', error);
                addTerminalLine('ERROR', `Failed to load trading bot data: ${error.message}`);
            }
        }

        async function loadSystemHealth() {
            try {
                console.log('Loading system health with token:', adminToken ? 'Token present' : 'No token');
                const response = await fetch('/api/admin/trading-bot/health', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('System health data received:', data);
                    document.getElementById('bot-overall-status').textContent = data.overall_status || 'Unknown';
                    document.getElementById('bot-active-containers').textContent = data.active_containers || '0';
                    document.getElementById('bot-market-data').textContent = data.market_data_status || 'Unknown';
                    document.getElementById('bot-ml-service').textContent = data.ml_service_status || 'Unknown';

                    // Display detailed health information
                    displayHealthDetails(data);

                    addTerminalLine('HEALTH', `System status: ${data.overall_status} | Containers: ${data.active_containers} | Market: ${data.market_data_status}`);
                } else {
                    console.log('System health request failed:', response.status, response.statusText);
                    document.getElementById('bot-overall-status').textContent = 'Offline';
                    document.getElementById('bot-active-containers').textContent = '0';
                    document.getElementById('bot-market-data').textContent = 'Disconnected';
                    document.getElementById('bot-ml-service').textContent = 'Offline';

                    addTerminalLine('ERROR', 'Failed to retrieve system health status');
                }
            } catch (error) {
                console.error('Error loading system health:', error);
            }
        }

        async function loadPerformanceData() {
            try {
                console.log('Loading performance data with token:', adminToken ? 'Token present' : 'No token');
                const response = await fetch('/api/admin/trading-bot/performance', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('Performance data received:', data);
                    document.getElementById('bot-total-trades').textContent = data.total_trades || '0';
                    document.getElementById('bot-win-rate').textContent = data.win_rate ? `${data.win_rate}%` : '0%';
                    document.getElementById('bot-total-pnl').textContent = data.total_pnl || '$0.00';
                    document.getElementById('bot-avg-trades').textContent = data.avg_trades_per_day || '0';
                    document.getElementById('bot-active-alerts').textContent = data.active_alerts || '0';
                } else {
                    document.getElementById('bot-total-trades').textContent = '0';
                    document.getElementById('bot-win-rate').textContent = '0%';
                    document.getElementById('bot-total-pnl').textContent = '$0.00';
                    document.getElementById('bot-avg-trades').textContent = '0';
                    document.getElementById('bot-active-alerts').textContent = '0';
                }
            } catch (error) {
                console.error('Error loading performance data:', error);
            }
        }

        async function loadTradingSignals() {
            try {
                console.log('Loading trading signals with token:', adminToken ? 'Token present' : 'No token');
                const response = await fetch('/api/admin/trading-bot/current-signal', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('Trading signals data received:', data);

                    // Update signal status elements
                    const signalElement = document.getElementById('signal-status');
                    const confidenceElement = document.getElementById('signal-confidence');
                    const priceElement = document.getElementById('signal-price');
                    const timestampElement = document.getElementById('signal-timestamp');

                    if (signalElement && confidenceElement && priceElement && timestampElement) {
                        // Update signal status with appropriate styling
                        const signal = data.signal || 'HOLD';
                        signalElement.textContent = signal;

                        // Apply signal-specific styling
                        signalElement.className = 'px-3 py-1 rounded-full text-sm font-medium ';
                        if (signal === 'BUY') {
                            signalElement.className += 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200';
                        } else if (signal === 'SELL') {
                            signalElement.className += 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200';
                        } else {
                            signalElement.className += 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200';
                        }

                        // Update other fields
                        confidenceElement.textContent = data.confidence ? `${data.confidence}%` : '--';
                        priceElement.textContent = data.current_price ? `$${data.current_price}` : '--';

                        // Format timestamp
                        if (data.timestamp) {
                            const date = new Date(data.timestamp);
                            timestampElement.textContent = date.toLocaleTimeString();
                        } else {
                            timestampElement.textContent = '--';
                        }
                    }
                } else if (response.status === 401) {
                    console.error('Unauthorized access to trading signals - token may be invalid');
                    throw new Error('Unauthorized access to trading signals');
                } else {
                    console.error('Failed to load trading signals:', response.status, response.statusText);
                    throw new Error(`Failed to load trading signals: ${response.status}`);
                }
            } catch (error) {
                console.error('Error loading trading signals:', error);
                // Set default values for signal display
                const signalElement = document.getElementById('signal-status');
                const confidenceElement = document.getElementById('signal-confidence');
                const priceElement = document.getElementById('signal-price');
                const timestampElement = document.getElementById('signal-timestamp');

                if (signalElement) {
                    signalElement.textContent = 'UNAVAILABLE';
                    signalElement.className = 'px-3 py-1 rounded-full text-sm font-medium bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200';
                }
                if (confidenceElement) confidenceElement.textContent = '--';
                if (priceElement) priceElement.textContent = '--';
                if (timestampElement) timestampElement.textContent = '--';

                throw error; // Re-throw to be caught by Promise.allSettled
            }
        }

        // Refresh trading signals function
        async function refreshTradingSignals() {
            const refreshBtn = document.getElementById('refresh-signals-btn');
            const originalContent = refreshBtn.innerHTML;

            // Show loading state
            refreshBtn.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <span>Refreshing...</span>
            `;
            refreshBtn.disabled = true;

            try {
                await loadTradingSignals();

                // Show success feedback briefly
                refreshBtn.innerHTML = `
                    <svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span>Updated</span>
                `;

                setTimeout(() => {
                    refreshBtn.innerHTML = originalContent;
                    refreshBtn.disabled = false;
                }, 1500);

            } catch (error) {
                console.error('Error refreshing signals:', error);

                // Show error feedback
                refreshBtn.innerHTML = `
                    <svg class="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    <span>Error</span>
                `;

                setTimeout(() => {
                    refreshBtn.innerHTML = originalContent;
                    refreshBtn.disabled = false;
                }, 2000);
            }
        }

        async function loadTradingAlerts() {
            try {
                const response = await fetch('/api/admin/trading-bot/alerts', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const alertsContent = document.getElementById('trading-alerts-content');
                    const lastUpdated = document.getElementById('alerts-last-updated');

                    if (data.alerts && data.alerts.length > 0) {
                        alertsContent.innerHTML = data.alerts.map(alert =>
                            `<div class="mb-2 p-2 bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded text-sm">
                                <span class="font-medium">${alert.type}:</span> ${alert.message}
                                <span class="text-xs text-gray-500 dark:text-gray-400">(${alert.timestamp})</span>
                            </div>`
                        ).join('');
                    } else {
                        alertsContent.innerHTML = '<p class="text-gray-600 dark:text-gray-400">No recent alerts</p>';
                    }

                    lastUpdated.textContent = new Date().toLocaleTimeString();
                } else {
                    document.getElementById('trading-alerts-content').innerHTML = '<p class="text-red-600 dark:text-red-400">Unable to load alerts</p>';
                }
            } catch (error) {
                console.error('Error loading trading alerts:', error);
                document.getElementById('trading-alerts-content').innerHTML = '<p class="text-red-600 dark:text-red-400">Error loading alerts</p>';
            }
        }

        // Auto-refresh functionality
        function toggleAutoRefresh() {
            const button = document.getElementById('auto-refresh-toggle');

            if (isAutoRefreshEnabled) {
                clearInterval(autoRefreshInterval);
                isAutoRefreshEnabled = false;
                button.textContent = 'Auto Refresh: OFF';
                button.className = 'bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200';
            } else {
                autoRefreshInterval = setInterval(loadTradingBotData, 30000); // Refresh every 30 seconds
                isAutoRefreshEnabled = true;
                button.textContent = 'Auto Refresh: ON';
                button.className = 'bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200';
            }
        }

        // Manual refresh
        function refreshTradingData() {
            loadTradingBotData();
        }

        // Reset 2FA for user
        async function resetUser2FA(userId) {
            if (!confirm('Are you sure you want to reset 2FA for this user? This will disable their 2FA and they will need to set it up again.')) {
                return;
            }

            try {
                const response = await fetch(`/api/admin/users/${userId}/reset-2fa`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        reason: 'Admin reset 2FA via user profile'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('2FA reset successfully. User will need to set up 2FA again.');
                    // Refresh the user profile to show updated 2FA status
                    const currentUserId = document.getElementById('profile-user-id').textContent;
                    loadUserProfile(currentUserId);
                } else {
                    const data = await response.json();
                    alert(`Error: ${data.message || 'Failed to reset 2FA'}`);
                }
            } catch (error) {
                console.error('Error resetting user 2FA:', error);
                alert('Error resetting user 2FA');
            }
        }

        // Terminal Functions
        function addTerminalLine(type, message, customColor = null) {
            const now = new Date();
            const timestamp = now.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const messageColor = customColor || 'text-green-400';
            const line = `<div class="mb-1"><span class="text-gray-500">[${timestamp}]</span> <span class="text-yellow-400">[${type}]</span> <span class="${messageColor}">${message}</span></div>`;

            // Add new lines at the beginning (top) for reverse chronological order
            terminalLines.unshift(line);
            if (terminalLines.length > maxTerminalLines) {
                terminalLines.pop(); // Remove from the end (oldest)
            }

            const terminalOutput = document.getElementById('terminal-output');
            terminalOutput.innerHTML = terminalLines.join('');
            // Don't auto-scroll to bottom since newest content is at top
            // Users can scroll down to see older logs if needed
        }

        // Function to add multiple lines as a batch (newest batch at top)
        function addTerminalBatch(lines) {
            const now = new Date();
            const timestamp = now.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            // Create HTML for all lines in the batch with same timestamp
            const batchHtml = lines.map(lineData => {
                const messageColor = lineData.color || 'text-green-400';
                return `<div class="mb-1"><span class="text-gray-500">[${timestamp}]</span> <span class="text-yellow-400">[${lineData.type}]</span> <span class="${messageColor}">${lineData.message}</span></div>`;
            }).join('');

            // Add entire batch at the beginning (top) as one unit
            terminalLines.unshift(batchHtml);

            // Remove old batches if we exceed the limit
            if (terminalLines.length > maxTerminalLines) {
                terminalLines = terminalLines.slice(0, maxTerminalLines);
            }

            const terminalOutput = document.getElementById('terminal-output');
            terminalOutput.innerHTML = terminalLines.join('');
        }

        function startTerminalMonitoring() {
            addTerminalLine('SYSTEM', 'Starting trading bot monitoring...');
            addTerminalLine('SYSTEM', 'Logs displayed in reverse chronological order (newest first)');
            addTerminalLine('HEALTH', 'Checking system health status...');

            // Load real trading conditions every 5 seconds
            setInterval(async () => {
                await loadTradingConditions();
            }, 5000);

            // Initial load
            loadTradingConditions();
        }

        async function loadTradingConditions() {
            try {
                const response = await fetch('/api/admin/trading-bot/conditions', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.conditions && data.conditions.length > 0) {
                        // Collect all lines for this batch
                        const batchLines = [];

                        // Add summary first (will appear at top of batch)
                        const passedChecks = data.passed_checks || 0;
                        const totalChecks = data.total_checks || 0;
                        batchLines.push({
                            type: 'SUMMARY',
                            message: `Trading conditions: ${passedChecks}/${totalChecks} checks passed`,
                            color: 'text-green-400'
                        });

                        // Add individual condition checks in order (will appear below summary in batch)
                        data.conditions.forEach(condition => {
                            const status = condition.status === 'PASS' ? '✓' : '✗';
                            const color = condition.status === 'PASS' ? 'text-green-400' : 'text-red-400';
                            batchLines.push({
                                type: 'CHECK',
                                message: `${status} ${condition.check}: ${condition.details}`,
                                color: color
                            });
                        });

                        // Add entire batch at once (newest batch will appear at top)
                        addTerminalBatch(batchLines);
                    } else {
                        addTerminalLine('ERROR', 'No trading conditions data available');
                    }
                } else {
                    addTerminalLine('ERROR', 'Failed to fetch trading conditions');
                }
            } catch (error) {
                addTerminalLine('ERROR', `Trading conditions check failed: ${error.message}`);
            }
        }

        function updateTerminalWithData(data) {
            if (data.containers && data.containers.length > 0) {
                addTerminalLine('CONTAINERS', `Found ${data.containers.length} active trading containers`);
                data.containers.forEach(container => {
                    addTerminalLine('USER', `${container.user_email} - Tier ${container.tier} - ${container.status}`);
                });
            }
        }

        function displayHealthDetails(data) {
            const healthDetailsDiv = document.getElementById('health-details');
            const healthDetailsContent = document.getElementById('health-details-content');
            const failedServicesAlert = document.getElementById('failed-services-alert');
            const failedServicesList = document.getElementById('failed-services-list');

            // Show/hide detailed health information
            if (data.detailed_status) {
                healthDetailsDiv.style.display = 'block';

                // Clear previous content
                healthDetailsContent.innerHTML = '';

                // Display each service status
                const services = [
                    { name: 'Database', key: 'database' },
                    { name: 'Market Data', key: 'market_data' },
                    { name: 'ML Service', key: 'ml_service' }
                ];

                services.forEach(service => {
                    const serviceData = data.detailed_status[service.key];
                    const statusColor = serviceData.healthy ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
                    const statusIcon = serviceData.healthy ? '✓' : '✗';
                    const statusText = serviceData.healthy ? 'Healthy' : 'Failed';

                    const serviceDiv = document.createElement('div');
                    serviceDiv.className = 'flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg';

                    serviceDiv.innerHTML = `
                        <div class="flex items-center">
                            <span class="${statusColor} font-mono text-lg mr-3">${statusIcon}</span>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white">${service.name}</p>
                                <p class="text-sm ${statusColor}">${statusText}</p>
                            </div>
                        </div>
                        ${serviceData.error ? `<div class="text-sm text-red-600 dark:text-red-400 max-w-md text-right">${serviceData.error}</div>` : ''}
                    `;

                    healthDetailsContent.appendChild(serviceDiv);
                });
            }

            // Show/hide failed services alert
            if (data.failed_services && data.failed_services.length > 0) {
                failedServicesAlert.style.display = 'block';

                // Clear previous content
                failedServicesList.innerHTML = '';

                // Add each failed service
                data.failed_services.forEach(service => {
                    const li = document.createElement('li');
                    li.textContent = service;
                    failedServicesList.appendChild(li);
                });
            } else {
                failedServicesAlert.style.display = 'none';
            }
        }

        // User Profile Functions
        function showUserProfile(userId) {
            console.log('showUserProfile called with userId:', userId);

            // Find the dashboard and profile page elements using ID selectors
            const dashboard = document.getElementById('admin-dashboard');
            const profilePage = document.getElementById('user-profile-page');

            if (!dashboard) {
                console.error('Dashboard element not found');
                return;
            }

            if (!profilePage) {
                console.error('Profile page element not found');
                return;
            }

            // Hide dashboard and show profile page
            dashboard.style.display = 'none';
            profilePage.classList.remove('hidden');

            // Load user profile data
            loadUserProfile(userId);
        }

        async function loadUserProfile(userId) {
            try {
                // Add cache-busting parameter to ensure fresh data
                const timestamp = new Date().getTime();
                const response = await fetch(`/api/admin/users/${userId}/profile?t=${timestamp}`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    populateUserProfile(data);
                } else {
                    alert('Failed to load user profile');
                }
            } catch (error) {
                console.error('Error loading user profile:', error);
                alert('Error loading user profile');
            }
        }

        function populateUserProfile(data) {
            // User info
            document.getElementById('profile-user-email').textContent = data.user_info.email;
            document.getElementById('profile-user-id').textContent = data.user_info.id;
            document.getElementById('profile-user-created').textContent = new Date(data.user_info.created_at).toLocaleDateString();
            document.getElementById('profile-auto-trading').textContent = data.user_info.auto_trading_enabled ? 'Enabled' : 'Disabled';
            document.getElementById('profile-2fa-status').textContent = data.user_info.two_fa_enabled ? 'Enabled' : 'Disabled';
            document.getElementById('profile-user-tier').textContent = `Tier ${data.tier_status.current_tier}`;

            // Show/hide Reset 2FA button based on 2FA status
            const reset2faBtn = document.getElementById('reset-2fa-btn');
            if (data.user_info.two_fa_enabled) {
                reset2faBtn.style.display = 'inline-block';
                reset2faBtn.title = 'Reset 2FA for this user';
            } else {
                reset2faBtn.style.display = 'none';
            }

            // Status styling
            const statusElement = document.getElementById('profile-user-status');
            statusElement.textContent = data.user_info.status.charAt(0).toUpperCase() + data.user_info.status.slice(1);

            // Status color coding
            statusElement.className = 'px-3 py-1 rounded-full text-sm font-medium ';
            if (data.user_info.status === 'active') {
                statusElement.className += 'bg-green-100 text-green-800';
            } else if (data.user_info.status === 'inactive') {
                statusElement.className += 'bg-red-100 text-red-800';
            } else if (data.user_info.status === 'unverified') {
                statusElement.className += 'bg-yellow-100 text-yellow-800';
            } else {
                statusElement.className += 'bg-gray-100 text-gray-800';
            }

            // Stats
            document.getElementById('profile-total-trades').textContent = data.financial_info.total_trades || 0;
            document.getElementById('profile-total-profit').textContent = `$${data.financial_info.total_profit || 0}`;
            document.getElementById('profile-profit-share').textContent = `$${data.financial_info.profit_share_owed || 0}`;
            document.getElementById('profile-total-payments').textContent = data.financial_info.total_payments || 0;

            // Trading container info
            populateTradingContainer(data.user_info.id, data.user_info);

            // Populate trades table
            populateTradesTable(data.trades);

            // Populate payments table
            populatePaymentsTable(data.payments);

            // Populate exchange connections
            populateExchangeConnections(data.api_credentials);

            // Store user ID for delete function
            document.getElementById('delete-user-btn').setAttribute('data-user-id', data.user_info.id);

            // Apply admin privilege restrictions to delete button
            const tokenPayload = JSON.parse(atob(adminToken.split('.')[1]));
            const isSuperAdmin = tokenPayload.is_super_admin || false;
            const deleteUserBtn = document.getElementById('delete-user-btn');

            if (!isSuperAdmin) {
                deleteUserBtn.disabled = true;
                deleteUserBtn.className = 'bg-gray-400 cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium opacity-50';
                deleteUserBtn.title = 'Super admin privileges required';
            } else {
                deleteUserBtn.disabled = false;
                deleteUserBtn.className = 'bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200';
                deleteUserBtn.title = '';
            }
        }

        async function populateTradingContainer(userId, userInfo = null) {
            try {
                const response = await fetch('/api/admin/trading-bot/containers', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const userContainer = data.containers?.find(container => container.user_id === userId);

                    if (userContainer) {
                        document.getElementById('container-status').textContent = userContainer.status || 'Unknown';
                        document.getElementById('container-current-trade').textContent = userContainer.current_trade || 'None';
                        document.getElementById('container-risk').textContent = userContainer.risk_percentage ? `${userContainer.risk_percentage}%` : '-';
                        document.getElementById('container-leverage').textContent = userContainer.leverage ? `${userContainer.leverage}x` : '-';

                    } else {
                        document.getElementById('container-status').textContent = 'Inactive';
                        document.getElementById('container-current-trade').textContent = 'None';
                        document.getElementById('container-risk').textContent = '-';
                        document.getElementById('container-leverage').textContent = '-';
                    }

                    // Set up reset 2FA button (available regardless of trading status)
                    const reset2FABtn = document.getElementById('reset-2fa-btn');
                    reset2FABtn.onclick = () => resetUser2FA(userId);

                    // Enable/disable based on 2FA status from userInfo parameter
                    if (userInfo) {
                        const has2FA = userInfo.two_fa_enabled;
                        reset2FABtn.disabled = !has2FA;
                        reset2FABtn.title = has2FA ? 'Reset user\\'s 2FA settings' : 'User does not have 2FA enabled';
                    } else {
                        // Fallback if userInfo is not available
                        reset2FABtn.disabled = false;
                        reset2FABtn.title = 'Reset user\\'s 2FA settings';
                    }
                } else {
                    console.error('Failed to load trading containers');
                }
            } catch (error) {
                console.error('Error loading trading container:', error);
            }

            // Set up refresh button
            const refreshBtn = document.getElementById('refresh-container-btn');
            refreshBtn.onclick = () => populateTradingContainer(userId, userInfo);
        }

        function populateTradesTable(trades) {
            const tbody = document.getElementById('profile-trades-table');
            tbody.innerHTML = '';

            if (trades.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">No trades found</td></tr>';
                return;
            }

            trades.forEach(trade => {
                const row = document.createElement('tr');
                const pnlColor = trade.pnl > 0 ? 'text-green-600' : trade.pnl < 0 ? 'text-red-600' : 'text-gray-600';

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${trade.symbol || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${trade.side || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">$${trade.entry_price || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">$${trade.exit_price || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${trade.quantity || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm ${pnlColor}">$${trade.pnl || '0.00'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${trade.status || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${trade.entry_time ? new Date(trade.entry_time).toLocaleString() : '-'}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function populatePaymentsTable(payments) {
            const tbody = document.getElementById('profile-payments-table');
            tbody.innerHTML = '';

            if (payments.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">No payments found</td></tr>';
                return;
            }

            payments.forEach(payment => {
                const row = document.createElement('tr');
                const statusColor = payment.status === 'completed' ? 'text-green-600' : payment.status === 'failed' ? 'text-red-600' : 'text-yellow-600';

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">$${payment.amount || '0.00'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${payment.currency || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm ${statusColor}">${payment.status || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${payment.payment_method || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${payment.created_at ? new Date(payment.created_at).toLocaleString() : '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${payment.description || '-'}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function populateExchangeConnections(apiCredentials) {
            const container = document.getElementById('profile-exchange-connections');

            if (!apiCredentials || apiCredentials.length === 0) {
                container.innerHTML = '<p class="text-sm text-gray-500 dark:text-gray-400">No exchange connections found</p>';
                return;
            }

            // Exchange icons mapping
            const exchangeIcons = {
                'binance': '🟡',
                'binance_us': '🟡',
                'kraken': '🟣',
                'bitso': '🔵'
            };

            // Exchange display names
            const exchangeNames = {
                'binance': 'Binance',
                'binance_us': 'Binance US',
                'kraken': 'Kraken',
                'bitso': 'Bitso'
            };

            let html = '<div class="grid grid-cols-1 sm:grid-cols-2 gap-3">';

            apiCredentials.forEach(credential => {
                const exchangeKey = credential.exchange.toLowerCase();
                const icon = exchangeIcons[exchangeKey] || '🔗';
                const name = exchangeNames[exchangeKey] || credential.exchange;
                const statusColor = credential.is_active ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
                const statusText = credential.is_active ? 'Connected' : 'Disconnected';
                const bgColor = credential.is_active ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800';

                html += `
                    <div class="flex items-center justify-between p-3 rounded-lg border ${bgColor}">
                        <div class="flex items-center space-x-3">
                            <span class="text-lg">${icon}</span>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white">${name}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Added: ${credential.created_at ? new Date(credential.created_at).toLocaleDateString() : 'Unknown'}</p>
                            </div>
                        </div>
                        <span class="text-sm font-medium ${statusColor}">${statusText}</span>
                    </div>
                `;
            });

            html += '</div>';
            container.innerHTML = html;
        }

        // Back to dashboard
        document.addEventListener('DOMContentLoaded', function() {
            const backButton = document.getElementById('back-to-dashboard');
            if (backButton) {
                backButton.addEventListener('click', function() {
                    const dashboard = document.getElementById('admin-dashboard');
                    const profilePage = document.getElementById('user-profile-page');

                    if (profilePage) {
                        profilePage.classList.add('hidden');
                    }
                    if (dashboard) {
                        dashboard.style.display = 'block';
                    }
                });
            }

            // Profile theme toggle
            const profileThemeToggle = document.getElementById('profile-theme-toggle');
            if (profileThemeToggle) {
                profileThemeToggle.addEventListener('click', function() {
                    document.documentElement.classList.toggle('dark');
                    localStorage.setItem('theme', document.documentElement.classList.contains('dark') ? 'dark' : 'light');
                });
            }

            // Delete user functionality
            document.getElementById('delete-user-btn').addEventListener('click', function() {
                const userId = this.getAttribute('data-user-id');
                const userEmailElement = document.getElementById('profile-user-email');
                const deleteModalEmailElement = document.getElementById('delete-modal-user-email');
                const deleteModalIdElement = document.getElementById('delete-modal-user-id');
                const deleteModalElement = document.getElementById('delete-user-modal');

                // Debug logging
                console.log('Delete button clicked');
                console.log('User ID:', userId);
                console.log('User email element:', userEmailElement);
                console.log('Modal elements:', {
                    deleteModalEmailElement,
                    deleteModalIdElement,
                    deleteModalElement
                });

                // Additional debug - check if modal exists anywhere in DOM
                console.log('All elements with delete-user-modal ID:', document.querySelectorAll('#delete-user-modal'));
                console.log('All elements with delete-modal-user-email ID:', document.querySelectorAll('#delete-modal-user-email'));

                if (!userEmailElement || !deleteModalEmailElement || !deleteModalIdElement || !deleteModalElement) {
                    console.error('Required elements not found');
                    alert('Error: Could not find required elements for deletion modal');
                    return;
                }

                const userEmail = userEmailElement.textContent;

                // Show confirmation modal
                deleteModalEmailElement.textContent = userEmail;
                deleteModalIdElement.textContent = userId;
                deleteModalElement.classList.remove('hidden');
            });

            // Modal cancel button
            const cancelDeleteBtn = document.getElementById('cancel-delete-btn');
            if (cancelDeleteBtn) {
                cancelDeleteBtn.addEventListener('click', function() {
                    const deleteModal = document.getElementById('delete-user-modal');
                    if (deleteModal) {
                        deleteModal.classList.add('hidden');
                    }
                });
            }

            // Modal confirm delete button
            const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
            if (confirmDeleteBtn) {
                confirmDeleteBtn.addEventListener('click', async function() {
                    const userIdElement = document.getElementById('delete-modal-user-id');
                    const userEmailElement = document.getElementById('delete-modal-user-email');

                    if (!userIdElement || !userEmailElement) {
                        console.error('Modal elements not found');
                        alert('Error: Could not find modal elements');
                        return;
                    }

                    const userId = userIdElement.textContent;
                    const userEmail = userEmailElement.textContent;

                // Hide modal
                const deleteModal = document.getElementById('delete-user-modal');
                if (deleteModal) {
                    deleteModal.classList.add('hidden');
                }

                try {
                    const response = await fetch(`/api/admin/users/${userId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${adminToken}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        // Show success message and reload page
                        alert(`User ${userEmail} deleted successfully!\n\nClick OK to refresh the dashboard.`);

                        // Simply reload the admin page to refresh everything
                        window.location.reload();
                    } else {
                        const error = await response.json();
                        alert(`Failed to delete user: ${error.message}`);
                    }
                } catch (error) {
                    console.error('Error deleting user:', error);
                    alert('Error deleting user');
                }
            });
            }

            // Close modal when clicking outside
            const deleteModal = document.getElementById('delete-user-modal');
            if (deleteModal) {
                deleteModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        this.classList.add('hidden');
                    }
                });
            }
        });

        // Create coupon functionality
        function showCreateCouponModal() {
            // Check admin privileges
            const tokenPayload = JSON.parse(atob(adminToken.split('.')[1]));
            const isSuperAdmin = tokenPayload.is_super_admin || false;

            if (!isSuperAdmin) {
                alert('Super admin privileges required to create coupons.');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
                    <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white">Create New Coupon</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <div class="p-6 space-y-4">
                        <!-- Coupon Code Section -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Coupon Code
                            </label>
                            <div class="flex space-x-2">
                                <input type="text" id="coupon-code-input"
                                       class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                                       placeholder="Enter custom code or generate random">
                                <button onclick="generateRandomCouponCode()"
                                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200">
                                    <svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Generate
                                </button>
                            </div>
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                Code must be 4-20 characters, letters and numbers only
                            </p>
                        </div>

                        <!-- Tier Level -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Tier Level
                            </label>
                            <select id="coupon-tier-select"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="2">Tier 2 - Premium Access</option>
                                <option value="3">Tier 3 - VIP Access</option>
                            </select>
                        </div>

                        <!-- Expiration Days -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Valid for (Days)
                            </label>
                            <select id="coupon-days-select"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="7">7 days</option>
                                <option value="14">14 days</option>
                                <option value="30" selected>30 days</option>
                                <option value="60">60 days</option>
                                <option value="90">90 days</option>
                                <option value="180">180 days (6 months)</option>
                                <option value="365">365 days (1 year)</option>
                            </select>
                        </div>

                        <!-- Maximum Uses -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Maximum Uses
                            </label>
                            <select id="coupon-uses-select"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="1" selected>1 use (Single use)</option>
                                <option value="5">5 uses</option>
                                <option value="10">10 uses</option>
                                <option value="25">25 uses</option>
                                <option value="50">50 uses</option>
                                <option value="100">100 uses</option>
                                <option value="unlimited">Unlimited uses</option>
                            </select>
                        </div>

                        <!-- Description -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Description (Optional)
                            </label>
                            <textarea id="coupon-description-input"
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                                      rows="2"
                                      placeholder="Brief description of this coupon..."></textarea>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
                        <button onclick="this.closest('.fixed').remove()"
                                class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg font-medium transition-colors duration-200">
                            Cancel
                        </button>
                        <button onclick="createCouponFromModal()"
                                class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors duration-200">
                            Create Coupon
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Focus on the code input
            setTimeout(() => {
                document.getElementById('coupon-code-input').focus();
            }, 100);
        }

        // Generate random coupon code
        function generateRandomCouponCode() {
            const prefix = 'DT';
            const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let result = prefix;

            // Generate 6 random characters
            for (let i = 0; i < 6; i++) {
                result += characters.charAt(Math.floor(Math.random() * characters.length));
            }

            // Set the generated code in the input field
            document.getElementById('coupon-code-input').value = result;
        }

        // Create coupon from modal data
        async function createCouponFromModal() {
            const codeInput = document.getElementById('coupon-code-input');
            const tierSelect = document.getElementById('coupon-tier-select');
            const daysSelect = document.getElementById('coupon-days-select');
            const usesSelect = document.getElementById('coupon-uses-select');
            const descriptionInput = document.getElementById('coupon-description-input');

            const code = codeInput.value.trim().toUpperCase();
            const tierLevel = parseInt(tierSelect.value);
            const expirationDays = parseInt(daysSelect.value);
            const maxUsesValue = usesSelect.value;
            const description = descriptionInput.value.trim();

            // Validation
            if (!code) {
                alert('❌ Please enter a coupon code or generate one.');
                codeInput.focus();
                return;
            }

            if (!/^[A-Z0-9]{4,20}$/.test(code)) {
                alert('❌ Coupon code must be 4-20 characters, letters and numbers only.');
                codeInput.focus();
                return;
            }

            // Convert unlimited uses to a large number
            const maxUses = maxUsesValue === 'unlimited' ? 999999 : parseInt(maxUsesValue);

            try {
                // Show loading state
                const createButton = document.querySelector('button[onclick="createCouponFromModal()"]');
                const originalText = createButton.textContent;
                createButton.textContent = 'Creating...';
                createButton.disabled = true;

                const response = await fetch('/api/admin/coupons', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        custom_code: code,
                        tier_level: tierLevel,
                        expiration_days: expirationDays,
                        max_uses: maxUses,
                        description: description
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    alert(`✅ Coupon created successfully!\n\nCode: ${data.coupon.code}\nTier: ${data.coupon.tier_level}\nValid for: ${expirationDays} days\nMax uses: ${maxUsesValue}\n${description ? `Description: ${description}` : ''}`);

                    // Close modal and refresh table
                    document.querySelector('.fixed').remove();
                    loadCoupons();
                } else {
                    const errorData = await response.json();
                    alert(`❌ Error creating coupon: ${errorData.message || 'Unknown error'}`);
                }
            } catch (error) {
                console.error('Error creating coupon:', error);
                alert('❌ Error creating coupon. Please try again.');
            } finally {
                // Reset button state
                const createButton = document.querySelector('button[onclick="createCouponFromModal()"]');
                if (createButton) {
                    createButton.textContent = originalText;
                    createButton.disabled = false;
                }
            }
        }

        async function createCoupon(code, tier, maxUses, expiresAt) {
            try {
                const response = await fetch('/api/admin/coupons', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: code,
                        tier: tier,
                        max_uses: maxUses,
                        expires_at: expiresAt
                    })
                });

                if (response.ok) {
                    alert('Coupon created successfully');
                    loadCoupons(); // Refresh the table
                } else {
                    const data = await response.json();
                    alert(`Error: ${data.message || 'Failed to create coupon'}`);
                }
            } catch (error) {
                console.error('Error creating coupon:', error);
                alert('Error creating coupon');
            }
        }

        // IP Management Functions
        function initializeIPManagement() {
            loadIPStats();
            loadIPAccessLogs();

            // Set up IP management tab switching
            document.getElementById('ip-access-logs-tab').addEventListener('click', () => switchIPTab('access-logs'));
            document.getElementById('ip-blacklist-tab').addEventListener('click', () => switchIPTab('blacklist'));
            document.getElementById('ip-rate-limits-tab').addEventListener('click', () => switchIPTab('rate-limits'));
            document.getElementById('admin-connections-tab').addEventListener('click', () => switchIPTab('admin-connections'));

            // Set up pagination event listeners
            document.getElementById('users-per-page').addEventListener('change', function() {
                const perPage = parseInt(this.value);
                loadUsers(1, perPage);
            });

            document.getElementById('users-prev-btn').addEventListener('click', function() {
                if (currentUsersPage > 1) {
                    loadUsers(currentUsersPage - 1, currentUsersPerPage);
                }
            });

            document.getElementById('users-next-btn').addEventListener('click', function() {
                loadUsers(currentUsersPage + 1, currentUsersPerPage);
            });

            // Set up coupons pagination event listeners
            document.getElementById('coupons-per-page').addEventListener('change', function() {
                const perPage = parseInt(this.value);
                loadCoupons(1, perPage);
            });

            document.getElementById('coupons-prev-btn').addEventListener('click', function() {
                if (currentCouponsPage > 1) {
                    loadCoupons(currentCouponsPage - 1, currentCouponsPerPage);
                }
            });

            document.getElementById('coupons-next-btn').addEventListener('click', function() {
                loadCoupons(currentCouponsPage + 1, currentCouponsPerPage);
            });

            // Set up IP logs pagination event listeners
            document.getElementById('ip-logs-per-page').addEventListener('change', function() {
                const perPage = parseInt(this.value);
                loadIPAccessLogs(1, perPage);
            });

            document.getElementById('ip-logs-prev-btn').addEventListener('click', function() {
                if (currentIPLogsPage > 1) {
                    loadIPAccessLogs(currentIPLogsPage - 1, currentIPLogsPerPage);
                }
            });

            document.getElementById('ip-logs-next-btn').addEventListener('click', function() {
                loadIPAccessLogs(currentIPLogsPage + 1, currentIPLogsPerPage);
            });

            // Set up IP management event listeners
            document.getElementById('ban-ip-btn').addEventListener('click', showBanIPModal);
            document.getElementById('view-suspicious-ips-btn').addEventListener('click', showSuspiciousIPs);
            document.getElementById('refresh-ip-logs-btn').addEventListener('click', loadIPAccessLogs);

            // Set up search and filter
            document.getElementById('ip-search').addEventListener('input', debounce(() => loadIPAccessLogs(1, currentIPLogsPerPage), 500));
            document.getElementById('ip-filter-success').addEventListener('change', () => loadIPAccessLogs(1, currentIPLogsPerPage));
        }

        // Switch IP management tabs
        function switchIPTab(tabName) {
            // Hide all tab contents
            document.getElementById('ip-access-logs-content').classList.add('hidden');
            document.getElementById('ip-blacklist-content').classList.add('hidden');
            document.getElementById('ip-rate-limits-content').classList.add('hidden');
            document.getElementById('admin-connections-content').classList.add('hidden');

            // Remove active state from all tabs
            document.querySelectorAll('[id$="-tab"]').forEach(tab => {
                tab.className = 'py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300';
            });

            // Show selected tab content and set active state
            if (tabName === 'access-logs') {
                document.getElementById('ip-access-logs-content').classList.remove('hidden');
                document.getElementById('ip-access-logs-tab').className = 'py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 dark:text-blue-400';
                loadIPAccessLogs();
            } else if (tabName === 'blacklist') {
                document.getElementById('ip-blacklist-content').classList.remove('hidden');
                document.getElementById('ip-blacklist-tab').className = 'py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 dark:text-blue-400';
                loadIPBlacklist();
            } else if (tabName === 'rate-limits') {
                document.getElementById('ip-rate-limits-content').classList.remove('hidden');
                document.getElementById('ip-rate-limits-tab').className = 'py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 dark:text-blue-400';
                loadIPRateLimits();
            } else if (tabName === 'admin-connections') {
                document.getElementById('admin-connections-content').classList.remove('hidden');
                document.getElementById('admin-connections-tab').className = 'py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 dark:text-blue-400';
                loadAdminConnections();
            }
        }

        // Load IP statistics
        async function loadIPStats() {
            try {
                const response = await fetch('/api/admin/ip/stats?days=30', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('ip-stats-total-logins').textContent = data.total_logins.toLocaleString();
                    document.getElementById('ip-stats-success-rate').textContent = data.success_rate + '%';
                    document.getElementById('ip-stats-banned').textContent = data.active_bans.toLocaleString();
                    document.getElementById('ip-stats-rate-limited').textContent = data.rate_limited_ips.toLocaleString();
                }
            } catch (error) {
                console.error('Error loading IP stats:', error);
            }
        }

        // Load IP access logs with pagination
        async function loadIPAccessLogs(page = 1, perPage = 25) {
            try {
                currentIPLogsPage = page;
                currentIPLogsPerPage = perPage;

                const searchTerm = document.getElementById('ip-search').value;
                const successFilter = document.getElementById('ip-filter-success').value;

                let url = `/api/admin/ip/access-logs?page=${page}&per_page=${perPage}`;
                if (searchTerm) url += `&ip_address=${encodeURIComponent(searchTerm)}`;
                if (successFilter) url += `&login_successful=${successFilter}`;

                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayIPAccessLogs(data.access_logs);
                    updateIPLogsPagination(data.pagination);
                } else if (response.status === 401) {
                    console.error('Unauthorized access to IP logs');
                    alert('Session expired. Please login again.');
                    showLogin();
                } else {
                    console.error('Error loading IP access logs:', response.status);
                }
            } catch (error) {
                console.error('Error loading IP access logs:', error);
            }
        }

        // Update IP logs pagination
        function updateIPLogsPagination(pagination) {
            const paginationInfo = document.getElementById('ip-logs-pagination-info');
            const pageInfo = document.getElementById('ip-logs-page-info');
            const prevBtn = document.getElementById('ip-logs-prev-btn');
            const nextBtn = document.getElementById('ip-logs-next-btn');

            if (pagination) {
                const start = ((pagination.page - 1) * pagination.per_page) + 1;
                const end = Math.min(pagination.page * pagination.per_page, pagination.total);

                paginationInfo.textContent = `Showing ${start}-${end} of ${pagination.total} access logs`;
                pageInfo.textContent = `Page ${pagination.page} of ${pagination.pages}`;

                prevBtn.disabled = !pagination.has_prev;
                nextBtn.disabled = !pagination.has_next;
            }
        }

        // Display IP access logs in table
        function displayIPAccessLogs(logs) {
            const tbody = document.getElementById('ip-access-logs-table');
            tbody.innerHTML = '';

            logs.forEach(log => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150';

                const statusBadge = log.login_successful
                    ? '<span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Success</span>'
                    : '<span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">Failed</span>';

                // Since we're only showing frontend server logs, user_id should always be present
                const userInfo = log.user_id
                    ? `User ID: ${log.user_id.substring(0, 8)}...`
                    : 'Unknown';

                const location = log.geographic_location || 'Unknown';
                const timestamp = new Date(log.login_timestamp).toLocaleString('en-US', {
                    timeZone: 'America/Sao_Paulo', // GMT-3
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900 dark:text-gray-100">${log.ip_address}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${userInfo}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${location}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${statusBadge}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${timestamp}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <button onclick="viewIPDetails('${log.ip_address}')" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                            View Details
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Load IP blacklist
        async function loadIPBlacklist() {
            try {
                const response = await fetch('/api/admin/ip/blacklist?page=1&per_page=50', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayIPBlacklist(data.blacklist);
                }
            } catch (error) {
                console.error('Error loading IP blacklist:', error);
            }
        }

        // Display IP blacklist
        function displayIPBlacklist(blacklist) {
            const tbody = document.getElementById('ip-blacklist-table');
            tbody.innerHTML = '';

            blacklist.forEach(ban => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150';

                const statusBadge = ban.is_active && !ban.is_expired
                    ? '<span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">Active</span>'
                    : '<span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">Inactive</span>';

                const bannedAt = new Date(ban.banned_at).toLocaleString();
                const expiresAt = ban.expires_at ? new Date(ban.expires_at).toLocaleString() : 'Never';

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900 dark:text-gray-100">${ban.ip_address}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${ban.ban_reason}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${ban.banned_by_username || 'System'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${bannedAt}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${expiresAt}</td>
                    <td class="px-6 py-4 whitespace-nowrap">${statusBadge}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        ${ban.is_active ?
                            `<button onclick="unbanIP('${ban.ip_address}')" class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 font-medium mr-2">Unban</button>` :
                            ''
                        }
                        <button onclick="viewIPDetails('${ban.ip_address}')" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">Details</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Load IP rate limits
        async function loadIPRateLimits() {
            try {
                const response = await fetch('/api/admin/ip/rate-limits?page=1&per_page=50', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayIPRateLimits(data.rate_limits);
                }
            } catch (error) {
                console.error('Error loading IP rate limits:', error);
            }
        }

        // Display IP rate limits
        function displayIPRateLimits(rateLimits) {
            const tbody = document.getElementById('ip-rate-limits-table');
            tbody.innerHTML = '';

            rateLimits.forEach(limit => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150';

                const firstAttempt = new Date(limit.first_attempt_at).toLocaleString('en-US', {
                    timeZone: 'America/Sao_Paulo', // GMT-3
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });
                const lastAttempt = new Date(limit.last_attempt_at).toLocaleString('en-US', {
                    timeZone: 'America/Sao_Paulo', // GMT-3
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });
                const blockedUntil = limit.blocked_until ? new Date(limit.blocked_until).toLocaleString('en-US', {
                    timeZone: 'America/Sao_Paulo', // GMT-3
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                }) : 'Not blocked';

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900 dark:text-gray-100">${limit.ip_address}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${limit.attempt_count}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${firstAttempt}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${lastAttempt}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${blockedUntil}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <button onclick="resetRateLimit('${limit.ip_address}')" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium mr-2">Reset</button>
                        <button onclick="viewIPDetails('${limit.ip_address}')" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">Details</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Show ban IP modal
        function showBanIPModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

            const content = document.createElement('div');
            content.className = 'bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl';

            content.innerHTML = `
                <div class="mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Ban IP Address</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Enter the IP address and reason for banning.</p>
                </div>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">IP Address</label>
                        <input type="text" id="ban-ip-input" placeholder="***********"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Reason</label>
                        <textarea id="ban-reason-input" placeholder="Reason for banning this IP address..." rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Expiration (Optional)</label>
                        <input type="datetime-local" id="ban-expiry-input"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-6">
                    <button id="cancel-ban-btn" class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md font-medium transition-colors">
                        Cancel
                    </button>
                    <button id="confirm-ban-btn" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md font-medium transition-colors">
                        Ban IP
                    </button>
                </div>
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);

            // Focus on IP input
            setTimeout(() => {
                document.getElementById('ban-ip-input').focus();
            }, 100);

            // Handle cancel
            document.getElementById('cancel-ban-btn').addEventListener('click', () => {
                document.body.removeChild(modal);
            });

            // Handle confirm
            document.getElementById('confirm-ban-btn').addEventListener('click', async () => {
                const ipAddress = document.getElementById('ban-ip-input').value.trim();
                const reason = document.getElementById('ban-reason-input').value.trim();
                const expiry = document.getElementById('ban-expiry-input').value;

                if (!ipAddress || !reason) {
                    alert('Please enter both IP address and reason');
                    return;
                }

                await performIPBan(ipAddress, reason, expiry);
                document.body.removeChild(modal);
            });

            // Close on outside click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        }

        // Perform IP ban
        async function performIPBan(ipAddress, reason, expiry) {
            try {
                const requestBody = {
                    ip_address: ipAddress,
                    reason: reason
                };

                if (expiry) {
                    requestBody.expires_at = new Date(expiry).toISOString();
                }

                const response = await fetch('/api/admin/ip/ban', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                if (response.ok) {
                    showToast('IP address banned successfully', 'success');
                    loadIPBlacklist();
                    loadIPStats(); // Refresh stats
                } else {
                    const error = await response.json();
                    showToast('Error banning IP: ' + (error.message || 'Unknown error'), 'error');
                }
            } catch (error) {
                showToast('Network error: ' + error.message, 'error');
            }
        }

        // Ban IP address
        async function banIP(ipAddress, reason, expiresAt = null) {
            try {
                const response = await fetch('/api/admin/ip/ban', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        ip_address: ipAddress,
                        reason: reason,
                        expires_at: expiresAt
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    alert('IP address banned successfully');
                    loadIPBlacklist();
                    loadIPStats();
                } else {
                    alert('Error: ' + data.error);
                }
            } catch (error) {
                alert('Network error: ' + error.message);
            }
        }

        // Unban IP address
        async function unbanIP(ipAddress) {
            if (!confirm(`Are you sure you want to unban IP address ${ipAddress}?`)) {
                return;
            }

            try {
                const response = await fetch('/api/admin/ip/unban', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        ip_address: ipAddress
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    alert('IP address unbanned successfully');
                    loadIPBlacklist();
                    loadIPStats();
                } else {
                    alert('Error: ' + data.error);
                }
            } catch (error) {
                alert('Network error: ' + error.message);
            }
        }

        // Reset rate limit for IP
        async function resetRateLimit(ipAddress) {
            if (!confirm(`Are you sure you want to reset rate limit for IP address ${ipAddress}?`)) {
                return;
            }

            try {
                const response = await fetch('/api/admin/ip/rate-limits/reset', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        ip_address: ipAddress
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    alert('Rate limit reset successfully');
                    loadIPRateLimits();
                } else {
                    alert('Error: ' + data.error);
                }
            } catch (error) {
                alert('Network error: ' + error.message);
            }
        }

        // View IP details
        async function viewIPDetails(ipAddress) {
            try {
                const response = await fetch(`/api/admin/ip/summary/${encodeURIComponent(ipAddress)}`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    showIPDetailsModal(data);
                } else {
                    alert('Error loading IP details');
                }
            } catch (error) {
                alert('Network error: ' + error.message);
            }
        }

        // Show IP details modal
        function showIPDetailsModal(ipData) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';

            const content = document.createElement('div');
            content.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col';

            content.innerHTML = `
                <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white">IP Details: ${ipData.ip_address}</h3>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="flex-1 overflow-y-auto p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-medium text-gray-900 dark:text-white mb-3">Geolocation</h4>
                            <div class="space-y-1">
                                <p class="text-sm text-gray-600 dark:text-gray-300">Country: <span class="font-medium">${ipData.geolocation?.country || 'Unknown'}</span></p>
                                <p class="text-sm text-gray-600 dark:text-gray-300">City: <span class="font-medium">${ipData.geolocation?.city || 'Unknown'}</span></p>
                                <p class="text-sm text-gray-600 dark:text-gray-300">ISP: <span class="font-medium">${ipData.geolocation?.isp || 'Unknown'}</span></p>
                            </div>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-medium text-gray-900 dark:text-white mb-3">Statistics</h4>
                            <div class="space-y-1">
                                <p class="text-sm text-gray-600 dark:text-gray-300">Total Logins: <span class="font-medium">${ipData.total_logins || 0}</span></p>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Failed Attempts: <span class="font-medium">${ipData.failed_attempts || 0}</span></p>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Last Activity: <span class="font-medium">${ipData.last_activity ? new Date(ipData.last_activity).toLocaleString() : 'Never'}</span></p>
                            </div>
                        </div>
                    </div>

                    <!-- User Information Section -->
                    ${ipData.access_logs.length > 0 && ipData.access_logs[0].user_email ? `
                        <div class="mb-6">
                            <h4 class="font-medium text-gray-900 dark:text-white mb-3">
                                <i class="fas fa-user mr-2"></i>Primary User Information
                            </h4>
                            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-4 rounded-lg">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="text-sm font-medium text-blue-800 dark:text-blue-200">Complete User ID:</label>
                                        <p class="text-sm font-mono text-blue-900 dark:text-blue-100 bg-white dark:bg-gray-800 p-2 rounded border mt-1">
                                            ${ipData.access_logs[0].complete_user_id}
                                        </p>
                                    </div>
                                    <div>
                                        <label class="text-sm font-medium text-blue-800 dark:text-blue-200">Email Address:</label>
                                        <p class="text-sm text-blue-900 dark:text-blue-100 bg-white dark:bg-gray-800 p-2 rounded border mt-1">
                                            ${ipData.access_logs[0].user_email}
                                        </p>
                                    </div>
                                    ${ipData.access_logs[0].user_full_name ? `
                                        <div class="md:col-span-2">
                                            <label class="text-sm font-medium text-blue-800 dark:text-blue-200">Full Name:</label>
                                            <p class="text-sm text-blue-900 dark:text-blue-100 bg-white dark:bg-gray-800 p-2 rounded border mt-1">
                                                ${ipData.access_logs[0].user_full_name}
                                            </p>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    ` : ''}

                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white mb-3">Recent Access Logs</h4>
                        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                            <div class="overflow-x-auto">
                                <table class="min-w-full text-sm">
                                    <thead class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Timestamp</th>
                                            <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">User Type</th>
                                            <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Complete User ID</th>
                                            <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Email</th>
                                            <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Status</th>
                                        </tr>
                            </thead>
                            <tbody>
                                ${ipData.access_logs.slice(0, 10).map(log => `
                                    <tr class="border-b border-gray-100 dark:border-gray-700">
                                        <td class="py-2 px-4 text-gray-900 dark:text-gray-100">${new Date(log.login_timestamp).toLocaleString('en-US', {
                                            timeZone: 'America/Sao_Paulo', // GMT-3
                                            year: 'numeric',
                                            month: '2-digit',
                                            day: '2-digit',
                                            hour: '2-digit',
                                            minute: '2-digit',
                                            second: '2-digit',
                                            hour12: false
                                        })}</td>
                                        <td class="py-2 px-4 text-gray-900 dark:text-gray-100">${log.admin_id ? 'Admin' : 'Frontend User'}</td>
                                        <td class="py-2 px-4 text-gray-900 dark:text-gray-100">
                                            <span class="font-mono text-xs">${log.complete_user_id || 'N/A'}</span>
                                        </td>
                                        <td class="py-2 px-4 text-gray-900 dark:text-gray-100">
                                            <span class="text-xs">${log.user_email || 'N/A'}</span>
                                        </td>
                                        <td class="py-2 px-4">
                                            <span class="px-2 py-1 text-xs rounded-full ${log.login_successful ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'}">
                                                ${log.login_successful ? 'Success' : 'Failed'}
                                            </span>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);
        }

        // Show suspicious IPs
        async function showSuspiciousIPs() {
            try {
                const response = await fetch('/api/admin/ip/suspicious?days=7&min_attempts=3', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    showSuspiciousIPsModal(data.suspicious_ips);
                } else {
                    alert('Error loading suspicious IPs');
                }
            } catch (error) {
                alert('Network error: ' + error.message);
            }
        }

        // Show suspicious IPs modal
        function showSuspiciousIPsModal(suspiciousIPs) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';

            const content = document.createElement('div');
            content.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col';

            content.innerHTML = `
                <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white">Suspicious IP Addresses</h3>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="p-6 overflow-x-auto">
                    <table class="min-w-full text-sm">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr class="border-b border-gray-200 dark:border-gray-600">
                                <th class="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">IP Address</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">Failed Attempts</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">Last Attempt</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">Status</th>
                                <th class="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            ${suspiciousIPs.map(ip => `
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                                    <td class="py-3 px-4 font-mono text-gray-900 dark:text-gray-100">${ip.ip_address}</td>
                                    <td class="py-3 px-4 text-gray-900 dark:text-gray-100">${ip.failed_attempts}</td>
                                    <td class="py-3 px-4 text-gray-900 dark:text-gray-100">${ip.last_attempt ? new Date(ip.last_attempt).toLocaleString() : 'N/A'}</td>
                                    <td class="py-3 px-4">
                                        <span class="px-2 py-1 text-xs rounded-full ${ip.is_banned ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'}">
                                            ${ip.is_banned ? 'Banned' : 'Active'}
                                        </span>
                                    </td>
                                    <td class="py-3 px-4 space-x-2">
                                        ${!ip.is_banned ?
                                            `<button onclick="banIP('${ip.ip_address}', 'Suspicious activity detected')" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 font-medium">Ban</button>` :
                                            ''
                                        }
                                        <button onclick="viewIPDetails('${ip.ip_address}')" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">Details</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);
        }

        // Load admin connections
        async function loadAdminConnections() {
            try {
                const response = await fetch('/api/admin/ip/admin-connections', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayAdminConnections(data.admin_connections);
                } else if (response.status === 401) {
                    console.error('Unauthorized access to admin connections');
                    alert('Session expired. Please login again.');
                    showLogin();
                } else {
                    console.error('Error loading admin connections:', response.status);
                }
            } catch (error) {
                console.error('Error loading admin connections:', error);
            }
        }

        // Display admin connections
        function displayAdminConnections(connections) {
            const tbody = document.getElementById('admin-connections-table');
            tbody.innerHTML = '';

            connections.forEach(admin => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150';

                const roleBadge = admin.is_super_admin
                    ? '<span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">Super Admin</span>'
                    : '<span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">Limited Admin</span>';

                const lastLogin = admin.last_login ? new Date(admin.last_login).toLocaleString('en-US', {
                    timeZone: 'America/Sao_Paulo', // GMT-3
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                }) : 'Never';
                const lastIP = admin.last_login_ip || 'Unknown';
                const location = admin.recent_logs.length > 0 ? admin.recent_logs[0].geographic_location || 'Unknown' : 'Unknown';

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div>
                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">${admin.username}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">${roleBadge}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900 dark:text-gray-100">${lastIP}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${location}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${lastLogin}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${admin.ip_login_count}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Active</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <button onclick="viewAdminIPHistory('${admin.admin_id}', '${admin.username}')" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium mr-2">
                            View History
                        </button>
                        ${admin.last_login_ip ?
                            `<button onclick="viewIPDetails('${admin.last_login_ip}')" class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 font-medium">IP Details</button>` :
                            ''
                        }
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // View admin IP history
        async function viewAdminIPHistory(adminId, username) {
            try {
                const response = await fetch(`/api/admin/ip/access-logs?admin_id=${adminId}&page=1&per_page=20`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    showAdminIPHistoryModal(username, data.access_logs);
                } else {
                    alert('Error loading admin IP history');
                }
            } catch (error) {
                alert('Network error: ' + error.message);
            }
        }

        // Show admin IP history modal
        function showAdminIPHistoryModal(username, logs) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';

            const content = document.createElement('div');
            content.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col';

            content.innerHTML = `
                <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white">IP History for Admin: ${username}</h3>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="flex-1 overflow-y-auto p-6">
                    <div class="overflow-x-auto">
                    <table class="min-w-full text-sm">
                        <thead>
                            <tr class="border-b border-gray-200 dark:border-gray-700">
                                <th class="text-left py-2 px-4 font-medium text-gray-900 dark:text-white">IP Address</th>
                                <th class="text-left py-2 px-4 font-medium text-gray-900 dark:text-white">Location</th>
                                <th class="text-left py-2 px-4 font-medium text-gray-900 dark:text-white">Timestamp</th>
                                <th class="text-left py-2 px-4 font-medium text-gray-900 dark:text-white">Status</th>
                                <th class="text-left py-2 px-4 font-medium text-gray-900 dark:text-white">User Agent</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${logs.map(log => `
                                <tr class="border-b border-gray-100 dark:border-gray-700">
                                    <td class="py-2 px-4 font-mono text-gray-900 dark:text-gray-100">${log.ip_address}</td>
                                    <td class="py-2 px-4 text-gray-900 dark:text-gray-100">${log.geographic_location || 'Unknown'}</td>
                                    <td class="py-2 px-4 text-gray-900 dark:text-gray-100">${new Date(log.login_timestamp).toLocaleString('en-US', {
                                        timeZone: 'America/Sao_Paulo', // GMT-3
                                        year: 'numeric',
                                        month: '2-digit',
                                        day: '2-digit',
                                        hour: '2-digit',
                                        minute: '2-digit',
                                        second: '2-digit',
                                        hour12: false
                                    })}</td>
                                    <td class="py-2 px-4">
                                        <span class="px-2 py-1 text-xs rounded-full ${log.login_successful ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'}">
                                            ${log.login_successful ? 'Success' : 'Failed'}
                                        </span>
                                    </td>
                                    <td class="py-2 px-4 text-xs max-w-xs truncate text-gray-900 dark:text-gray-100" title="${log.user_agent || 'Unknown'}">${log.user_agent || 'Unknown'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                    </div>
                </div>
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);
        }

        // Utility function for debouncing
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Simple toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg text-white font-medium transition-all duration-300 transform translate-x-full ${
                type === 'success' ? 'bg-green-600' :
                type === 'error' ? 'bg-red-600' :
                'bg-blue-600'
            }`;
            toast.textContent = message;

            document.body.appendChild(toast);

            // Slide in
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);

            // Slide out and remove
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // Check for existing admin token on page load
        window.addEventListener('DOMContentLoaded', () => {
            // Clear any old tokens for debugging (temporary fix)
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('v') === '17') {
                console.log('Clearing localStorage for fresh start (v=17)');
                localStorage.removeItem('admin_token');
                localStorage.removeItem('adminToken');
            }

            const savedToken = localStorage.getItem('admin_token');
            if (savedToken) {
                adminToken = savedToken;
                // Verify token is still valid by making a test request to a simple endpoint
                fetch('/api/admin/verify-token', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                }).then(response => {
                    if (response.ok) {
                        return response.json();
                    } else {
                        throw new Error(`Token verification failed: ${response.status}`);
                    }
                }).then(data => {
                    if (data.valid) {
                        console.log('Token verified successfully for admin:', data.admin.username);
                        document.getElementById('admin-username').textContent = data.admin.username;
                        showDashboard();
                        loadDashboardData();
                    } else {
                        console.error('Token validation returned invalid');
                        throw new Error('Token validation returned invalid');
                    }
                }).catch((error) => {
                    console.error('Token verification error:', error.message);
                    console.log('Clearing invalid token and redirecting to login');
                    // Network error or invalid token - clear both possible localStorage keys
                    localStorage.removeItem('admin_token');
                    localStorage.removeItem('adminToken');
                    adminToken = null;
                    showLogin();
                });
            } else {
                showLogin();
            }
        });

        // Admin Settings Modal Navigation
        document.getElementById('admin-settings-btn').addEventListener('click', function() {
            // Open the settings modal instead of navigating to a separate page
            if (adminToken) {
                document.getElementById('admin-settings-modal').classList.remove('hidden');
                console.log('Settings modal opened');
            } else {
                alert('Please log in first');
            }
        });

        document.getElementById('close-settings-modal').addEventListener('click', function() {
            document.getElementById('admin-settings-modal').classList.add('hidden');
        });

        // Tab switching
        document.getElementById('password-tab').addEventListener('click', function() {
            switchTab('password');
        });

        document.getElementById('email-tab').addEventListener('click', function() {
            switchTab('email');
        });

        document.getElementById('admin-management-tab').addEventListener('click', function() {
            switchTab('admin-management');
            loadAdminList();
        });

        document.getElementById('create-admin-tab').addEventListener('click', function() {
            switchTab('create-admin');
        });

        function switchTab(tab) {
            // Reset tab styles
            const tabClass = 'py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300';
            const activeTabClass = 'py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 dark:text-blue-400';

            document.getElementById('password-tab').className = tabClass;
            document.getElementById('email-tab').className = tabClass;
            document.getElementById('admin-management-tab').className = tabClass;
            document.getElementById('create-admin-tab').className = tabClass;

            // Hide all content
            document.getElementById('password-content').classList.add('hidden');
            document.getElementById('email-content').classList.add('hidden');
            document.getElementById('admin-management-content').classList.add('hidden');
            document.getElementById('create-admin-content').classList.add('hidden');

            // Show selected tab
            if (tab === 'password') {
                document.getElementById('password-tab').className = activeTabClass;
                document.getElementById('password-content').classList.remove('hidden');
            } else if (tab === 'email') {
                document.getElementById('email-tab').className = activeTabClass;
                document.getElementById('email-content').classList.remove('hidden');
            } else if (tab === 'admin-management') {
                document.getElementById('admin-management-tab').className = activeTabClass;
                document.getElementById('admin-management-content').classList.remove('hidden');
            } else if (tab === 'create-admin') {
                document.getElementById('create-admin-tab').className = activeTabClass;
                document.getElementById('create-admin-content').classList.remove('hidden');
            }
        }

        // Send verification code for password change
        document.getElementById('send-verification-btn').addEventListener('click', async function() {
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            if (!newPassword || !confirmPassword) {
                alert('Please fill in both password fields');
                return;
            }

            if (newPassword !== confirmPassword) {
                alert('Passwords do not match');
                return;
            }

            if (newPassword.length < 8) {
                alert('Password must be at least 8 characters long');
                return;
            }

            try {
                const response = await fetch('/api/admin/request-password-change', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        new_password: newPassword
                    })
                });

                const data = await response.json();
                if (response.ok) {
                    alert('Verification code sent to your email');
                    document.getElementById('verification-section').classList.remove('hidden');
                    document.getElementById('send-verification-btn').classList.add('hidden');
                } else {
                    alert(data.message || 'Failed to send verification code');
                }
            } catch (error) {
                console.error('Error sending verification code:', error);
                alert('Network error. Please try again.');
            }
        });

        // Confirm password change with verification code
        document.getElementById('confirm-password-change-btn').addEventListener('click', async function() {
            const verificationCode = document.getElementById('verification-code').value;
            const newPassword = document.getElementById('new-password').value;

            if (!verificationCode) {
                alert('Please enter the verification code');
                return;
            }

            try {
                const response = await fetch('/api/admin/verify-password-change', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        verification_code: verificationCode
                    })
                });

                const data = await response.json();
                if (response.ok) {
                    alert('Password changed successfully!');
                    // Reset form
                    document.getElementById('new-password').value = '';
                    document.getElementById('confirm-password').value = '';
                    document.getElementById('verification-code').value = '';
                    document.getElementById('verification-section').classList.add('hidden');
                    document.getElementById('send-verification-btn').classList.remove('hidden');
                } else {
                    alert(data.message || 'Failed to change password');
                }
            } catch (error) {
                console.error('Error confirming password change:', error);
                alert('Network error. Please try again.');
            }
        });

        // Send verification codes for email change
        document.getElementById('send-email-verification-btn').addEventListener('click', async function() {
            const newEmail = document.getElementById('new-email').value;
            const confirmEmail = document.getElementById('confirm-email').value;

            if (!newEmail || !confirmEmail) {
                alert('Please fill in both email fields');
                return;
            }

            if (newEmail !== confirmEmail) {
                alert('Email addresses do not match');
                return;
            }

            // Basic email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(newEmail)) {
                alert('Please enter a valid email address');
                return;
            }

            try {
                const response = await fetch('/api/admin/request-email-change', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        new_email: newEmail
                    })
                });

                const data = await response.json();
                if (response.ok) {
                    alert('Verification codes sent to both email addresses');
                    document.getElementById('email-verification-section').classList.remove('hidden');
                    document.getElementById('send-email-verification-btn').classList.add('hidden');
                } else {
                    alert(data.message || 'Failed to send verification codes');
                }
            } catch (error) {
                console.error('Error sending email verification codes:', error);
                alert('Network error. Please try again.');
            }
        });

        // Confirm email change with verification codes
        document.getElementById('confirm-email-change-btn').addEventListener('click', async function() {
            const currentEmailCode = document.getElementById('current-email-code').value;
            const newEmailCode = document.getElementById('new-email-code').value;
            const newEmail = document.getElementById('new-email').value;

            if (!currentEmailCode || !newEmailCode) {
                alert('Please enter both verification codes');
                return;
            }

            try {
                const response = await fetch('/api/admin/verify-email-change', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        old_email_code: currentEmailCode,
                        new_email_code: newEmailCode
                    })
                });

                const data = await response.json();
                if (response.ok) {
                    alert('Email changed successfully!');
                    // Reset form
                    document.getElementById('new-email').value = '';
                    document.getElementById('confirm-email').value = '';
                    document.getElementById('current-email-code').value = '';
                    document.getElementById('new-email-code').value = '';
                    document.getElementById('email-verification-section').classList.add('hidden');
                    document.getElementById('send-email-verification-btn').classList.remove('hidden');

                    // Update displayed username
                    document.getElementById('admin-username').textContent = newEmail;
                } else {
                    alert(data.message || 'Failed to change email');
                }
            } catch (error) {
                console.error('Error confirming email change:', error);
                alert('Network error. Please try again.');
            }
        });

        // Create admin functionality
        document.getElementById('create-admin-btn').addEventListener('click', async function() {
            const email = document.getElementById('admin-email').value;
            const password = document.getElementById('admin-password').value;
            const level = document.getElementById('admin-level').value;

            if (!email || !password) {
                alert('Please fill in all fields');
                return;
            }

            if (password.length < 8) {
                alert('Password must be at least 8 characters long');
                return;
            }

            try {
                const response = await fetch('/api/admin/create-admin', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        admin_level: level
                    })
                });

                if (response.ok) {
                    alert('Admin created successfully');
                    document.getElementById('admin-email').value = '';
                    document.getElementById('admin-password').value = '';
                    document.getElementById('admin-level').value = 'super_admin';
                } else {
                    const error = await response.json();
                    alert(error.message || 'Failed to create admin');
                }
            } catch (error) {
                console.error('Error creating admin:', error);
                alert('Error creating admin');
            }
        });

        // Admin Management Functions
        async function loadAdminList() {
            try {
                console.log('Loading admin list...');

                // First verify current admin privileges
                const verifyResponse = await fetch('/api/admin/verify-token', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (verifyResponse.ok) {
                    const verifyData = await verifyResponse.json();
                    console.log('Current admin info:', verifyData.admin);

                    if (!verifyData.admin.is_super_admin) {
                        console.log('Current admin is not super admin, cannot access admin list');
                        document.getElementById('admin-list').innerHTML = '<p class="text-gray-500 text-center py-4">Super admin privileges required to view admin list</p>';
                        return;
                    }
                }

                const response = await fetch('/api/admin/admins', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                console.log('Admin list response status:', response.status);

                if (response.ok) {
                    const data = await response.json();
                    console.log('Admin list data:', data);
                    displayAdminList(data.admins);

                    // Calculate basic statistics from the admin list
                    const totalAdmins = data.admins.length;
                    const superAdmins = data.admins.filter(admin => admin.is_super_admin).length;
                    updateAdminStats({
                        total_admins: totalAdmins,
                        super_admins: superAdmins
                    });
                } else {
                    const errorData = await response.json();
                    console.error('Failed to load admin list:', errorData);
                    alert('Failed to load admin list: ' + (errorData.message || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error loading admin list:', error);
                alert('Failed to load admin list');
            }
        }

        function displayAdminList(admins) {
            const adminList = document.getElementById('admin-list');
            adminList.innerHTML = '';

            admins.forEach(admin => {
                const adminItem = document.createElement('div');
                adminItem.className = 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-3';

                const statusBadge = admin.is_active
                    ? '<span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Active</span>'
                    : '<span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">Inactive</span>';

                const roleBadge = admin.is_super_admin
                    ? '<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Super Admin</span>'
                    : '<span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">Limited Admin</span>';

                adminItem.innerHTML = `
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="font-medium text-gray-900 dark:text-white">${admin.username}</span>
                                ${roleBadge}
                                ${statusBadge}
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                <div>Created: ${admin.created_at ? new Date(admin.created_at).toLocaleDateString() : 'N/A'}</div>
                                <div>Last Login: ${admin.last_login_timestamp ? new Date(admin.last_login_timestamp).toLocaleDateString() : 'Never'}</div>
                                <div>Total Logins: ${admin.total_logins || 0}</div>
                            </div>
                        </div>
                        <div class="flex space-x-1">
                            <button onclick="resetAdminPassword(${admin.id}, '${admin.username}')"
                                    class="bg-yellow-500 hover:bg-yellow-600 text-white px-2 py-1 rounded text-xs">
                                Reset Password
                            </button>
                            ${admin.id !== getCurrentAdminId() ? `
                                <button onclick="toggleAdminStatus(${admin.id}, ${admin.is_active})"
                                        class="${admin.is_active ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'} text-white px-2 py-1 rounded text-xs">
                                    ${admin.is_active ? 'Disable' : 'Enable'}
                                </button>
                                ${admin.id !== 1 ? `
                                    <button onclick="deleteAdmin(${admin.id}, '${admin.username}')"
                                            class="bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs">
                                        Delete
                                    </button>
                                ` : `
                                    <span class="bg-gray-400 text-white px-2 py-1 rounded text-xs cursor-not-allowed"
                                          title="Primary admin cannot be deleted">
                                        Protected
                                    </span>
                                `}
                            ` : ''}
                        </div>
                    </div>
                `;

                adminList.appendChild(adminItem);
            });
        }

        function updateAdminStats(stats) {
            document.getElementById('total-admins').textContent = stats.total_admins;
            document.getElementById('super-admins').textContent = stats.super_admins;
        }

        function getCurrentAdminId() {
            try {
                const tokenPayload = JSON.parse(atob(adminToken.split('.')[1]));
                return tokenPayload.admin_id;
            } catch (error) {
                return null;
            }
        }

        async function resetAdminPassword(adminId, username) {
            const newPassword = prompt(`Enter new password for ${username}:`);
            if (!newPassword) return;

            if (newPassword.length < 8) {
                alert('Password must be at least 8 characters long');
                return;
            }

            try {
                const response = await fetch(`/api/admin/admins/${adminId}/reset-password`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ new_password: newPassword })
                });

                if (response.ok) {
                    alert(`Password reset successfully for ${username}`);
                } else {
                    const error = await response.json();
                    alert(error.message || 'Failed to reset password');
                }
            } catch (error) {
                console.error('Error resetting password:', error);
                alert('Error resetting password');
            }
        }

        async function toggleAdminStatus(adminId, currentStatus) {
            const action = currentStatus ? 'disable' : 'enable';
            if (!confirm(`Are you sure you want to ${action} this admin?`)) return;

            try {
                const response = await fetch(`/api/admin/admins/${adminId}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ is_active: !currentStatus })
                });

                if (response.ok) {
                    alert(`Admin ${action}d successfully`);
                    loadAdminList(); // Refresh the list
                } else {
                    const error = await response.json();
                    alert(error.message || `Failed to ${action} admin`);
                }
            } catch (error) {
                console.error(`Error ${action}ing admin:`, error);
                alert(`Error ${action}ing admin`);
            }
        }

        async function deleteAdmin(adminId, username) {
            // Double confirmation for delete action
            if (!confirm(`⚠️ WARNING: Are you sure you want to DELETE admin account "${username}"?\n\nThis action cannot be undone!`)) {
                return;
            }

            if (!confirm(`🚨 FINAL CONFIRMATION: Delete admin "${username}"?\n\nType "DELETE" in the next prompt to confirm.`)) {
                return;
            }

            const confirmText = prompt(`To confirm deletion of admin "${username}", type "DELETE" (all caps):`);
            if (confirmText !== 'DELETE') {
                alert('Deletion cancelled. Confirmation text did not match.');
                return;
            }

            try {
                const response = await fetch(`/api/admin/admins/${adminId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    alert(`✅ Admin "${username}" deleted successfully`);
                    loadAdminList(); // Refresh the list
                } else {
                    const error = await response.json();
                    alert(`❌ Failed to delete admin: ${error.message || 'Unknown error'}`);
                }
            } catch (error) {
                console.error('Error deleting admin:', error);
                alert('❌ Network error while deleting admin');
            }
        }

        // Refresh admins button
        document.getElementById('refresh-admins-btn').addEventListener('click', loadAdminList);

        // Prune unverified users functionality
        function showPruneUsersModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
                    <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white">Prune Unverified Users</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="p-6">
                        <div class="mb-4">
                            <p class="text-gray-700 dark:text-gray-300 mb-4">
                                This will permanently delete all unverified user accounts that have been inactive for the selected period.
                            </p>
                            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mb-4">
                                <div class="flex">
                                    <svg class="w-5 h-5 text-yellow-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    <p class="text-sm text-yellow-800 dark:text-yellow-200">
                                        <strong>Warning:</strong> This action cannot be undone. Deleted accounts cannot be recovered.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Delete accounts inactive for:
                            </label>
                            <select id="prune-inactivity-period" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="1">1 day (Test)</option>
                                <option value="30" selected>30 days</option>
                                <option value="90">90 days</option>
                                <option value="365">1 year</option>
                            </select>
                        </div>
                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" id="prune-confirm-checkbox" class="rounded border-gray-300 dark:border-gray-600 text-red-600 focus:ring-red-500 dark:focus:ring-red-400">
                                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                    I understand this action is permanent and cannot be undone
                                </span>
                            </label>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-lg font-medium transition-colors duration-200">
                                Cancel
                            </button>
                            <button onclick="executePruneUsers()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors duration-200">
                                Prune Users
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        async function executePruneUsers() {
            const inactivityPeriod = document.getElementById('prune-inactivity-period').value;
            const confirmCheckbox = document.getElementById('prune-confirm-checkbox');

            if (!confirmCheckbox.checked) {
                alert('Please confirm that you understand this action is permanent.');
                return;
            }

            try {
                const response = await fetch('/api/admin/users/prune', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        inactivity_days: parseInt(inactivityPeriod)
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    alert(`✅ Successfully pruned ${data.deleted_count} unverified users that were inactive for ${inactivityPeriod} days.`);

                    // Close modal and refresh users list
                    document.querySelector('.fixed').remove();
                    loadUsers(1, currentUsersPerPage);
                } else if (response.status === 401) {
                    alert('❌ Unauthorized. Please login again.');
                    showLogin();
                } else {
                    const errorData = await response.json();
                    alert(`❌ Error: ${errorData.message || 'Failed to prune users'}`);
                }
            } catch (error) {
                console.error('Error pruning users:', error);
                alert('❌ Network error while pruning users');
            }
        }

        // Set up prune button event listener
        document.getElementById('prune-users-btn').addEventListener('click', showPruneUsersModal);

        // Set up refresh users button event listener
        document.getElementById('refresh-users-btn').addEventListener('click', async function() {
            const refreshBtn = this;
            const originalText = refreshBtn.innerHTML;

            // Show loading state
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = `
                <svg class="w-4 h-4 inline-block mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refreshing...
            `;

            try {
                console.log('Refreshing users list...');
                await loadUsers(currentUsersPage, currentUsersPerPage);

                // Show success feedback briefly
                refreshBtn.innerHTML = `
                    <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Refreshed
                `;

                // Reset to original state after 1 second
                setTimeout(() => {
                    refreshBtn.innerHTML = originalText;
                    refreshBtn.disabled = false;
                }, 1000);

            } catch (error) {
                console.error('Error refreshing users:', error);

                // Show error state
                refreshBtn.innerHTML = `
                    <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Error
                `;

                // Reset to original state after 2 seconds
                setTimeout(() => {
                    refreshBtn.innerHTML = originalText;
                    refreshBtn.disabled = false;
                }, 2000);
            }
        });

        // Clear IP logs functionality
        function showClearIPLogsModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
                    <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white">Clear IP Access Logs</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="p-6">
                        <div class="mb-4">
                            <p class="text-gray-700 dark:text-gray-300 mb-4">
                                This will permanently delete IP access logs older than the selected period to improve database performance.
                            </p>
                            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mb-4">
                                <div class="flex">
                                    <svg class="w-5 h-5 text-yellow-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    <p class="text-sm text-yellow-800 dark:text-yellow-200">
                                        <strong>Warning:</strong> This action cannot be undone. Deleted logs cannot be recovered.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Clear logs older than:
                            </label>
                            <select id="clear-logs-period" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="7">7 days</option>
                                <option value="30" selected>30 days</option>
                                <option value="60">60 days</option>
                                <option value="90">90 days</option>
                                <option value="180">6 months</option>
                                <option value="365">1 year</option>
                            </select>
                        </div>
                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" id="clear-logs-confirm-checkbox" class="rounded border-gray-300 dark:border-gray-600 text-orange-600 focus:ring-orange-500 dark:focus:ring-orange-400">
                                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                    I understand this action is permanent and cannot be undone
                                </span>
                            </label>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-lg font-medium transition-colors duration-200">
                                Cancel
                            </button>
                            <button onclick="executeClearIPLogs()" class="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg font-medium transition-colors duration-200">
                                Clear Logs
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        async function executeClearIPLogs() {
            const clearPeriod = document.getElementById('clear-logs-period').value;
            const confirmCheckbox = document.getElementById('clear-logs-confirm-checkbox');

            if (!confirmCheckbox.checked) {
                alert('Please confirm that you understand this action is permanent.');
                return;
            }

            // Validate days_to_keep value
            const daysToKeep = parseInt(clearPeriod);
            if (isNaN(daysToKeep) || daysToKeep < 1) {
                alert('❌ Invalid period selected. Must be a positive number of days.');
                return;
            }

            try {
                const response = await fetch('/api/admin/ip/cleanup', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        days_to_keep: daysToKeep
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const periodText = clearPeriod === '0' ? 'all logs' : `logs older than ${clearPeriod} days`;
                    alert(`✅ Successfully cleared ${data.deleted_count} IP access logs (${periodText}).`);

                    // Close modal and refresh IP data
                    document.querySelector('.fixed').remove();

                    // Refresh IP stats and access logs
                    await loadIPStats();
                    await loadIPAccessLogs(1, currentIPLogsPerPage);
                } else if (response.status === 401) {
                    alert('❌ Unauthorized. Please login again.');
                    showLogin();
                } else if (response.status === 400) {
                    const errorData = await response.json();
                    alert(`❌ Invalid request: ${errorData.error || 'Bad request'}`);
                } else if (response.status === 403) {
                    alert('❌ Access denied. Super admin privileges required.');
                } else {
                    const errorData = await response.json();
                    alert(`❌ Error: ${errorData.error || errorData.message || 'Failed to clear logs'}`);
                }
            } catch (error) {
                console.error('Error clearing IP logs:', error);
                alert('❌ Network error while clearing logs');
            }
        }

        // Set up clear logs button event listener
        document.getElementById('clear-ip-logs-btn').addEventListener('click', showClearIPLogsModal);

        // Set up refresh IP management button event listener
        document.getElementById('refresh-ip-management-btn').addEventListener('click', async function() {
            const refreshBtn = this;
            const originalText = refreshBtn.innerHTML;

            // Show loading state
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = `
                <svg class="w-4 h-4 inline-block mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refreshing...
            `;

            try {
                console.log('Refreshing IP management data...');

                // Refresh IP stats
                await loadIPStats();

                // Refresh the currently active tab
                const activeTab = document.querySelector('.border-blue-500');
                if (activeTab) {
                    const tabId = activeTab.id;
                    if (tabId === 'ip-access-logs-tab') {
                        await loadIPAccessLogs();
                    } else if (tabId === 'ip-blacklist-tab') {
                        await loadIPBlacklist();
                    } else if (tabId === 'ip-rate-limits-tab') {
                        await loadIPRateLimits();
                    } else if (tabId === 'admin-connections-tab') {
                        await loadAdminConnections();
                    }
                }

                // Show success feedback briefly
                refreshBtn.innerHTML = `
                    <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Refreshed
                `;

                // Reset to original state after 1 second
                setTimeout(() => {
                    refreshBtn.innerHTML = originalText;
                    refreshBtn.disabled = false;
                }, 1000);

            } catch (error) {
                console.error('Error refreshing IP management:', error);

                // Show error state
                refreshBtn.innerHTML = `
                    <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Error
                `;

                // Reset to original state after 2 seconds
                setTimeout(() => {
                    refreshBtn.innerHTML = originalText;
                    refreshBtn.disabled = false;
                }, 2000);
            }
        });

        // 2FA Reset Requests Management
        async function load2FAResetRequests() {
            try {
                const response = await fetch('/api/admin/2fa-reset-requests', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to load 2FA reset requests');
                }

                const data = await response.json();
                display2FAResetRequests(data.requests);
                update2FAResetStats(data.requests);

            } catch (error) {
                console.error('Error loading 2FA reset requests:', error);
                document.getElementById('2fa-requests-table').innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-red-600 dark:text-red-400">
                            Error loading 2FA reset requests: ${error.message}
                        </td>
                    </tr>
                `;
            }
        }

        function display2FAResetRequests(requests) {
            const tbody = document.getElementById('2fa-requests-table');

            if (!requests || requests.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                            No 2FA reset requests found
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = requests.map(request => {
                const riskColor = {
                    'low': 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20',
                    'medium': 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20',
                    'high': 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20'
                }[request.risk_level] || 'text-gray-600 bg-gray-100';

                const statusColor = {
                    'pending': 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20',
                    'approved': 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20',
                    'rejected': 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20',
                    'completed': 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900/20'
                }[request.status] || 'text-gray-600 bg-gray-100';

                return `
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">${request.user?.email || request.email_provided}</div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">${request.full_name_provided}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-medium rounded-full ${riskColor}">
                                ${request.risk_level.toUpperCase()}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-medium rounded-full ${statusColor}">
                                ${request.status.toUpperCase()}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            ${new Date(request.created_at).toLocaleDateString()}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">
                            ${request.reason}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="view2FAResetRequest('${request.id}')" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">
                                View
                            </button>
                            ${request.status === 'pending' ? `
                                <button onclick="approve2FAResetRequest('${request.id}')" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3">
                                    Approve
                                </button>
                                <button onclick="reject2FAResetRequest('${request.id}')" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                    Reject
                                </button>
                            ` : ''}
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function update2FAResetStats(requests) {
            const stats = {
                pending: requests.filter(r => r.status === 'pending').length,
                approved: requests.filter(r => r.status === 'approved' || r.status === 'completed').length,
                rejected: requests.filter(r => r.status === 'rejected').length,
                highRisk: requests.filter(r => r.risk_level === 'high').length
            };

            document.getElementById('2fa-stats-pending').textContent = stats.pending;
            document.getElementById('2fa-stats-approved').textContent = stats.approved;
            document.getElementById('2fa-stats-rejected').textContent = stats.rejected;
            document.getElementById('2fa-stats-high-risk').textContent = stats.highRisk;
        }

        async function view2FAResetRequest(requestId) {
            try {
                const response = await fetch(`/api/admin/2fa-reset-requests/${requestId}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to load request details');
                }

                const request = await response.json();
                show2FAResetRequestModal(request);

            } catch (error) {
                console.error('Error loading request details:', error);
                alert('Failed to load request details');
            }
        }

        function show2FAResetRequestModal(request) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">2FA Reset Request Details</h3>
                            <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-white mb-3">Request Information</h4>
                                <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                    <p><strong class="text-gray-900 dark:text-white">Request ID:</strong> ${request.id}</p>
                                    <p><strong class="text-gray-900 dark:text-white">Status:</strong> <span class="px-2 py-1 rounded text-xs ${request.status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' : request.status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'}">${request.status.toUpperCase()}</span></p>
                                    <p><strong class="text-gray-900 dark:text-white">Risk Level:</strong> <span class="px-2 py-1 rounded text-xs ${request.risk_level === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' : request.risk_level === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' : 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'}">${request.risk_level.toUpperCase()}</span></p>
                                    <p><strong class="text-gray-900 dark:text-white">Submitted:</strong> ${new Date(request.created_at).toLocaleString()}</p>
                                    <p><strong class="text-gray-900 dark:text-white">IP Address:</strong> ${request.ip_address || 'Unknown'}</p>
                                </div>
                            </div>

                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-white mb-3">User Verification</h4>
                                <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                    <p><strong class="text-gray-900 dark:text-white">Provided Name:</strong> ${request.full_name_provided}</p>
                                    <p><strong class="text-gray-900 dark:text-white">Actual Name:</strong> ${request.verification_data?.user_actual_info?.full_name || 'N/A'}</p>
                                    <p><strong class="text-gray-900 dark:text-white">Provided Email:</strong> ${request.email_provided}</p>
                                    <p><strong class="text-gray-900 dark:text-white">Actual Email:</strong> ${request.verification_data?.user_actual_info?.email || 'N/A'}</p>
                                    <p><strong class="text-gray-900 dark:text-white">Account Created:</strong> ${request.verification_data?.user_actual_info?.created_at ? new Date(request.verification_data.user_actual_info.created_at).toLocaleDateString() : 'N/A'}</p>
                                    <p><strong class="text-gray-900 dark:text-white">Last Login:</strong> ${request.verification_data?.user_actual_info?.last_login ? new Date(request.verification_data.user_actual_info.last_login).toLocaleDateString() : 'N/A'}</p>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 dark:text-white mb-3">Reason for Reset</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded">${request.reason}</p>
                        </div>

                        ${request.security_answer_1 || request.security_answer_2 || request.security_answer_3 ? `
                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 dark:text-white mb-3">Security Questions</h4>
                            <div class="space-y-3 text-sm text-gray-700 dark:text-gray-300">
                                ${request.security_answer_1 ? `<div><strong class="text-gray-900 dark:text-white">Q:</strong> ${request.security_question_1}<br><strong class="text-gray-900 dark:text-white">A:</strong> ${request.security_answer_1}</div>` : ''}
                                ${request.security_answer_2 ? `<div><strong class="text-gray-900 dark:text-white">Q:</strong> ${request.security_question_2}<br><strong class="text-gray-900 dark:text-white">A:</strong> ${request.security_answer_2}</div>` : ''}
                                ${request.security_answer_3 ? `<div><strong class="text-gray-900 dark:text-white">Q:</strong> ${request.security_question_3}<br><strong class="text-gray-900 dark:text-white">A:</strong> ${request.security_answer_3}</div>` : ''}
                            </div>
                        </div>
                        ` : ''}

                        ${request.status === 'pending' ? `
                        <div class="mt-6 flex space-x-3">
                            <button onclick="approve2FAResetRequest('${request.id}'); this.closest('.fixed').remove();" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                                Approve Request
                            </button>
                            <button onclick="reject2FAResetRequest('${request.id}'); this.closest('.fixed').remove();" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg">
                                Reject Request
                            </button>
                        </div>
                        ` : ''}

                        ${request.admin_notes ? `
                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 dark:text-white mb-3">Admin Notes</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded">${request.admin_notes}</p>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        async function approve2FAResetRequest(requestId) {
            const notes = prompt('Enter admin notes for approval (optional):');
            if (notes === null) return; // User cancelled

            try {
                const response = await fetch(`/api/admin/2fa-reset-requests/${requestId}/approve`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ admin_notes: notes })
                });

                if (!response.ok) {
                    throw new Error('Failed to approve request');
                }

                alert('✅ 2FA reset request approved successfully');
                load2FAResetRequests(); // Refresh the list

            } catch (error) {
                console.error('Error approving request:', error);
                alert('❌ Failed to approve request');
            }
        }

        async function reject2FAResetRequest(requestId) {
            const notes = prompt('Enter reason for rejection (required):');
            if (!notes || notes.trim() === '') {
                alert('Rejection reason is required');
                return;
            }

            try {
                const response = await fetch(`/api/admin/2fa-reset-requests/${requestId}/reject`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ admin_notes: notes })
                });

                if (!response.ok) {
                    throw new Error('Failed to reject request');
                }

                alert('✅ 2FA reset request rejected successfully');
                load2FAResetRequests(); // Refresh the list

            } catch (error) {
                console.error('Error rejecting request:', error);
                alert('❌ Failed to reject request');
            }
        }

        // Set up 2FA reset requests event listeners
        document.getElementById('refresh-2fa-requests-btn').addEventListener('click', load2FAResetRequests);

        // Load 2FA reset requests on page load
        load2FAResetRequests();

    </script>
</body>
</html>
"""








@main_bp.route('/')
def index():
    """Admin panel route - serves admin login and dashboard."""
    return render_template_string(admin_panel_html)

# Admin settings route removed - now using modal in main admin panel

@main_bp.route('/oauth-callback')
def oauth_callback_handler():
    """Handle OAuth callback when Google redirects to the base domain."""
    from flask import request
    
    try:
        # Get authorization code from query parameters
        code = request.args.get('code')
        state = request.args.get('state')
        error = request.args.get('error')
        
        if error:
            # Handle OAuth error
            return f"""<!DOCTYPE html>
<html>
<head>
<title>Authentication Error</title>
<script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen">
<div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
<h1 class="text-2xl font-bold text-red-600 mb-4">Authentication Error</h1>
<p class="text-gray-600 mb-4">Google authentication failed: {error}</p>
<a href="/" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">Return to Home</a>
</div>
</body>
</html>"""
        
        if not code:
            return '''<!DOCTYPE html>
<html>
<head>
    <title>Authentication Error</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen">
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
        <h1 class="text-2xl font-bold text-red-600 mb-4">Authentication Error</h1>
        <p class="text-gray-600 mb-4">No authorization code received from Google.</p>
        <a href="/" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
            Return to Home
        </a>
    </div>
</body>
</html>'''
        
        return f"""<!DOCTYPE html>
<html>
<head>
    <title>Completing Authentication...</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen">
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
        <h1 class="text-2xl font-bold text-blue-600 mb-4">Completing Authentication...</h1>
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p class="text-gray-600 mt-4">Please wait while we complete your authentication.</p>
    </div>

    <script>

        async function completeOAuth() {{
            try {{
                const response = await fetch('/api/auth/callback', {{
                    method: 'POST',
                    headers: {{
                        'Content-Type': 'application/json'
                    }},
                    body: JSON.stringify({{
                        code: '{code}',
                        state: '{state}',
                        redirect_uri: window.location.origin + '/'
                    }})
                }});

                const data = await response.json();

                if (response.ok) {{
                    if (data.requires_2fa) {{

                        localStorage.setItem('tempToken', data.temp_token);
                        localStorage.setItem('userInfo', JSON.stringify(data.user));
                        alert('2FA verification required. This feature will be implemented in the next update.');

                        window.location.href = '/';
                    }} else {{

                        localStorage.setItem('authToken', data.access_token);
                        localStorage.setItem('refreshToken', data.refresh_token);
                        authToken = data.access_token;
                        localStorage.setItem('userInfo', JSON.stringify(data.user));
                        window.location.href = '/';
                    }}
                }} else {{
                    throw new Error(data.message || 'Authentication failed');
                }}
            }} catch (error) {{
                alert('Authentication failed: ' + error.message);
                window.location.href = '/';
            }}
        }}


        completeOAuth();
    </script>
</body>
</html>"""
        
    except Exception as e:
        current_app.logger.error(f"OAuth callback error: {str(e)}")
        return '''<!DOCTYPE html>
<html>
<head>
<title>Authentication Error</title>
<script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen">
<div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
<h1 class="text-2xl font-bold text-red-600 mb-4">Authentication Error</h1>
<p class="text-gray-600 mb-4">An error occurred during authentication. Please try again.</p>
<a href="/" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">Return to Home</a>
</div>
</body>
</html>'''




@main_bp.route('/health')
def health_check():
    """Health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'version': '1.0.0',
        'timestamp': current_app.config.get('STARTUP_TIME', 'unknown')
    })

@main_bp.route('/api/forecast/stream')
def forecast_stream():
    def event_stream():
        last_candle_time = None
        
        while True:
            try:
                # Check if we have a new forecast
                if hasattr(ml_service, 'last_candle_time') and ml_service.last_candle_time != last_candle_time:
                    last_candle_time = ml_service.last_candle_time
                    event_data = {
                        'event': 'new_forecast',
                        'timestamp': last_candle_time,
                        'time': datetime.utcnow().isoformat()
                    }
                    yield f"data: {json.dumps(event_data)}\n\n"
                
                # Sleep to prevent high CPU usage
                time.sleep(5)
                
            except Exception as e:
                current_app.logger.error(f"Error in forecast stream: {str(e)}")
                time.sleep(5)  # Wait before retrying on error
    
    return Response(
        event_stream(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # Disable buffering for nginx
        }
    )



@main_bp.route('/api/status')
@jwt_required(optional=True)
def api_status():
    """API status endpoint with optional authentication."""
    user_id = get_jwt_identity()
    
    status = {
        'api_version': '1.0.0',
        'authenticated': user_id is not None,
        'features': {
            'google_oauth': True,
            'two_factor_auth': True,
            'multi_tier_subscriptions': True,
            'encrypted_api_storage': True,
            'automated_fee_calculation': True,
            'ml_predictions': True
        }
    }
    
    if user_id:
        user = User.query.get(user_id)
        if user:
            subscription = user.get_current_subscription()
            status['user'] = {
                'id': user.id,
                'email': user.email,
                'tier': user.get_tier(),
                'two_fa_enabled': user.two_fa_enabled,
                'subscription_active': subscription.is_active() if subscription else False
            }
    
    return jsonify(status)