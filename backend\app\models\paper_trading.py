import uuid
from datetime import datetime
from decimal import Decimal
from enum import Enum
from flask_sqlalchemy import SQLAlchemy
from app import db


class PaperTradingStatus(Enum):
    """Status enum for paper trading sessions"""
    ACTIVE = "active"
    PAUSED = "paused"
    ENDED = "ended"


class PaperTradeStatus(Enum):
    """Status enum for individual paper trades"""
    OPEN = "open"
    CLOSED = "closed"
    CANCELLED = "cancelled"


class PaperTradeSide(Enum):
    """Side enum for paper trades"""
    BUY = "buy"
    SELL = "sell"
    LONG = "long"
    SHORT = "short"


class PaperTradingAccount(db.Model):
    """Model to track user's paper trading account and performance."""
    __tablename__ = 'paper_trading_accounts'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.Foreign<PERSON>ey('users.id'), nullable=False, unique=True)
    
    # Balance tracking
    virtual_balance = db.Column(db.Numeric(20, 8), nullable=False, default=10000.********)
    initial_balance = db.Column(db.Numeric(20, 8), nullable=False, default=10000.********)
    
    # Performance metrics
    total_pnl = db.Column(db.Numeric(20, 8), default=0.********)
    total_trades_count = db.Column(db.Integer, default=0)
    winning_trades_count = db.Column(db.Integer, default=0)
    losing_trades_count = db.Column(db.Integer, default=0)
    win_rate = db.Column(db.Numeric(5, 2), default=0.00)  # Percentage
    
    # Reset tracking
    reset_count = db.Column(db.Integer, default=0)
    last_reset_at = db.Column(db.DateTime, nullable=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    user = db.relationship('User', backref='paper_trading_account')
    paper_trades = db.relationship('PaperTrade', backref='paper_account', lazy='dynamic', cascade='all, delete-orphan')
    paper_sessions = db.relationship('PaperTradingSession', backref='paper_account', lazy='dynamic', cascade='all, delete-orphan')
    balance_snapshots = db.relationship('PaperBalanceSnapshot', backref='paper_account', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, user_id, initial_balance=None):
        self.user_id = user_id
        if initial_balance:
            self.initial_balance = Decimal(str(initial_balance))
            self.virtual_balance = Decimal(str(initial_balance))
    
    def update_balance(self, new_balance, pnl_change=None, transaction_type='trade'):
        """Update virtual balance and create snapshot."""
        old_balance = self.virtual_balance
        self.virtual_balance = Decimal(str(new_balance))
        self.updated_at = datetime.utcnow()
        
        # Create balance snapshot
        snapshot = PaperBalanceSnapshot(
            paper_account_id=self.id,
            balance=self.virtual_balance,
            pnl_change=Decimal(str(pnl_change)) if pnl_change else Decimal('0'),
            transaction_type=transaction_type
        )
        db.session.add(snapshot)
    
    def calculate_performance_metrics(self):
        """Recalculate performance metrics based on trades."""
        trades = self.paper_trades.filter(PaperTrade.status == PaperTradeStatus.CLOSED).all()
        
        self.total_trades_count = len(trades)
        self.winning_trades_count = len([t for t in trades if t.pnl and t.pnl > 0])
        self.losing_trades_count = len([t for t in trades if t.pnl and t.pnl <= 0])
        
        if self.total_trades_count > 0:
            self.win_rate = Decimal(str(self.winning_trades_count / self.total_trades_count * 100))
        else:
            self.win_rate = Decimal('0')
        
        # Calculate total PnL
        self.total_pnl = sum([t.pnl for t in trades if t.pnl], Decimal('0'))
        
        self.updated_at = datetime.utcnow()
    
    def reset_account(self, new_initial_balance=None):
        """Reset paper trading account to initial state."""
        if new_initial_balance:
            self.initial_balance = Decimal(str(new_initial_balance))
            self.virtual_balance = Decimal(str(new_initial_balance))
        else:
            self.virtual_balance = self.initial_balance
        
        # Reset metrics
        self.total_pnl = Decimal('0')
        self.total_trades_count = 0
        self.winning_trades_count = 0
        self.losing_trades_count = 0
        self.win_rate = Decimal('0')
        
        # Update reset tracking
        self.reset_count += 1
        self.last_reset_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        
        # Archive old trades and sessions (soft delete)
        for trade in self.paper_trades:
            trade.archived = True
        for session in self.paper_sessions:
            session.status = PaperTradingStatus.ENDED
    
    def can_reset_today(self, max_resets_per_day=3):
        """Check if user can reset account today."""
        if not self.last_reset_at:
            return True
        
        today = datetime.utcnow().date()
        last_reset_date = self.last_reset_at.date()
        
        if last_reset_date < today:
            return True
        
        # Count resets today
        resets_today = PaperBalanceSnapshot.query.filter(
            PaperBalanceSnapshot.paper_account_id == self.id,
            PaperBalanceSnapshot.transaction_type == 'reset',
            db.func.date(PaperBalanceSnapshot.created_at) == today
        ).count()
        
        return resets_today < max_resets_per_day
    
    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'virtual_balance': float(self.virtual_balance),
            'initial_balance': float(self.initial_balance),
            'total_pnl': float(self.total_pnl),
            'total_trades_count': self.total_trades_count,
            'winning_trades_count': self.winning_trades_count,
            'losing_trades_count': self.losing_trades_count,
            'win_rate': float(self.win_rate),
            'reset_count': self.reset_count,
            'last_reset_at': self.last_reset_at.isoformat() if self.last_reset_at else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<PaperTradingAccount {self.id} - User: {self.user_id} - Balance: {self.virtual_balance}>'


class PaperTrade(db.Model):
    """Model to track individual paper trades."""
    __tablename__ = 'paper_trades'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    paper_account_id = db.Column(db.String(36), db.ForeignKey('paper_trading_accounts.id'), nullable=False)
    session_id = db.Column(db.String(36), db.ForeignKey('paper_trading_sessions.id'), nullable=True)
    
    # Trade details
    symbol = db.Column(db.String(20), nullable=False)
    side = db.Column(db.Enum(PaperTradeSide), nullable=False)
    quantity = db.Column(db.Numeric(20, 8), nullable=False)
    source = db.Column(db.String(20), nullable=False, default='app')  # 'app' or 'manual'
    
    # Price information
    entry_price = db.Column(db.Numeric(20, 8), nullable=False)
    exit_price = db.Column(db.Numeric(20, 8))
    stop_loss = db.Column(db.Numeric(20, 8))
    take_profit = db.Column(db.Numeric(20, 8))
    
    # P&L and fees (simulated)
    pnl = db.Column(db.Numeric(20, 8))  # Profit and Loss
    simulated_fee = db.Column(db.Numeric(20, 8), default=0)  # Simulated exchange fee
    
    # Status and metadata
    status = db.Column(db.Enum(PaperTradeStatus), nullable=False, default=PaperTradeStatus.OPEN)
    exit_reason = db.Column(db.String(100))  # 'take_profit', 'stop_loss', 'manual', etc.
    archived = db.Column(db.Boolean, default=False)  # For soft delete on reset
    
    # Timestamps
    entry_time = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    exit_time = db.Column(db.DateTime)
    
    def __init__(self, user_id, paper_account_id, symbol, side, quantity, entry_price,
                 stop_loss=None, take_profit=None, session_id=None, source='app'):
        self.user_id = user_id
        self.paper_account_id = paper_account_id
        self.symbol = symbol
        self.side = side
        self.quantity = Decimal(str(quantity))
        self.entry_price = Decimal(str(entry_price))
        self.stop_loss = Decimal(str(stop_loss)) if stop_loss else None
        self.take_profit = Decimal(str(take_profit)) if take_profit else None
        self.session_id = session_id
        self.source = source
    
    def is_long(self):
        """Check if this is a long position."""
        return self.side in [PaperTradeSide.BUY, PaperTradeSide.LONG]
    
    def is_short(self):
        """Check if this is a short position."""
        return self.side in [PaperTradeSide.SELL, PaperTradeSide.SHORT]
    
    def calculate_pnl(self, current_price):
        """Calculate current P&L based on current market price."""
        if not current_price:
            return Decimal('0')
        
        current_price = Decimal(str(current_price))
        
        if self.is_long():
            # Long position: profit when price goes up
            pnl = (current_price - self.entry_price) * self.quantity
        else:
            # Short position: profit when price goes down
            pnl = (self.entry_price - current_price) * self.quantity
        
        return pnl
    
    def close_trade(self, exit_price, exit_reason='manual'):
        """Close the trade and calculate final P&L."""
        self.exit_price = Decimal(str(exit_price))
        self.exit_time = datetime.utcnow()
        self.status = PaperTradeStatus.CLOSED
        self.exit_reason = exit_reason
        
        # Calculate final P&L
        self.pnl = self.calculate_pnl(exit_price)
        
        # Simulate exchange fee (0.1% of trade value)
        trade_value = self.quantity * self.exit_price
        self.simulated_fee = trade_value * Decimal('0.001')
        
        # Subtract fee from P&L
        self.pnl -= self.simulated_fee
    
    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'paper_account_id': self.paper_account_id,
            'session_id': self.session_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'quantity': float(self.quantity),
            'source': self.source,
            'entry_price': float(self.entry_price),
            'exit_price': float(self.exit_price) if self.exit_price else None,
            'stop_loss': float(self.stop_loss) if self.stop_loss else None,
            'take_profit': float(self.take_profit) if self.take_profit else None,
            'pnl': float(self.pnl) if self.pnl else None,
            'simulated_fee': float(self.simulated_fee),
            'status': self.status.value,
            'exit_reason': self.exit_reason,
            'entry_time': self.entry_time.isoformat(),
            'exit_time': self.exit_time.isoformat() if self.exit_time else None,
            'archived': self.archived
        }
    
    def __repr__(self):
        return f'<PaperTrade {self.id} - {self.symbol} {self.side.value} - Status: {self.status.value}>'


class PaperTradingSession(db.Model):
    """Model to track paper trading sessions."""
    __tablename__ = 'paper_trading_sessions'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    paper_account_id = db.Column(db.String(36), db.ForeignKey('paper_trading_accounts.id'), nullable=False)

    # Session details
    symbol = db.Column(db.String(20), nullable=False)  # e.g., 'BTCUSDT'
    status = db.Column(db.Enum(PaperTradingStatus), nullable=False, default=PaperTradingStatus.ACTIVE)

    # Balance tracking
    initial_balance = db.Column(db.Numeric(20, 8), nullable=False)
    current_balance = db.Column(db.Numeric(20, 8), nullable=False)

    # Settings
    leverage = db.Column(db.Integer, default=1)
    investment_percentage = db.Column(db.Integer, default=0)  # Percentage of balance to use

    # Performance metrics for this session
    total_trades = db.Column(db.Integer, default=0)
    winning_trades = db.Column(db.Integer, default=0)
    total_pnl = db.Column(db.Numeric(20, 8), default=0)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    ended_at = db.Column(db.DateTime)

    # Relationships
    paper_trades = db.relationship('PaperTrade', backref='paper_session', lazy='dynamic')

    def __init__(self, user_id, paper_account_id, symbol, initial_balance, leverage=1, investment_percentage=0):
        self.user_id = user_id
        self.paper_account_id = paper_account_id
        self.symbol = symbol
        self.initial_balance = Decimal(str(initial_balance))
        self.current_balance = Decimal(str(initial_balance))
        self.leverage = leverage
        self.investment_percentage = investment_percentage

    def update_session_metrics(self):
        """Update session performance metrics based on trades."""
        trades = self.paper_trades.filter(PaperTrade.status == PaperTradeStatus.CLOSED).all()

        self.total_trades = len(trades)
        self.winning_trades = len([t for t in trades if t.pnl and t.pnl > 0])
        self.total_pnl = sum([t.pnl for t in trades if t.pnl], Decimal('0'))

        # Update current balance based on P&L
        self.current_balance = self.initial_balance + self.total_pnl

    def end_session(self):
        """End the trading session."""
        self.status = PaperTradingStatus.ENDED
        self.ended_at = datetime.utcnow()
        self.update_session_metrics()

    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'paper_account_id': self.paper_account_id,
            'symbol': self.symbol,
            'status': self.status.value,
            'initial_balance': float(self.initial_balance),
            'current_balance': float(self.current_balance),
            'leverage': self.leverage,
            'investment_percentage': self.investment_percentage,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'total_pnl': float(self.total_pnl),
            'created_at': self.created_at.isoformat(),
            'ended_at': self.ended_at.isoformat() if self.ended_at else None
        }

    def __repr__(self):
        return f'<PaperTradingSession {self.id} - User: {self.user_id} - Symbol: {self.symbol} - Status: {self.status.value}>'


class PaperBalanceSnapshot(db.Model):
    """Model to track paper trading balance changes over time."""
    __tablename__ = 'paper_balance_snapshots'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    paper_account_id = db.Column(db.String(36), db.ForeignKey('paper_trading_accounts.id'), nullable=False)

    # Snapshot data
    balance = db.Column(db.Numeric(20, 8), nullable=False)
    pnl_change = db.Column(db.Numeric(20, 8), default=0)  # Change in P&L since last snapshot
    transaction_type = db.Column(db.String(20), nullable=False)  # 'trade', 'reset', 'manual'

    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def __init__(self, paper_account_id, balance, pnl_change=0, transaction_type='trade'):
        self.paper_account_id = paper_account_id
        self.balance = Decimal(str(balance))
        self.pnl_change = Decimal(str(pnl_change))
        self.transaction_type = transaction_type

    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            'id': self.id,
            'paper_account_id': self.paper_account_id,
            'balance': float(self.balance),
            'pnl_change': float(self.pnl_change),
            'transaction_type': self.transaction_type,
            'created_at': self.created_at.isoformat()
        }

    def __repr__(self):
        return f'<PaperBalanceSnapshot {self.id} - Balance: {self.balance} - Type: {self.transaction_type}>'
