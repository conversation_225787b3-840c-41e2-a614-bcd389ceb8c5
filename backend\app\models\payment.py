import uuid
from datetime import datetime
from enum import Enum
from app import db

class PaymentMethod(Enum):
    WEB3_USDC = 'web3_usdc'
    STRIPE_CRYPTO = 'stripe_crypto'
    BINANCE_PAY = 'binance_pay'
    MANUAL = 'manual'

class PaymentStatus(Enum):
    PENDING = 'pending'
    CONFIRMED = 'confirmed'
    FAILED = 'failed'
    REFUNDED = 'refunded'
    CANCELLED = 'cancelled'

class Payment(db.Model):
    """Payment model for subscription fees."""
    __tablename__ = 'payments'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.<PERSON>Key('users.id'), nullable=False)
    subscription_id = db.Column(db.String(36), db.ForeignKey('subscriptions.id'), nullable=True)
    
    # Payment details
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    currency = db.Column(db.String(10), nullable=False, default='USDC')
    payment_method = db.Column(db.Enum(PaymentMethod), nullable=False)
    status = db.Column(db.Enum(PaymentStatus), nullable=False, default=PaymentStatus.PENDING)
    
    # Transaction details
    transaction_hash = db.Column(db.String(255))  # Blockchain transaction hash
    external_payment_id = db.Column(db.String(255))  # Stripe payment intent ID, etc.
    
    # Payment metadata (renamed to avoid SQLAlchemy reserved word)
    payment_metadata = db.Column(db.JSON)  # Store additional payment info
    
    # Blockchain specific fields
    from_address = db.Column(db.String(255))
    to_address = db.Column(db.String(255))
    block_number = db.Column(db.BigInteger)
    gas_used = db.Column(db.BigInteger)
    gas_price = db.Column(db.BigInteger)
    
    # Stripe specific fields
    stripe_payment_intent_id = db.Column(db.String(255))
    stripe_charge_id = db.Column(db.String(255))
    
    # Processing info
    processed_at = db.Column(db.DateTime)
    confirmation_count = db.Column(db.Integer, default=0)
    required_confirmations = db.Column(db.Integer, default=12)  # For blockchain payments
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __init__(self, user_id, amount, currency='USDC', payment_method=PaymentMethod.WEB3_USDC, subscription_id=None):
        self.user_id = user_id
        self.amount = amount
        self.currency = currency
        self.payment_method = payment_method
        self.subscription_id = subscription_id
    
    def is_pending(self):
        """Check if payment is pending."""
        return self.status == PaymentStatus.PENDING
    
    def is_confirmed(self):
        """Check if payment is confirmed."""
        return self.status == PaymentStatus.CONFIRMED
    
    def is_failed(self):
        """Check if payment failed."""
        return self.status == PaymentStatus.FAILED
    
    def is_blockchain_payment(self):
        """Check if this is a blockchain payment."""
        return self.payment_method == PaymentMethod.WEB3_USDC
    
    def needs_confirmation(self):
        """Check if payment needs more confirmations."""
        if not self.is_blockchain_payment():
            return False
        return self.confirmation_count < self.required_confirmations
    
    def confirm_payment(self, transaction_hash=None, block_number=None):
        """Confirm the payment."""
        self.status = PaymentStatus.CONFIRMED
        self.processed_at = datetime.utcnow()
        
        if transaction_hash:
            self.transaction_hash = transaction_hash
        if block_number:
            self.block_number = block_number
        
        self.updated_at = datetime.utcnow()
    
    def fail_payment(self, reason=None):
        """Mark payment as failed."""
        self.status = PaymentStatus.FAILED
        self.processed_at = datetime.utcnow()
        
        if reason:
            if not self.payment_metadata:
                self.payment_metadata = {}
            self.payment_metadata['failure_reason'] = reason
        
        self.updated_at = datetime.utcnow()
    
    def cancel_payment(self):
        """Cancel the payment."""
        self.status = PaymentStatus.CANCELLED
        self.processed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def refund_payment(self, refund_transaction_hash=None):
        """Mark payment as refunded."""
        self.status = PaymentStatus.REFUNDED
        
        if refund_transaction_hash:
            if not self.payment_metadata:
                self.payment_metadata = {}
            self.payment_metadata['refund_transaction_hash'] = refund_transaction_hash
        
        self.updated_at = datetime.utcnow()
    
    def add_confirmation(self):
        """Add a blockchain confirmation."""
        if self.is_blockchain_payment():
            self.confirmation_count += 1
            self.updated_at = datetime.utcnow()
            
            # Auto-confirm once we have enough confirmations
            if self.confirmation_count >= self.required_confirmations and self.is_pending():
                self.confirm_payment()
    
    def set_stripe_details(self, payment_intent_id, charge_id=None):
        """Set Stripe payment details."""
        self.stripe_payment_intent_id = payment_intent_id
        if charge_id:
            self.stripe_charge_id = charge_id
        self.external_payment_id = payment_intent_id
    
    def set_blockchain_details(self, transaction_hash, from_address, to_address, 
                             block_number=None, gas_used=None, gas_price=None):
        """Set blockchain transaction details."""
        self.transaction_hash = transaction_hash
        self.from_address = from_address
        self.to_address = to_address
        
        if block_number:
            self.block_number = block_number
        if gas_used:
            self.gas_used = gas_used
        if gas_price:
            self.gas_price = gas_price
    
    def get_explorer_url(self):
        """Get blockchain explorer URL for transaction."""
        if not self.transaction_hash:
            return None
        
        # Ethereum mainnet explorer
        if self.currency == 'USDC':
            return f"https://etherscan.io/tx/{self.transaction_hash}"
        
        return None
    
    def to_dict(self):
        """Convert payment to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'subscription_id': self.subscription_id,
            'amount': float(self.amount),
            'currency': self.currency,
            'payment_method': self.payment_method.value,
            'status': self.status.value,
            'transaction_hash': self.transaction_hash,
            'external_payment_id': self.external_payment_id,
            'from_address': self.from_address,
            'to_address': self.to_address,
            'block_number': self.block_number,
            'confirmation_count': self.confirmation_count,
            'required_confirmations': self.required_confirmations,
            'needs_confirmation': self.needs_confirmation(),
            'explorer_url': self.get_explorer_url(),
            'payment_metadata': self.payment_metadata,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<Payment {self.id} - User: {self.user_id} - Amount: {self.amount} {self.currency} - Status: {self.status.value}>'