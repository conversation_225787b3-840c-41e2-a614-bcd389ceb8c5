"""
Referral System Models
Handles referral relationships, earnings, and payments.
"""

from datetime import datetime
from app import db
from decimal import Decimal
import secrets
import string


class Referral(db.Model):
    """Track referral relationships between users."""
    __tablename__ = 'referrals'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(__import__('uuid').uuid4()))
    
    # Referral relationship
    referrer_id = db.Column(db.String(36), db.<PERSON><PERSON>('users.id'), nullable=False)  # User who referred
    referee_id = db.Column(db.String(36), db.<PERSON><PERSON><PERSON>('users.id'), nullable=False)   # User who was referred
    
    # Referral details
    referral_code = db.Column(db.String(20), unique=True, nullable=False)
    referral_link = db.Column(db.String(255))
    
    # Status tracking
    status = db.Column(db.String(20), default='active')  # 'active', 'inactive', 'suspended'
    is_verified = db.Column(db.Boole<PERSON>, default=False)  # True when referee makes first trade
    
    # Earnings tracking
    total_earnings = db.Column(db.Numeric(20, 8), default=0)  # Total earnings from this referral
    total_paid = db.Column(db.Numeric(20, 8), default=0)     # Total amount paid to referrer
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    verified_at = db.Column(db.DateTime)  # When referee made first trade
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    referrer = db.relationship('User', foreign_keys=[referrer_id], backref='referrals_made')
    referee = db.relationship('User', foreign_keys=[referee_id], backref='referral_received')
    earnings = db.relationship('ReferralEarning', backref='referral', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, referrer_id, referee_id, referral_code=None):
        self.referrer_id = referrer_id
        self.referee_id = referee_id
        self.referral_code = referral_code or self._generate_referral_code()
        self.referral_link = f"/register?ref={self.referral_code}"
        self.total_earnings = Decimal('0')
        self.total_paid = Decimal('0')
    
    @staticmethod
    def _generate_referral_code():
        """Generate a unique referral code."""
        while True:
            code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
            if not Referral.query.filter_by(referral_code=code).first():
                return code
    
    def verify_referral(self):
        """Mark referral as verified when referee makes first trade."""
        if not self.is_verified:
            self.is_verified = True
            self.verified_at = datetime.utcnow()
            self.updated_at = datetime.utcnow()
    
    def add_earning(self, amount, source_trade_id=None, description=None):
        """Add earnings from referee's profit share."""
        earning = ReferralEarning(
            referral_id=self.id,
            amount=amount,
            source_trade_id=source_trade_id,
            description=description or f"Referral earning from {self.referee.full_name}"
        )
        db.session.add(earning)
        
        self.total_earnings += Decimal(str(amount))
        self.updated_at = datetime.utcnow()
        
        return earning
    
    def record_payment(self, amount, transaction_hash=None):
        """Record payment made to referrer."""
        self.total_paid += Decimal(str(amount))
        self.updated_at = datetime.utcnow()
        
        # Update earnings as paid
        unpaid_earnings = self.earnings.filter_by(is_paid=False).order_by(ReferralEarning.created_at.asc())
        remaining_amount = Decimal(str(amount))
        
        for earning in unpaid_earnings:
            if remaining_amount <= 0:
                break
            
            if earning.amount <= remaining_amount:
                earning.mark_as_paid(transaction_hash)
                remaining_amount -= earning.amount
            else:
                # Partial payment - split the earning
                paid_amount = remaining_amount
                remaining_amount = Decimal('0')
                earning.mark_as_partially_paid(paid_amount, transaction_hash)
    
    def get_unpaid_earnings(self):
        """Get total unpaid earnings."""
        return self.total_earnings - self.total_paid
    
    def get_referrer_tier_rate(self):
        """Get referral earning rate based on referrer's tier."""
        from app.models.user_tier_status import UserTierStatus
        
        tier_status = UserTierStatus.query.filter_by(user_id=self.referrer_id).first()
        if not tier_status:
            return 0.005  # Default 0.5% for tier 1
        
        tier = tier_status.get_current_tier()
        rates = {
            1: 0.005,  # 0.5%
            2: 0.01,   # 1%
            3: 0.02    # 2%
        }
        return rates.get(tier, 0.005)
    
    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            'id': self.id,
            'referrer_id': self.referrer_id,
            'referee_id': self.referee_id,
            'referral_code': self.referral_code,
            'referral_link': self.referral_link,
            'status': self.status,
            'is_verified': self.is_verified,
            'total_earnings': float(self.total_earnings),
            'total_paid': float(self.total_paid),
            'unpaid_earnings': float(self.get_unpaid_earnings()),
            'referrer_tier_rate': self.get_referrer_tier_rate(),
            'created_at': self.created_at.isoformat(),
            'verified_at': self.verified_at.isoformat() if self.verified_at else None,
            'updated_at': self.updated_at.isoformat(),
            'referee_name': self.referee.full_name if self.referee else None,
            'referrer_name': self.referrer.full_name if self.referrer else None
        }
    
    def __repr__(self):
        return f'<Referral {self.referral_code} - Referrer: {self.referrer_id} - Referee: {self.referee_id}>'


class ReferralEarning(db.Model):
    """Track individual earnings from referrals."""
    __tablename__ = 'referral_earnings'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(__import__('uuid').uuid4()))
    referral_id = db.Column(db.String(36), db.ForeignKey('referrals.id'), nullable=False)
    
    # Earning details
    amount = db.Column(db.Numeric(20, 8), nullable=False)
    source_trade_id = db.Column(db.String(36), db.ForeignKey('trades.id'), nullable=True)
    description = db.Column(db.Text)
    
    # Payment tracking
    is_paid = db.Column(db.Boolean, default=False)
    paid_amount = db.Column(db.Numeric(20, 8), default=0)
    payment_date = db.Column(db.DateTime)
    transaction_hash = db.Column(db.String(255))  # Solana transaction hash
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    source_trade = db.relationship('Trade', backref='referral_earnings')
    
    def __init__(self, referral_id, amount, source_trade_id=None, description=None):
        self.referral_id = referral_id
        self.amount = Decimal(str(amount))
        self.source_trade_id = source_trade_id
        self.description = description
        self.paid_amount = Decimal('0')
    
    def mark_as_paid(self, transaction_hash=None):
        """Mark earning as fully paid."""
        self.is_paid = True
        self.paid_amount = self.amount
        self.payment_date = datetime.utcnow()
        self.transaction_hash = transaction_hash
        self.updated_at = datetime.utcnow()
    
    def mark_as_partially_paid(self, amount, transaction_hash=None):
        """Mark earning as partially paid."""
        self.paid_amount += Decimal(str(amount))
        if self.paid_amount >= self.amount:
            self.is_paid = True
        self.payment_date = datetime.utcnow()
        self.transaction_hash = transaction_hash
        self.updated_at = datetime.utcnow()
    
    def get_remaining_amount(self):
        """Get remaining unpaid amount."""
        return self.amount - self.paid_amount
    
    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            'id': self.id,
            'referral_id': self.referral_id,
            'amount': float(self.amount),
            'paid_amount': float(self.paid_amount),
            'remaining_amount': float(self.get_remaining_amount()),
            'source_trade_id': self.source_trade_id,
            'description': self.description,
            'is_paid': self.is_paid,
            'payment_date': self.payment_date.isoformat() if self.payment_date else None,
            'transaction_hash': self.transaction_hash,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<ReferralEarning {self.id} - Amount: {self.amount} - Paid: {self.is_paid}>'


class ReferrerProfile(db.Model):
    """Extended profile for users who participate in referral program."""
    __tablename__ = 'referrer_profiles'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(__import__('uuid').uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False, unique=True)
    
    # Referral settings
    referral_code = db.Column(db.String(20), unique=True, nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    
    # Payment settings
    solana_wallet_address = db.Column(db.String(255))  # For receiving payments
    auto_payment_enabled = db.Column(db.Boolean, default=True)
    minimum_payout = db.Column(db.Numeric(20, 8), default=10)  # Minimum amount for payout
    
    # Statistics
    total_referrals = db.Column(db.Integer, default=0)
    active_referrals = db.Column(db.Integer, default=0)
    total_earnings = db.Column(db.Numeric(20, 8), default=0)
    total_paid = db.Column(db.Numeric(20, 8), default=0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    user = db.relationship('User', backref='referrer_profile')
    
    def __init__(self, user_id, referral_code=None):
        self.user_id = user_id
        self.referral_code = referral_code or Referral._generate_referral_code()
        self.total_earnings = Decimal('0')
        self.total_paid = Decimal('0')
        self.minimum_payout = Decimal('10')
    
    def update_statistics(self):
        """Update referral statistics."""
        referrals = Referral.query.filter_by(referrer_id=self.user_id).all()
        
        self.total_referrals = len(referrals)
        self.active_referrals = len([r for r in referrals if r.status == 'active'])
        self.total_earnings = sum(r.total_earnings for r in referrals)
        self.total_paid = sum(r.total_paid for r in referrals)
        self.updated_at = datetime.utcnow()
    
    def get_pending_payout(self):
        """Get total pending payout amount."""
        return self.total_earnings - self.total_paid
    
    def is_eligible_for_payout(self):
        """Check if eligible for automatic payout."""
        return (self.auto_payment_enabled and 
                self.solana_wallet_address and 
                self.get_pending_payout() >= self.minimum_payout)
    
    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'referral_code': self.referral_code,
            'is_active': self.is_active,
            'solana_wallet_address': self.solana_wallet_address,
            'auto_payment_enabled': self.auto_payment_enabled,
            'minimum_payout': float(self.minimum_payout),
            'total_referrals': self.total_referrals,
            'active_referrals': self.active_referrals,
            'total_earnings': float(self.total_earnings),
            'total_paid': float(self.total_paid),
            'pending_payout': float(self.get_pending_payout()),
            'eligible_for_payout': self.is_eligible_for_payout(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<ReferrerProfile {self.user_id} - Code: {self.referral_code} - Earnings: {self.total_earnings}>'
