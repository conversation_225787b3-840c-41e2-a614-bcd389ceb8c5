"""
Solana Payment Models
Handles USDT payments on Solana blockchain for profit sharing and membership fees.
"""

from datetime import datetime
from app import db
from decimal import Decimal
from enum import Enum


class SolanaPaymentType(Enum):
    """Types of Solana payments."""
    PROFIT_SHARE = "profit_share"
    MEMBERSHIP_FEE = "membership_fee"
    REFERRAL_PAYOUT = "referral_payout"


class SolanaPaymentStatus(Enum):
    """Status of Solana payments."""
    PENDING = "pending"
    CONFIRMED = "confirmed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SolanaPayment(db.Model):
    """Track USDT payments on Solana blockchain."""
    __tablename__ = 'solana_payments'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(__import__('uuid').uuid4()))
    
    # Payment details
    user_id = db.Column(db.String(36), db.<PERSON>ey('users.id'), nullable=False)
    payment_type = db.Column(db.Enum(SolanaPaymentType), nullable=False)
    amount = db.Column(db.Numeric(20, 8), nullable=False)  # Amount in USDT
    
    # Solana transaction details
    transaction_signature = db.Column(db.String(255), unique=True)  # Solana transaction signature
    from_address = db.Column(db.String(255))  # Sender's wallet address
    to_address = db.Column(db.String(255), nullable=False)  # Receiver's wallet address (treasury)
    
    # Status and confirmation
    status = db.Column(db.Enum(SolanaPaymentStatus), default=SolanaPaymentStatus.PENDING)
    confirmations = db.Column(db.Integer, default=0)
    required_confirmations = db.Column(db.Integer, default=12)
    
    # Blockchain details
    block_number = db.Column(db.BigInteger)
    slot = db.Column(db.BigInteger)
    block_time = db.Column(db.DateTime)
    
    # Fee information
    transaction_fee = db.Column(db.Numeric(20, 8), default=0)  # Solana transaction fee
    
    # Related records
    profit_share_id = db.Column(db.String(36), nullable=True)  # Related profit share record
    membership_billing_id = db.Column(db.String(36), nullable=True)  # Related membership billing
    referral_earning_id = db.Column(db.String(36), nullable=True)  # Related referral earning
    
    # Processing details
    processed_at = db.Column(db.DateTime)
    error_message = db.Column(db.Text)
    retry_count = db.Column(db.Integer, default=0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    user = db.relationship('User', backref='solana_payments')
    
    def __init__(self, user_id, payment_type, amount, to_address, **kwargs):
        self.user_id = user_id
        self.payment_type = payment_type
        self.amount = Decimal(str(amount))
        self.to_address = to_address
        self.transaction_fee = Decimal('0')
        
        # Set related record IDs
        self.profit_share_id = kwargs.get('profit_share_id')
        self.membership_billing_id = kwargs.get('membership_billing_id')
        self.referral_earning_id = kwargs.get('referral_earning_id')
    
    def update_transaction_details(self, signature, from_address, block_number=None, slot=None, block_time=None):
        """Update transaction details after submission to blockchain."""
        self.transaction_signature = signature
        self.from_address = from_address
        self.block_number = block_number
        self.slot = slot
        self.block_time = block_time
        self.updated_at = datetime.utcnow()
    
    def update_confirmations(self, confirmations):
        """Update confirmation count."""
        self.confirmations = confirmations
        
        if confirmations >= self.required_confirmations and self.status == SolanaPaymentStatus.PENDING:
            self.status = SolanaPaymentStatus.CONFIRMED
            self.processed_at = datetime.utcnow()
        
        self.updated_at = datetime.utcnow()
    
    def mark_as_failed(self, error_message=None):
        """Mark payment as failed."""
        self.status = SolanaPaymentStatus.FAILED
        self.error_message = error_message
        self.processed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def mark_as_cancelled(self):
        """Mark payment as cancelled."""
        self.status = SolanaPaymentStatus.CANCELLED
        self.processed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def increment_retry(self):
        """Increment retry count."""
        self.retry_count += 1
        self.updated_at = datetime.utcnow()
    
    def is_confirmed(self):
        """Check if payment is confirmed."""
        return self.status == SolanaPaymentStatus.CONFIRMED
    
    def is_pending(self):
        """Check if payment is pending."""
        return self.status == SolanaPaymentStatus.PENDING
    
    def is_failed(self):
        """Check if payment failed."""
        return self.status == SolanaPaymentStatus.FAILED
    
    def get_explorer_url(self):
        """Get Solana explorer URL for transaction."""
        if self.transaction_signature:
            return f"https://explorer.solana.com/tx/{self.transaction_signature}"
        return None
    
    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'payment_type': self.payment_type.value,
            'amount': float(self.amount),
            'transaction_signature': self.transaction_signature,
            'from_address': self.from_address,
            'to_address': self.to_address,
            'status': self.status.value,
            'confirmations': self.confirmations,
            'required_confirmations': self.required_confirmations,
            'block_number': self.block_number,
            'slot': self.slot,
            'block_time': self.block_time.isoformat() if self.block_time else None,
            'transaction_fee': float(self.transaction_fee),
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'explorer_url': self.get_explorer_url(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<SolanaPayment {self.id} - Type: {self.payment_type.value} - Amount: {self.amount} - Status: {self.status.value}>'


class MembershipBilling(db.Model):
    """Track monthly membership billing for Tier 2 users."""
    __tablename__ = 'membership_billing'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(__import__('uuid').uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    
    # Billing details
    billing_period_start = db.Column(db.Date, nullable=False)
    billing_period_end = db.Column(db.Date, nullable=False)
    amount = db.Column(db.Numeric(20, 8), nullable=False)  # Monthly fee amount
    
    # Payment status
    is_paid = db.Column(db.Boolean, default=False)
    payment_due_date = db.Column(db.Date, nullable=False)
    paid_date = db.Column(db.DateTime)
    
    # Related payment
    solana_payment_id = db.Column(db.String(36), db.ForeignKey('solana_payments.id'), nullable=True)
    
    # Status tracking
    is_overdue = db.Column(db.Boolean, default=False)
    grace_period_end = db.Column(db.Date)  # End of grace period before tier downgrade
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    user = db.relationship('User', backref='membership_billings')
    solana_payment = db.relationship('SolanaPayment', backref='membership_billing')
    
    def __init__(self, user_id, billing_period_start, billing_period_end, amount, payment_due_date):
        self.user_id = user_id
        self.billing_period_start = billing_period_start
        self.billing_period_end = billing_period_end
        self.amount = Decimal(str(amount))
        self.payment_due_date = payment_due_date
        
        # Set grace period (7 days after due date)
        from datetime import timedelta
        self.grace_period_end = payment_due_date + timedelta(days=7)
    
    def mark_as_paid(self, solana_payment_id=None):
        """Mark billing as paid."""
        self.is_paid = True
        self.paid_date = datetime.utcnow()
        self.solana_payment_id = solana_payment_id
        self.is_overdue = False
        self.updated_at = datetime.utcnow()
    
    def check_overdue_status(self):
        """Check and update overdue status."""
        from datetime import date
        
        if not self.is_paid and date.today() > self.payment_due_date:
            self.is_overdue = True
            self.updated_at = datetime.utcnow()
    
    def is_in_grace_period(self):
        """Check if still in grace period."""
        from datetime import date
        return not self.is_paid and self.is_overdue and date.today() <= self.grace_period_end
    
    def should_downgrade_tier(self):
        """Check if user should be downgraded due to non-payment."""
        from datetime import date
        return not self.is_paid and date.today() > self.grace_period_end
    
    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'billing_period_start': self.billing_period_start.isoformat(),
            'billing_period_end': self.billing_period_end.isoformat(),
            'amount': float(self.amount),
            'is_paid': self.is_paid,
            'payment_due_date': self.payment_due_date.isoformat(),
            'paid_date': self.paid_date.isoformat() if self.paid_date else None,
            'is_overdue': self.is_overdue,
            'grace_period_end': self.grace_period_end.isoformat(),
            'in_grace_period': self.is_in_grace_period(),
            'should_downgrade': self.should_downgrade_tier(),
            'solana_payment_id': self.solana_payment_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<MembershipBilling {self.id} - User: {self.user_id} - Amount: {self.amount} - Paid: {self.is_paid}>'


class SolanaWalletConfig(db.Model):
    """Configuration for Solana wallet integration."""
    __tablename__ = 'solana_wallet_config'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Treasury wallet configuration
    treasury_wallet_address = db.Column(db.String(255), nullable=False)
    treasury_private_key_encrypted = db.Column(db.Text)  # Encrypted private key
    
    # USDT token configuration
    usdt_token_mint = db.Column(db.String(255), nullable=False)  # USDT token mint address
    usdt_decimals = db.Column(db.Integer, default=6)
    
    # Network configuration
    rpc_endpoint = db.Column(db.String(255), nullable=False)
    network = db.Column(db.String(20), default='mainnet')  # 'mainnet', 'devnet', 'testnet'
    
    # Transaction settings
    default_confirmation_count = db.Column(db.Integer, default=12)
    max_retry_attempts = db.Column(db.Integer, default=3)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def to_dict(self):
        """Convert to dictionary for API responses (excluding sensitive data)."""
        return {
            'id': self.id,
            'treasury_wallet_address': self.treasury_wallet_address,
            'usdt_token_mint': self.usdt_token_mint,
            'usdt_decimals': self.usdt_decimals,
            'rpc_endpoint': self.rpc_endpoint,
            'network': self.network,
            'default_confirmation_count': self.default_confirmation_count,
            'max_retry_attempts': self.max_retry_attempts,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<SolanaWalletConfig {self.treasury_wallet_address} - Network: {self.network}>'
