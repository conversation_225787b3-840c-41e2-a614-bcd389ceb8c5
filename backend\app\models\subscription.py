import uuid
from datetime import datetime, timedelta
from enum import Enum
from app import db
from app.models.user_tier_status import UserTierStatus

class SubscriptionTier(Enum):
    TIER_1 = 1  # Free tier
    TIER_2 = 2  # Premium tier
    TIER_3 = 3  # Future NFT staking tier

class SubscriptionStatus(Enum):
    ACTIVE = 'ACTIVE'      # Active and in good standing
    TRIALING = 'TRIALING'  # In trial period
    PAST_DUE = 'PAST_DUE'  # Payment failed but still in grace period
    CANCELED = 'CANCELED'  # Subscription was canceled
    UNPAID = 'UNPAID'      # Payment failed and grace period ended
    INCOMPLETE = 'INCOMPLETE'  # Initial payment failed
    INCOMPLETE_EXPIRED = 'INCOMPLETE_EXPIRED'  # Initial payment failed and expired
    PAUSED = 'PAUSED'      # Subscription is paused

class Subscription(db.Model):
    """User subscription model."""
    __tablename__ = 'subscriptions'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.<PERSON><PERSON><PERSON>('users.id'), nullable=False)
    
    # Subscription details - store tier as integer in database
    _tier = db.Column('tier', db.Integer, nullable=False, default=SubscriptionTier.TIER_1.value)
    status = db.Column(db.Enum(SubscriptionStatus), nullable=False, default=SubscriptionStatus.ACTIVE)
    
    @property
    def tier(self):
        """Get tier as SubscriptionTier enum."""
        return SubscriptionTier(self._tier) if self._tier is not None else None
    
    @tier.setter
    def tier(self, value):
        """Set tier from SubscriptionTier enum or integer."""
        if isinstance(value, SubscriptionTier):
            self._tier = value.value
        elif isinstance(value, int):
            # Validate the tier value is valid
            if value not in [t.value for t in SubscriptionTier]:
                raise ValueError(f"Invalid tier value: {value}. Must be one of {[t.value for t in SubscriptionTier]}")
            self._tier = value
        else:
            raise ValueError("tier must be a SubscriptionTier enum or integer value")
    
    payment_method_id = db.Column(db.String(255), nullable=True)
    
    # Pricing
    monthly_fee = db.Column(db.Numeric(10, 2), default=0.00)
    profit_share_rate = db.Column(db.Numeric(5, 4), nullable=False, default=0.30)  # Default to 30% for free tier
    
    # Billing cycle
    current_period_start = db.Column(db.DateTime, nullable=True)
    current_period_end = db.Column(db.DateTime, nullable=True)
    cancel_at_period_end = db.Column(db.Boolean, default=False)
    
    # Dates
    start_date = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    end_date = db.Column(db.DateTime)
    next_billing_date = db.Column(db.DateTime)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    payments = db.relationship('Payment', backref='subscription', lazy='dynamic')
    
    def __init__(self, user_id, tier=SubscriptionTier.TIER_1):
        from flask import current_app
        
        self.user_id = user_id
        self.tier = tier  # This will use our setter
        
        # Initialize required fields with defaults
        current_app.logger.info("=== SUBSCRIPTION INITIALIZATION ===")
        current_app.logger.info(f"Setting initial status to: {SubscriptionStatus.ACTIVE}")
        current_app.logger.info(f"Status type: {type(SubscriptionStatus.ACTIVE)}")
        current_app.logger.info(f"Status value: {SubscriptionStatus.ACTIVE.value}")
        
        self.status = SubscriptionStatus.ACTIVE
        
        # Log after assignment
        current_app.logger.info(f"Status after assignment: {self.status}")
        current_app.logger.info(f"Status type after assignment: {type(self.status)}")
        if hasattr(self.status, 'value'):
            current_app.logger.info(f"Status value after assignment: {self.status.value}")
        
        self.monthly_fee = 0.00
        self.profit_share_rate = 0.30  # Default to 30% for free tier
        self.cancel_at_period_end = False
        self.start_date = datetime.utcnow()
        self.created_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        
        # Set tier-specific defaults
        self.set_tier_defaults()
        
        current_app.logger.info(f"Final status after initialization: {self.status}")
        current_app.logger.info("===============================")
    
    def set_tier_defaults(self):
        """Set default values based on tier."""
        from flask import current_app
        
        if self.tier == SubscriptionTier.TIER_1.value:
            self.monthly_fee = 0.00
            self.profit_share_rate = current_app.config.get('TIER_1_PROFIT_SHARE', 0.40)
            self.end_date = None  # Free tier doesn't expire
            self.next_billing_date = None
        elif self.tier == SubscriptionTier.TIER_2.value:
            self.monthly_fee = current_app.config.get('TIER_2_MONTHLY_FEE', 29.99)
            self.profit_share_rate = current_app.config.get('TIER_2_PROFIT_SHARE', 0.20)
            now = datetime.utcnow()
            self.current_period_start = now
            self.current_period_end = now + timedelta(days=30)
            self.next_billing_date = self.current_period_end
            self.end_date = None  # Will be set when canceled
        elif self.tier == SubscriptionTier.TIER_3:
            # Future NFT staking tier
            self.monthly_fee = 0.00  # No monthly fee for NFT staking
            self.profit_share_rate = current_app.config.get('TIER_3_PROFIT_SHARE', 0.10)
            self.end_date = None
    
    def is_active(self):
        """Check if subscription is currently active."""
        active_statuses = [
            SubscriptionStatus.ACTIVE,
            SubscriptionStatus.TRIALING
        ]
        
        if self.status not in active_statuses:
            return False
        
        # For tier 1 (free), always active if status is active
        if self.tier == SubscriptionTier.TIER_1:
            return True
        
        # For paid tiers, check if current time is within the billing period
        now = datetime.utcnow()
        if self.current_period_end and now > self.current_period_end:
            return False
            
        return True
    
    def is_expired(self):
        """Check if subscription has expired."""
        if self.tier == SubscriptionTier.TIER_1.value:
            return False  # Free tier doesn't expire
            
        if self.tier == SubscriptionTier.TIER_3.value:
            # For NFT staking, check NFT balance
            # This will be implemented in the future
            return False
            
        # For tier 2, check if current time is past the billing period end
        now = datetime.utcnow()
        return self.current_period_end and now > self.current_period_end
    
    def days_until_expiry(self):
        """Get days until subscription expires."""
        if self.tier == SubscriptionTier.TIER_1.value:
            return None  # Free tier doesn't expire
            
        now = datetime.utcnow()
        
        if self.tier == SubscriptionTier.TIER_2.value and self.current_period_end:
            delta = self.current_period_end - now
            return max(0, delta.days)
            
        return None
    
    def extend_subscription(self, days=30):
        """Extend subscription by specified days."""
        if self.tier == SubscriptionTier.TIER_1.value:
            return  # Free tier doesn't need extension
            
        now = datetime.utcnow()
        
        if self.tier == SubscriptionTier.TIER_2:
            # For tier 2, update the current period end
            if self.current_period_end and self.current_period_end > now:
                self.current_period_end += timedelta(days=days)
            else:
                self.current_period_end = now + timedelta(days=days)
                
            self.next_billing_date = self.current_period_end
            if self.end_date is not None and datetime.utcnow() > self.end_date:
                # If expired, extend from now
                self.end_date = datetime.utcnow() + timedelta(days=days)
            else:
                # If not expired, extend from current end date
                self.end_date
        self.updated_at = now
        self.status = SubscriptionStatus.ACTIVE
        self.updated_at = datetime.utcnow()
    
    def cancel_subscription(self):
        """Cancel the subscription."""
        self.status = SubscriptionStatus.CANCELED
        self.updated_at = datetime.utcnow()
    
    def upgrade_to_tier_2(self):
        """Upgrade subscription to Tier 2."""
        old_tier = self.tier
        self.tier = SubscriptionTier.TIER_2
        self.set_tier_defaults()
        self.status = SubscriptionStatus.INCOMPLETE  # Pending payment (use INCOMPLETE as closest match)
        self.updated_at = datetime.utcnow()
        return old_tier
    
    def downgrade_to_tier_1(self):
        """Downgrade subscription to Tier 1."""
        old_tier = self.tier
        self.tier = SubscriptionTier.TIER_1
        self.set_tier_defaults()
        self.status = SubscriptionStatus.ACTIVE
        self.updated_at = datetime.utcnow()
        return old_tier
    
    def get_profit_share_percentage(self):
        """Get profit share rate as percentage."""
        return float(self.profit_share_rate) * 100
    
    def requires_payment(self):
        """Check if subscription requires payment."""
        return self.tier == SubscriptionTier.TIER_2 and self.monthly_fee > 0
    
    def to_dict(self):
        """Convert subscription to dictionary."""
        from flask import current_app
        
        # Handle status value consistently
        if self.status is None:
            status_value = None
        elif hasattr(self.status, 'value'):
            # It's an enum, get its value
            status_value = self.status.value
            current_app.logger.info(f"[DEBUG] Subscription.to_dict - Status enum value: {status_value}")
        else:
            # It's already a string or something else
            status_value = str(self.status)
            current_app.logger.info(f"[DEBUG] Subscription.to_dict - Status raw value: {status_value}")
        
        # Ensure status is always uppercase
        if status_value is not None:
            status_value = status_value.upper()
            current_app.logger.info(f"[DEBUG] Subscription.to_dict - Final status value: {status_value}")
        
        return {
            'id': self.id,
            'user_id': self.user_id,
            'tier': str(self.tier.value) if self.tier is not None else None,
            'status': status_value,
            'monthly_fee': float(self.monthly_fee),
            'profit_share_rate': float(self.profit_share_rate),
            'profit_share_percentage': self.get_profit_share_percentage(),
            'start_date': self.start_date.isoformat(),
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'next_billing_date': self.next_billing_date.isoformat() if self.next_billing_date else None,
            'is_active': self.is_active(),
            'is_expired': self.is_expired(),
            'days_until_expiry': self.days_until_expiry(),
            'requires_payment': self.requires_payment(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<Subscription {self.id} - User: {self.user_id} - Tier: {self.tier.value if self.tier is not None else None} - Status: {self.status.value if self.status is not None else None}>'