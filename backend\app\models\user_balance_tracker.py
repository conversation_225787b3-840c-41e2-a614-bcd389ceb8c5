"""
User Balance Tracker Model
Tracks user balance changes and calculates true profit for profit-share calculations.
Only considers profit when user balance exceeds their initial starting balance.
"""

from datetime import datetime
from app import db
from decimal import Decimal


class UserBalanceTracker(db.Model):
    """Track user balance changes and calculate true profit for profit sharing."""
    __tablename__ = 'user_balance_tracker'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(__import__('uuid').uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    
    # Balance tracking
    initial_balance = db.Column(db.Numeric(20, 8), nullable=False)  # Starting balance when user began trading
    current_balance = db.Column(db.Numeric(20, 8), nullable=False)  # Current balance
    total_deposits = db.Column(db.Numeric(20, 8), default=0)  # Total deposits made
    total_withdrawals = db.Column(db.Numeric(20, 8), default=0)  # Total withdrawals made
    
    # Profit tracking
    trading_profit = db.Column(db.Numeric(20, 8), default=0)  # Profit from trading only
    true_profit = db.Column(db.Numeric(20, 8), default=0)  # Profit above initial balance
    profit_share_calculated = db.Column(db.Numeric(20, 8), default=0)  # Total profit share calculated
    profit_share_paid = db.Column(db.Numeric(20, 8), default=0)  # Total profit share paid
    
    # Status tracking
    is_in_profit = db.Column(db.Boolean, default=False)  # True when current balance > initial balance
    last_profit_calculation = db.Column(db.DateTime)  # Last time profit share was calculated
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    user = db.relationship('User', backref='balance_tracker')
    balance_snapshots = db.relationship('BalanceSnapshot', backref='tracker', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, user_id, initial_balance):
        self.user_id = user_id
        self.initial_balance = Decimal(str(initial_balance))
        self.current_balance = Decimal(str(initial_balance))
        self.trading_profit = Decimal('0')
        self.true_profit = Decimal('0')
        self.profit_share_calculated = Decimal('0')
        self.profit_share_paid = Decimal('0')
        self.total_deposits = Decimal('0')
        self.total_withdrawals = Decimal('0')
        self.is_in_profit = False
    
    def update_balance(self, new_balance, transaction_type='trade', amount=None):
        """
        Update balance and recalculate profit.
        
        Args:
            new_balance: New total balance
            transaction_type: 'trade', 'deposit', 'withdrawal'
            amount: Amount of deposit/withdrawal (for tracking)
        """
        old_balance = self.current_balance
        self.current_balance = Decimal(str(new_balance))
        
        # Track deposits and withdrawals separately
        if transaction_type == 'deposit' and amount:
            self.total_deposits += Decimal(str(amount))
        elif transaction_type == 'withdrawal' and amount:
            self.total_withdrawals += Decimal(str(amount))
        elif transaction_type == 'trade':
            # Calculate trading profit/loss
            balance_change = self.current_balance - old_balance
            self.trading_profit += balance_change
        
        # Calculate true profit (only when above initial balance + net deposits)
        adjusted_initial = self.initial_balance + self.total_deposits - self.total_withdrawals
        if self.current_balance > adjusted_initial:
            self.true_profit = self.current_balance - adjusted_initial
            self.is_in_profit = True
        else:
            self.true_profit = Decimal('0')
            self.is_in_profit = False
        
        self.updated_at = datetime.utcnow()
        
        # Create balance snapshot
        self._create_snapshot(transaction_type, amount)
    
    def _create_snapshot(self, transaction_type, amount=None):
        """Create a balance snapshot for historical tracking."""
        snapshot = BalanceSnapshot(
            tracker_id=self.id,
            balance=self.current_balance,
            true_profit=self.true_profit,
            transaction_type=transaction_type,
            transaction_amount=amount or 0
        )
        db.session.add(snapshot)
    
    def calculate_profit_share(self, tier_rate):
        """
        Calculate profit share owed based on true profit and tier rate.
        Only calculates on profit above initial balance.
        
        Args:
            tier_rate: Profit share rate (0.1 for 10%, 0.2 for 20%, etc.)
        
        Returns:
            Decimal: Amount of profit share owed
        """
        if not self.is_in_profit or self.true_profit <= 0:
            return Decimal('0')
        
        # Calculate profit share on new profit since last calculation
        uncalculated_profit = self.true_profit - (self.profit_share_calculated / Decimal(str(tier_rate)))
        
        if uncalculated_profit > 0:
            new_profit_share = uncalculated_profit * Decimal(str(tier_rate))
            self.profit_share_calculated += new_profit_share
            self.last_profit_calculation = datetime.utcnow()
            return new_profit_share
        
        return Decimal('0')
    
    def record_profit_share_payment(self, amount):
        """Record a profit share payment."""
        self.profit_share_paid += Decimal(str(amount))
        self.updated_at = datetime.utcnow()
    
    def get_profit_share_owed(self):
        """Get total profit share owed (calculated - paid)."""
        return self.profit_share_calculated - self.profit_share_paid
    
    def get_profit_percentage(self):
        """Get profit percentage from initial balance."""
        if self.initial_balance == 0:
            return 0
        return float((self.true_profit / self.initial_balance) * 100)
    
    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'initial_balance': float(self.initial_balance),
            'current_balance': float(self.current_balance),
            'total_deposits': float(self.total_deposits),
            'total_withdrawals': float(self.total_withdrawals),
            'trading_profit': float(self.trading_profit),
            'true_profit': float(self.true_profit),
            'profit_share_calculated': float(self.profit_share_calculated),
            'profit_share_paid': float(self.profit_share_paid),
            'profit_share_owed': float(self.get_profit_share_owed()),
            'is_in_profit': self.is_in_profit,
            'profit_percentage': self.get_profit_percentage(),
            'last_profit_calculation': self.last_profit_calculation.isoformat() if self.last_profit_calculation else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<UserBalanceTracker {self.user_id} - Balance: {self.current_balance} - Profit: {self.true_profit}>'


class BalanceSnapshot(db.Model):
    """Historical snapshots of user balance for tracking and auditing."""
    __tablename__ = 'balance_snapshots'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(__import__('uuid').uuid4()))
    tracker_id = db.Column(db.String(36), db.ForeignKey('user_balance_tracker.id'), nullable=False)
    
    # Snapshot data
    balance = db.Column(db.Numeric(20, 8), nullable=False)
    true_profit = db.Column(db.Numeric(20, 8), nullable=False)
    transaction_type = db.Column(db.String(20), nullable=False)  # 'trade', 'deposit', 'withdrawal'
    transaction_amount = db.Column(db.Numeric(20, 8), default=0)
    
    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            'id': self.id,
            'tracker_id': self.tracker_id,
            'balance': float(self.balance),
            'true_profit': float(self.true_profit),
            'transaction_type': self.transaction_type,
            'transaction_amount': float(self.transaction_amount),
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<BalanceSnapshot {self.id} - Balance: {self.balance} - Type: {self.transaction_type}>'
