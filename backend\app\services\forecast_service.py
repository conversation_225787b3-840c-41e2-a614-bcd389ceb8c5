"""
Background service for ML forecast generation and management.
This service runs in a separate thread and handles periodic forecast updates.
"""

import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Optional

from app.services.market_data import ml_service

logger = logging.getLogger(__name__)

class ForecastBackgroundService:
    """Background service for managing ML forecasts."""
    
    def __init__(self):
        self.running = False
        self.thread: Optional[threading.Thread] = None
        self.symbols = ['BTCUSDT']  # Focus on BTC/USDT only
        self.timeframes = ['1h']
        self.update_interval = 3600  # 1 hour in seconds
        self.next_update_time: Optional[datetime] = None
        
    def start(self):
        """Start the background forecast service."""
        if self.running:
            logger.warning("Forecast service is already running")
            return
            
        logger.info("Starting ML forecast background service...")
        self.running = True
        
        # Calculate next update time (next hour at minute :00)
        self._calculate_next_update_time()
        
        # Start the background thread
        self.thread = threading.Thread(target=self._run_service, daemon=True)
        self.thread.start()
        
        logger.info(f"ML forecast service started. Next update: {self.next_update_time}")
    
    def stop(self):
        """Stop the background forecast service."""
        if not self.running:
            logger.warning("Forecast service is not running")
            return
            
        logger.info("Stopping ML forecast background service...")
        self.running = False
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
            
        logger.info("ML forecast service stopped")
    
    def _calculate_next_update_time(self):
        """Calculate the next update time (next hour at minute :00, second :00)."""
        now = datetime.utcnow()
        # Round up to the next hour at exactly :00:00
        next_hour = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
        self.next_update_time = next_hour
        
    def _run_service(self):
        """Main service loop that runs in the background thread."""
        logger.info("ML forecast service thread started")
        logger.info(f"Service will update forecasts every hour at minute :00")

        if self.next_update_time is not None:
            logger.info(f"First update scheduled for: {self.next_update_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")

        # Generate initial forecasts on startup
        logger.info("Generating initial forecasts on startup...")
        self._generate_forecasts()

        while self.running:
            try:
                current_time = datetime.utcnow()

                # Check if it's time for the next update (with 1-second precision)
                if self.next_update_time is not None and current_time >= self.next_update_time:
                    logger.info(f"Time for scheduled forecast update at {current_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")
                    self._generate_forecasts()
                    self._calculate_next_update_time()
                    if self.next_update_time is not None:
                        logger.info(f"Next forecast update scheduled for: {self.next_update_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")

                # Calculate time until next check - use shorter intervals near update time
                if self.next_update_time is not None:
                    time_until_update = (self.next_update_time - current_time).total_seconds()
                else:
                    time_until_update = 3600  # Default to 1 hour if next_update_time is None

                if time_until_update <= 120:  # Within 2 minutes of update time
                    sleep_time = 1  # Check every second
                elif time_until_update <= 600:  # Within 10 minutes of update time
                    sleep_time = 10  # Check every 10 seconds
                else:
                    sleep_time = 60  # Check every minute

                time.sleep(sleep_time)

            except Exception as e:
                logger.error(f"Error in forecast service loop: {e}")
                # Continue running even if there's an error
                time.sleep(10)
        
        logger.info("ML forecast service thread stopped")
    
    def _generate_forecasts(self):
        """Generate forecasts for all configured symbols and timeframes."""
        logger.info("Starting scheduled forecast generation...")
        
        try:
            # Clear expired forecasts first
            cleared_count = ml_service.clear_expired_forecasts()
            if cleared_count > 0:
                logger.info(f"Cleared {cleared_count} expired forecasts")
            
            results = {
                'forecasts_generated': 0,
                'forecasts_failed': 0,
                'symbols_processed': [],
                'errors': []
            }
            
            for symbol in self.symbols:
                for timeframe in self.timeframes:
                    try:
                        logger.info(f"Generating forecast for {symbol} {timeframe}...")
                        
                        # Clear existing cache for this symbol/timeframe to force regeneration
                        cache_key = f"forecast_{symbol}_{timeframe}"
                        if hasattr(ml_service, 'cache') and cache_key in ml_service.cache:
                            del ml_service.cache[cache_key]
                            logger.debug(f"Cleared cached forecast for {symbol} {timeframe}")
                        
                        # Generate new forecast
                        forecast = ml_service.generate_ensemble_forecast(
                            symbol=symbol,
                            timeframe=timeframe,
                            future_hours=72
                        )
                        
                        if forecast and 'error' not in forecast:
                            results['forecasts_generated'] += 1
                            results['symbols_processed'].append(f"{symbol}_{timeframe}")
                            logger.info(f"Successfully generated forecast for {symbol} {timeframe}")
                        else:
                            results['forecasts_failed'] += 1
                            error_msg = f"Failed to generate forecast for {symbol} {timeframe}"
                            results['errors'].append(error_msg)
                            logger.error(error_msg)
                            
                    except Exception as e:
                        results['forecasts_failed'] += 1
                        error_msg = f"Error generating forecast for {symbol} {timeframe}: {str(e)}"
                        results['errors'].append(error_msg)
                        logger.error(error_msg)
            
            logger.info(f"Scheduled forecast generation completed:")
            logger.info(f"  - Forecasts generated: {results['forecasts_generated']}")
            logger.info(f"  - Forecasts failed: {results['forecasts_failed']}")
            logger.info(f"  - Symbols processed: {results['symbols_processed']}")
            
            if results['errors']:
                logger.warning(f"  - Errors encountered: {len(results['errors'])}")
                for error in results['errors'][:3]:  # Log first 3 errors
                    logger.warning(f"    {error}")
                if len(results['errors']) > 3:
                    logger.warning(f"    ... and {len(results['errors']) - 3} more errors")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in scheduled forecast generation: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return None
    
    def force_update(self):
        """Force an immediate forecast update (useful for testing)."""
        logger.info("Forcing immediate forecast update...")
        return self._generate_forecasts()
    
    def get_status(self):
        """Get the current status of the forecast service."""
        current_time = datetime.utcnow()
        time_until_next_update = None

        if self.next_update_time:
            time_until_next_update = (self.next_update_time - current_time).total_seconds()

        status = {
            'running': self.running,
            'current_time': current_time.isoformat() + 'Z',
            'next_update_time': self.next_update_time.isoformat() + 'Z' if self.next_update_time else None,
            'time_until_next_update_seconds': time_until_next_update,
            'symbols': self.symbols,
            'timeframes': self.timeframes,
            'update_interval_hours': self.update_interval / 3600,
            'thread_alive': self.thread.is_alive() if self.thread else False
        }

        # Get cache status from ml_service
        if hasattr(ml_service, 'cache'):
            status['cached_forecasts'] = len(ml_service.cache)
            status['cache_keys'] = list(ml_service.cache.keys())

            # Add cache details with timestamps
            cache_details = {}
            for key, forecast in ml_service.cache.items():
                if isinstance(forecast, dict) and 'generated_at' in forecast:
                    cache_details[key] = {
                        'generated_at': forecast['generated_at'],
                        'symbol': forecast.get('symbol', 'unknown'),
                        'timeframe': forecast.get('timeframe', 'unknown')
                    }
            status['cache_details'] = cache_details
        else:
            status['cached_forecasts'] = 0
            status['cache_keys'] = []
            status['cache_details'] = {}

        return status

# Global instance
forecast_service = ForecastBackgroundService()

def start_forecast_service():
    """Start the global forecast service instance."""
    forecast_service.start()

def stop_forecast_service():
    """Stop the global forecast service instance."""
    forecast_service.stop()

def get_forecast_service_status():
    """Get status of the global forecast service instance."""
    return forecast_service.get_status()

def force_forecast_update():
    """Force an immediate forecast update."""
    return forecast_service.force_update()

def restart_forecast_service():
    """Restart the global forecast service instance."""
    forecast_service.stop()
    forecast_service.start()
    return forecast_service.get_status()
