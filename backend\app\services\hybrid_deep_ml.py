#!/usr/bin/env python3
"""
Hybrid Deep Learning + Traditional ML System
Integrates deep learning alongside existing ML without replacing it
"""

# Suppress TensorFlow warnings and optimize for hardware
import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # Suppress INFO and WARNING logs
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'  # Disable oneDNN warnings

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, Dense, Dropout, Conv1D, MaxPooling1D, Flatten, Input, Concatenate
from tensorflow.keras.optimizers import <PERSON>
from sklearn.preprocessing import MinMaxScaler
import logging
from typing import Dict, Optional, Tuple
from datetime import datetime

# Auto-detect and configure optimal hardware setup
def configure_tensorflow_hardware():
    """Auto-detect and configure optimal TensorFlow hardware setup"""
    logger = logging.getLogger(__name__)

    # Check for GPU availability
    gpus = tf.config.experimental.list_physical_devices('GPU')

    if gpus:
        try:
            # Enable memory growth to prevent GPU memory allocation issues
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)

            # Use mixed precision for better GPU performance
            tf.keras.mixed_precision.set_global_policy('mixed_float16')

            logger.info(f"🚀 GPU DETECTED: {len(gpus)} GPU(s) available")
            logger.info("✅ Configured for GPU acceleration with mixed precision")
            return {
                'device': 'GPU',
                'count': len(gpus),
                'mixed_precision': True,
                'batch_size': 64,  # Larger batch size for GPU
                'epochs': 100      # More epochs with GPU
            }
        except RuntimeError as e:
            logger.warning(f"⚠️ GPU configuration failed: {e}")
            logger.info("🔄 Falling back to CPU configuration")

    # CPU configuration
    # Configure CPU threads for optimal performance
    tf.config.threading.set_inter_op_parallelism_threads(0)  # Use all available cores
    tf.config.threading.set_intra_op_parallelism_threads(0)  # Use all available cores

    logger.info("💻 CPU CONFIGURATION: Optimized for multi-core CPU")
    logger.info("✅ Configured for CPU with multi-threading")
    return {
        'device': 'CPU',
        'count': os.cpu_count(),
        'mixed_precision': False,
        'batch_size': 32,  # Smaller batch size for CPU
        'epochs': 50       # Fewer epochs for CPU
    }

# Configure hardware on import
HARDWARE_CONFIG = configure_tensorflow_hardware()

class HybridDeepMLEnhancer:
    """
    Enhances existing ML predictions with deep learning insights
    Works alongside current Elite ML, SL/TP ML, and Chart systems
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Hardware configuration
        self.hardware_config = HARDWARE_CONFIG
        self.logger.info(f"🔧 Hardware: {self.hardware_config['device']} ({self.hardware_config['count']} cores/GPUs)")

        # Deep learning models
        self.lstm_model = None
        self.cnn_model = None
        self.hybrid_ensemble = None

        # Scalers for deep learning
        self.price_scaler = MinMaxScaler()
        self.volume_scaler = MinMaxScaler()

        # Model paths
        self.model_path = 'models'
        os.makedirs(self.model_path, exist_ok=True)

        # Integration weights (how much to trust each system)
        self.integration_weights = {
            'traditional_ml': 0.7,  # Current ML gets 70% weight
            'deep_learning': 0.3    # Deep learning gets 30% weight
        }

        self.is_trained = False
        
    def enhance_elite_ml_prediction(self, market_data: pd.DataFrame, 
                                  traditional_prediction: Dict) -> Dict:
        """
        Enhance Elite ML prediction with deep learning insights
        """
        try:
            if not self.is_trained:
                self.logger.info("🤖 Deep learning models not trained, using traditional ML only")
                return traditional_prediction
            
            # Get deep learning prediction
            deep_prediction = self._get_deep_learning_prediction(market_data)
            
            if deep_prediction is None:
                return traditional_prediction
            
            # Combine traditional ML + deep learning
            enhanced_prediction = self._combine_predictions(
                traditional_prediction, deep_prediction
            )
            
            self.logger.info(f"🔄 Enhanced prediction: Traditional={traditional_prediction.get('confidence', 0):.1f}%, "
                           f"Deep={deep_prediction.get('confidence', 0):.1f}%, "
                           f"Final={enhanced_prediction.get('confidence', 0):.1f}%")
            
            return enhanced_prediction
            
        except Exception as e:
            self.logger.error(f"Error enhancing ML prediction: {e}")
            return traditional_prediction  # Fallback to traditional ML
    
    def enhance_sl_tp_prediction(self, market_data: pd.DataFrame, 
                               entry_price: float, signal: str,
                               traditional_sl_tp: Dict) -> Dict:
        """
        Enhance SL/TP ML predictions with deep learning
        """
        try:
            if not self.is_trained:
                return traditional_sl_tp
            
            # Get deep learning SL/TP insights
            deep_sl_tp = self._get_deep_sl_tp_prediction(market_data, entry_price, signal)
            
            if deep_sl_tp is None:
                return traditional_sl_tp
            
            # Combine traditional + deep learning SL/TP
            enhanced_sl_tp = self._combine_sl_tp_predictions(
                traditional_sl_tp, deep_sl_tp
            )
            
            self.logger.info(f"🎯 Enhanced SL/TP: Traditional SL={traditional_sl_tp['sl_result']['sl_price']:.2f}, "
                           f"Deep SL={deep_sl_tp.get('sl_price', 0):.2f}, "
                           f"Final SL={enhanced_sl_tp['sl_result']['sl_price']:.2f}")
            
            return enhanced_sl_tp
            
        except Exception as e:
            self.logger.error(f"Error enhancing SL/TP prediction: {e}")
            return traditional_sl_tp
    
    def _build_lstm_model(self, sequence_length: int = 60, features: int = 10) -> Model:
        """
        Build LSTM model for time series pattern recognition
        """
        model = Sequential([
            LSTM(128, return_sequences=True, input_shape=(sequence_length, features)),
            Dropout(0.2),
            LSTM(64, return_sequences=True),
            Dropout(0.2),
            LSTM(32, return_sequences=False),
            Dropout(0.2),
            Dense(16, activation='relu'),
            Dense(3, activation='softmax', name='signal_output')  # BUY/SELL/HOLD
        ])
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def _build_cnn_model(self, sequence_length: int = 50, features: int = 5) -> Model:
        """
        Build CNN model for candlestick pattern recognition
        """
        inputs = Input(shape=(sequence_length, features))
        
        # 1D CNN for time series patterns
        conv1 = Conv1D(32, 3, activation='relu')(inputs)
        pool1 = MaxPooling1D(2)(conv1)
        
        conv2 = Conv1D(64, 3, activation='relu')(pool1)
        pool2 = MaxPooling1D(2)(conv2)
        
        conv3 = Conv1D(128, 3, activation='relu')(pool2)
        
        flatten = Flatten()(conv3)
        dense1 = Dense(64, activation='relu')(flatten)
        dropout = Dropout(0.3)(dense1)
        
        # Multiple outputs
        signal_output = Dense(3, activation='softmax', name='signal')(dropout)
        sl_output = Dense(1, activation='linear', name='sl_distance')(dropout)
        tp_output = Dense(1, activation='linear', name='tp_distance')(dropout)
        
        model = Model(inputs=inputs, outputs=[signal_output, sl_output, tp_output])
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss={
                'signal': 'categorical_crossentropy',
                'sl_distance': 'mse',
                'tp_distance': 'mse'
            },
            loss_weights={
                'signal': 1.0,
                'sl_distance': 0.5,
                'tp_distance': 0.5
            },
            metrics={
                'signal': 'accuracy',
                'sl_distance': 'mae',
                'tp_distance': 'mae'
            }
        )
        
        return model
    
    def _build_hybrid_ensemble(self) -> Model:
        """
        Build ensemble model that combines LSTM + CNN + Traditional ML features
        """
        # LSTM input
        lstm_input = Input(shape=(60, 10), name='lstm_input')
        lstm_features = LSTM(64, return_sequences=False)(lstm_input)
        
        # CNN input
        cnn_input = Input(shape=(50, 5), name='cnn_input')
        cnn_conv = Conv1D(32, 3, activation='relu')(cnn_input)
        cnn_pool = MaxPooling1D(2)(cnn_conv)
        cnn_features = Flatten()(cnn_pool)
        
        # Traditional ML features input
        traditional_input = Input(shape=(20,), name='traditional_features')
        
        # Combine all features
        combined = Concatenate()([lstm_features, cnn_features, traditional_input])
        
        # Final prediction layers
        dense1 = Dense(128, activation='relu')(combined)
        dropout1 = Dropout(0.3)(dense1)
        
        dense2 = Dense(64, activation='relu')(dropout1)
        dropout2 = Dropout(0.2)(dense2)
        
        # Outputs
        signal_output = Dense(3, activation='softmax', name='enhanced_signal')(dropout2)
        confidence_output = Dense(1, activation='sigmoid', name='confidence')(dropout2)
        
        model = Model(
            inputs=[lstm_input, cnn_input, traditional_input],
            outputs=[signal_output, confidence_output]
        )
        
        model.compile(
            optimizer=Adam(learning_rate=0.0005),
            loss={
                'enhanced_signal': 'categorical_crossentropy',
                'confidence': 'mse'
            },
            metrics={
                'enhanced_signal': 'accuracy',
                'confidence': 'mae'
            }
        )
        
        return model
    
    def train_deep_models(self, market_data: pd.DataFrame) -> Dict:
        """
        Train deep learning models on historical data
        """
        try:
            self.logger.info("🚀 Training deep learning models...")
            
            # Prepare training data
            X_lstm, X_cnn, X_traditional, y_signal, y_sl, y_tp = self._prepare_training_data(market_data)
            
            if X_lstm is None:
                self.logger.error("Failed to prepare training data")
                return {'success': False}
            
            # Train LSTM model
            self.logger.info("🧠 Training LSTM model...")
            self.lstm_model = self._build_lstm_model()
            
            lstm_history = self.lstm_model.fit(
                X_lstm, y_signal,
                epochs=self.hardware_config['epochs'],
                batch_size=self.hardware_config['batch_size'],
                validation_split=0.2,
                verbose=0
            )
            
            # Train CNN model
            self.logger.info("🔍 Training CNN model...")
            self.cnn_model = self._build_cnn_model()
            
            cnn_history = self.cnn_model.fit(
                X_cnn, [y_signal, y_sl, y_tp],
                epochs=self.hardware_config['epochs'],
                batch_size=self.hardware_config['batch_size'],
                validation_split=0.2,
                verbose=0
            )
            
            # Train hybrid ensemble
            self.logger.info("🤝 Training hybrid ensemble...")
            self.hybrid_ensemble = self._build_hybrid_ensemble()
            
            # Create confidence scores (mock for now)
            y_confidence = np.random.uniform(0.6, 0.95, len(y_signal))
            
            ensemble_history = self.hybrid_ensemble.fit(
                [X_lstm, X_cnn, X_traditional],
                [y_signal, y_confidence],
                epochs=int(self.hardware_config['epochs'] * 0.6),  # 60% of base epochs for ensemble
                batch_size=self.hardware_config['batch_size'],
                validation_split=0.2,
                verbose=0
            )
            
            # Save models
            self._save_models()
            
            self.is_trained = True
            
            # Calculate final accuracies
            lstm_accuracy = max(lstm_history.history['val_accuracy'])
            cnn_accuracy = max(cnn_history.history['val_signal_accuracy'])
            ensemble_accuracy = max(ensemble_history.history['val_enhanced_signal_accuracy'])
            
            self.logger.info(f"✅ Deep learning training completed!")
            self.logger.info(f"📊 LSTM Accuracy: {lstm_accuracy:.3f}")
            self.logger.info(f"📊 CNN Accuracy: {cnn_accuracy:.3f}")
            self.logger.info(f"📊 Ensemble Accuracy: {ensemble_accuracy:.3f}")
            
            return {
                'success': True,
                'lstm_accuracy': lstm_accuracy,
                'cnn_accuracy': cnn_accuracy,
                'ensemble_accuracy': ensemble_accuracy,
                'models_trained': 3
            }
            
        except Exception as e:
            self.logger.error(f"Error training deep models: {e}")
            return {'success': False, 'error': str(e)}
    
    def _prepare_training_data(self, df: pd.DataFrame) -> Tuple:
        """
        Prepare training data for deep learning models
        """
        try:
            if len(df) < 200:
                return None, None, None, None, None, None
            
            # Prepare sequences for LSTM (60 time steps)
            sequence_length = 60
            X_lstm = []
            X_cnn = []
            X_traditional = []
            y_signal = []
            y_sl = []
            y_tp = []
            
            # Scale price data
            price_data = df[['open', 'high', 'low', 'close', 'volume']].values
            price_scaled = self.price_scaler.fit_transform(price_data)
            
            for i in range(sequence_length, len(df) - 1):
                # LSTM features (10 features: OHLCV + technical indicators)
                lstm_features = self._extract_lstm_features(df.iloc[i-sequence_length:i])
                X_lstm.append(lstm_features)
                
                # CNN features (5 features: OHLCV)
                cnn_features = price_scaled[i-50:i, :5]  # Last 50 candles
                X_cnn.append(cnn_features)
                
                # Traditional ML features (20 features)
                traditional_features = self._extract_traditional_features(df.iloc[i])
                X_traditional.append(traditional_features)
                
                # Target: future price movement
                current_price = df['close'].iloc[i]
                future_price = df['close'].iloc[i + 1]
                price_change = (future_price - current_price) / current_price
                
                # Signal classification
                if price_change > 0.005:  # 0.5% up
                    signal = [1, 0, 0]  # BUY
                elif price_change < -0.005:  # 0.5% down
                    signal = [0, 1, 0]  # SELL
                else:
                    signal = [0, 0, 1]  # HOLD
                
                y_signal.append(signal)
                
                # SL/TP distances (as percentages)
                y_sl.append(abs(price_change) * 0.5)  # SL at 50% of expected move
                y_tp.append(abs(price_change) * 2.0)  # TP at 200% of expected move
            
            return (
                np.array(X_lstm),
                np.array(X_cnn),
                np.array(X_traditional),
                np.array(y_signal),
                np.array(y_sl),
                np.array(y_tp)
            )
            
        except Exception as e:
            self.logger.error(f"Error preparing training data: {e}")
            return None, None, None, None, None, None

    def _extract_lstm_features(self, df_slice: pd.DataFrame) -> np.ndarray:
        """Extract features for LSTM model"""
        try:
            features = []

            # Price features
            close_prices = df_slice['close'].values
            features.extend([
                close_prices,
                df_slice['high'].values,
                df_slice['low'].values,
                df_slice['volume'].values
            ])

            # Technical indicators
            returns = pd.Series(close_prices).pct_change().fillna(0).values
            sma_5 = pd.Series(close_prices).rolling(5).mean().fillna(method='bfill').values
            rsi = self._calculate_rsi(pd.Series(close_prices)).fillna(50).values

            features.extend([returns, sma_5, rsi])

            # Volatility
            volatility = pd.Series(returns).rolling(10).std().fillna(0).values
            features.extend([volatility])

            # Volume indicators
            volume_sma = pd.Series(df_slice['volume'].values).rolling(5).mean().fillna(method='bfill').values
            features.extend([volume_sma])

            # Momentum
            momentum = pd.Series(close_prices).pct_change(5).fillna(0).values
            features.extend([momentum])

            # Stack features (10 features x sequence_length)
            feature_matrix = np.column_stack(features)

            # Normalize
            feature_matrix = (feature_matrix - np.mean(feature_matrix, axis=0)) / (np.std(feature_matrix, axis=0) + 1e-8)

            return feature_matrix

        except Exception as e:
            self.logger.error(f"Error extracting LSTM features: {e}")
            return np.zeros((60, 10))

    def _extract_traditional_features(self, row: pd.Series) -> np.ndarray:
        """Extract traditional ML features for ensemble"""
        try:
            features = []

            # Basic price features
            features.extend([
                row['open'], row['high'], row['low'], row['close'], row['volume']
            ])

            # Mock additional features (in real implementation, use actual technical indicators)
            features.extend([
                row['close'] * 1.01,  # Mock RSI
                row['close'] * 0.99,  # Mock MACD
                row['volume'] * 1.1,  # Mock volume indicator
                row['high'] - row['low'],  # Range
                (row['close'] - row['open']) / row['open'],  # Body percentage
                row['high'] - max(row['open'], row['close']),  # Upper shadow
                min(row['open'], row['close']) - row['low'],  # Lower shadow
                row['close'],  # Current price
                row['volume'],  # Current volume
                1.0,  # Mock trend strength
                0.5,  # Mock momentum
                0.7,  # Mock volatility
                1.2,  # Mock support level
                0.8,  # Mock resistance level
                0.6   # Mock market regime
            ])

            return np.array(features[:20])  # Ensure exactly 20 features

        except Exception as e:
            self.logger.error(f"Error extracting traditional features: {e}")
            return np.zeros(20)

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return pd.Series([50] * len(prices))

    def _get_deep_learning_prediction(self, market_data: pd.DataFrame) -> Optional[Dict]:
        """Get prediction from deep learning models"""
        try:
            if not self.is_trained or len(market_data) < 60:
                return None

            # Prepare input data
            lstm_input = self._extract_lstm_features(market_data.tail(60))
            cnn_input = self.price_scaler.transform(
                market_data[['open', 'high', 'low', 'close', 'volume']].tail(50).values
            )
            traditional_input = self._extract_traditional_features(market_data.iloc[-1])

            # Get predictions from ensemble
            signal_pred, confidence_pred = self.hybrid_ensemble.predict([
                lstm_input.reshape(1, 60, 10),
                cnn_input.reshape(1, 50, 5),
                traditional_input.reshape(1, 20)
            ])

            # Convert to signal
            signal_idx = np.argmax(signal_pred[0])
            signals = ['BUY', 'SELL', 'HOLD']
            signal = signals[signal_idx]

            confidence = float(confidence_pred[0][0]) * 100

            return {
                'signal': signal,
                'confidence': confidence,
                'method': 'DEEP_LEARNING_ENSEMBLE',
                'signal_probabilities': {
                    'BUY': float(signal_pred[0][0]),
                    'SELL': float(signal_pred[0][1]),
                    'HOLD': float(signal_pred[0][2])
                }
            }

        except Exception as e:
            self.logger.error(f"Error getting deep learning prediction: {e}")
            return None

    def _get_deep_sl_tp_prediction(self, market_data: pd.DataFrame,
                                 entry_price: float, signal: str) -> Optional[Dict]:
        """Get SL/TP prediction from CNN model with chart forecast integration"""
        try:
            if not self.is_trained or len(market_data) < 50:
                return None

            # 🎯 Get chart forecast for TP synchronization (same as SL/TP ML predictors)
            chart_forecast = self._get_chart_forecast()

            # Prepare CNN input
            cnn_input = self.price_scaler.transform(
                market_data[['open', 'high', 'low', 'close', 'volume']].tail(50).values
            )

            # Get predictions
            signal_pred, sl_dist, tp_dist = self.cnn_model.predict(
                cnn_input.reshape(1, 50, 5)
            )

            # Convert distances to actual prices
            sl_distance_pct = float(sl_dist[0][0])
            tp_distance_pct = float(tp_dist[0][0])

            # 🎯 CHART FORECAST INTEGRATION - Adjust TP based on chart prediction
            if chart_forecast:
                if signal == 'BUY':
                    chart_target = chart_forecast.get('highest_price', entry_price * 1.02)
                    # Blend deep learning TP with chart target
                    base_tp = entry_price * (1 + abs(tp_distance_pct))
                    chart_influence = 0.3  # 30% chart influence
                    tp_price = base_tp * (1 - chart_influence) + chart_target * chart_influence
                else:  # SELL
                    chart_target = chart_forecast.get('lowest_price', entry_price * 0.98)
                    # Blend deep learning TP with chart target
                    base_tp = entry_price * (1 - abs(tp_distance_pct))
                    chart_influence = 0.3  # 30% chart influence
                    tp_price = base_tp * (1 - chart_influence) + chart_target * chart_influence

                self.logger.info(f"🎯 Deep Learning TP enhanced with chart forecast: "
                               f"Base=${base_tp:.2f}, Chart=${chart_target:.2f}, Final=${tp_price:.2f}")
            else:
                # No chart forecast available, use pure deep learning prediction
                if signal == 'BUY':
                    tp_price = entry_price * (1 + abs(tp_distance_pct))
                else:  # SELL
                    tp_price = entry_price * (1 - abs(tp_distance_pct))

            # SL calculation (not affected by chart forecast)
            if signal == 'BUY':
                sl_price = entry_price * (1 - abs(sl_distance_pct))
            else:  # SELL
                sl_price = entry_price * (1 + abs(sl_distance_pct))

            return {
                'sl_price': sl_price,
                'tp_price': tp_price,
                'sl_distance_pct': abs(sl_distance_pct) * 100,
                'tp_distance_pct': abs(tp_distance_pct) * 100,
                'method': 'DEEP_LEARNING_CNN',
                'chart_enhanced': chart_forecast is not None
            }

        except Exception as e:
            self.logger.error(f"Error getting deep SL/TP prediction: {e}")
            return None

    def _get_chart_forecast(self) -> Optional[Dict]:
        """
        Fetch current chart forecast for TP synchronization
        Same method as used in SL/TP ML predictors
        """
        try:
            from app.services.market_data import ml_service

            # Get latest forecast for BTC/USDT 1h
            forecast = ml_service.generate_ensemble_forecast(
                symbol='BTCUSDT',
                timeframe='1h',
                future_hours=24  # Next 24 hours for TP planning
            )

            if forecast and not forecast.get('is_mock', False):
                self.logger.info(f"📊 Chart forecast synced for deep learning: "
                               f"High=${forecast.get('highest_price', 0):.2f}, "
                               f"Low=${forecast.get('lowest_price', 0):.2f}")
                return forecast
            else:
                self.logger.warning("⚠️ Chart forecast not available or is mock data for deep learning")
                return None

        except Exception as e:
            self.logger.error(f"Error fetching chart forecast for deep learning: {e}")
            return None

    def _combine_predictions(self, traditional: Dict, deep: Dict) -> Dict:
        """Combine traditional ML and deep learning predictions"""
        try:
            # Weight the confidences
            trad_weight = self.integration_weights['traditional_ml']
            deep_weight = self.integration_weights['deep_learning']

            trad_conf = traditional.get('confidence', 0)
            deep_conf = deep.get('confidence', 0)

            # Weighted confidence
            combined_confidence = (trad_conf * trad_weight) + (deep_conf * deep_weight)

            # Signal agreement check
            trad_signal = traditional.get('signal', 'HOLD')
            deep_signal = deep.get('signal', 'HOLD')

            if trad_signal == deep_signal:
                # Both agree - boost confidence
                final_signal = trad_signal
                final_confidence = min(combined_confidence * 1.1, 100)  # 10% boost, max 100%
                agreement = 'FULL_AGREEMENT'
            else:
                # Disagreement - use traditional ML (more reliable) but lower confidence
                final_signal = trad_signal
                final_confidence = combined_confidence * 0.9  # 10% penalty
                agreement = 'DISAGREEMENT'

            return {
                **traditional,  # Keep all traditional fields
                'confidence': final_confidence,
                'signal': final_signal,
                'method': f"{traditional.get('method', 'TRADITIONAL')}_+_DEEP_LEARNING",
                'deep_learning_enhancement': {
                    'deep_signal': deep_signal,
                    'deep_confidence': deep_conf,
                    'agreement': agreement,
                    'enhancement_applied': True
                }
            }

        except Exception as e:
            self.logger.error(f"Error combining predictions: {e}")
            return traditional

    def _combine_sl_tp_predictions(self, traditional: Dict, deep: Dict) -> Dict:
        """Combine traditional and deep learning SL/TP predictions"""
        try:
            trad_weight = self.integration_weights['traditional_ml']
            deep_weight = self.integration_weights['deep_learning']

            # Weighted SL/TP prices
            trad_sl = traditional['sl_result']['sl_price']
            trad_tp = traditional['tp_result']['tp_price']

            deep_sl = deep.get('sl_price', trad_sl)
            deep_tp = deep.get('tp_price', trad_tp)

            # Weighted combination
            final_sl = (trad_sl * trad_weight) + (deep_sl * deep_weight)
            final_tp = (trad_tp * trad_weight) + (deep_tp * deep_weight)

            # Update the traditional result with enhanced values
            enhanced_result = traditional.copy()
            enhanced_result['sl_result']['sl_price'] = final_sl
            enhanced_result['tp_result']['tp_price'] = final_tp

            # Add deep learning info
            enhanced_result['deep_learning_enhancement'] = {
                'deep_sl': deep_sl,
                'deep_tp': deep_tp,
                'traditional_sl': trad_sl,
                'traditional_tp': trad_tp,
                'final_sl': final_sl,
                'final_tp': final_tp,
                'method': 'WEIGHTED_COMBINATION'
            }

            return enhanced_result

        except Exception as e:
            self.logger.error(f"Error combining SL/TP predictions: {e}")
            return traditional

    def _save_models(self):
        """Save trained deep learning models"""
        try:
            # Create models directory if it doesn't exist
            os.makedirs(self.model_path, exist_ok=True)

            if self.lstm_model:
                self.lstm_model.save(f"{self.model_path}/hybrid_lstm_model.keras")
                self.logger.info("✅ LSTM model saved")
            if self.cnn_model:
                self.cnn_model.save(f"{self.model_path}/hybrid_cnn_model.keras")
                self.logger.info("✅ CNN model saved")
            if self.hybrid_ensemble:
                self.hybrid_ensemble.save(f"{self.model_path}/hybrid_ensemble_model.keras")
                self.logger.info("✅ Hybrid ensemble saved")

            self.logger.info("✅ All deep learning models saved successfully")

        except Exception as e:
            self.logger.error(f"Error saving models: {e}")

    def load_models(self):
        """Load pre-trained deep learning models"""
        try:
            lstm_path = f"{self.model_path}/hybrid_lstm_model.keras"
            cnn_path = f"{self.model_path}/hybrid_cnn_model.keras"
            ensemble_path = f"{self.model_path}/hybrid_ensemble_model.keras"

            models_loaded = 0

            if os.path.exists(lstm_path):
                try:
                    self.lstm_model = tf.keras.models.load_model(lstm_path)
                    self.logger.info("✅ LSTM model loaded")
                    models_loaded += 1
                except Exception as e:
                    self.logger.warning(f"⚠️ Could not load LSTM model: {e}")

            if os.path.exists(cnn_path):
                try:
                    self.cnn_model = tf.keras.models.load_model(cnn_path)
                    self.logger.info("✅ CNN model loaded")
                    models_loaded += 1
                except Exception as e:
                    self.logger.warning(f"⚠️ Could not load CNN model: {e}")

            if os.path.exists(ensemble_path):
                try:
                    self.hybrid_ensemble = tf.keras.models.load_model(ensemble_path)
                    self.logger.info("✅ Hybrid ensemble loaded")
                    models_loaded += 1
                except Exception as e:
                    self.logger.warning(f"⚠️ Could not load ensemble model: {e}")

            if models_loaded >= 2:  # At least 2 out of 3 models loaded
                self.is_trained = True
                self.logger.info(f"🚀 Deep learning models loaded successfully ({models_loaded}/3)")
                return True
            else:
                self.logger.warning(f"⚠️ Only {models_loaded}/3 models loaded - insufficient for operation")
                return False

        except Exception as e:
            self.logger.error(f"Error loading models: {e}")
            return False
