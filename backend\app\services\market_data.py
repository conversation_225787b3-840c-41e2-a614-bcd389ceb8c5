"""
Market Data Service for fetching real-time data from Binance API
Enhanced with ML forecasting and futures trading support
"""
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from flask import current_app
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_squared_error
import plotly.graph_objs as go
import hmac
import hashlib
import time

logger = logging.getLogger(__name__)

class BinanceMarketData:
    """Service for fetching market data from Binance API and Futures."""
    
    BASE_URL = "https://api.binance.com/api/v3"
    FUTURES_URL = "https://fapi.binance.com"
    
    # Hardcoded API credentials for market data (from trade.py)
    API_KEY = '73UnYTIA60rZV2c92ULMSUnEVHHjdaG5z1YAqlPz1oHT1ux1eQV5DZzLxFj21DNL'
    API_SECRET = 'wKPsGu54IFCtsGnEC5RooQy34lOmGRVJjRmivFtBP3tmPzWueee9PXzaVwm6ffYB'
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'DeepTrade/1.0'
        })
    
    def create_signed_params(self, params):
        """Create signed parameters for authenticated API calls."""
        query_string = '&'.join([f'{k}={v}' for k, v in params.items()])
        signature = hmac.new(self.API_SECRET.encode('utf-8'), query_string.encode('utf-8'), hashlib.sha256).hexdigest()
        params['signature'] = signature
        return params
    
    def get_futures_klines(self, symbol: str, interval: str = '1h', limit: int = 1000) -> List[Dict]:
        """
        Fetch futures kline/candlestick data from Binance Futures API.
        
        Args:
            symbol: Trading pair symbol (e.g., 'BTCUSDT')
            interval: Kline interval (1m, 5m, 15m, 30m, 1h, 4h, 1d, 1w)
            limit: Number of klines to retrieve (max 1000)
        
        Returns:
            List of kline data dictionaries
        """
        try:
            url = f"{self.FUTURES_URL}/fapi/v1/klines"
            params = {
                'symbol': symbol.upper(),
                'interval': interval,
                'limit': min(limit, 1000)
            }
            
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            klines_data = response.json()
            
            # Convert to more readable format
            klines = []
            for kline in klines_data:
                klines.append({
                    'timestamp': int(kline[0]),
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': float(kline[5]),
                    'close_time': int(kline[6]),
                    'quote_volume': float(kline[7]),
                    'trades_count': int(kline[8]),
                    'taker_buy_volume': float(kline[9]),
                    'taker_buy_quote_volume': float(kline[10])
                })
            
            return klines
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching futures klines for {symbol}: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error fetching futures klines: {e}")
            return []
    
    def get_futures_balance(self) -> Optional[float]:
        """
        Get USDT balance from Binance Futures account.
        
        Returns:
            USDT balance or None if error
        """
        try:
            endpoint = '/fapi/v2/account'
            timestamp = int(time.time() * 1000)

            params = {
                'timestamp': timestamp,
                'recvWindow': 5000
            }

            params = self.create_signed_params(params)

            response = self.session.get(self.FUTURES_URL + endpoint, params=params,
                                      headers={'X-MBX-APIKEY': self.API_KEY}, timeout=10)
            response.raise_for_status()
            
            data = response.json()

            for asset in data.get('assets', []):
                if asset['asset'] == 'USDT':
                    return float(asset['walletBalance'])
                    
            return None
            
        except Exception as e:
            logger.error(f"Error fetching USDT balance: {e}")
            return None
    
    def get_klines(self, symbol: str, interval: str = '1h', limit: int = 500) -> List[Dict]:
        """
        Fetch kline/candlestick data from Binance.
        
        Args:
            symbol: Trading pair symbol (e.g., 'BTCUSDT')
            interval: Kline interval (1m, 5m, 15m, 30m, 1h, 4h, 1d, 1w)
            limit: Number of klines to retrieve (max 1000)
        
        Returns:
            List of kline data dictionaries
        """
        try:
            url = f"{self.BASE_URL}/klines"
            params = {
                'symbol': symbol.upper(),
                'interval': interval,
                'limit': min(limit, 1000)
            }
            
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            klines_data = response.json()
            
            # Convert to more readable format
            klines = []
            for kline in klines_data:
                klines.append({
                    'timestamp': int(kline[0]),
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': float(kline[5]),
                    'close_time': int(kline[6]),
                    'quote_volume': float(kline[7]),
                    'trades_count': int(kline[8]),
                    'taker_buy_volume': float(kline[9]),
                    'taker_buy_quote_volume': float(kline[10])
                })
            
            return klines
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching klines for {symbol}: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error fetching klines: {e}")
            return []
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """
        Get current price for a symbol.
        
        Args:
            symbol: Trading pair symbol
            
        Returns:
            Current price or None if error
        """
        try:
            url = f"{self.BASE_URL}/ticker/price"
            params = {'symbol': symbol.upper()}
            
            response = self.session.get(url, params=params, timeout=5)
            response.raise_for_status()
            
            data = response.json()
            return float(data['price'])
            
        except Exception as e:
            logger.error(f"Error fetching current price for {symbol}: {e}")
            return None
    
    def get_24hr_ticker(self, symbol: str) -> Optional[Dict]:
        """
        Get 24hr ticker statistics.
        
        Args:
            symbol: Trading pair symbol
            
        Returns:
            24hr ticker data or None if error
        """
        try:
            url = f"{self.BASE_URL}/ticker/24hr"
            params = {'symbol': symbol.upper()}
            
            response = self.session.get(url, params=params, timeout=5)
            response.raise_for_status()
            
            data = response.json()
            return {
                'symbol': data['symbol'],
                'price_change': float(data['priceChange']),
                'price_change_percent': float(data['priceChangePercent']),
                'weighted_avg_price': float(data['weightedAvgPrice']),
                'prev_close_price': float(data['prevClosePrice']),
                'last_price': float(data['lastPrice']),
                'bid_price': float(data['bidPrice']),
                'ask_price': float(data['askPrice']),
                'open_price': float(data['openPrice']),
                'high_price': float(data['highPrice']),
                'low_price': float(data['lowPrice']),
                'volume': float(data['volume']),
                'quote_volume': float(data['quoteVolume'])
            }
            
        except Exception as e:
            logger.error(f"Error fetching 24hr ticker for {symbol}: {e}")
            return None


class MLPredictionService:
    """Service for generating ML-based price predictions."""
    
    def __init__(self):
        self.market_data = BinanceMarketData()
        # Caching variables to match trade.py approach
        self.last_candle_time = None
        self.cached_forecast = None
        self.cached_symbol = None
        self.cached_timeframe = None
        # Initialize cache dictionary
        self.cache = {}

    def _is_forecast_valid(self, forecast_data: dict) -> bool:
        """
        Check if a cached forecast is still valid (less than 1 hour old).

        Args:
            forecast_data: The cached forecast data dictionary

        Returns:
            bool: True if forecast is still valid, False otherwise
        """
        try:
            if 'generated_at' not in forecast_data:
                return False

            from datetime import datetime, timedelta

            # Parse the generated_at timestamp
            generated_at = datetime.fromisoformat(forecast_data['generated_at'].replace('Z', '+00:00'))
            generated_at = generated_at.replace(tzinfo=None)  # Remove timezone for comparison

            # Check if forecast is less than 1 hour old
            current_time = datetime.utcnow()
            time_diff = current_time - generated_at

            is_valid = time_diff < timedelta(hours=1)

            if not is_valid:
                logger.info(f"Forecast expired: generated {time_diff.total_seconds()/3600:.2f} hours ago")

            return is_valid

        except Exception as e:
            logger.warning(f"Error checking forecast validity: {e}")
            return False

    def clear_expired_forecasts(self) -> int:
        """
        Clear all expired forecasts from cache.

        Returns:
            int: Number of forecasts cleared
        """
        try:
            if not hasattr(self, 'cache'):
                self.cache = {}
                return 0

            expired_keys = []
            for cache_key, forecast_data in self.cache.items():
                if isinstance(forecast_data, dict) and not self._is_forecast_valid(forecast_data):
                    expired_keys.append(cache_key)

            for key in expired_keys:
                del self.cache[key]
                logger.info(f"Cleared expired forecast: {key}")

            return len(expired_keys)

        except Exception as e:
            logger.error(f"Error clearing expired forecasts: {e}")
            return 0

    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators from price data."""
        # Ensure numeric data types
        df['close'] = pd.to_numeric(df['close'], errors='coerce')
        df['high'] = pd.to_numeric(df['high'], errors='coerce')
        df['low'] = pd.to_numeric(df['low'], errors='coerce')
        df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
        
        # Simple Moving Averages
        df['sma_20'] = df['close'].rolling(window=20).mean()
        df['sma_50'] = df['close'].rolling(window=50).mean()
        
        # Exponential Moving Averages
        df['ema_12'] = df['close'].ewm(span=12).mean()
        df['ema_26'] = df['close'].ewm(span=26).mean()
        
        # MACD
        df['macd'] = df['ema_12'] - df['ema_26']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # RSI - using simple calculation
        delta = df['close'].diff()
        gain = delta.clip(lower=0)
        loss = (-delta).clip(lower=0)
        gain_avg = gain.rolling(window=14).mean()
        loss_avg = loss.rolling(window=14).mean()
        rs = gain_avg / (loss_avg + 1e-10)  # Add small value to avoid division by zero
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        
        # Volume indicators
        df['volume_sma'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        return df
    
    def calculate_support_resistance(self, df: pd.DataFrame, current_price: float) -> Tuple[float, float]:
        """Calculate dynamic support and resistance levels."""
        # Get recent highs and lows
        recent_highs = df['high'].tail(50)
        recent_lows = df['low'].tail(50)
        
        # Find potential resistance levels (previous highs near current price)
        resistance_candidates = recent_highs[recent_highs > current_price]
        if len(resistance_candidates) > 0:
            resistance = resistance_candidates.min()
        else:
            resistance = current_price * 1.05  # Default 5% above
        
        # Find potential support levels (previous lows near current price)
        support_candidates = recent_lows[recent_lows < current_price]
        if len(support_candidates) > 0:
            support = support_candidates.max()
        else:
            support = current_price * 0.95  # Default 5% below
        
        return support, resistance
    
    def generate_ensemble_forecast(self, symbol: str, timeframe: str = '1h', future_hours: int = 72) -> dict:
        """
        Generate an ensemble forecast using multiple ML models with optimizations.

        Args:
            symbol: Trading pair symbol (e.g., 'BTCUSDT')
            timeframe: Timeframe for the data (e.g., '1h', '4h', '1d')
            future_hours: Number of hours to forecast into the future

        Returns:
            dict: Dictionary containing forecast data and visualization
        """
        cache_key = f"forecast_{symbol}_{timeframe}"
        cached = getattr(self, 'cache', {}).get(cache_key)

        # Check if cached forecast is still valid (less than 1 hour old)
        if cached and self._is_forecast_valid(cached):
            logger.info(f"Using cached forecast for {symbol}")
            return cached
        elif cached:
            logger.info(f"Cached forecast for {symbol} is expired, generating new one")
            # Remove expired forecast from cache
            del self.cache[cache_key]

        try:
            # Get current market data
            logger.info(f"Generating new forecast for {symbol}")
            
            # Try futures API first, then fallback to spot API
            klines = self.market_data.get_futures_klines(symbol, timeframe, limit=1000)
            if not klines or len(klines) < 300:
                logger.warning(f"Insufficient futures data for {symbol}, trying spot API")
                klines = self.market_data.get_klines(symbol, timeframe, limit=1000)
            
            if not klines or len(klines) < 300:
                logger.error(f"Insufficient data for {symbol} (need at least 300 data points)")
                return self._generate_mock_forecast(symbol, timeframe, future_hours)
            
            # Convert to DataFrame
            df = pd.DataFrame(klines)
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('datetime', inplace=True)
            
            # Ensure numeric data types
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Get current price
            current_price = float(df['close'].iloc[-1])
            
            # Prepare data with optimized parameters
            scaler = RobustScaler()
            # Convert to numpy array and ensure 2D array for scaler
            close_prices = np.array(df['close']).reshape(-1, 1)
            scaled_data = scaler.fit_transform(close_prices)
            
            def prepare_data(data, time_steps):
                X, y = [], []
                for i in range(len(data) - time_steps):
                    X.append(data[i:(i + time_steps)].flatten())
                    y.append(data[i + time_steps][0])
                return np.array(X), np.array(y)
            
            
            
            
            
            
            
            
            # Using fixed time_steps=288 (12 days of hourly data)
            time_steps = 288
            X, y = prepare_data(scaled_data, time_steps)
            
            # Split data (80% train, 20% validation)
            split_ratio = 0.8
            split_index = max(1, int(split_ratio * len(X)))
            X_train, X_test = X[:split_index], X[split_index:]
            y_train, y_test = y[:split_index], y[split_index:]
            
            # Initialize models with optimized parameters
            rf_model = RandomForestRegressor(n_estimators=200, n_jobs=-1, random_state=42)
            gb_model = GradientBoostingRegressor(n_estimators=100, learning_rate=0.1, random_state=42)
            lr_model = LinearRegression(n_jobs=-1)
            
            # Train models in parallel
            models = [rf_model, gb_model, lr_model]
            for model in models:
                model.fit(X_train, y_train)
            
            # Calculate model weights based on validation performance
            rf_pred_val = rf_model.predict(X_test)
            gb_pred_val = gb_model.predict(X_test)
            lr_pred_val = lr_model.predict(X_test)
            
            rf_score = 1 / (mean_squared_error(y_test, rf_pred_val) + 1e-6)
            gb_score = 1 / (mean_squared_error(y_test, gb_pred_val) + 1e-6)
            lr_score = 1 / (mean_squared_error(y_test, lr_pred_val) + 1e-6)
            
            total_score = rf_score + gb_score + lr_score
            rf_weight = rf_score / total_score
            gb_weight = gb_score / total_score
            lr_weight = lr_score / total_score
            
            logger.info(f"Model weights - RF: {rf_weight:.2f}, GB: {gb_weight:.2f}, LR: {lr_weight:.2f}")
            
            # Generate predictions with exponential smoothing
            alpha = 0.7  # Smoothing factor
            last_pred = scaled_data[-1][0]  # Initialize with last known value
            predicted_prices_scaled = []
            X_future = scaled_data[-time_steps:].reshape(1, -1)
            
            for _ in range(future_hours):
                # Get predictions from all models
                rf_pred = rf_model.predict(X_future)
                gb_pred = gb_model.predict(X_future)
                lr_pred = lr_model.predict(X_future)
                
                # Weighted ensemble prediction
                ensemble_pred = (rf_weight * rf_pred +
                              gb_weight * gb_pred +
                              lr_weight * lr_pred)
                
                # Apply exponential smoothing
                smoothed_pred = alpha * ensemble_pred + (1 - alpha) * last_pred
                last_pred = smoothed_pred[0]
                predicted_prices_scaled.append(last_pred)
                
                # Update future window
                X_future = np.append(X_future[:, 1:], smoothed_pred.reshape(1, 1), axis=1)
            
            # Convert scaled predictions back to original scale
            predicted_prices = scaler.inverse_transform(np.array(predicted_prices_scaled).reshape(-1, 1)).flatten()
            
            # Calculate price levels and other metrics
            highest_price = np.max(predicted_prices)
            lowest_price = np.min(predicted_prices)
            upper_price_levels = predicted_prices * 1.01
            lower_price_levels = predicted_prices * 0.99
            highest_upper_price = np.max(upper_price_levels)
            lowest_lower_price = np.min(lower_price_levels)
            
            # Calculate support and resistance
            support, resistance = self.calculate_support_resistance(df, current_price)
            
            # Generate forecast chart HTML
            chart_html = self.plot_forecast_chart(df, predicted_prices, upper_price_levels, lower_price_levels, symbol)
            
            # Prepare historical data for the response (last 120 hours)
            historical_dates = [dt.strftime('%Y-%m-%d %H:%M:%S') for dt in df.index[-120:]]
            historical_opens = df['open'].astype(float).round(2).tolist()[-120:]
            historical_highs = df['high'].astype(float).round(2).tolist()[-120:]
            historical_lows = df['low'].astype(float).round(2).tolist()[-120:]
            historical_closes = df['close'].astype(float).round(2).tolist()[-120:]
            
            # Generate forecast dates (next 72 hours from last historical date)
            last_date = df.index[-1]
            forecast_dates = pd.date_range(
                start=last_date + pd.Timedelta(hours=1),
                periods=len(predicted_prices),
                freq='h'
            ).strftime('%Y-%m-%d %H:%M:%S').tolist()
            
            # Create forecast result with historical data
            forecast_result = {
                'symbol': symbol,
                'timeframe': timeframe,
                'current_price': float(round(current_price, 2)),
                'forecast': [round(float(p), 2) for p in predicted_prices],
                'upper_levels': [round(float(p), 2) for p in upper_price_levels],
                'lower_levels': [round(float(p), 2) for p in lower_price_levels],
                'historical_dates': historical_dates,
                'historical_opens': historical_opens,
                'historical_highs': historical_highs,
                'historical_lows': historical_lows,
                'historical_closes': historical_closes,
                'forecast_dates': forecast_dates,
                'highest_price': float(round(highest_price, 2)),
                'lowest_price': float(round(lowest_price, 2)),
                'highest_upper_price': float(round(highest_upper_price, 2)),
                'lowest_lower_price': float(round(lowest_lower_price, 2)),
                'support_level': float(round(support, 2)),
                'resistance_level': float(round(resistance, 2)),
                'chart_html': chart_html,
                'model_weights': {
                    'random_forest': float(round(rf_weight, 4)),
                    'gradient_boosting': float(round(gb_weight, 4)),
                    'linear_regression': float(round(lr_weight, 4))
                },
                'generated_at': datetime.utcnow().isoformat() + 'Z',
                'cached_at': datetime.utcnow().isoformat() + 'Z'
            }
            
            # Cache should already be initialized in __init__, but double-check
            if not hasattr(self, 'cache'):
                self.cache = {}

            # Cache the result
            self.cache[cache_key] = forecast_result
            logger.info(f"Forecast cached for {symbol} (expires in 1 hour)")

            return forecast_result
            
        except Exception as e:
            logger.error(f"Error in generate_ensemble_forecast: {str(e)}", exc_info=True)
            # Use mock forecast with default values since we can't generate a real one
            return self._generate_mock_forecast(symbol, timeframe, future_hours)
    
    def plot_forecast_chart(self, historical_df: pd.DataFrame, forecast: np.ndarray,
                           upper_levels: np.ndarray, lower_levels: np.ndarray,
                           symbol: str) -> Optional[str]:
        """Generate interactive Plotly chart HTML for forecast visualization."""
        try:
            # Get the last 5 days of data for the candlestick chart
            historical_data = historical_df.tail(120)  # Last 120 hours (5 days)
            
            # Create figure
            fig = go.Figure()
            
            # Add candlestick chart
            fig.add_trace(go.Candlestick(
                x=historical_data.index,
                open=historical_data['open'],
                high=historical_data['high'],
                low=historical_data['low'],
                close=historical_data['close'],
                name='Current Price',
                increasing_line_color='#22C55E',
                decreasing_line_color='#EF4444',
                increasing_fillcolor='rgba(34, 197, 94, 0.6)',
                decreasing_fillcolor='rgba(239, 68, 68, 0.6)'
            ))
            
            # Create future dates for forecast
            last_timestamp = historical_data.index[-1]
            future_index = pd.date_range(start=last_timestamp, periods=len(forecast), freq='h')
            
            # Add prediction traces
            fig.add_trace(go.Scatter(
                x=future_index,
                y=forecast,
                mode='lines',
                name='Predicted Price',
                line=dict(
                    color='rgba(14, 165, 233, 0.9)',
                    width=2.5,
                    dash='dot'
                ),
                hovertemplate='Price: $%{y:.4f}<extra></extra>'
            ))
            
            # Add price bounds
            fig.add_trace(go.Scatter(
                x=future_index,
                y=lower_levels,
                mode='lines',
                showlegend=False,
                line=dict(color='rgba(34, 197, 94, 0.4)', width=0),
                hovertemplate='Lower: $%{y:.4f}<extra></extra>'
            ))
            
            fig.add_trace(go.Scatter(
                x=future_index,
                y=upper_levels,
                mode='lines',
                name='Price Range',
                fill='tonexty',
                fillcolor='rgba(34, 197, 94, 0.15)',
                line=dict(color='rgba(34, 197, 94, 0.4)', width=0),
                hovertemplate='Upper: $%{y:.4f}<extra></extra>'
            ))
            
            # Create timestamp for display
            from datetime import datetime
            timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')
            
            # Update layout
            fig.update_layout(
                title={
                    'text': f'{symbol} ML Price Forecast<br><sub style="font-size: 12px; color: #6B7280;">Generated: {timestamp}</sub>',
                    'y': 0.95,
                    'x': 0.5,
                    'xanchor': 'center',
                    'yanchor': 'top',
                    'font': dict(size=18)
                },
                xaxis_title='Time (UTC)',
                yaxis_title='Price (USD)',
                plot_bgcolor='rgba(0,0,0,0.02)',  # Light gray background
                paper_bgcolor='rgba(255,255,255,0.8)',  # Slightly transparent white
                xaxis=dict(
                    showgrid=True,
                    gridcolor='rgba(0,0,0,0.05)',  # Light gray grid
                    showline=True,
                    linecolor='rgba(0,0,0,0.1)',  # Light gray border
                    mirror=True
                ),
                yaxis=dict(
                    showgrid=True,
                    gridcolor='rgba(0,0,0,0.05)',  # Light gray grid
                    showline=True,
                    linecolor='rgba(0,0,0,0.1)',  # Light gray border
                    mirror=True
                ),
                legend=dict(
                    yanchor='top',
                    y=0.99,
                    xanchor='left',
                    x=0.01,
                    bgcolor='rgba(255, 255, 255, 0.85)',
                    bordercolor='rgba(0, 0, 0, 0.15)',
                    borderwidth=1,
                    font=dict(size=10, color='#1E293B')
                ),
                margin=dict(l=40, r=40, t=60, b=40, pad=10),  # Added padding
                autosize=True,
                height=400,  # Slightly taller to accommodate borders
                xaxis_rangeslider_visible=False,
                font=dict(family='Arial, sans-serif', size=11, color='#2E4053'),
                hovermode='x unified',
                modebar_orientation='h',
                shapes=[
                    # Add a rectangle around the plot area
                    dict(
                        type="rect",
                        xref="paper",
                        yref="paper",
                        x0=0,
                        y0=0,
                        x1=1,
                        y1=1,
                        line=dict(
                            color="rgba(0,0,0,0.1)",  # Light gray border
                            width=1,
                        ),
                    )
                ],
                annotations=[
                    dict(
                        text=f"Last Updated: {timestamp}",
                        xref="paper", yref="paper",
                        x=1, y=0,
                        xanchor='right', yanchor='bottom',
                        showarrow=False,
                        font=dict(size=10, color='#9CA3AF'),
                        bgcolor='rgba(255, 255, 255, 0.8)',
                        bordercolor='rgba(156, 163, 175, 0.3)',
                        borderwidth=1
                    )
                ]
            )
            
            # Return HTML with responsive configuration
            config = {
                'responsive': True,
                'displayModeBar': True,
                'displaylogo': False,
                'modeBarButtonsToRemove': ['pan2d', 'lasso2d', 'select2d', 'zoomIn', 'zoomOut', 'autoScale', 'resetScale'],
                'scrollZoom': False,
                'staticPlot': False,
                'toImageButtonOptions': {
                    'format': 'png',
                    'filename': f'{symbol}_forecast',
                    'height': 500,
                    'width': 1000,
                    'scale': 1
                }
            }
            return fig.to_html(full_html=False, include_plotlyjs='cdn', config=config)
            
        except Exception as e:
            logger.error(f"Error generating forecast chart: {e}")
            return None
    
    def generate_prediction(self, symbol: str, timeframe: str = '1h') -> Optional[Dict]:
        """
        Generate ML-based price prediction.
        
        Args:
            symbol: Trading pair symbol
            timeframe: Timeframe for analysis
            
        Returns:
            Prediction dictionary or None if error
        """
        try:
            # Fetch historical data
            klines = self.market_data.get_klines(symbol, timeframe, limit=200)
            
            if not klines or len(klines) < 50:
                logger.error(f"Insufficient data for {symbol}")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(klines)
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # Calculate technical indicators
            df = self.calculate_technical_indicators(df)
            
            # Get current price
            current_price = self.market_data.get_current_price(symbol)
            if not current_price:
                current_price = df['close'].iloc[-1]
            
            # Calculate support and resistance
            support, resistance = self.calculate_support_resistance(df, current_price)
            
            # Generate prediction using technical analysis
            prediction = self._analyze_market_conditions(df, current_price, support, resistance)
            
            # Get 24hr ticker for additional context
            ticker = self.market_data.get_24hr_ticker(symbol)
            
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'current_price': current_price,
                'prediction': prediction,
                'market_context': {
                    'price_change_24h': ticker['price_change_percent'] if ticker else 0,
                    'volume_24h': ticker['volume'] if ticker else 0,
                    'high_24h': ticker['high_price'] if ticker else current_price,
                    'low_24h': ticker['low_price'] if ticker else current_price
                },
                'generated_at': datetime.utcnow().isoformat() + 'Z'
            }
            
        except Exception as e:
            logger.error(f"Error generating prediction for {symbol}: {e}")
            return None
    
    def _analyze_market_conditions(self, df: pd.DataFrame, current_price: float, 
                                 support: float, resistance: float) -> Dict:
        """Analyze market conditions and generate prediction."""
        latest = df.iloc[-1]
        
        # Technical indicators analysis
        rsi = latest['rsi'] if not pd.isna(latest['rsi']) else 50
        macd = latest['macd'] if not pd.isna(latest['macd']) else 0
        macd_signal = latest['macd_signal'] if not pd.isna(latest['macd_signal']) else 0
        
        # Price position relative to moving averages
        sma_20 = latest['sma_20'] if not pd.isna(latest['sma_20']) else current_price
        sma_50 = latest['sma_50'] if not pd.isna(latest['sma_50']) else current_price
        
        # Volume analysis
        volume_ratio = latest['volume_ratio'] if not pd.isna(latest['volume_ratio']) else 1
        
        # Trend analysis
        short_term_trend = 1 if current_price > sma_20 else -1
        long_term_trend = 1 if sma_20 > sma_50 else -1
        
        # Momentum analysis
        momentum_score = 0
        
        # RSI signals
        if rsi < 30:
            momentum_score += 1  # Oversold, potential buy
        elif rsi > 70:
            momentum_score -= 1  # Overbought, potential sell
        
        # MACD signals
        if macd > macd_signal:
            momentum_score += 0.5  # Bullish momentum
        else:
            momentum_score -= 0.5  # Bearish momentum
        
        # Trend signals
        momentum_score += short_term_trend * 0.3
        momentum_score += long_term_trend * 0.2
        
        # Volume confirmation
        if volume_ratio > 1.2:
            momentum_score += 0.3  # High volume confirms move
        
        # Determine direction and confidence
        if momentum_score > 0.5:
            direction = 'bullish'
            confidence = min(0.85, 0.5 + abs(momentum_score) * 0.2)
        elif momentum_score < -0.5:
            direction = 'bearish'
            confidence = min(0.85, 0.5 + abs(momentum_score) * 0.2)
        else:
            direction = 'neutral'
            confidence = 0.5
        
        # Calculate target price based on direction
        if direction == 'bullish':
            target_price = current_price + (resistance - current_price) * confidence
        elif direction == 'bearish':
            target_price = current_price - (current_price - support) * confidence
        else:
            target_price = current_price
        
        return {
            'direction': direction,
            'confidence': round(confidence, 2),
            'target_price': round(target_price, 2),
            'support_level': round(support, 2),
            'resistance_level': round(resistance, 2),
            'technical_analysis': {
                'rsi': round(rsi, 2),
                'macd': round(macd, 4),
                'price_vs_sma20': round((current_price / sma_20 - 1) * 100, 2),
                'volume_ratio': round(volume_ratio, 2),
                'momentum_score': round(momentum_score, 2)
            }
        }
    
    def _generate_mock_forecast(self, symbol: str, timeframe: str, future_hours: int) -> Dict:
        """Generate mock forecast data when API is unavailable."""
        import random
        
        # Generate mock current price based on symbol
        base_price = 100000 if 'BTC' in symbol else 3000 if 'ETH' in symbol else 100
        current_price = base_price + random.uniform(-base_price * 0.05, base_price * 0.05)
        
        # Generate mock forecast data
        forecast_prices = []
        price = current_price
        
        for i in range(future_hours):
            # Random walk with slight upward bias
            change_percent = random.uniform(-0.02, 0.025)
            price = price * (1 + change_percent)
            forecast_prices.append(price)
        
        highest_price = max(forecast_prices)
        lowest_price = min(forecast_prices)
        
        # Generate mock support/resistance
        support = current_price * 0.95
        resistance = current_price * 1.05
        
        # Generate timestamp for mock chart
        timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')
        
        # Generate mock chart HTML
        mock_chart_html = f'''
        <div style="text-align: center; padding: 40px; background: #f8f9fa; border-radius: 8px; border: 2px dashed #dee2e6;">
            <h3 style="color: #6c757d; margin-bottom: 10px;">📊 Mock Forecast Chart</h3>
            <p style="color: #9ca3af; font-size: 11px; margin-bottom: 20px;">Generated: {timestamp}</p>
            <p style="color: #495057; margin-bottom: 10px;"><strong>{symbol}</strong> Price Forecast</p>
            <p style="color: #007bff; font-size: 24px; font-weight: bold; margin: 20px 0;">
                Current: ${current_price:,.2f}
            </p>
            <div style="display: flex; justify-content: space-around; margin: 20px 0;">
                <div>
                    <span style="color: #28a745; font-weight: bold;">📈 High: ${highest_price:,.2f}</span>
                </div>
                <div>
                    <span style="color: #dc3545; font-weight: bold;">📉 Low: ${lowest_price:,.2f}</span>
                </div>
            </div>
            <p style="color: #6c757d; font-size: 12px; margin-top: 30px;">
                ⚠️ Mock data - API temporarily unavailable<br>
                Real-time data will load when connection is restored
            </p>
        </div>
        '''
        
        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'current_price': current_price,
            'forecast': forecast_prices,
            'upper_levels': [p * 1.01 for p in forecast_prices],
            'lower_levels': [p * 0.99 for p in forecast_prices],
            'highest_price': highest_price,
            'lowest_price': lowest_price,
            'highest_upper_price': highest_price * 1.01,
            'lowest_lower_price': lowest_price * 0.99,
            'support_level': support,
            'resistance_level': resistance,
            'chart_html': mock_chart_html,
            'model_weights': {
                'random_forest': 0.33,
                'gradient_boosting': 0.33,
                'linear_regression': 0.34
            },
            'generated_at': datetime.utcnow().isoformat() + 'Z',
            'is_mock': True
        }


# Global instance
ml_service = MLPredictionService()