"""
Paper Trading Exchange Service

This service simulates real exchange behavior for paper trading.
It uses live market data but executes trades virtually without real money.
"""

import time
import random
from decimal import Decimal
from typing import Dict, Optional, List
from flask import current_app

from app.services.exchange_service import ExchangeService
from app.services.market_data import BinanceMarketData
from app.models.paper_trading import PaperTradingAccount, PaperTrade, PaperTradeStatus, PaperTradeSide
from app import db


class PaperExchangeService(ExchangeService):
    """
    Simulated exchange service for paper trading.
    Implements the same interface as real exchange services but executes trades virtually.
    """
    
    def __init__(self, user_id: str):
        """Initialize paper exchange service for a specific user."""
        self.user_id = user_id
        self.market_data = BinanceMarketData()
        self.simulated_latency = True  # Add realistic latency to simulate real trading
        self.fee_rate = Decimal('0.001')  # 0.1% simulated trading fee

        # Get or create paper trading account
        self.paper_account = self._get_or_create_paper_account()
    
    def _get_or_create_paper_account(self) -> PaperTradingAccount:
        """Get existing paper trading account or create new one."""
        account = PaperTradingAccount.query.filter_by(user_id=self.user_id).first()
        
        if not account:
            # Create new paper trading account with default balance
            initial_balance = current_app.config.get('PAPER_TRADING_INITIAL_BALANCE', 10000)
            account = PaperTradingAccount(
                user_id=self.user_id,
                initial_balance=initial_balance
            )
            db.session.add(account)
            db.session.commit()
            current_app.logger.info(f"Created new paper trading account for user {self.user_id} with balance {initial_balance}")
        
        return account
    
    def _simulate_network_latency(self):
        """Simulate realistic network latency for paper trading."""
        if self.simulated_latency:
            # Simulate 50-200ms latency like real exchanges
            latency = random.uniform(0.05, 0.2)
            time.sleep(latency)
    
    def _add_price_slippage(self, price: float, side: str) -> float:
        """Add realistic price slippage to simulate market conditions."""
        # Simulate 0.01-0.05% slippage
        slippage_pct = random.uniform(0.0001, 0.0005)
        
        if side.upper() in ['BUY', 'LONG']:
            # Buy orders get slightly worse price (higher)
            return price * (1 + slippage_pct)
        else:
            # Sell orders get slightly worse price (lower)
            return price * (1 - slippage_pct)
    
    def get_balance(self, quote_asset: str = 'USDT') -> Decimal:
        """Get virtual balance from paper trading account."""
        try:
            self._simulate_network_latency()
            return self.paper_account.virtual_balance
        except Exception as e:
            current_app.logger.error(f"Error getting paper trading balance for user {self.user_id}: {e}")
            return Decimal('0')
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price using real market data."""
        try:
            self._simulate_network_latency()
            
            # Use real market data service to get current price
            price = self.market_data.get_current_price(symbol)
            
            if price:
                current_app.logger.debug(f"Paper trading - Current price for {symbol}: {price}")
                return float(price)
            
            return None
            
        except Exception as e:
            current_app.logger.error(f"Error getting current price for {symbol} in paper trading: {e}")
            return None
    
    def place_order(self, symbol: str, side: str, quantity: float, price: Optional[float] = None) -> Dict:
        """
        Simulate placing an order in paper trading.
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            side: Order side ('BUY', 'SELL', 'LONG', 'SHORT')
            quantity: Order quantity
            price: Limit price (None for market order)
        
        Returns:
            Dict with order result
        """
        try:
            self._simulate_network_latency()
            
            # Get current market price
            current_price = self.get_current_price(symbol)
            if not current_price:
                return {
                    'error': f'Unable to get current price for {symbol}',
                    'success': False
                }
            
            # Use market price for market orders, or limit price for limit orders
            execution_price = price if price else current_price
            
            # Add realistic slippage for market orders
            if not price:
                execution_price = self._add_price_slippage(execution_price, side)
            
            # Calculate trade value
            trade_value = Decimal(str(quantity)) * Decimal(str(execution_price))
            
            # Check if user has sufficient virtual balance
            required_balance = trade_value  # For simplicity, require full trade value
            if self.paper_account.virtual_balance < required_balance:
                return {
                    'error': f'Insufficient virtual balance. Required: {required_balance}, Available: {self.paper_account.virtual_balance}',
                    'success': False
                }
            
            # Create paper trade record
            paper_trade = PaperTrade(
                user_id=self.user_id,
                paper_account_id=self.paper_account.id,
                symbol=symbol,
                side=PaperTradeSide(side.lower()),
                quantity=quantity,
                entry_price=execution_price,
                source='app'
            )
            
            db.session.add(paper_trade)
            
            # Update virtual balance (deduct trade value for long positions)
            if side.upper() in ['BUY', 'LONG']:
                self.paper_account.update_balance(
                    new_balance=self.paper_account.virtual_balance - trade_value,
                    pnl_change=-trade_value,
                    transaction_type='trade'
                )
            
            db.session.commit()
            
            # Simulate order ID
            simulated_order_id = f"paper_{int(time.time() * 1000)}_{random.randint(1000, 9999)}"
            
            current_app.logger.info(f"Paper trade executed for user {self.user_id}: {symbol} {side} {quantity} @ {execution_price}")
            
            return {
                'success': True,
                'orderId': simulated_order_id,
                'symbol': symbol,
                'side': side,
                'quantity': str(quantity),
                'price': str(execution_price),
                'status': 'FILLED',
                'type': 'MARKET' if not price else 'LIMIT',
                'paper_trade_id': paper_trade.id
            }
            
        except Exception as e:
            current_app.logger.error(f"Error placing paper order for user {self.user_id}: {e}")
            db.session.rollback()
            return {
                'error': f'Failed to place paper order: {str(e)}',
                'success': False
            }
    
    def get_open_positions(self) -> List[Dict]:
        """Get open paper trading positions."""
        try:
            self._simulate_network_latency()
            
            open_trades = PaperTrade.query.filter_by(
                user_id=self.user_id,
                status=PaperTradeStatus.OPEN,
                archived=False
            ).all()
            
            positions = []
            for trade in open_trades:
                # Get current price for P&L calculation
                current_price = self.get_current_price(trade.symbol)
                current_pnl = trade.calculate_pnl(current_price) if current_price else Decimal('0')
                
                positions.append({
                    'symbol': trade.symbol,
                    'side': trade.side.value,
                    'size': str(trade.quantity),
                    'entryPrice': str(trade.entry_price),
                    'markPrice': str(current_price) if current_price else '0',
                    'pnl': str(current_pnl),
                    'percentage': str((current_pnl / (trade.quantity * trade.entry_price)) * 100) if trade.quantity * trade.entry_price > 0 else '0',
                    'paper_trade_id': trade.id,
                    'stopLoss': str(trade.stop_loss) if trade.stop_loss else None,
                    'takeProfit': str(trade.take_profit) if trade.take_profit else None
                })
            
            return positions
            
        except Exception as e:
            current_app.logger.error(f"Error getting paper trading positions for user {self.user_id}: {e}")
            return []
    
    def get_open_orders(self, symbol: str) -> List[Dict]:
        """Get open orders (simulated - paper trading executes immediately)."""
        # Paper trading executes orders immediately, so no pending orders
        return []
    
    def cancel_order(self, symbol: str, order_id: str) -> bool:
        """Cancel order (simulated)."""
        # Paper trading executes immediately, so cancellation is not applicable
        return True
    
    def cancel_all_orders(self, symbol: str) -> bool:
        """Cancel all orders (simulated)."""
        # Paper trading executes immediately, so cancellation is not applicable
        return True
    
    def set_margin_mode(self, symbol: str, margin_mode: str) -> bool:
        """Set margin mode (simulated)."""
        # Paper trading doesn't require actual margin mode setting
        current_app.logger.debug(f"Paper trading - Set margin mode for {symbol}: {margin_mode}")
        return True
    
    def set_leverage(self, symbol: str, leverage: int) -> bool:
        """Set leverage (simulated)."""
        # Paper trading doesn't require actual leverage setting
        current_app.logger.debug(f"Paper trading - Set leverage for {symbol}: {leverage}x")
        return True
    
    def close_position(self, paper_trade_id: str, exit_reason: str = 'manual') -> Dict:
        """Close a paper trading position."""
        try:
            trade = PaperTrade.query.filter_by(
                id=paper_trade_id,
                user_id=self.user_id,
                status=PaperTradeStatus.OPEN
            ).first()
            
            if not trade:
                return {
                    'error': 'Trade not found or already closed',
                    'success': False
                }
            
            # Get current market price
            current_price = self.get_current_price(trade.symbol)
            if not current_price:
                return {
                    'error': f'Unable to get current price for {trade.symbol}',
                    'success': False
                }
            
            # Add slippage for market close
            exit_price = self._add_price_slippage(current_price, 'SELL' if trade.is_long() else 'BUY')
            
            # Close the trade
            trade.close_trade(exit_price, exit_reason)
            
            # Update account balance with P&L
            if trade.pnl:
                new_balance = self.paper_account.virtual_balance + trade.pnl
                self.paper_account.update_balance(
                    new_balance=new_balance,
                    pnl_change=trade.pnl,
                    transaction_type='trade'
                )
            
            # Update account performance metrics
            self.paper_account.calculate_performance_metrics()
            
            db.session.commit()
            
            current_app.logger.info(f"Paper trade closed for user {self.user_id}: {trade.symbol} P&L: {trade.pnl}")
            
            return {
                'success': True,
                'trade_id': trade.id,
                'exit_price': str(exit_price),
                'pnl': str(trade.pnl),
                'exit_reason': exit_reason
            }
            
        except Exception as e:
            current_app.logger.error(f"Error closing paper trade {paper_trade_id} for user {self.user_id}: {e}")
            db.session.rollback()
            return {
                'error': f'Failed to close paper trade: {str(e)}',
                'success': False
            }

    def validate_credentials(self) -> tuple:
        """Validate paper trading credentials (always valid)."""
        return True, {
            'permissions': ['read', 'trade'],
            'account_type': 'paper',
            'status': 'active'
        }

    def get_account_info(self) -> Dict:
        """Get paper trading account information."""
        try:
            return {
                'account_type': 'paper',
                'balance': float(self.get_balance()),
                'currency': 'USDT',
                'user_id': self.user_id,
                'account_id': self.paper_account.id,
                'status': 'active'
            }
        except Exception as e:
            current_app.logger.error(f"Error getting account info for user {self.user_id}: {e}")
            return {}
