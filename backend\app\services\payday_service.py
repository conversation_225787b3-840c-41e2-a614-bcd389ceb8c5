"""
Service for managing Saturday payday limits and account enforcement.
"""
from datetime import datetime, timedelta
from app import db
from app.models.user_tier_status import UserTierStatus
from app.models.user import User
from app.services.notification_service import NotificationService
from app.services.email_service import EmailService
from app.services.localized_email_service import localized_email_service
import logging

logger = logging.getLogger(__name__)

class PaydayService:
    """Service for handling Saturday payday limits and account management."""
    
    @staticmethod
    def initialize_payday_deadlines():
        """Initialize payday deadlines for all users who don't have one set."""
        try:
            users_without_deadline = UserTierStatus.query.filter(
                UserTierStatus.next_payday_deadline == None
            ).all()
            
            updated_count = 0
            for tier_status in users_without_deadline:
                tier_status.update_payday_deadline()
                updated_count += 1
            
            if updated_count > 0:
                db.session.commit()
                logger.info(f"Initialized payday deadlines for {updated_count} users")
            
            return updated_count
            
        except Exception as e:
            logger.error(f"Error initializing payday deadlines: {str(e)}")
            db.session.rollback()
            return 0
    
    @staticmethod
    def send_email_warnings():
        """Send localized email warnings to users 3, 2, and 1 days before deadline."""
        try:
            # Use localized email service for multi-language support
            total_emails_sent = 0

            # Get users who need email warnings
            users_needing_warnings = UserTierStatus.query.filter(
                UserTierStatus.profit_share_owed > 0,
                UserTierStatus.payment_status == 'unpaid',
                UserTierStatus.next_payday_deadline != None
            ).all()

            for tier_status in users_needing_warnings:
                user = User.query.get(tier_status.user_id)
                if not user or not user.email:
                    continue

                emails_sent_for_user = 0

                # Check for 3-day warning
                if tier_status.needs_email_warning_3_days():
                    if localized_email_service.send_payday_warning_email(user, tier_status, 3):
                        tier_status.mark_email_warning_sent(3)
                        emails_sent_for_user += 1
                        logger.info(f"Sent localized 3-day email warning to user {user.id} in {user.language_preference or 'en'}")

                # Check for 2-day warning
                if tier_status.needs_email_warning_2_days():
                    if localized_email_service.send_payday_warning_email(user, tier_status, 2):
                        tier_status.mark_email_warning_sent(2)
                        emails_sent_for_user += 1
                        logger.info(f"Sent localized 2-day email warning to user {user.id} in {user.language_preference or 'en'}")

                # Check for 1-day warning
                if tier_status.needs_email_warning_1_day():
                    if localized_email_service.send_payday_warning_email(user, tier_status, 1):
                        tier_status.mark_email_warning_sent(1)
                        emails_sent_for_user += 1
                        logger.info(f"Sent localized 1-day email warning to user {user.id} in {user.language_preference or 'en'}")

                total_emails_sent += emails_sent_for_user

            if total_emails_sent > 0:
                db.session.commit()
                logger.info(f"Sent {total_emails_sent} email warnings")

            return total_emails_sent

        except Exception as e:
            logger.error(f"Error sending email warnings: {str(e)}")
            db.session.rollback()
            return 0

    @staticmethod
    def send_payday_warnings():
        """Send payday warning notifications to users 24 hours before deadline."""
        try:
            users_needing_warning = UserTierStatus.query.filter(
                UserTierStatus.profit_share_owed > 0,
                UserTierStatus.payment_status == 'unpaid',
                UserTierStatus.payday_warning_sent == False,
                UserTierStatus.next_payday_deadline != None
            ).all()
            
            warning_count = 0
            for tier_status in users_needing_warning:
                if tier_status.needs_payday_warning():
                    # Send warning notification
                    user = User.query.get(tier_status.user_id)
                    if user:
                        PaydayService._send_payday_warning_notification(user, tier_status)
                        tier_status.mark_payday_warning_sent()
                        warning_count += 1
            
            if warning_count > 0:
                db.session.commit()
                logger.info(f"Sent payday warnings to {warning_count} users")
            
            return warning_count
            
        except Exception as e:
            logger.error(f"Error sending payday warnings: {str(e)}")
            db.session.rollback()
            return 0
    
    @staticmethod
    def enforce_payday_limits():
        """Disable accounts for users who haven't paid by Saturday 0:00 GMT."""
        try:
            users_to_disable = UserTierStatus.query.filter(
                UserTierStatus.profit_share_owed > 0,
                UserTierStatus.payment_status == 'unpaid',
                UserTierStatus.account_disabled == False,
                UserTierStatus.next_payday_deadline != None
            ).all()
            
            disabled_count = 0
            for tier_status in users_to_disable:
                if tier_status.should_disable_account():
                    # Disable account
                    tier_status.disable_account()
                    
                    # Update next payday deadline for next week
                    tier_status.update_payday_deadline()
                    
                    # Send account disabled notifications
                    user = User.query.get(tier_status.user_id)
                    if user:
                        PaydayService._send_account_disabled_notification(user, tier_status)
                        # Also send email notification
                        try:
                            email_service = EmailService()
                            email_service.send_account_disabled_email(user, tier_status)
                        except Exception as email_error:
                            logger.error(f"Failed to send account disabled email to user {user.id}: {str(email_error)}")
                    
                    disabled_count += 1
                    logger.info(f"Disabled account for user {tier_status.user_id} due to unpaid fees")
            
            if disabled_count > 0:
                db.session.commit()
                logger.info(f"Disabled {disabled_count} accounts for unpaid fees")
            
            return disabled_count
            
        except Exception as e:
            logger.error(f"Error enforcing payday limits: {str(e)}")
            db.session.rollback()
            return 0
    
    @staticmethod
    def update_all_payday_deadlines():
        """Update payday deadlines for all users to next Saturday."""
        try:
            all_tier_statuses = UserTierStatus.query.all()
            
            updated_count = 0
            for tier_status in all_tier_statuses:
                tier_status.update_payday_deadline()
                updated_count += 1
            
            if updated_count > 0:
                db.session.commit()
                logger.info(f"Updated payday deadlines for {updated_count} users")
            
            return updated_count
            
        except Exception as e:
            logger.error(f"Error updating payday deadlines: {str(e)}")
            db.session.rollback()
            return 0
    
    @staticmethod
    def get_users_with_outstanding_debt():
        """Get users who have outstanding profit share debt."""
        unpaid_users = UserTierStatus.query.filter(
            UserTierStatus.profit_share_owed > 0,
            UserTierStatus.payment_status == 'unpaid'
        ).all()

        partial_users = UserTierStatus.query.filter(
            UserTierStatus.profit_share_owed > 0,
            UserTierStatus.payment_status == 'partial'
        ).all()

        return unpaid_users + partial_users
    
    @staticmethod
    def get_disabled_accounts():
        """Get all disabled accounts."""
        return UserTierStatus.query.filter(
            UserTierStatus.account_disabled == True
        ).all()
    
    @staticmethod
    def _send_payday_warning_notification(user, tier_status):
        """Send payday warning notification to user."""
        try:
            message = (
                f"⚠️ Payment Reminder: You have ${tier_status.profit_share_owed:.2f} in unpaid profit share fees. "
                f"Payment is due by Saturday 0:00 GMT ({tier_status.next_payday_deadline.strftime('%Y-%m-%d %H:%M UTC')}). "
                f"Your account will be disabled if payment is not received by the deadline."
            )
            
            # Use notification service if available
            NotificationService.send_user_notification(
                user_id=user.id,
                title="Payment Due - Account Will Be Disabled",
                message=message,
                notification_type="payment_warning"
            )
            
        except Exception as e:
            logger.error(f"Error sending payday warning to user {user.id}: {str(e)}")
    
    @staticmethod
    def _send_account_disabled_notification(user, tier_status):
        """Send account disabled notification to user."""
        try:
            message = (
                f"🚫 Account Disabled: Your account has been disabled due to unpaid profit share fees of "
                f"${tier_status.profit_share_owed:.2f}. To reactivate your account, please make a payment "
                f"immediately. Your account will be re-enabled after successful payment confirmation."
            )
            
            # Use notification service if available
            NotificationService.send_user_notification(
                user_id=user.id,
                title="Account Disabled - Payment Required",
                message=message,
                notification_type="account_disabled"
            )
            
        except Exception as e:
            logger.error(f"Error sending account disabled notification to user {user.id}: {str(e)}")
    
    @staticmethod
    def run_daily_payday_tasks():
        """Run all daily payday-related tasks."""
        logger.info("Starting daily payday tasks...")

        # Initialize deadlines for new users
        initialized = PaydayService.initialize_payday_deadlines()

        # Send email warnings (3, 2, 1 days before)
        email_warnings_sent = PaydayService.send_email_warnings()

        # Send in-app warnings (24 hours before)
        warnings_sent = PaydayService.send_payday_warnings()

        # Enforce limits
        accounts_disabled = PaydayService.enforce_payday_limits()

        logger.info(f"Daily payday tasks completed: {initialized} initialized, {email_warnings_sent} email warnings sent, {warnings_sent} in-app warnings sent, {accounts_disabled} accounts disabled")

        return {
            'initialized_deadlines': initialized,
            'email_warnings_sent': email_warnings_sent,
            'warnings_sent': warnings_sent,
            'accounts_disabled': accounts_disabled
        }
