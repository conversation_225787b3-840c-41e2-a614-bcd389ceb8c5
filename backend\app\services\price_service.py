"""
Centralized Price Service for DeepTrade Platform
Fetches live price data from Binance and serves it to all frontend clients.
"""

import threading
import time
import json
import logging
from datetime import datetime
from typing import Dict, Optional, List, Callable
from app.services.market_data import BinanceMarketData

logger = logging.getLogger(__name__)

class PriceService:
    """Centralized service for fetching and distributing live price data."""
    
    def __init__(self):
        self.market_data = BinanceMarketData()
        self.price_data: Dict[str, Dict] = {}
        self.subscribers: List[Callable] = []
        self.is_running = False
        self.fetch_thread = None
        self.lock = threading.Lock()
        
        # Default symbols to track
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
        
        # Update interval (1 second for real-time updates)
        self.update_interval = 1.0
        
    def add_subscriber(self, callback: Callable[[str, Dict], None]):
        """Add a callback function to receive price updates."""
        with self.lock:
            self.subscribers.append(callback)
            
    def remove_subscriber(self, callback: Callable[[str, Dict], None]):
        """Remove a callback function from price updates."""
        with self.lock:
            if callback in self.subscribers:
                self.subscribers.remove(callback)
                
    def notify_subscribers(self, symbol: str, price_data: Dict):
        """Notify all subscribers of price updates."""
        with self.lock:
            for callback in self.subscribers[:]:  # Create a copy to avoid modification during iteration
                try:
                    callback(symbol, price_data)
                except Exception as e:
                    logger.error(f"Error notifying subscriber: {e}")
                    # Remove failed callback
                    if callback in self.subscribers:
                        self.subscribers.remove(callback)
                        
    def get_current_price(self, symbol: str) -> Optional[Dict]:
        """Get the current cached price data for a symbol."""
        with self.lock:
            return self.price_data.get(symbol.upper())
            
    def get_all_prices(self) -> Dict[str, Dict]:
        """Get all current cached price data."""
        with self.lock:
            return self.price_data.copy()
            
    def add_symbol(self, symbol: str):
        """Add a new symbol to track."""
        symbol = symbol.upper()
        if symbol not in self.symbols:
            self.symbols.append(symbol)
            logger.info(f"Added symbol {symbol} to price tracking")
            
    def remove_symbol(self, symbol: str):
        """Remove a symbol from tracking."""
        symbol = symbol.upper()
        if symbol in self.symbols:
            self.symbols.remove(symbol)
            with self.lock:
                if symbol in self.price_data:
                    del self.price_data[symbol]
            logger.info(f"Removed symbol {symbol} from price tracking")
            
    def fetch_price_data(self, symbol: str) -> Optional[Dict]:
        """Fetch price data for a single symbol."""
        try:
            # Get current price
            price = self.market_data.get_current_price(symbol)
            if price is None:
                return None
                
            # Get 24hr ticker for additional data
            ticker_data = self.market_data.get_24hr_ticker(symbol)
            
            price_data = {
                'symbol': symbol,
                'price': price,
                'timestamp': int(time.time() * 1000),
                'last_updated': datetime.now().isoformat()
            }
            
            # Add 24hr change data if available
            if ticker_data:
                price_data.update({
                    'change_24h': float(ticker_data.get('priceChange', 0)),
                    'change_percent_24h': float(ticker_data.get('priceChangePercent', 0)),
                    'high_24h': float(ticker_data.get('highPrice', 0)),
                    'low_24h': float(ticker_data.get('lowPrice', 0)),
                    'volume_24h': float(ticker_data.get('volume', 0))
                })
                
            return price_data
            
        except Exception as e:
            logger.error(f"Error fetching price data for {symbol}: {e}")
            return None
            
    def update_prices(self):
        """Update prices for all tracked symbols."""
        for symbol in self.symbols[:]:  # Create a copy to avoid modification during iteration
            try:
                new_price_data = self.fetch_price_data(symbol)
                if new_price_data:
                    with self.lock:
                        old_price_data = self.price_data.get(symbol)
                        self.price_data[symbol] = new_price_data
                        
                    # Check if price changed and notify subscribers
                    if old_price_data is None or old_price_data.get('price') != new_price_data.get('price'):
                        self.notify_subscribers(symbol, new_price_data)
                        
            except Exception as e:
                logger.error(f"Error updating price for {symbol}: {e}")
                
    def run_price_fetcher(self):
        """Main loop for fetching price data."""
        logger.info("Starting price fetcher service...")
        
        while self.is_running:
            try:
                start_time = time.time()
                self.update_prices()
                
                # Calculate sleep time to maintain consistent interval
                elapsed_time = time.time() - start_time
                sleep_time = max(0, self.update_interval - elapsed_time)
                
                if sleep_time > 0:
                    time.sleep(sleep_time)
                    
            except Exception as e:
                logger.error(f"Error in price fetcher loop: {e}")
                time.sleep(self.update_interval)
                
        logger.info("Price fetcher service stopped")
        
    def start(self):
        """Start the price fetching service."""
        if self.is_running:
            logger.warning("Price service is already running")
            return
            
        self.is_running = True
        self.fetch_thread = threading.Thread(target=self.run_price_fetcher, daemon=True)
        self.fetch_thread.start()
        logger.info("Price service started")
        
    def stop(self):
        """Stop the price fetching service."""
        if not self.is_running:
            logger.warning("Price service is not running")
            return
            
        self.is_running = False
        if self.fetch_thread:
            self.fetch_thread.join(timeout=5)
        logger.info("Price service stopped")
        
    def get_status(self) -> Dict:
        """Get the current status of the price service."""
        with self.lock:
            return {
                'is_running': self.is_running,
                'symbols_tracked': len(self.symbols),
                'symbols': self.symbols,
                'subscribers': len(self.subscribers),
                'last_prices': {symbol: data.get('price') for symbol, data in self.price_data.items()},
                'update_interval': self.update_interval
            }

# Global price service instance
price_service = PriceService()
