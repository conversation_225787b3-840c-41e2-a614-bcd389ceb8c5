"""
Redis service for caching and temporary data storage.
"""
import json
import logging
from flask import current_app
from app import get_redis_client

logger = logging.getLogger(__name__)

class RedisService:
    """Service for Redis operations."""
    
    @staticmethod
    def get_client():
        """Get Redis client."""
        try:
            return get_redis_client()
        except Exception as e:
            logger.error(f"Failed to get Redis client: {str(e)}")
            return None
    
    @staticmethod
    def set(key, value, ex=None):
        """Set a key-value pair in Redis."""
        try:
            client = RedisService.get_client()
            if not client:
                logger.warning("Redis client not available")
                return False
            
            # Serialize value to JSON if it's not a string
            if not isinstance(value, str):
                value = json.dumps(value)
            
            if ex:
                client.setex(key, ex, value)
            else:
                client.set(key, value)
            
            return True
        except Exception as e:
            logger.error(f"Redis SET error for key {key}: {str(e)}")
            return False
    
    @staticmethod
    def set_with_expiry(key, value, expiry_seconds):
        """Set a key-value pair with expiry in Redis."""
        return RedisService.set(key, value, ex=expiry_seconds)
    
    @staticmethod
    def get(key):
        """Get a value from Redis."""
        try:
            client = RedisService.get_client()
            if not client:
                logger.warning("Redis client not available")
                return None
            
            value = client.get(key)
            if value is None:
                return None
            
            # Handle different Redis response types
            if isinstance(value, bytes):
                value = value.decode('utf-8')
            
            # Try to deserialize JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                # Return as string if not JSON
                return value
                
        except Exception as e:
            logger.error(f"Redis GET error for key {key}: {str(e)}")
            return None
    
    @staticmethod
    def delete(key):
        """Delete a key from Redis."""
        try:
            client = RedisService.get_client()
            if not client:
                logger.warning("Redis client not available")
                return False
            
            result = client.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"Redis DELETE error for key {key}: {str(e)}")
            return False
    
    @staticmethod
    def exists(key):
        """Check if a key exists in Redis."""
        try:
            client = RedisService.get_client()
            if not client:
                logger.warning("Redis client not available")
                return False
            
            return client.exists(key) > 0
        except Exception as e:
            logger.error(f"Redis EXISTS error for key {key}: {str(e)}")
            return False
    
    @staticmethod
    def expire(key, seconds):
        """Set expiry for a key in Redis."""
        try:
            client = RedisService.get_client()
            if not client:
                logger.warning("Redis client not available")
                return False
            
            return client.expire(key, seconds)
        except Exception as e:
            logger.error(f"Redis EXPIRE error for key {key}: {str(e)}")
            return False
    
    @staticmethod
    def ttl(key):
        """Get time to live for a key in Redis."""
        try:
            client = RedisService.get_client()
            if not client:
                logger.warning("Redis client not available")
                return -1
            
            return client.ttl(key)
        except Exception as e:
            logger.error(f"Redis TTL error for key {key}: {str(e)}")
            return -1
    
    @staticmethod
    def ping():
        """Test Redis connection."""
        try:
            client = RedisService.get_client()
            if not client:
                return False
            
            client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis PING error: {str(e)}")
            return False
