import threading
import requests
import json
import time
from app.models.user import User
from app.models.trade import TradingSession, Trade
from app.models.security_log import APICredential
from app.services.exchange_service import get_exchange_service
from app.services.market_data import ml_service
from app.services.paper_trading_service import PaperTradingService
from app.models.paper_trading import PaperTrade, PaperTradeStatus, PaperTradingAccount
from flask import current_app
from decimal import Decimal

class UserTradingContainer:
    def __init__(self, user_id):
        self.user_id = user_id
        self.active = False
        self.risk_params = {
            "allocation_pct": 0,  # Default to 0% - users must configure
            "margin_mode": "isolated",
            "leverage": 1  # Default to 1x - users must configure
        }
        self.current_trade = None
        self.session = None
        self.lock = threading.Lock()
        self.last_signal = None  # Track last signal for reversal detection
        self.pending_reversal = None  # Store pending reversal signal
        self._load_persistent_settings()

    def _load_persistent_settings(self):
        """Load user's risk settings from database."""
        try:
            from app.models.user import User
            user = User.query.get(self.user_id)
            if user and user.risk_settings_configured:
                # Update risk params with user's settings
                self.risk_params["allocation_pct"] = float(user.investment_percentage)
                self.risk_params["leverage"] = float(user.leverage_multiplier)
                # Keep margin mode as isolated for safety
                self.risk_params["margin_mode"] = "isolated"

                current_app.logger.info(f"Loaded user risk settings for {self.user_id}: "
                                      f"allocation={self.risk_params['allocation_pct']}%, "
                                      f"leverage={self.risk_params['leverage']}x")
            else:
                current_app.logger.warning(f"User {self.user_id} has not configured risk settings - using defaults")
        except Exception as e:
            current_app.logger.error(f"Error loading risk settings for user {self.user_id}: {str(e)}")
            # Keep default safe settings if loading fails

    def refresh_user_settings(self):
        """Refresh user's risk settings from database."""
        self._load_persistent_settings()

    def _is_paper_trading_mode(self):
        """Check if user is in paper trading mode."""
        try:
            user = User.query.get(self.user_id)
            return user.paper_trading_mode if user else False
        except Exception as e:
            current_app.logger.error(f"Error checking paper trading mode for user {self.user_id}: {e}")
            return False

    def _get_exchange_service_for_mode(self):
        """Get appropriate exchange service based on trading mode."""
        if self._is_paper_trading_mode():
            # Return paper trading exchange service
            return PaperTradingService.get_paper_exchange_service(self.user_id)
        else:
            # Return real exchange service
            user = User.query.get(self.user_id)
            if not user:
                current_app.logger.error(f"User {self.user_id} not found")
                return None

            exchange = user.selected_exchange
            credential = APICredential.query.filter_by(
                user_id=self.user_id,
                exchange=exchange,
                is_active=True,
                is_valid=True
            ).first()

            if not credential:
                current_app.logger.error(f"No valid credentials found for user {self.user_id}")
                return None

            creds = credential.get_credentials()
            if not creds:
                current_app.logger.error(f"Failed to decrypt credentials for user {self.user_id}")
                return None

            return get_exchange_service(exchange, creds['api_key'], creds['secret_key'], creds.get('passphrase'))

    def update_risk_params(self, allocation_pct=None, margin_mode=None, leverage=None):
        with self.lock:
            if allocation_pct is not None:
                self.risk_params["allocation_pct"] = allocation_pct
            if margin_mode is not None:
                self.risk_params["margin_mode"] = margin_mode
            if leverage is not None:
                self.risk_params["leverage"] = leverage
            # Persist changes if needed

    def set_active(self, active: bool):
        with self.lock:
            self.active = active

    def _disable_auto_trading_with_notification(self, reason: str):
        """Disable auto-trading and create notification for user"""
        try:
            from app import db

            # Disable auto-trading for user
            user = User.query.get(self.user_id)
            if user:
                user.auto_trading_enabled = False
                db.session.commit()

                current_app.logger.warning(f"Auto-trading disabled for user {self.user_id}: {reason}")

                # Create notification record (you can extend this to send actual notifications)
                # For now, we'll log it and the frontend can check this via API
                from app.auth.security import SecurityManager
                SecurityManager.log_security_event(
                    user_id=self.user_id,
                    event_type='auto_trading_disabled',
                    ip_address='system',
                    details={'reason': reason},
                    risk_level='medium'
                )

                return True
        except Exception as e:
            current_app.logger.error(f"Failed to disable auto-trading for user {self.user_id}: {str(e)}")
            return False

    def _get_tier_based_leverage(self, user):
        """Get leverage based on user tier"""
        try:
            tier_status = getattr(user, 'tier_status', None)
            if tier_status and hasattr(tier_status, 'get_current_tier'):
                tier = tier_status.get_current_tier()
                if tier == 1:
                    return 3  # Tier 1: 3x leverage
                elif tier == 2:
                    return 5  # Tier 2: 5x leverage
                elif tier == 3:
                    return 10  # Tier 3: 10x leverage

            # Default to Tier 1 leverage
            return 3
        except Exception as e:
            current_app.logger.error(f"Error getting tier-based leverage for user {self.user_id}: {str(e)}")
            return 3  # Safe default

    def handle_signal_reversal(self, new_signal, forecast_data):
        """Handle signal reversal with smart P&L-based logic"""
        with self.lock:
            if not self.current_trade or self.current_trade.status != 'OPEN':
                return False

            current_app.logger.info(f"Signal reversal detected for user {self.user_id}: {self.last_signal} -> {new_signal}")

            # Get current price to calculate P&L
            symbol = self.current_trade.symbol
            exchange = forecast_data.get("exchange")

            # Get exchange service
            credential = APICredential.query.filter_by(
                user_id=self.user_id,
                exchange=exchange,
                is_active=True,
                is_valid=True
            ).first()

            if not credential:
                current_app.logger.error(f"No valid credentials for reversal check, user {self.user_id}")
                return False

            creds = credential.get_credentials()
            service = get_exchange_service(exchange, creds['api_key'], creds['secret_key'], creds.get('passphrase'))

            current_price = self._get_current_price(symbol, service)
            if not current_price:
                current_app.logger.error(f"Cannot get current price for reversal check, user {self.user_id}")
                return False

            # Calculate current P&L including fees
            entry_price = float(self.current_trade.entry_price)
            quantity = float(self.current_trade.quantity)

            if self.current_trade.is_long():
                pnl = (current_price - entry_price) * quantity
            else:
                pnl = (entry_price - current_price) * quantity

            # Estimate trading fees (0.1% per trade, both entry and exit)
            estimated_fees = (entry_price * quantity * 0.001) + (current_price * quantity * 0.001)
            net_pnl = pnl - estimated_fees

            current_app.logger.info(f"Reversal P&L check for user {self.user_id}: PnL={pnl:.4f}, Fees={estimated_fees:.4f}, Net={net_pnl:.4f}")

            # Only close if profitable or at breakeven
            if net_pnl >= 0:
                current_app.logger.info(f"Closing profitable/breakeven position for reversal, user {self.user_id}")

                # Clear any pending orders before closing for reversal
                try:
                    pending_orders = service.get_open_orders(symbol)
                    if pending_orders:
                        current_app.logger.info(f"Clearing {len(pending_orders)} pending orders before reversal close for {symbol}, user {self.user_id}")
                        service.cancel_all_orders(symbol)
                except Exception as e:
                    current_app.logger.error(f"Error clearing orders before reversal close: {str(e)}")

                self._close_position(self.current_trade, current_price, "signal_reversal", service)

                # Clear pending reversal since we're closing
                self.pending_reversal = None
                return True
            else:
                # Position is losing, store as pending reversal
                current_app.logger.info(f"Position losing, storing pending reversal for user {self.user_id}")
                self.pending_reversal = {
                    'signal': new_signal,
                    'forecast_data': forecast_data,
                    'detected_at': time.time()
                }
                return False

    def handle_new_forecast(self, forecast_data):
        with self.lock:
            if not self.active:
                current_app.logger.debug(f"Trading container inactive for user {self.user_id}")
                return

            # Check if user has auto-trading enabled
            from app.models.user import User
            user = User.query.get(self.user_id)
            if not user:
                current_app.logger.error(f"[CRITICAL] User {self.user_id} not found in database")
                return

            if not user.auto_trading_enabled:
                current_app.logger.info(f"Auto-trading disabled for user {self.user_id}")
                return

            # Determine new signal direction
            direction = forecast_data.get("direction")
            new_signal = "BUY" if direction == "bullish" else "SELL"

            from app.models.trade import TradeStatus
            if self.current_trade and self.current_trade.status == TradeStatus.OPEN:
                # Check for signal reversal
                if self.last_signal and self.last_signal != new_signal:
                    if self.handle_signal_reversal(new_signal, forecast_data):
                        # Position was closed due to reversal, continue with new trade
                        pass
                    else:
                        # Position not closed (losing), skip new trade
                        current_app.logger.debug(f"Signal reversal detected but position losing, skipping new trade for user {self.user_id}")
                        return
                else:
                    current_app.logger.debug(f"Trade already open for user {self.user_id}, skipping new trade.")
                    return  # Only one trade at a time

            # Get appropriate exchange service based on trading mode
            service = self._get_exchange_service_for_mode()
            if not service:
                current_app.logger.error(f"Failed to get exchange service for user {self.user_id}")
                return

            # For live trading, we still need to check credentials and exchange
            if not self._is_paper_trading_mode():
                exchange = forecast_data.get("exchange")
                if not exchange:
                    current_app.logger.warning(f"No exchange specified in forecast data for user {self.user_id}")
                    return
            else:
                current_app.logger.info(f"Paper trading mode active for user {self.user_id}")

            # Determine trade direction and price targets
            target_price = forecast_data.get("target_price")
            stop_loss = forecast_data.get("support_level") if direction == "bullish" else forecast_data.get("resistance_level")
            symbol = forecast_data.get("symbol")

            # Use default symbol if not specified
            if not symbol:
                symbol = current_app.config.get('DEFAULT_TRADING_SYMBOL', 'BTCUSDT')
                current_app.logger.info(f"No symbol specified, using default: {symbol} for user {self.user_id}")

            # CRITICAL: Check for existing open positions for this symbol/user
            from app.models.trade import Trade
            existing_trade = Trade.query.filter_by(
                user_id=self.user_id,
                symbol=symbol,
                status=TradeStatus.OPEN
            ).first()

            if existing_trade:
                current_app.logger.warning(f"User {self.user_id} already has open position for {symbol} (Trade ID: {existing_trade.id})")
                return

            # CRITICAL: Check for and clear any pending orders before opening new trade
            try:
                pending_orders = service.get_open_orders(symbol)
                if pending_orders:
                    current_app.logger.warning(f"Found {len(pending_orders)} pending orders for {symbol}, user {self.user_id}. Clearing them...")

                    # Cancel all pending orders for this symbol
                    cancel_success = service.cancel_all_orders(symbol)
                    if cancel_success:
                        current_app.logger.info(f"Successfully cleared pending orders for {symbol}, user {self.user_id}")
                    else:
                        current_app.logger.error(f"Failed to clear pending orders for {symbol}, user {self.user_id}")
                        # Continue with trade execution even if order cancellation fails
                else:
                    current_app.logger.debug(f"No pending orders found for {symbol}, user {self.user_id}")

            except Exception as e:
                current_app.logger.error(f"Error checking/clearing pending orders for {symbol}, user {self.user_id}: {str(e)}")
                # Continue with trade execution even if order check fails

            # Extract quote asset from symbol (e.g., BTCUSDT -> USDT)
            quote_asset = current_app.config.get('DEFAULT_QUOTE_ASSET', 'USDT')
            if symbol:
                # Try to extract quote asset from symbol
                supported_quotes = current_app.config.get('SUPPORTED_QUOTE_ASSETS', ['USDT', 'USDC', 'USD1', 'BUSD'])
                for quote in supported_quotes:
                    if symbol.endswith(quote):
                        quote_asset = quote
                        break

            # Get balance and validate minimum amount
            try:
                balance = service.get_balance(quote_asset)
                balance_decimal = Decimal(str(balance))

                # CRITICAL: Minimum balance validation (100 of quote asset)
                MIN_BALANCE = Decimal('100.0')
                if balance_decimal < MIN_BALANCE:
                    current_app.logger.warning(f"Insufficient balance for user {self.user_id}: {balance_decimal} {quote_asset} (minimum: {MIN_BALANCE} {quote_asset})")

                    # Disable auto-trading and notify user
                    self._disable_auto_trading_with_notification(
                        f"Insufficient balance: {balance_decimal} {quote_asset}. Minimum required: {MIN_BALANCE} {quote_asset}"
                    )
                    return

                # Sync balance tracker with current exchange balance
                try:
                    from app.models.user_balance_tracker import UserBalanceTracker
                    tracker = UserBalanceTracker.query.filter_by(user_id=self.user_id).first()
                    if tracker:
                        # Only sync if balance has changed significantly (avoid constant updates)
                        current_tracker_balance = float(tracker.current_balance)
                        exchange_balance = float(balance)
                        balance_diff = abs(current_tracker_balance - exchange_balance)

                        if balance_diff > 0.01:  # Only sync if difference > $0.01
                            tracker.update_balance(exchange_balance, transaction_type='sync')
                            current_app.logger.info(f"[PROFIT_TRACKING] Synced balance tracker for user {self.user_id}: {current_tracker_balance} -> {exchange_balance} USDT")
                except Exception as sync_error:
                    current_app.logger.error(f"Error syncing balance tracker for user {self.user_id}: {str(sync_error)}")

            except Exception as e:
                current_app.logger.error(f"Failed to get balance for user {self.user_id}: {str(e)}")
                return

            # Refresh user settings to ensure we have the latest configuration
            self.refresh_user_settings()

            # Validate user has configured risk settings
            if not user.risk_settings_configured or user.investment_percentage <= 0:
                current_app.logger.warning(f"User {self.user_id} has not configured risk settings or investment percentage is 0%")
                self._disable_auto_trading_with_notification(
                    "Please configure your risk settings in the Dashboard before trading"
                )
                return

            # Validate investment percentage is within allowed range (0-10%)
            investment_pct = float(user.investment_percentage)
            if not (0 < investment_pct <= 10):
                current_app.logger.error(f"User {self.user_id} investment percentage {investment_pct}% is outside allowed range (0-10%)")
                self._disable_auto_trading_with_notification(
                    f"Investment percentage {investment_pct}% is outside allowed range (0-10%). Please update your risk settings."
                )
                return

            # Validate user has selected exchange and account type
            if not user.selected_exchange or not user.account_type:
                current_app.logger.error(f"User {self.user_id} has not selected exchange or account type")
                self._disable_auto_trading_with_notification(
                    "Please select your exchange and account type in the risk settings before trading"
                )
                return

            # Use user's configured leverage with validation
            user_leverage = float(user.leverage_multiplier)
            max_allowed_leverage = user.get_max_leverage_for_exchange(
                user.selected_exchange,
                user.account_type
            )

            if user_leverage > max_allowed_leverage:
                current_app.logger.error(f"User {self.user_id} leverage {user_leverage}x exceeds maximum {max_allowed_leverage}x")
                self._disable_auto_trading_with_notification(
                    f"Leverage {user_leverage}x exceeds maximum allowed {max_allowed_leverage}x for your tier and exchange"
                )
                return

            leverage = user_leverage
            current_app.logger.info(f"Using user-configured leverage {leverage}x for user {self.user_id}")

            # Force isolated margin mode for all trades
            margin_mode = "isolated"
            current_app.logger.info(f"Using isolated margin mode for user {self.user_id}")

            # Use the user's current investment percentage directly from database (not cached risk_params)
            allocation = balance_decimal * (Decimal(str(investment_pct)) / Decimal('100'))
            current_app.logger.info(f"Using user-configured investment percentage {investment_pct}% for user {self.user_id} (allocation: {allocation} USDT)")

            # Calculate quantity with proper decimal handling
            current_price = forecast_data.get("current_price")
            if not current_price or current_price <= 0:
                current_app.logger.error(f"Invalid current price for user {self.user_id}: {current_price}")
                return

            current_price_decimal = Decimal(str(current_price))
            leverage_decimal = Decimal(str(leverage))

            # Calculate position size: allocation * leverage / current_price
            qty_decimal = (allocation * leverage_decimal) / current_price_decimal
            qty = float(qty_decimal)

            current_app.logger.info(f"Position calculation for user {self.user_id}: Balance={balance_decimal}, Allocation={allocation}, Leverage={leverage}x, Price={current_price_decimal}, Qty={qty_decimal}")

            # Set margin mode and leverage before placing order
            try:
                # Set isolated margin mode
                margin_success = service.set_margin_mode(symbol, margin_mode)
                if margin_success:
                    current_app.logger.info(f"Margin mode set to {margin_mode} for {symbol}, user {self.user_id}")
                else:
                    current_app.logger.warning(f"Failed to set margin mode to {margin_mode} for {symbol}, user {self.user_id}")

                # Set tier-based leverage
                leverage_success = service.set_leverage(symbol, int(leverage))
                if leverage_success:
                    current_app.logger.info(f"Leverage set to {leverage}x for {symbol}, user {self.user_id}")
                else:
                    current_app.logger.warning(f"Failed to set leverage to {leverage}x for {symbol}, user {self.user_id}")

            except Exception as e:
                current_app.logger.error(f"Error setting margin/leverage for user {self.user_id}: {str(e)}")
                # Continue with order placement even if margin/leverage setting fails

            # Place order
            side = new_signal  # Use the signal we calculated earlier

            try:
                order = service.place_order(symbol, side, qty)
                current_app.logger.info(f"Order placed for user {self.user_id}: {side} {qty} {symbol} at {current_price} (leverage: {leverage}x, margin: {margin_mode})")

                # Store the last signal for reversal detection
                self.last_signal = new_signal

            except Exception as e:
                current_app.logger.error(f"Failed to place order for user {self.user_id}: {str(e)}")
                return

            # Record trade in DB (different handling for paper vs live trading)
            from app import db

            if self._is_paper_trading_mode():
                # Create paper trade
                paper_account = PaperTradingService.get_or_create_paper_account(self.user_id)
                paper_trade = PaperTrade(
                    user_id=self.user_id,
                    paper_account_id=paper_account.id,
                    symbol=symbol,
                    side=side.lower(),  # Paper trade uses lowercase
                    quantity=qty,
                    entry_price=current_price,
                    stop_loss=stop_loss,
                    take_profit=target_price,
                    source='app'
                )

                try:
                    db.session.add(paper_trade)
                    db.session.commit()
                    current_app.logger.info(f"Paper trade saved to DB for user {self.user_id}: {paper_trade.id}")
                    self.current_trade = paper_trade  # Store paper trade reference

                    # Start position monitoring thread for paper trade
                    self._start_paper_position_monitoring(paper_trade, service)

                except Exception as e:
                    current_app.logger.error(f"Failed to save paper trade to DB for user {self.user_id}: {str(e)}")
                    db.session.rollback()
                    return
            else:
                # Create live trade
                trade = Trade(
                    user_id=self.user_id,
                    symbol=symbol,
                    side=side,
                    quantity=qty,
                    entry_price=current_price,
                    stop_loss=stop_loss,
                    take_profit=target_price,
                    source='app'
                )

                try:
                    db.session.add(trade)
                    db.session.commit()
                    current_app.logger.info(f"Live trade saved to DB for user {self.user_id}: {trade.id}")
                    self.current_trade = trade

                    # Start position monitoring thread for live trade
                    self._start_position_monitoring(trade, service)

                except Exception as e:
                    current_app.logger.error(f"Failed to save live trade to DB for user {self.user_id}: {str(e)}")
                    db.session.rollback()
                    return

            # Log trading parameters for audit trail
            mode = "Paper" if self._is_paper_trading_mode() else "Live"
            current_app.logger.info(f"{mode} trade created for user {self.user_id}: leverage={leverage}x, margin_mode={margin_mode}, balance_required={MIN_BALANCE} USDT")

    def close_trade(self, reason="manual"):
        with self.lock:
            from app.models.trade import TradeStatus
            from app.models.user_tier_status import UserTierStatus
            from app import db

            if not self.current_trade:
                return

            # Get exchange service for current price
            service = self._get_exchange_service_for_mode()
            if not service:
                current_app.logger.error(f"Failed to get exchange service for closing trade, user {self.user_id}")
                return

            # Handle paper trade closure
            if isinstance(self.current_trade, PaperTrade) and self.current_trade.status == PaperTradeStatus.OPEN:
                # Get current price for paper trade
                current_price = self._get_current_price(self.current_trade.symbol, service)
                if current_price:
                    self._close_paper_position(self.current_trade, current_price, reason, service)
                else:
                    current_app.logger.error(f"Cannot get current price to close paper trade {self.current_trade.id}")

            # Handle live trade closure
            elif hasattr(self.current_trade, 'status') and self.current_trade.status == TradeStatus.OPEN:
                # Get current price for live trade
                current_price = self._get_current_price(self.current_trade.symbol, service)
                exit_price = current_price if current_price else self.current_trade.entry_price

                self.current_trade.close_trade(exit_price=exit_price, exit_reason=reason)

                # Update profit share if trade was profitable (only for live trades)
                if self.current_trade.pnl and float(self.current_trade.pnl) > 0:
                    tier_status = UserTierStatus.query.filter_by(user_id=self.user_id).first()
                    if tier_status:
                        tier_status.add_profit_share(float(self.current_trade.pnl))
                        db.session.add(tier_status)

                db.session.commit()
                self.current_trade = None

    def _start_position_monitoring(self, trade, exchange_service):
        """Start monitoring thread for stop loss and take profit"""
        def monitor_position():
            current_app.logger.info(f"Starting position monitoring for trade {trade.id}")

            while trade.status == 'OPEN':
                try:
                    # Get current price
                    current_price = self._get_current_price(trade.symbol, exchange_service)
                    if not current_price:
                        time.sleep(1)
                        continue

                    # Check stop loss first (highest priority)
                    if trade.should_stop_loss(current_price):
                        current_app.logger.info(f"Stop loss triggered for trade {trade.id} at price {current_price}")
                        self._close_position(trade, current_price, "stop_loss", exchange_service)
                        break

                    # Check pending reversal (second priority)
                    if self.pending_reversal:
                        entry_price = float(trade.entry_price)
                        quantity = float(trade.quantity)

                        if trade.is_long():
                            pnl = (current_price - entry_price) * quantity
                        else:
                            pnl = (entry_price - current_price) * quantity

                        # Estimate fees
                        estimated_fees = (entry_price * quantity * 0.001) + (current_price * quantity * 0.001)
                        net_pnl = pnl - estimated_fees

                        # Close if now profitable/breakeven
                        if net_pnl >= 0:
                            current_app.logger.info(f"Pending reversal now profitable, closing position for trade {trade.id}")
                            self._close_position(trade, current_price, "pending_reversal", exchange_service)

                            # Process the pending reversal signal
                            self.handle_new_forecast(self.pending_reversal['forecast_data'])
                            break

                    # Check take profit (lowest priority)
                    if trade.should_take_profit(current_price):
                        current_app.logger.info(f"Take profit triggered for trade {trade.id} at price {current_price}")
                        self._close_position(trade, current_price, "take_profit", exchange_service)
                        break

                    # Sleep for 1 second before next check
                    time.sleep(1)

                except Exception as e:
                    current_app.logger.error(f"Error in position monitoring for trade {trade.id}: {str(e)}")
                    time.sleep(5)  # Wait longer on error

            current_app.logger.info(f"Position monitoring ended for trade {trade.id}")

        # Start monitoring thread
        monitor_thread = threading.Thread(target=monitor_position, daemon=True)
        monitor_thread.start()

    def _start_paper_position_monitoring(self, paper_trade, exchange_service):
        """Start monitoring thread for paper trading stop loss and take profit"""
        def monitor_paper_position():
            current_app.logger.info(f"Starting paper position monitoring for trade {paper_trade.id}")

            while paper_trade.status == PaperTradeStatus.OPEN:
                try:
                    # Get current price
                    current_price = self._get_current_price(paper_trade.symbol, exchange_service)
                    if not current_price:
                        time.sleep(1)
                        continue

                    # Check stop loss
                    if paper_trade.stop_loss:
                        if paper_trade.is_long() and current_price <= float(paper_trade.stop_loss):
                            current_app.logger.info(f"Paper trade stop loss triggered for {paper_trade.id} at price {current_price}")
                            self._close_paper_position(paper_trade, current_price, "stop_loss", exchange_service)
                            break
                        elif paper_trade.is_short() and current_price >= float(paper_trade.stop_loss):
                            current_app.logger.info(f"Paper trade stop loss triggered for {paper_trade.id} at price {current_price}")
                            self._close_paper_position(paper_trade, current_price, "stop_loss", exchange_service)
                            break

                    # Check take profit
                    if paper_trade.take_profit:
                        if paper_trade.is_long() and current_price >= float(paper_trade.take_profit):
                            current_app.logger.info(f"Paper trade take profit triggered for {paper_trade.id} at price {current_price}")
                            self._close_paper_position(paper_trade, current_price, "take_profit", exchange_service)
                            break
                        elif paper_trade.is_short() and current_price <= float(paper_trade.take_profit):
                            current_app.logger.info(f"Paper trade take profit triggered for {paper_trade.id} at price {current_price}")
                            self._close_paper_position(paper_trade, current_price, "take_profit", exchange_service)
                            break

                    # Sleep for 1 second before next check
                    time.sleep(1)

                except Exception as e:
                    current_app.logger.error(f"Error in paper position monitoring for trade {paper_trade.id}: {str(e)}")
                    time.sleep(5)  # Wait longer on error

            current_app.logger.info(f"Paper position monitoring ended for trade {paper_trade.id}")

        # Start monitoring thread
        monitor_thread = threading.Thread(target=monitor_paper_position, daemon=True)
        monitor_thread.start()

    def _get_current_price(self, symbol, exchange_service):
        """Get current market price for symbol"""
        try:
            return exchange_service.get_current_price(symbol)
        except Exception as e:
            current_app.logger.error(f"Failed to get current price for {symbol}: {str(e)}")
            return None

    def _close_paper_position(self, paper_trade, exit_price, reason, exchange_service):
        """Close paper trading position and update records"""
        from app import db
        try:

            # Close the paper trade
            paper_trade.close_trade(exit_price, reason)

            # Update paper account balance with P&L
            paper_account = PaperTradingAccount.query.get(paper_trade.paper_account_id)
            if paper_account and paper_trade.pnl:
                new_balance = paper_account.virtual_balance + paper_trade.pnl
                paper_account.update_balance(
                    new_balance=new_balance,
                    pnl_change=paper_trade.pnl,
                    transaction_type='trade'
                )

                # Update performance metrics
                paper_account.calculate_performance_metrics()

            db.session.commit()
            current_app.logger.info(f"Paper position closed for trade {paper_trade.id}: {reason} at {exit_price}, P&L: {paper_trade.pnl}")

            # Clear current trade reference
            self.current_trade = None

        except Exception as e:
            current_app.logger.error(f"Error closing paper position for trade {paper_trade.id}: {str(e)}")
            db.session.rollback()

    def _close_position(self, trade, exit_price, reason, exchange_service):
        """Close position and update trade record"""
        try:
            from app import db

            # Place closing order
            close_side = "SELL" if trade.side == "BUY" else "BUY"
            order = exchange_service.place_order(trade.symbol, close_side, trade.quantity)

            # Update trade record
            trade.close_trade(exit_price=exit_price, exit_reason=reason)

            # Update balance tracker with real exchange balance after trade
            try:
                from app.services.profit_share_service import ProfitShareService
                from app.models.user_balance_tracker import UserBalanceTracker

                # Get current balance from exchange
                current_balance = exchange_service.get_balance()

                # Update balance tracker
                tracker = UserBalanceTracker.query.filter_by(user_id=self.user_id).first()
                if tracker:
                    tracker.update_balance(float(current_balance), transaction_type='trade')
                    current_app.logger.info(f"[PROFIT_TRACKING] Updated balance tracker for user {self.user_id}: {current_balance} USDT (trade PnL: {trade.pnl})")

                    # Process profit share if trade was profitable and user is in profit
                    if trade.pnl and float(trade.pnl) > 0 and tracker.is_in_profit:
                        ProfitShareService.update_balance_from_trade(self.user_id, float(trade.pnl), trade.id)
                        current_app.logger.info(f"[PROFIT_TRACKING] Processed profit share for user {self.user_id} from trade {trade.id}")
                else:
                    current_app.logger.warning(f"[PROFIT_TRACKING] No balance tracker found for user {self.user_id}")

            except Exception as balance_error:
                current_app.logger.error(f"Error updating balance tracker for user {self.user_id}: {str(balance_error)}")
                # Continue without balance update - trade is still recorded

            db.session.commit()
            current_app.logger.info(f"Position closed for trade {trade.id}: {reason} at {exit_price}")

            # CRITICAL: Clear any remaining pending orders after closing position
            try:
                pending_orders = exchange_service.get_open_orders(trade.symbol)
                if pending_orders:
                    current_app.logger.warning(f"Found {len(pending_orders)} pending orders after closing position for {trade.symbol}, user {self.user_id}. Clearing them...")

                    cancel_success = exchange_service.cancel_all_orders(trade.symbol)
                    if cancel_success:
                        current_app.logger.info(f"Successfully cleared pending orders after closing position for {trade.symbol}, user {self.user_id}")
                    else:
                        current_app.logger.error(f"Failed to clear pending orders after closing position for {trade.symbol}, user {self.user_id}")
                else:
                    current_app.logger.debug(f"No pending orders to clear after closing position for {trade.symbol}, user {self.user_id}")

            except Exception as e:
                current_app.logger.error(f"Error clearing pending orders after closing position for {trade.symbol}, user {self.user_id}: {str(e)}")

            # Clear current trade and pending reversal
            with self.lock:
                self.current_trade = None
                if reason in ["signal_reversal", "pending_reversal"]:
                    self.pending_reversal = None

        except Exception as e:
            current_app.logger.error(f"Failed to close position for trade {trade.id}: {str(e)}")

# Example event listener thread
def start_forecast_event_listener(user_id, container: UserTradingContainer):
    def listen():
        url = "http://localhost:5000/api/forecast/stream"
        with requests.get(url, stream=True) as response:
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode().replace("data: ", ""))
                        if data.get("event") == "new_forecast":
                            # Fetch latest forecast for the user
                            forecast = ml_service.generate_prediction(data.get("symbol", "BTCUSDT"))
                            if forecast:
                                container.handle_new_forecast(forecast["prediction"])
                    except Exception as e:
                        current_app.logger.error(f"Error in forecast event listener: {e}")
                time.sleep(1)
    thread = threading.Thread(target=listen, daemon=True)
    thread.start()