// === Automated Trading Container UI Logic ===
async function loadTradingContainerStatus() {
    const resp = await fetch('/api/container/status', {headers: {'Authorization': localStorage.getItem('jwt') ? 'Bearer ' + localStorage.getItem('jwt') : ''}});
    if (!resp.ok) return;
    const data = await resp.json();
    document.getElementById('trading-container-toggle').checked = data.active;
    document.getElementById('allocation-pct').value = data.risk_params.allocation_pct;
    document.getElementById('leverage').value = data.risk_params.leverage;
    document.getElementById('margin-mode').value = data.risk_params.margin_mode;
    document.getElementById('trading-container-status').innerText = data.active ? "Automation Enabled" : "Automation Disabled";
}

document.addEventListener('DOMContentLoaded', function() {
    // Attach event listeners after DOM is loaded
    const toggle = document.getElementById('trading-container-toggle');
    if (toggle) {
        toggle.addEventListener('change', async function() {
            const active = this.checked;
            await fetch('/api/container/toggle', {
                method: 'POST',
                headers: {'Content-Type': 'application/json', 'Authorization': localStorage.getItem('jwt') ? 'Bearer ' + localStorage.getItem('jwt') : ''},
                body: JSON.stringify({active})
            });
            loadTradingContainerStatus();
        });
    }

    const form = document.getElementById('trading-container-form');
    if (form) {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            const allocation_pct = parseInt(document.getElementById('allocation-pct').value, 10);
            const leverage = parseInt(document.getElementById('leverage').value, 10);
            const margin_mode = document.getElementById('margin-mode').value;
            await fetch('/api/container/risk', {
                method: 'POST',
                headers: {'Content-Type': 'application/json', 'Authorization': localStorage.getItem('jwt') ? 'Bearer ' + localStorage.getItem('jwt') : ''},
                body: JSON.stringify({allocation_pct, leverage, margin_mode})
            });
            loadTradingContainerStatus();
        });
    }

    loadTradingContainerStatus();
});