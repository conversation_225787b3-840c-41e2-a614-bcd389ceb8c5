#!/usr/bin/env python3
"""
Paper Trading Migration Script

This script applies the database changes needed for paper trading functionality.
Run this script to create the necessary tables and add the required columns.
"""

import os
import sys
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.paper_trading import (
    PaperTradingAccount, 
    PaperTrade, 
    PaperTradingSession, 
    PaperBalanceSnapshot
)

def apply_migration():
    """Apply paper trading database migration."""
    app = create_app()
    
    with app.app_context():
        try:
            print("🚀 Starting paper trading migration...")
            
            # Create all tables
            print("📊 Creating paper trading tables...")
            db.create_all()
            
            # Check if paper_trading_mode column exists in users table
            print("🔍 Checking users table for paper_trading_mode column...")
            
            # Try to add the column if it doesn't exist
            try:
                db.engine.execute("ALTER TABLE users ADD COLUMN paper_trading_mode BOOLEAN NOT NULL DEFAULT FALSE")
                print("✅ Added paper_trading_mode column to users table")
            except Exception as e:
                if "Duplicate column name" in str(e) or "already exists" in str(e):
                    print("ℹ️  paper_trading_mode column already exists in users table")
                else:
                    print(f"⚠️  Error adding paper_trading_mode column: {e}")
            
            # Verify tables were created
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            required_tables = [
                'paper_trading_accounts',
                'paper_trades', 
                'paper_trading_sessions',
                'paper_balance_snapshots'
            ]
            
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                print(f"❌ Missing tables: {missing_tables}")
                return False
            
            print("✅ All paper trading tables created successfully!")
            
            # Create indexes for better performance
            print("📈 Creating database indexes...")
            
            try:
                db.engine.execute("""
                    CREATE INDEX IF NOT EXISTS idx_paper_trades_user_id ON paper_trades(user_id);
                    CREATE INDEX IF NOT EXISTS idx_paper_trades_account_id ON paper_trades(paper_account_id);
                    CREATE INDEX IF NOT EXISTS idx_paper_trades_status ON paper_trades(status);
                    CREATE INDEX IF NOT EXISTS idx_paper_trades_symbol ON paper_trades(symbol);
                    CREATE INDEX IF NOT EXISTS idx_paper_trades_entry_time ON paper_trades(entry_time);
                    
                    CREATE INDEX IF NOT EXISTS idx_paper_sessions_user_id ON paper_trading_sessions(user_id);
                    CREATE INDEX IF NOT EXISTS idx_paper_sessions_account_id ON paper_trading_sessions(paper_account_id);
                    CREATE INDEX IF NOT EXISTS idx_paper_sessions_status ON paper_trading_sessions(status);
                    
                    CREATE INDEX IF NOT EXISTS idx_paper_snapshots_account_id ON paper_balance_snapshots(paper_account_id);
                    CREATE INDEX IF NOT EXISTS idx_paper_snapshots_created_at ON paper_balance_snapshots(created_at);
                    CREATE INDEX IF NOT EXISTS idx_paper_snapshots_transaction_type ON paper_balance_snapshots(transaction_type);
                """)
                print("✅ Database indexes created successfully!")
            except Exception as e:
                print(f"⚠️  Warning: Some indexes may already exist: {e}")
            
            print("🎉 Paper trading migration completed successfully!")
            print("\n📋 Summary:")
            print("   - Added paper_trading_mode column to users table")
            print("   - Created paper_trading_accounts table")
            print("   - Created paper_trades table")
            print("   - Created paper_trading_sessions table")
            print("   - Created paper_balance_snapshots table")
            print("   - Created performance indexes")
            print("\n🚀 Paper trading system is ready to use!")
            
            return True
            
        except Exception as e:
            print(f"❌ Migration failed: {e}")
            return False

def verify_migration():
    """Verify that the migration was applied correctly."""
    app = create_app()
    
    with app.app_context():
        try:
            print("\n🔍 Verifying paper trading migration...")
            
            # Test creating a paper trading account
            from app.models.user import User
            
            # Find a test user or create one
            test_user = User.query.first()
            if not test_user:
                print("⚠️  No users found for testing. Please create a user first.")
                return False
            
            # Test paper trading account creation
            account = PaperTradingAccount.query.filter_by(user_id=test_user.id).first()
            if not account:
                account = PaperTradingAccount(
                    user_id=test_user.id,
                    initial_balance=10000
                )
                db.session.add(account)
                db.session.commit()
                print("✅ Test paper trading account created successfully")
            else:
                print("ℹ️  Paper trading account already exists for test user")
            
            # Test paper trading mode flag
            test_user.paper_trading_mode = True
            db.session.commit()
            print("✅ Paper trading mode flag updated successfully")
            
            # Reset the flag
            test_user.paper_trading_mode = False
            db.session.commit()
            
            print("✅ Paper trading migration verification completed!")
            return True
            
        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return False

if __name__ == "__main__":
    print("🎯 DeepTrade Paper Trading Migration")
    print("=" * 50)
    
    # Apply migration
    if apply_migration():
        # Verify migration
        if verify_migration():
            print("\n🎉 Paper trading system is fully operational!")
            print("\n📖 Next steps:")
            print("   1. Restart your Flask application")
            print("   2. Test the paper trading toggle in the dashboard")
            print("   3. Enable auto-trading in paper mode to test the system")
        else:
            print("\n⚠️  Migration applied but verification failed")
            sys.exit(1)
    else:
        print("\n❌ Migration failed")
        sys.exit(1)
