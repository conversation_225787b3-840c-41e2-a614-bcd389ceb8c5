#!/usr/bin/env python3
"""
Check admin IDs and see which admin is the "first" admin
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.admin import Admin<PERSON>ser

def check_admin_ids():
    """Check all admin IDs and identify the first admin"""
    print("🔍 CHECKING ADMIN IDS AND HIERARCHY")
    print("="*50)
    
    app = create_app()
    
    with app.app_context():
        # Get all admins ordered by ID (first created first)
        admins = AdminUser.query.order_by(AdminUser.id.asc()).all()
        
        if not admins:
            print("❌ No admins found in database")
            return
        
        print(f"📊 Total admins found: {len(admins)}")
        print("\n📋 Admin List (ordered by creation):")
        print("-" * 80)
        
        for i, admin in enumerate(admins, 1):
            status = "✅ Active" if admin.is_active else "❌ Inactive"
            role = "🔑 Super Admin" if admin.is_super_admin else "👤 Limited Admin"
            first_marker = "🥇 FIRST ADMIN" if i == 1 else ""
            
            print(f"{i:2d}. ID: {admin.id:2d} | {admin.username:30s} | {role:15s} | {status:10s} | {first_marker}")
            print(f"    Created: {admin.created_at}")
            print(f"    Created by: {admin.created_by}")
            print(f"    Last login: {admin.last_login or 'Never'}")
            print("-" * 80)
        
        # Identify the first admin
        first_admin = admins[0]
        print(f"\n🎯 FIRST ADMIN IDENTIFIED:")
        print(f"   ID: {first_admin.id}")
        print(f"   Username: {first_admin.username}")
        print(f"   Is Super Admin: {first_admin.is_super_admin}")
        print(f"   Is Active: {first_admin.is_active}")
        print(f"   Created by: {first_admin.created_by}")
        
        # Check if there are any special restrictions
        print(f"\n🔒 DELETION RESTRICTIONS:")
        print(f"   - Self-deletion: Prevented (admin cannot delete themselves)")
        print(f"   - First admin protection: {'NONE DETECTED' if not has_first_admin_protection() else 'PROTECTED'}")
        
        return first_admin

def has_first_admin_protection():
    """Check if there's any special protection for the first admin"""
    # This would check for any business logic that protects the first admin
    # Currently, based on the code review, there doesn't seem to be any
    return False

if __name__ == "__main__":
    check_admin_ids()
