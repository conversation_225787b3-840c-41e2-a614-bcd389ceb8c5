#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check for common TypeScript syntax errors in translation files
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_syntax_errors(lang):
    """Check for common syntax errors in a .ts file"""
    print(f"  Checking {lang.upper()}:")
    
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        errors = []
        
        for i, line in enumerate(lines, 1):
            # Check for double commas
            if ',,' in line:
                errors.append(f"Line {i}: Double comma found: {line.strip()}")
            
            # Check for missing commas after closing braces
            if re.search(r'}\s*"', line):
                errors.append(f"Line {i}: Missing comma after closing brace: {line.strip()}")
            
            # Check for trailing commas before closing braces
            if re.search(r',\s*}', line) and not re.search(r'{\s*}', line):
                # This is actually OK in TypeScript, but let's note it
                pass
            
            # Check for unmatched quotes
            quote_count = line.count('"')
            if quote_count % 2 != 0 and not line.strip().startswith('//'):
                errors.append(f"Line {i}: Unmatched quotes: {line.strip()}")
        
        # Check overall brace balance
        open_braces = content.count('{')
        close_braces = content.count('}')
        if open_braces != close_braces:
            errors.append(f"Brace mismatch: {open_braces} open, {close_braces} close")
        
        if errors:
            print(f"     [ERRORS FOUND] {len(errors)} syntax errors:")
            for error in errors:
                print(f"       - {error}")
            return False
        else:
            print(f"     [CLEAN] No syntax errors found")
            return True
            
    except Exception as e:
        print(f"     [ERROR] Could not read file: {str(e)}")
        return False

def fix_double_commas(lang):
    """Fix double commas in a .ts file"""
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix double commas
        original_content = content
        content = re.sub(r',,+', ',', content)
        
        if content != original_content:
            with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"     [FIXED] Double commas fixed in {lang}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"     [ERROR] Could not fix file: {str(e)}")
        return False

def run_syntax_check():
    """Run syntax check on all language files"""
    print("TYPESCRIPT SYNTAX ERROR CHECK")
    print("=" * 60)
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    clean_count = 0
    error_count = 0
    fixed_count = 0
    
    for lang in languages:
        if check_syntax_errors(lang):
            clean_count += 1
        else:
            error_count += 1
            # Try to fix common issues
            if fix_double_commas(lang):
                fixed_count += 1
                # Re-check after fix
                print(f"  Re-checking {lang.upper()} after fix:")
                if check_syntax_errors(lang):
                    clean_count += 1
                    error_count -= 1
    
    print("\n" + "=" * 60)
    print("SYNTAX CHECK SUMMARY")
    print("=" * 60)
    print(f"Total files checked: {len(languages)}")
    print(f"Clean files: {clean_count}")
    print(f"Files with errors: {error_count}")
    print(f"Files auto-fixed: {fixed_count}")
    
    if error_count == 0:
        print("\n✅ ALL TYPESCRIPT FILES HAVE CLEAN SYNTAX!")
        print("✅ Frontend should compile without errors")
        print("\n🎯 NEXT STEPS:")
        print("1. Frontend development server should restart automatically")
        print("2. Check that the compilation error is resolved")
        print("3. Access & Security page should load properly")
        return True
    else:
        print(f"\n❌ {error_count} file(s) still have syntax errors")
        print("Manual review and fixing may be required")
        return False

if __name__ == "__main__":
    success = run_syntax_check()
    sys.exit(0 if success else 1)
