#!/usr/bin/env python3
"""
Check existing users in the database
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User

def check_users():
    """Check existing users in the database"""
    print("👥 CHECKING EXISTING USERS...")
    print("="*50)
    
    app = create_app()
    
    with app.app_context():
        users = User.query.all()
        
        if not users:
            print("❌ No users found in database")
            return
        
        print(f"✅ Found {len(users)} users:")
        for user in users:
            print(f"   - Email: {user.email}")
            print(f"     ID: {user.id}")
            print(f"     Active: {user.is_active}")
            print(f"     Email Verified: {user.email_verified}")
            print(f"     Registration Type: {getattr(user, 'registration_type', 'unknown')}")
            print(f"     Created: {user.created_at}")
            print()

def reset_existing_user_password():
    """Reset password for existing user"""
    print("🔧 RESETTING EXISTING USER PASSWORD...")
    print("="*50)

    app = create_app()

    with app.app_context():
        # Get the existing user
        existing_user = User.query.filter_by(email='<EMAIL>').first()
        if existing_user:
            existing_user.set_password('testpassword123')
            existing_user.email_verified = True  # Make sure email is verified
            existing_user.is_active = True  # Make sure user is active

            db.session.commit()

            print("✅ Reset password for existing user:")
            print("   Email: <EMAIL>")
            print("   Password: testpassword123")
        else:
            print("❌ User not found")

def create_test_user():
    """Create a test user with known credentials"""
    print("🔧 CREATING TEST USER...")
    print("="*50)

    app = create_app()

    with app.app_context():
        # Check if test user already exists
        existing_user = User.query.filter_by(email='<EMAIL>').first()
        if existing_user:
            print("✅ Test user already exists: <EMAIL>")
            return

        # Create new test user
        test_user = User(
            email='<EMAIL>',
            full_name='Test User',
            is_active=True,
            email_verified=True
        )
        test_user.set_password('testpassword123')

        db.session.add(test_user)
        db.session.commit()

        print("✅ Created test user:")
        print("   Email: <EMAIL>")
        print("   Password: testpassword123")

if __name__ == "__main__":
    check_users()
    reset_existing_user_password()
    create_test_user()
