#!/usr/bin/env python3
"""
Comprehensive test to verify all language files for syntax and completion
"""

import sys
import os
import re
import json
import subprocess

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_syntax_errors(lang):
    """Check for TypeScript syntax errors in a language file"""
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        errors = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            # Check for double commas
            if ',,' in line:
                errors.append(f"Line {i}: Double comma: {line.strip()}")
            
            # Check for double closing braces
            if '}},' in line and not line.strip().startswith('//'):
                errors.append(f"Line {i}: Double closing brace: {line.strip()}")
            
            # Check for missing commas after closing braces
            if re.search(r'}\s*"', line):
                errors.append(f"Line {i}: Missing comma after brace: {line.strip()}")
        
        # Check overall brace balance
        open_braces = content.count('{')
        close_braces = content.count('}')
        if open_braces != close_braces:
            errors.append(f"Brace mismatch: {open_braces} open, {close_braces} close")
        
        return errors
        
    except Exception as e:
        return [f"Could not read file: {str(e)}"]

def check_auth_structure(lang):
    """Check if auth structure is correct"""
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for auth section
        has_auth = '"auth":' in content
        
        # Check for correct auth subsections
        has_login = bool(re.search(r'"auth":\s*{[^}]*"login":', content, re.DOTALL))
        has_register = bool(re.search(r'"auth":\s*{[^}]*"register":', content, re.DOTALL))
        has_pwd_req = bool(re.search(r'"auth":\s*{[^}]*"passwordRequirements":', content, re.DOTALL))
        has_errors = bool(re.search(r'"auth":\s*{[^}]*"errors":', content, re.DOTALL))
        
        # Check for incorrect nested structure
        has_nested_errors = bool(re.search(r'"login":\s*{[^}]*"errors":', content, re.DOTALL))
        
        structure = {
            'has_auth': has_auth,
            'has_login': has_login,
            'has_register': has_register,
            'has_pwd_req': has_pwd_req,
            'has_errors': has_errors,
            'has_nested_errors': has_nested_errors
        }
        
        # Determine if structure is correct
        correct = (has_auth and has_login and has_register and 
                  has_pwd_req and has_errors and not has_nested_errors)
        
        return structure, correct
        
    except Exception as e:
        return {}, False

def get_translation_completion():
    """Get translation completion percentages"""
    try:
        result = subprocess.run(['python', 'debug_translation_validation.py'], 
                              capture_output=True, text=True, cwd='.')
        output = result.stdout
        
        completion_rates = {}
        for line in output.split('\n'):
            if ' - ' in line and '% complete' in line:
                parts = line.split(' - ')
                if len(parts) == 2:
                    lang = parts[0].strip()
                    completion = parts[1].split('%')[0].strip()
                    try:
                        completion_rates[lang] = float(completion)
                    except ValueError:
                        pass
        
        return completion_rates
        
    except Exception as e:
        print(f"Error getting completion rates: {str(e)}")
        return {}

def run_comprehensive_test():
    """Run comprehensive test on all language files"""
    print("COMPREHENSIVE LANGUAGE FILE TEST")
    print("=" * 60)
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    print("\n1. SYNTAX ERROR CHECK:")
    print("-" * 30)
    syntax_results = {}
    for lang in languages:
        errors = check_syntax_errors(lang)
        syntax_results[lang] = errors
        
        if errors:
            print(f"❌ {lang.upper()}: {len(errors)} syntax error(s)")
            for error in errors[:3]:  # Show first 3 errors
                print(f"   - {error}")
            if len(errors) > 3:
                print(f"   ... and {len(errors) - 3} more")
        else:
            print(f"✅ {lang.upper()}: Clean syntax")
    
    print("\n2. AUTH STRUCTURE CHECK:")
    print("-" * 30)
    auth_results = {}
    for lang in languages:
        structure, correct = check_auth_structure(lang)
        auth_results[lang] = (structure, correct)
        
        if correct:
            print(f"✅ {lang.upper()}: Correct auth structure")
        else:
            print(f"❌ {lang.upper()}: Incorrect auth structure")
            if structure:
                missing = []
                if not structure.get('has_login'): missing.append('login')
                if not structure.get('has_register'): missing.append('register')
                if not structure.get('has_pwd_req'): missing.append('passwordRequirements')
                if not structure.get('has_errors'): missing.append('errors')
                if structure.get('has_nested_errors'): missing.append('nested_errors_found')
                if missing:
                    print(f"   Issues: {', '.join(missing)}")
    
    print("\n3. TRANSLATION COMPLETION CHECK:")
    print("-" * 35)
    completion_rates = get_translation_completion()
    
    if completion_rates:
        for lang in languages:
            if lang in completion_rates:
                rate = completion_rates[lang]
                if rate >= 98:
                    status = "🟢"
                elif rate >= 95:
                    status = "🟡"
                else:
                    status = "🔴"
                print(f"{status} {lang.upper()}: {rate}%")
            else:
                print(f"⚪ {lang.upper()}: No data")
    else:
        print("❌ Could not retrieve completion data")
    
    print("\n" + "=" * 60)
    print("COMPREHENSIVE TEST SUMMARY")
    print("=" * 60)
    
    # Count results
    clean_syntax = sum(1 for errors in syntax_results.values() if not errors)
    correct_auth = sum(1 for _, correct in auth_results.values() if correct)
    high_completion = sum(1 for rate in completion_rates.values() if rate >= 97)
    
    print(f"Languages with clean syntax: {clean_syntax}/{len(languages)}")
    print(f"Languages with correct auth structure: {correct_auth}/{len(languages)}")
    print(f"Languages with 97%+ completion: {high_completion}/{len(completion_rates)}")
    
    # Overall assessment
    if clean_syntax >= 7 and correct_auth >= 6 and high_completion >= 5:
        print(f"\n🎉 EXCELLENT STATUS!")
        print(f"✅ Most files have clean syntax")
        print(f"✅ Most files have correct auth structure")
        print(f"✅ Most files have high completion rates")
        
        print(f"\n🎯 EXPECTED RESULTS:")
        print(f"• Frontend should compile successfully")
        print(f"• Browser console should be clean")
        print(f"• Access & Security page should work perfectly")
        print(f"• Translation validation warnings should be minimal")
        
        print(f"\n🚀 READY FOR PRODUCTION!")
        return True
    elif clean_syntax >= 6 and correct_auth >= 4 and high_completion >= 3:
        print(f"\n🟡 GOOD PROGRESS!")
        print(f"✅ Significant improvements achieved")
        print(f"⚠️ Some issues remain but core functionality should work")
        
        print(f"\n🎯 NEXT STEPS:")
        print(f"1. Fix remaining syntax errors")
        print(f"2. Complete auth structure fixes")
        print(f"3. Address remaining translation gaps")
        return False
    else:
        print(f"\n🔴 NEEDS MORE WORK")
        print(f"❌ Multiple issues need to be addressed")
        print(f"❌ Core functionality may be affected")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
