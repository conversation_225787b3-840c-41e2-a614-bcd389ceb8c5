#!/usr/bin/env python3
"""
Direct table creation script for 2FA reset functionality
Run this from the backend directory
"""

import sys
import os

from app import create_app, db
from app.models.password_reset import PasswordResetToken, TwoFAResetRequest

def create_tables_directly():
    """Create tables directly using the current Flask configuration"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 Creating 2FA reset tables directly...")
            print(f"Database URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
            
            # Create only the specific tables we need
            print("Creating PasswordResetToken table...")
            PasswordResetToken.__table__.create(db.engine, checkfirst=True)
            
            print("Creating TwoFAResetRequest table...")
            TwoFAResetRequest.__table__.create(db.engine, checkfirst=True)
            
            print("✅ Successfully created tables:")
            print("   - password_reset_tokens")
            print("   - twofa_reset_requests")
            
            # Verify tables were created by querying the database engine
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            print(f"\n📋 All tables in database: {tables}")
            
            if 'twofa_reset_requests' in tables:
                print("✅ twofa_reset_requests table confirmed in database")
                columns = inspector.get_columns('twofa_reset_requests')
                print("   Columns:")
                for col in columns:
                    print(f"     - {col['name']} ({col['type']})")
            else:
                print("❌ twofa_reset_requests table not found")
            
            if 'password_reset_tokens' in tables:
                print("✅ password_reset_tokens table confirmed in database")
            else:
                print("❌ password_reset_tokens table not found")
            
            print("\n🎉 Table creation completed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Error creating tables: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = create_tables_directly()
    if success:
        print("\n✅ You can now test the 2FA reset functionality!")
    else:
        print("\n❌ Table creation failed!")
        sys.exit(1)
