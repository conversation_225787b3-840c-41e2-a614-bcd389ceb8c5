#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create admin tables manually
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.admin import Admin<PERSON>ser, CouponCode, CouponUsage, AdminAction

def create_admin_tables():
    """Create admin tables and default admin user"""
    app = create_app()
    
    with app.app_context():
        try:
            print("Creating admin tables...")
            
            # Create all tables
            db.create_all()
            
            print("Admin tables created successfully!")
            
            # Create default admin user
            print("Creating default admin user...")
            default_admin = AdminUser.create_default_admin()
            
            if default_admin:
                print(f"Default admin user created: {default_admin.username}")
                print("Default password: 12345")
                print("Please change the default password after first login!")
            else:
                print("Default admin user already exists")
            
            print("Setup completed successfully!")
            
        except Exception as e:
            print(f"Error creating admin tables: {str(e)}")
            db.session.rollback()
            return False
    
    return True

if __name__ == '__main__':
    success = create_admin_tables()
    sys.exit(0 if success else 1)
