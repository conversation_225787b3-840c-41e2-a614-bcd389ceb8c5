#!/usr/bin/env python3
"""
Create test access logs for testing the Access Security page
"""

import sys
import os
from datetime import datetime, timedelta
import random

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.ip_tracking import IPAccessLog

def create_test_data():
    """Create test access logs for existing users"""
    print("🔧 CREATING TEST ACCESS LOGS...")
    print("="*50)
    
    app = create_app()
    
    with app.app_context():
        # Get first user from database
        user = User.query.first()
        if not user:
            print("❌ No users found in database. Please create a user first.")
            return
        
        print(f"✅ Found user: {user.email} (ID: {user.id})")
        
        # Sample IP addresses and locations
        test_data = [
            {
                'ip': '*************',
                'location': 'New York, United States',
                'city': 'New York',
                'country_code': 'US',
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'success': True
            },
            {
                'ip': '*********',
                'location': 'Los Angeles, United States',
                'city': 'Los Angeles', 
                'country_code': 'US',
                'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'success': True
            },
            {
                'ip': '************',
                'location': 'London, United Kingdom',
                'city': 'London',
                'country_code': 'GB', 
                'user_agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'success': False,
                'failure_reason': 'Invalid password'
            },
            {
                'ip': '*************',
                'location': 'Toronto, Canada',
                'city': 'Toronto',
                'country_code': 'CA',
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
                'success': True
            },
            {
                'ip': '*************',
                'location': 'New York, United States', 
                'city': 'New York',
                'country_code': 'US',
                'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1',
                'success': True
            }
        ]
        
        # Create access logs with different timestamps
        for i, data in enumerate(test_data):
            timestamp = datetime.utcnow() - timedelta(hours=i*2, minutes=random.randint(0, 59))
            
            log = IPAccessLog(
                user_id=user.id,
                ip_address=data['ip'],
                user_agent=data['user_agent'],
                login_timestamp=timestamp,
                login_successful=data['success'],
                failure_reason=data.get('failure_reason'),
                geographic_location=data['location'],
                country_code=data['country_code'],
                city=data['city'],
                isp='Test ISP',
                is_proxy=False,
                is_vpn=False
            )
            
            db.session.add(log)
            print(f"   ✅ Created log: {data['ip']} - {data['location']} - {'Success' if data['success'] else 'Failed'}")
        
        db.session.commit()
        print(f"\n✅ Created {len(test_data)} test access logs for user {user.email}")
        
        # Show current log count
        total_logs = IPAccessLog.query.filter_by(user_id=user.id).count()
        print(f"📊 Total access logs for user: {total_logs}")

if __name__ == "__main__":
    create_test_data()
