#!/usr/bin/env python3
"""
Create a test coupon for manual testing in the frontend.
"""
import requests
import json
import sys
import os
from datetime import datetime, timed<PERSON><PERSON>

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.admin import AdminUser, CouponCode
from werkzeug.security import generate_password_hash

# Configuration
BACKEND_URL = "http://127.0.0.1:5000"

def create_test_coupon():
    """Create a test coupon for manual testing"""
    print("🎫 Creating test coupon for manual testing...")
    
    # First, ensure we have a super admin
    app = create_app()
    with app.app_context():
        try:
            # Create or update super admin
            super_admin = AdminUser.query.filter_by(username='test_admin').first()
            if not super_admin:
                super_admin = AdminUser(
                    username='test_admin',
                    password='admin123',
                    is_super_admin=True
                )
                super_admin.is_active = True
                db.session.add(super_admin)
            else:
                super_admin.is_super_admin = True
                super_admin.is_active = True
                super_admin.password_hash = generate_password_hash('admin123')
            
            db.session.commit()
            print(f"✅ Super admin ready: {super_admin.username}")
            
        except Exception as e:
            print(f"❌ Error setting up admin: {e}")
            return False
    
    # Login as admin
    try:
        admin_login_data = {
            "username": "test_admin",
            "password": "admin123"
        }
        
        response = requests.post(f"{BACKEND_URL}/api/admin/login", json=admin_login_data)
        
        if response.status_code == 200:
            admin_data = response.json()
            admin_token = admin_data.get('access_token')
            print(f"✅ Admin login successful")
        else:
            print(f"❌ Admin login failed: {response.status_code}")
            print(f"📝 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Admin login error: {e}")
        return False
    
    # Create test coupon
    try:
        headers = {
            'Authorization': f'Bearer {admin_token}',
            'Content-Type': 'application/json'
        }
        
        coupon_data = {
            'tier_level': 2,
            'expiration_days': 30,
            'max_uses': 10,
            'description': 'Manual testing coupon - Tier 2 upgrade',
            'custom_code': 'DTFB5GX'  # Custom code for easy testing
        }
        
        response = requests.post(
            f"{BACKEND_URL}/api/admin/coupons",
            headers=headers,
            json=coupon_data
        )
        
        if response.status_code == 201:
            data = response.json()
            coupon = data.get('coupon', {})
            
            print(f"✅ Test coupon created successfully!")
            print(f"📝 Coupon details:")
            print(f"   🎫 Code: {coupon.get('code')}")
            print(f"   🏆 Tier Level: {coupon.get('tier_level')}")
            print(f"   📊 Max Uses: {coupon.get('max_uses')}")
            print(f"   📅 Expires: {coupon.get('expiration_date')}")
            print(f"   📝 Description: {coupon.get('description')}")
            
            print(f"\n🌐 Ready for testing!")
            print(f"   Frontend URL: http://localhost:5173/redeem-coupon")
            print(f"   Test Coupon: {coupon.get('code')}")
            print(f"   Admin Panel: http://127.0.0.1:5000/admin")
            print(f"   Admin Login: test_admin / admin123")
            
            return True
        else:
            print(f"❌ Coupon creation failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"📝 Error response:")
                print(json.dumps(error_data, indent=2))
            except:
                print(f"📝 Raw response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Coupon creation error: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Setting up test coupon for manual testing")
    success = create_test_coupon()
    
    if success:
        print("\n🎉 Setup complete! You can now test the coupon redemption flow.")
    else:
        print("\n💥 Setup failed. Check the errors above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
