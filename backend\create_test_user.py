#!/usr/bin/env python3
"""
Create a test user for deletion testing
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.subscription import Subscription, SubscriptionTier
from app.models.user_tier_status import UserTierStatus
from app.models.security_log import LoginAttempt, SecurityLog

def create_test_user():
    """Create a test user with some associated data"""
    app = create_app()
    
    with app.app_context():
        # Create test user with unique email
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        test_user = User(
            email=f"test.deletion.{unique_id}@example.com",
            full_name="Test Deletion User",
            password="testpassword123"
        )
        
        db.session.add(test_user)
        db.session.flush()  # Get the user ID
        
        print(f"✅ Created test user: {test_user.id}")
        print(f"   Email: {test_user.email}")
        print(f"   Name: {test_user.full_name}")
        
        # Create a subscription
        subscription = Subscription(
            user_id=test_user.id,
            tier=SubscriptionTier.TIER_1
        )
        db.session.add(subscription)
        print(f"✅ Created subscription: {subscription.id}")
        
        # Create tier status
        tier_status = UserTierStatus(
            user_id=test_user.id,
            tier_1=True,
            tier_2=False,
            tier_3=False
        )
        db.session.add(tier_status)
        print(f"✅ Created tier status")
        
        # Create some login attempts
        login_attempt = LoginAttempt(
            ip_address="127.0.0.1",
            user_agent="Test Browser",
            user_id=test_user.id
        )
        login_attempt.mark_success()  # Mark as successful
        db.session.add(login_attempt)
        print(f"✅ Created login attempt")
        
        # Create security log
        security_log = SecurityLog(
            user_id=test_user.id,
            event_type="test_action",
            ip_address="127.0.0.1",
            user_agent="Test Browser"
        )
        db.session.add(security_log)
        print(f"✅ Created security log")
        
        # Commit all changes
        db.session.commit()
        
        print(f"\n🎯 Test user created successfully!")
        print(f"   User ID: {test_user.id}")
        print(f"   You can now test deletion in the admin panel")
        
        return test_user.id

if __name__ == "__main__":
    print("🚀 Creating test user for deletion testing...")
    user_id = create_test_user()
    print(f"\n✅ Test user ID: {user_id}")
    print("   Use this ID to test deletion in the admin panel")
