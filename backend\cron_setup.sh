#!/bin/bash

# DeepTrade Payday System Cron Job Setup
# This script sets up cron jobs for the payday system

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_PATH="/usr/bin/python3"  # Adjust this to your Python path
SCHEDULER_PATH="$SCRIPT_DIR/app/tasks/payday_scheduler.py"
FORECAST_SCHEDULER_PATH="$SCRIPT_DIR/app/tasks/forecast_scheduler.py"

echo "Setting up DeepTrade Payday System cron jobs..."

# Create a temporary cron file
TEMP_CRON=$(mktemp)

# Get existing cron jobs (excluding DeepTrade ones)
crontab -l 2>/dev/null | grep -v "DeepTrade Payday" > "$TEMP_CRON"

# Add DeepTrade Payday cron jobs
cat >> "$TEMP_CRON" << EOF

# DeepTrade Payday System - Email Warnings (runs every 6 hours)
0 */6 * * * cd $SCRIPT_DIR && $PYTHON_PATH $SCHEDULER_PATH --task email-warnings >> /var/log/deeptrade_email_warnings.log 2>&1

# DeepTrade Payday System - In-app Warnings (runs every 2 hours)
0 */2 * * * cd $SCRIPT_DIR && $PYTHON_PATH $SCHEDULER_PATH --task warnings >> /var/log/deeptrade_warnings.log 2>&1

# DeepTrade Payday System - Account Enforcement (runs every Saturday at 0:05 GMT)
5 0 * * 6 cd $SCRIPT_DIR && $PYTHON_PATH $SCHEDULER_PATH --task enforce >> /var/log/deeptrade_enforcement.log 2>&1

# DeepTrade Payday System - Full Daily Tasks (runs daily at 1:00 AM GMT)
0 1 * * * cd $SCRIPT_DIR && $PYTHON_PATH $SCHEDULER_PATH --task all >> /var/log/deeptrade_daily.log 2>&1

# DeepTrade Payday System - Status Check (runs every 12 hours)
0 */12 * * * cd $SCRIPT_DIR && $PYTHON_PATH $SCHEDULER_PATH --task status >> /var/log/deeptrade_status.log 2>&1

# DeepTrade Security Cleanup - Clean expired 2FA codes (runs every 4 hours)
0 */4 * * * cd $SCRIPT_DIR && $PYTHON_PATH $SCHEDULER_PATH --task security-cleanup >> /var/log/deeptrade_security_cleanup.log 2>&1

# DeepTrade ML Forecast - Generate new forecasts (runs every hour at minute :00)
0 * * * * cd $SCRIPT_DIR && $PYTHON_PATH $FORECAST_SCHEDULER_PATH --task maintenance >> /var/log/deeptrade_forecast.log 2>&1

# DeepTrade ML Training - Retrain Elite ML and SL/TP ML models (runs every hour at minute :30)
30 * * * * cd $SCRIPT_DIR && $PYTHON_PATH app/tasks/ml_training_scheduler.py --task hourly >> /var/log/deeptrade_ml_training.log 2>&1

# DeepTrade Deep Learning Training - Retrain deep learning models (runs daily at 2:00 AM GMT)
0 2 * * * cd $SCRIPT_DIR && $PYTHON_PATH app/tasks/ml_training_scheduler.py --task daily-deep >> /var/log/deeptrade_deep_learning.log 2>&1

EOF

# Install the new cron jobs
crontab "$TEMP_CRON"

# Clean up
rm "$TEMP_CRON"

echo "Cron jobs installed successfully!"
echo ""
echo "Installed cron jobs:"
echo "- Email warnings: Every 6 hours"
echo "- In-app warnings: Every 2 hours"
echo "- Account enforcement: Every Saturday at 0:05 GMT"
echo "- Full daily tasks: Daily at 1:00 AM GMT"
echo "- Status check: Every 12 hours"
echo "- ML forecast generation: Every hour at minute :00"
echo "- ML model training (Elite/SL-TP): Every hour at minute :30"
echo "- Deep learning training: Daily at 2:00 AM GMT"
echo ""
echo "Log files will be created in /var/log/"
echo "Make sure the log directory is writable and Python path is correct."
echo ""
echo "To view current cron jobs: crontab -l"
echo "To remove DeepTrade cron jobs: crontab -l | grep -v 'DeepTrade Payday' | crontab -"
