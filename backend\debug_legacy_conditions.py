#!/usr/bin/env python3
"""
Debug why optimized legacy is generating 0 signals
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_legacy_conditions():
    """Debug why optimized legacy generates no signals"""
    print("🔍 Debugging Optimized Legacy Conditions")
    print("=" * 60)
    
    try:
        # Import Flask app and create application context
        from app import create_app
        app = create_app()
        
        with app.app_context():
            # Import required services
            from app.services.market_data import BinanceMarketData
            from app.services.trading_signals import TradingSignalGenerator
            
            # Initialize services
            market_service = BinanceMarketData()
            signal_generator = TradingSignalGenerator("test_user", market_service)
            
            print("✅ Services initialized successfully")
            
            # Create a strong trending scenario that SHOULD generate signals
            print(f"\n🎯 Creating Strong Bull Trend Scenario...")
            
            # Generate 100 hours of data for better indicator calculation
            hours = 100
            dates = pd.date_range(start='2024-01-01', periods=hours, freq='H')
            
            # Strong upward trend
            base_price = 65000
            trend_per_hour = 30  # $30 per hour = $720/day strong trend
            
            prices = []
            for i in range(hours):
                trend_component = trend_per_hour * i
                noise = np.random.normal(0, 150)  # Moderate noise
                price = base_price + trend_component + noise
                prices.append(max(price, 60000))  # Floor at $60k
            
            # Create realistic OHLC with proper relationships
            mock_data = pd.DataFrame({
                'timestamp': [int(d.timestamp() * 1000) for d in dates],
                'open': prices,
                'high': [p * 1.008 for p in prices],  # 0.8% higher
                'low': [p * 0.992 for p in prices],   # 0.8% lower
                'close': [p * (1 + np.random.uniform(-0.003, 0.005)) for p in prices],  # Small close variation
                'volume': [np.random.uniform(3000, 7000) for _ in range(hours)]  # Good volume
            })
            
            # Ensure OHLC relationships
            for i in range(len(mock_data)):
                open_price = mock_data.loc[i, 'open']
                close_price = mock_data.loc[i, 'close']
                mock_data.loc[i, 'high'] = max(mock_data.loc[i, 'high'], open_price, close_price)
                mock_data.loc[i, 'low'] = min(mock_data.loc[i, 'low'], open_price, close_price)
            
            print(f"   📊 Data Points: {len(mock_data)}")
            print(f"   💰 Price Range: ${mock_data['close'].min():,.0f} - ${mock_data['close'].max():,.0f}")
            print(f"   📈 Total Trend: +${mock_data['close'].iloc[-1] - mock_data['close'].iloc[0]:,.0f}")
            
            # Test the last few hours for signals
            test_hours = [80, 85, 90, 95, 99]  # Test multiple points
            
            for test_hour in test_hours:
                print(f"\n🔍 Testing Hour {test_hour}:")
                print("-" * 40)
                
                # Use data up to test hour
                current_data = mock_data.iloc[:test_hour+1].copy()
                current_price = float(current_data['close'].iloc[-1])
                
                print(f"   💰 Current Price: ${current_price:,.2f}")
                
                # Create optimistic forecast (should trigger BUY)
                forecast = np.array([current_price * (1 + 0.02 + i*0.001) for i in range(24)])  # 2%+ upward forecast
                highest_price = np.max(forecast)
                lowest_price = np.min(forecast)
                
                print(f"   📈 Forecast High: ${highest_price:,.2f} (+{((highest_price/current_price)-1)*100:.1f}%)")
                print(f"   📉 Forecast Low: ${lowest_price:,.2f} (+{((lowest_price/current_price)-1)*100:.1f}%)")
                
                # Create favorable swing points
                swing_points = {
                    'swing_high': current_price * 1.03,  # 3% above (should allow BUY)
                    'swing_low': current_price * 0.95    # 5% below
                }
                
                print(f"   🔺 Swing High: ${swing_points['swing_high']:,.2f}")
                print(f"   🔻 Swing Low: ${swing_points['swing_low']:,.2f}")
                
                # Calculate Heikin-Ashi
                heikin_ashi = signal_generator._calculate_heikin_ashi(current_data)
                
                print(f"   🕯️ HA Color: {heikin_ashi.get('ha_color', 'N/A')}")
                
                # Test the optimized legacy system with debugging
                try:
                    result = signal_generator._analyze_trading_conditions(
                        market_data=current_data,
                        forecast=forecast,
                        swing_points=swing_points,
                        heikin_ashi=heikin_ashi,
                        highest_price=highest_price,
                        lowest_price=lowest_price
                    )
                    
                    if 'error' in result:
                        print(f"   ❌ Error: {result['error']}")
                        continue
                    
                    signal = result.get('signal', 'UNKNOWN')
                    confidence = result.get('confidence', 0)
                    
                    print(f"   🎯 Signal: {signal}")
                    print(f"   📊 Confidence: {confidence:.1f}%")
                    
                    # Show optimized indicators if available
                    if 'optimized_indicators' in result:
                        opt_ind = result['optimized_indicators']
                        
                        # Moving averages
                        ma = opt_ind.get('moving_averages', {})
                        print(f"   📈 MA Bullish Alignment: {ma.get('bullish_alignment', False)}")
                        print(f"   📉 MA Bearish Alignment: {ma.get('bearish_alignment', False)}")
                        
                        # RSI
                        mom = opt_ind.get('momentum', {})
                        rsi = mom.get('rsi', 0)
                        print(f"   ⚡ RSI: {rsi:.1f} (OS: {mom.get('rsi_oversold', False)}, OB: {mom.get('rsi_overbought', False)})")
                        
                        # MACD
                        macd = opt_ind.get('macd', {})
                        print(f"   📊 MACD Bullish Cross: {macd.get('bullish_cross', False)}")
                        print(f"   📊 MACD Bearish Cross: {macd.get('bearish_cross', False)}")
                        
                        # Volume
                        vol = opt_ind.get('volume', {})
                        print(f"   📊 Volume Surge: {vol.get('surge', False)} (Ratio: {vol.get('ratio', 0):.1f}x)")
                        
                        # Price momentum
                        pm = opt_ind.get('price_momentum', {})
                        print(f"   🚀 4H Momentum: {pm.get('4h_change', 0):.2f}%")
                    
                    # Calculate potential moves manually
                    potential_up = (highest_price - current_price) / current_price
                    potential_down = (current_price - lowest_price) / current_price
                    
                    print(f"   📈 Potential Up Move: {potential_up*100:.2f}%")
                    print(f"   📉 Potential Down Move: {potential_down*100:.2f}%")
                    
                    if signal != 'HOLD':
                        print(f"   ✅ SIGNAL GENERATED!")
                        break
                    else:
                        print(f"   ⚪ HOLD - Conditions not met")
                        
                except Exception as e:
                    print(f"   ❌ Error testing hour {test_hour}: {e}")
                    import traceback
                    traceback.print_exc()
            
            # Test with more relaxed conditions
            print(f"\n🔧 Testing with More Relaxed Conditions...")
            print("=" * 50)
            
            # Create even stronger scenario
            strong_data = mock_data.copy()
            
            # Boost the last few prices to create stronger trend
            for i in range(-10, 0):
                strong_data.iloc[i, strong_data.columns.get_loc('close')] *= (1 + abs(i) * 0.005)  # 0.5% per hour boost
                strong_data.iloc[i, strong_data.columns.get_loc('high')] *= (1 + abs(i) * 0.005)
            
            # Boost volume significantly
            strong_data.iloc[-10:, strong_data.columns.get_loc('volume')] *= 2.5  # 2.5x volume surge
            
            current_price = float(strong_data['close'].iloc[-1])
            print(f"   💰 Boosted Price: ${current_price:,.2f}")
            
            # Create very optimistic forecast
            forecast = np.array([current_price * (1 + 0.04 + i*0.002) for i in range(24)])  # 4%+ forecast
            highest_price = np.max(forecast)
            
            # Very favorable swing points
            swing_points = {
                'swing_high': current_price * 1.05,  # 5% above
                'swing_low': current_price * 0.92    # 8% below
            }
            
            heikin_ashi = signal_generator._calculate_heikin_ashi(strong_data)
            
            print(f"   📈 Forecast High: ${highest_price:,.2f} (+{((highest_price/current_price)-1)*100:.1f}%)")
            print(f"   🔺 Swing High: ${swing_points['swing_high']:,.2f}")
            print(f"   🕯️ HA Color: {heikin_ashi.get('ha_color', 'N/A')}")
            
            result = signal_generator._analyze_trading_conditions(
                market_data=strong_data,
                forecast=forecast,
                swing_points=swing_points,
                heikin_ashi=heikin_ashi,
                highest_price=highest_price,
                lowest_price=np.min(forecast)
            )
            
            signal = result.get('signal', 'UNKNOWN')
            confidence = result.get('confidence', 0)
            
            print(f"   🎯 Relaxed Test Signal: {signal}")
            print(f"   📊 Relaxed Test Confidence: {confidence:.1f}%")
            
            if signal != 'HOLD':
                print(f"   ✅ SUCCESS! Signal generated with relaxed conditions")
            else:
                print(f"   ❌ Still HOLD - System may be too conservative")
            
            return True
            
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 DeepTrade Legacy Conditions Debug")
    print("=" * 50)
    
    success = debug_legacy_conditions()
    
    if success:
        print(f"\n🎉 DEBUG COMPLETE!")
        print(f"\n💡 If still getting HOLD signals, the system may need:")
        print(f"   • Lower potential move threshold (1.5% → 1.0%)")
        print(f"   • Relaxed RSI thresholds (35/65 → 30/70)")
        print(f"   • Reduced confirmation requirements")
        print(f"   • More permissive volume requirements")
    else:
        print(f"\n❌ DEBUG FAILED")
