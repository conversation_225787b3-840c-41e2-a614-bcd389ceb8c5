#!/usr/bin/env python3
"""
Debug script to analyze why SELL signal is showing as HOLD
Based on the terminal output showing SELL conditions are met
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.market_data import BinanceMarketData, ml_service
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_signal_conditions():
    """Debug the signal generation conditions"""

    print("🔍 DEBUGGING SIGNAL CONDITIONS AFTER FIX")
    print("=" * 50)

    # Use the global ml_service instance
    market_service = BinanceMarketData()

    try:
        # Create signal generator with market service
        from app.services.trading_signals import TradingSignalGenerator
        signal_gen = TradingSignalGenerator(user_id=1, exchange_service=market_service)

        # Generate signal
        result = signal_gen.generate_signals('BTCUSDT', '1h')
        
        if 'error' in result:
            print(f"❌ Error: {result['error']}")
            return
            
        # Extract key values
        signal = result.get('signal', 'UNKNOWN')
        current_price = result.get('current_price', 0)
        swing_high = result.get('swing_high')
        swing_low = result.get('swing_low')
        ha_color = result.get('ha_color')
        prev_ha_color = result.get('prev_ha_color')
        potential_down_move = result.get('potential_down_move', 0)
        potential_up_move = result.get('potential_up_move', 0)
        sma12 = result.get('sma12')
        
        print(f"🎯 Current Signal: {signal}")
        print(f"💰 Current Price: ${current_price:,.2f}")
        print(f"📊 SMA12: {sma12}")
        print()
        
        print("📈 SELL Signal Conditions Analysis:")
        print("-" * 40)
        
        # Check each SELL condition
        condition1 = swing_high is not None and swing_high > current_price
        condition2 = potential_down_move > 1.0  # 1% = 0.01 in decimal
        condition3 = ha_color == "red"
        condition4 = prev_ha_color == "red"
        
        print(f"1. Swing High > Price: {condition1}")
        print(f"   - Swing High: {swing_high}")
        print(f"   - Current Price: {current_price}")
        print(f"   - Condition Met: {swing_high > current_price if swing_high else False}")
        print()
        
        print(f"2. Down Move > 1%: {condition2}")
        print(f"   - Potential Down Move: {potential_down_move}%")
        print(f"   - Condition Met: {potential_down_move > 1.0}")
        print()
        
        print(f"3. Current HA Red: {condition3}")
        print(f"   - HA Color: {ha_color}")
        print(f"   - Condition Met: {ha_color == 'red'}")
        print()
        
        print(f"4. Previous HA Red: {condition4}")
        print(f"   - Prev HA Color: {prev_ha_color}")
        print(f"   - Condition Met: {prev_ha_color == 'red'}")
        print()
        
        # The critical condition that might be failing
        print("🔍 CRITICAL CONDITION ANALYSIS:")
        print("-" * 40)
        
        # Get the Heikin-Ashi data to check the 5th condition
        market_data = signal_gen._fetch_market_data('BTCUSDT', '1h')
        if market_data is not None:
            heikin_ashi = signal_gen._calculate_heikin_ashi(market_data)
            previous_ha_close = heikin_ashi.get('previous_ha_close')
            
            # Calculate SMA12
            market_data['SMA12'] = market_data['close'].astype(float).rolling(window=12).mean()
            current_sma12 = float(market_data['SMA12'].iloc[-1])
            
            condition5 = previous_ha_close is not None and previous_ha_close < current_sma12
            
            print(f"5. Previous HA Close < SMA12: {condition5}")
            print(f"   - Previous HA Close: {previous_ha_close}")
            print(f"   - Current SMA12: {current_sma12}")
            print(f"   - Condition Met: {previous_ha_close < current_sma12 if previous_ha_close else False}")
            print()
            
            # Summary
            all_conditions = [condition1, condition2, condition3, condition4, condition5]
            print("📋 CONDITION SUMMARY:")
            print("-" * 20)
            for i, cond in enumerate(all_conditions, 1):
                status = "✅ PASS" if cond else "❌ FAIL"
                print(f"   Condition {i}: {status}")
            
            print()
            if all(all_conditions):
                print("🎉 ALL CONDITIONS MET - Should be SELL signal!")
            else:
                failed_conditions = [i+1 for i, cond in enumerate(all_conditions) if not cond]
                print(f"⚠️  FAILED CONDITIONS: {failed_conditions}")
                print("   This is why the signal shows HOLD instead of SELL")
        
        print()
        print("🔧 FIXES APPLIED:")
        print("-" * 40)
        print("1. DATA SOURCE FIX:")
        print("   - Terminal Bot: Uses Binance FUTURES API (https://fapi.binance.com)")
        print("   - Signal Generator: Now uses Binance FUTURES API (https://fapi.binance.com)")
        print("   - Both systems now use the same data source!")
        print()
        print("2. MISSING CONDITIONS FIX:")
        print("   - Added 'not has_open_position' check (equivalent to 'not final_check')")
        print("   - Added 'current_price > avg_lower_bound' check for SELL signals")
        print("   - Added 'current_price < avg_upper_bound' check for BUY signals")
        print()
        print("3. REFRESH BUTTON ADDED:")
        print("   - Added refresh button to the signal container in admin dashboard")
        print("   - Users can now manually refresh signals to see updates immediately")
        print()
        print("Terminal shows: SELL Signal Conditions: Swing High > Price: True | Down Move > 1%: True | Red Trend: True")
        print("Signal Generator should now match this with all conditions properly implemented!")
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_signal_conditions()
