#!/usr/bin/env python3
"""
Debug script to identify missing translation keys causing validation issues
"""

import sys
import os
import json
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def parse_ts_file(lang):
    """Parse a .ts file and extract all translation keys"""
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove the export default and closing brace
        content = content.replace('export default {', '{')
        
        # Convert TypeScript object to JSON-like format for parsing
        # This is a simplified approach - we'll extract keys manually
        keys = set()
        
        # Find all quoted keys in the format "key":
        key_pattern = r'"([^"]+)":\s*(?:"[^"]*"|{)'
        matches = re.findall(key_pattern, content)
        
        # Build nested keys
        lines = content.split('\n')
        current_path = []
        
        for line in lines:
            line = line.strip()
            
            # Skip empty lines and comments
            if not line or line.startswith('//'):
                continue
            
            # Check for opening braces (new section)
            if '"' in line and ':{' in line:
                # Extract the key name
                key_match = re.search(r'"([^"]+)":\s*{', line)
                if key_match:
                    key = key_match.group(1)
                    current_path.append(key)
            
            # Check for closing braces (end section)
            elif line.startswith('}'):
                if current_path:
                    current_path.pop()
            
            # Check for key-value pairs
            elif '"' in line and '":' in line and not ':{' in line:
                key_match = re.search(r'"([^"]+)":', line)
                if key_match:
                    key = key_match.group(1)
                    full_key = '.'.join(current_path + [key])
                    keys.add(full_key)
        
        return keys
        
    except Exception as e:
        print(f"Error parsing {lang}/common.ts: {str(e)}")
        return set()

def compare_translation_keys():
    """Compare translation keys across all languages"""
    print("TRANSLATION KEY COMPARISON")
    print("=" * 60)
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    all_keys = {}
    
    # Parse all language files
    for lang in languages:
        print(f"Parsing {lang.upper()}...")
        keys = parse_ts_file(lang)
        all_keys[lang] = keys
        print(f"  Found {len(keys)} keys")
    
    # Use English as reference
    en_keys = all_keys['en']
    print(f"\nUsing English as reference: {len(en_keys)} keys")
    
    # Compare each language against English
    print("\nMISSING KEYS ANALYSIS:")
    print("-" * 40)
    
    for lang in languages:
        if lang == 'en':
            continue
        
        lang_keys = all_keys[lang]
        missing_keys = en_keys - lang_keys
        extra_keys = lang_keys - en_keys
        
        completeness = ((len(en_keys) - len(missing_keys)) / len(en_keys)) * 100
        
        print(f"\n{lang.upper()} - {completeness:.2f}% complete:")
        
        if missing_keys:
            print(f"  Missing {len(missing_keys)} keys:")
            for key in sorted(missing_keys)[:10]:  # Show first 10
                print(f"    - {key}")
            if len(missing_keys) > 10:
                print(f"    ... and {len(missing_keys) - 10} more")
        
        if extra_keys:
            print(f"  Extra {len(extra_keys)} keys:")
            for key in sorted(extra_keys)[:5]:  # Show first 5
                print(f"    + {key}")
            if len(extra_keys) > 5:
                print(f"    ... and {len(extra_keys) - 5} more")
        
        if not missing_keys and not extra_keys:
            print("  ✅ Perfect match with English")

def find_common_missing_patterns():
    """Find common patterns in missing keys"""
    print("\n" + "=" * 60)
    print("COMMON MISSING PATTERNS ANALYSIS")
    print("=" * 60)
    
    languages = ['es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    all_missing = {}
    
    en_keys = parse_ts_file('en')
    
    for lang in languages:
        lang_keys = parse_ts_file(lang)
        missing = en_keys - lang_keys
        all_missing[lang] = missing
    
    # Find keys missing in multiple languages
    key_missing_count = {}
    for lang, missing_keys in all_missing.items():
        for key in missing_keys:
            if key not in key_missing_count:
                key_missing_count[key] = []
            key_missing_count[key].append(lang)
    
    # Sort by how many languages are missing each key
    sorted_missing = sorted(key_missing_count.items(), 
                          key=lambda x: len(x[1]), reverse=True)
    
    print("\nKeys missing in multiple languages:")
    for key, langs in sorted_missing[:20]:  # Show top 20
        if len(langs) > 1:
            print(f"  {key} - missing in {len(langs)} languages: {', '.join(langs)}")

def run_debug():
    """Run the debug analysis"""
    try:
        compare_translation_keys()
        find_common_missing_patterns()
        
        print("\n" + "=" * 60)
        print("RECOMMENDATIONS")
        print("=" * 60)
        print("1. Check if recent .ts file updates introduced inconsistencies")
        print("2. Verify that all languages have the same structure as English")
        print("3. Consider running a sync script to ensure all keys are present")
        print("4. The validation system may need to be updated after recent changes")
        
        return True
    except Exception as e:
        print(f"Error during debug: {str(e)}")
        return False

if __name__ == "__main__":
    success = run_debug()
    sys.exit(0 if success else 1)
