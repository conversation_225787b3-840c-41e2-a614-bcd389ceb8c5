#!/usr/bin/env python3
"""
Definitive fix for auth structure - rebuild from scratch to match English
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_english_auth_structure():
    """Extract the correct auth structure from English file"""
    try:
        with open('../frontend/src/i18n/locales/en/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the auth section
        auth_match = re.search(r'"auth":\s*{([^}]+(?:{[^}]*}[^}]*)*)}', content, re.DOTALL)
        if not auth_match:
            return None
        
        return auth_match.group(1)
    except Exception as e:
        print(f"Error reading English file: {str(e)}")
        return None

def rebuild_auth_section(lang):
    """Completely rebuild the auth section for a language"""
    print(f"  Rebuilding {lang.upper()}:")
    
    # Get the correct structure from English
    english_auth = get_english_auth_structure()
    if not english_auth:
        print(f"     [ERROR] Could not get English auth structure")
        return False
    
    # Define translations for the auth sections
    translations = {
        'es': {
            'login': {
                'title': 'Bienvenido de Nuevo',
                'subtitle': 'Ingresa tu email y contraseña para iniciar sesión',
                'email': 'Email',
                'password': 'Contraseña',
                'emailPlaceholder': '<EMAIL>',
                'passwordPlaceholder': '••••••••',
                'rememberMe': 'Recordarme',
                'forgotPassword': '¿Olvidaste tu contraseña?',
                'signIn': 'Iniciar Sesión',
                'noAccount': '¿No tienes una cuenta?',
                'signUp': 'Registrarse',
                'googleSignIn': 'Continuar con Google',
                'orContinueWith': 'O continuar con',
                'fillAllFields': 'Por favor completa todos los campos',
                'loginSuccess': '¡Inicio de sesión exitoso!',
                'loginFailed': 'Error al iniciar sesión',
                'loginFailedDescription': 'No se pudo iniciar sesión'
            },
            'register': {
                'title': 'Crear Cuenta',
                'subtitle': 'Ingresa tu información para crear una cuenta',
                'firstName': 'Nombre',
                'lastName': 'Apellido',
                'email': 'Email',
                'password': 'Contraseña',
                'confirmPassword': 'Confirmar Contraseña',
                'agreeTerms': 'Al hacer clic en continuar, aceptas nuestros',
                'createAccount': 'Crear cuenta',
                'hasAccount': '¿Ya tienes una cuenta?',
                'signIn': 'Iniciar sesión',
                'orContinueWith': 'O continuar con',
                'googleSignUp': 'Registrarse con Google',
                'termsOfService': 'Términos de Servicio',
                'privacyPolicy': 'Política de Privacidad'
            },
            'passwordRequirements': {
                'title': 'Requisitos de Contraseña',
                'length': 'Al menos 8 caracteres',
                'uppercase': 'Una letra mayúscula',
                'lowercase': 'Una letra minúscula',
                'number': 'Un número',
                'special': 'Un carácter especial'
            },
            'errors': {
                'invalidCredentials': 'Email o contraseña inválidos',
                'emailRequired': 'Email es requerido',
                'passwordRequired': 'Contraseña es requerida',
                'passwordTooShort': 'La contraseña debe tener al menos 8 caracteres',
                'passwordsNotMatch': 'Las contraseñas no coinciden',
                'emailInvalid': 'Por favor ingresa una dirección de email válida',
                'termsRequired': 'Debes aceptar los términos y condiciones'
            }
        },
        'pt': {
            'login': {
                'title': 'Bem-vindo de Volta',
                'subtitle': 'Digite seu email e senha para entrar',
                'email': 'Email',
                'password': 'Senha',
                'emailPlaceholder': '<EMAIL>',
                'passwordPlaceholder': '••••••••',
                'rememberMe': 'Lembrar de mim',
                'forgotPassword': 'Esqueceu a senha?',
                'signIn': 'Entrar',
                'noAccount': 'Não tem uma conta?',
                'signUp': 'Cadastrar-se',
                'googleSignIn': 'Continuar com Google',
                'orContinueWith': 'Ou continuar com',
                'fillAllFields': 'Por favor, preencha todos os campos',
                'loginSuccess': 'Login realizado com sucesso!',
                'loginFailed': 'Falha no login',
                'loginFailedDescription': 'Falha ao fazer login'
            },
            'register': {
                'title': 'Criar Conta',
                'subtitle': 'Digite suas informações para criar uma conta',
                'firstName': 'Nome',
                'lastName': 'Sobrenome',
                'email': 'Email',
                'password': 'Senha',
                'confirmPassword': 'Confirmar Senha',
                'agreeTerms': 'Ao clicar em continuar, você concorda com nossos',
                'createAccount': 'Criar conta',
                'hasAccount': 'Já tem uma conta?',
                'signIn': 'Entrar',
                'orContinueWith': 'Ou continuar com',
                'googleSignUp': 'Cadastrar-se com Google',
                'termsOfService': 'Termos de Serviço',
                'privacyPolicy': 'Política de Privacidade'
            },
            'passwordRequirements': {
                'title': 'Requisitos de Senha',
                'length': 'Pelo menos 8 caracteres',
                'uppercase': 'Uma letra maiúscula',
                'lowercase': 'Uma letra minúscula',
                'number': 'Um número',
                'special': 'Um caractere especial'
            },
            'errors': {
                'invalidCredentials': 'Email ou senha inválidos',
                'emailRequired': 'Email é obrigatório',
                'passwordRequired': 'Senha é obrigatória',
                'passwordTooShort': 'A senha deve ter pelo menos 8 caracteres',
                'passwordsNotMatch': 'As senhas não coincidem',
                'emailInvalid': 'Por favor, insira um endereço de email válido',
                'termsRequired': 'Você deve concordar com os termos e condições'
            }
        }
        # Add more languages as needed...
    }
    
    if lang not in translations:
        print(f"     [SKIP] No translations available for {lang}")
        return False
    
    try:
        # Read the current file
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Build the new auth section
        auth_lines = ['  "auth": {']
        
        # Add login section
        auth_lines.append('    "login": {')
        for key, value in translations[lang]['login'].items():
            auth_lines.append(f'      "{key}": "{value}",')
        # Remove last comma
        auth_lines[-1] = auth_lines[-1][:-1]
        auth_lines.append('    },')
        
        # Add register section
        auth_lines.append('    "register": {')
        for key, value in translations[lang]['register'].items():
            auth_lines.append(f'      "{key}": "{value}",')
        # Remove last comma
        auth_lines[-1] = auth_lines[-1][:-1]
        auth_lines.append('    },')
        
        # Add passwordRequirements section
        auth_lines.append('    "passwordRequirements": {')
        for key, value in translations[lang]['passwordRequirements'].items():
            auth_lines.append(f'      "{key}": "{value}",')
        # Remove last comma
        auth_lines[-1] = auth_lines[-1][:-1]
        auth_lines.append('    },')
        
        # Add errors section
        auth_lines.append('    "errors": {')
        for key, value in translations[lang]['errors'].items():
            auth_lines.append(f'      "{key}": "{value}",')
        # Remove last comma
        auth_lines[-1] = auth_lines[-1][:-1]
        auth_lines.append('    }')
        
        auth_lines.append('  },')
        
        new_auth_section = '\n'.join(auth_lines)
        
        # Replace the entire auth section
        new_content = re.sub(
            r'"auth":\s*{[^}]+(?:{[^}]*}[^}]*)*}',
            new_auth_section,
            content,
            flags=re.DOTALL
        )
        
        # Write back to file
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"     [SUCCESS] Auth section completely rebuilt")
        return True
        
    except Exception as e:
        print(f"     [ERROR] {str(e)}")
        return False

def test_final_structure(lang):
    """Test the final auth structure"""
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for correct structure
        has_auth_errors = bool(re.search(r'"auth":\s*{[^}]*"errors":\s*{[^}]*"emailInvalid"', content, re.DOTALL))
        has_auth_pwd_req = bool(re.search(r'"auth":\s*{[^}]*"passwordRequirements":\s*{[^}]*"title"', content, re.DOTALL))
        has_auth_login = bool(re.search(r'"auth":\s*{[^}]*"login":\s*{[^}]*"title"', content, re.DOTALL))
        has_auth_register = bool(re.search(r'"auth":\s*{[^}]*"register":\s*{[^}]*"title"', content, re.DOTALL))
        
        # Check for incorrect nested structure
        has_nested_errors = bool(re.search(r'"login":\s*{[^}]*"errors":', content, re.DOTALL))
        
        all_correct = (has_auth_errors and has_auth_pwd_req and has_auth_login and 
                      has_auth_register and not has_nested_errors)
        
        print(f"  Testing {lang.upper()}:")
        print(f"     auth.errors: {'✅' if has_auth_errors else '❌'}")
        print(f"     auth.passwordRequirements: {'✅' if has_auth_pwd_req else '❌'}")
        print(f"     auth.login: {'✅' if has_auth_login else '❌'}")
        print(f"     auth.register: {'✅' if has_auth_register else '❌'}")
        print(f"     No nested errors: {'✅' if not has_nested_errors else '❌'}")
        print(f"     Overall: {'✅ PASS' if all_correct else '❌ FAIL'}")
        
        return all_correct
        
    except Exception as e:
        print(f"  Testing {lang.upper()}: [ERROR] {str(e)}")
        return False

def run_definitive_fix():
    """Run the definitive auth structure fix"""
    print("DEFINITIVE AUTH STRUCTURE FIX")
    print("=" * 60)
    
    # Start with languages that have translations defined
    languages = ['es', 'pt']  # Start with these two first
    
    print("\n1. REBUILDING AUTH SECTIONS:")
    print("-" * 40)
    rebuilt_count = 0
    for lang in languages:
        if rebuild_auth_section(lang):
            rebuilt_count += 1
    
    print("\n2. TESTING FINAL STRUCTURES:")
    print("-" * 40)
    passed_count = 0
    for lang in languages:
        if test_final_structure(lang):
            passed_count += 1
    
    print("\n" + "=" * 60)
    print("DEFINITIVE FIX SUMMARY")
    print("=" * 60)
    print(f"Languages processed: {len(languages)}")
    print(f"Auth sections rebuilt: {rebuilt_count}")
    print(f"Final tests passed: {passed_count}")
    
    if passed_count == len(languages):
        print("\n✅ DEFINITIVE AUTH STRUCTURE FIX SUCCESSFUL!")
        print("✅ Auth sections completely rebuilt to match English structure")
        print("✅ All validation tests passed")
        
        print("\n🎯 EXPECTED RESULTS:")
        print("• auth.errors.emailInvalid should be found (no missing keys)")
        print("• auth.passwordRequirements.title should be found (no missing keys)")
        print("• No auth.login.errors.* extra keys")
        print("• Translation validation should improve significantly")
        
        print("\n🎯 NEXT STEPS:")
        print("1. Restart frontend development server")
        print("2. Check browser console for validation improvements")
        print("3. If successful, extend to remaining languages")
        return True
    else:
        print(f"\n❌ {len(languages) - passed_count} language(s) still have issues")
        return False

if __name__ == "__main__":
    success = run_definitive_fix()
    sys.exit(0 if success else 1)
