#!/usr/bin/env python3
"""
Direct API test to verify IP summary enhancement
"""

import requests
import json

def test_direct_api():
    """Test the API directly"""
    
    # Login first
    login_response = requests.post("http://127.0.0.1:5000/api/admin/login", json={
        "username": "<EMAIL>",
        "password": "admin123"
    })
    
    if login_response.status_code != 200:
        print(f"<PERSON><PERSON> failed: {login_response.status_code}")
        return
    
    token = login_response.json()['access_token']
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test different IP addresses
    test_ips = ["*************", "127.0.0.1", "************"]
    
    for ip in test_ips:
        print(f"\n=== Testing IP: {ip} ===")
        
        response = requests.get(f"http://127.0.0.1:5000/api/admin/ip/summary/{ip}", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"Total logs: {len(data.get('access_logs', []))}")
            
            if data.get('access_logs'):
                first_log = data['access_logs'][0]
                print(f"First log user_id: {first_log.get('user_id')}")
                print(f"First log complete_user_id: {first_log.get('complete_user_id')}")
                print(f"First log user_email: {first_log.get('user_email')}")
                print(f"First log admin_id: {first_log.get('admin_id')}")
                
                # Save this specific result
                with open(f'direct_test_{ip.replace(".", "_")}.json', 'w') as f:
                    json.dump(data, f, indent=2, default=str)
                    
        else:
            print(f"Failed: {response.status_code}")

if __name__ == "__main__":
    test_direct_api()
