#!/usr/bin/env python3
"""
Final test to verify auth validation issues are resolved
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_auth_validation_status():
    """Check the current auth validation status"""
    print("FINAL AUTH VALIDATION TEST")
    print("=" * 60)
    
    # Run our debug script and capture results
    import subprocess
    try:
        result = subprocess.run(['python', 'debug_translation_validation.py'], 
                              capture_output=True, text=True, cwd='.')
        output = result.stdout
        
        # Parse the output to check for auth-related issues
        lines = output.split('\n')
        
        auth_missing_keys = []
        auth_extra_keys = []
        completion_rates = {}
        
        current_lang = None
        in_missing_section = False
        in_extra_section = False
        
        for line in lines:
            # Check for completion rates
            if ' - ' in line and '% complete' in line:
                parts = line.split(' - ')
                if len(parts) == 2:
                    lang = parts[0].strip()
                    completion = parts[1].split('%')[0].strip()
                    completion_rates[lang] = float(completion)
            
            # Check for auth-related missing keys
            if 'Missing keys' in line and 'auth.' in line:
                # Extract auth keys from the line
                auth_keys = [key.strip("'") for key in line.split('[')[1].split(']')[0].split(',') 
                           if 'auth.' in key.strip("'")]
                auth_missing_keys.extend(auth_keys)
            
            # Check for auth-related extra keys
            if 'Extra keys' in line and 'auth.' in line:
                # Extract auth keys from the line
                auth_keys = [key.strip("'") for key in line.split('[')[1].split(']')[0].split(',') 
                           if 'auth.' in key.strip("'")]
                auth_extra_keys.extend(auth_keys)
        
        # Remove duplicates
        auth_missing_keys = list(set(auth_missing_keys))
        auth_extra_keys = list(set(auth_extra_keys))
        
        print("\n📊 COMPLETION RATES:")
        print("-" * 30)
        for lang, rate in sorted(completion_rates.items()):
            status = "🟢" if rate >= 98 else "🟡" if rate >= 95 else "🔴"
            print(f"{status} {lang}: {rate}%")
        
        print(f"\n🔍 AUTH-RELATED VALIDATION ISSUES:")
        print("-" * 40)
        print(f"Missing auth keys: {len(auth_missing_keys)}")
        if auth_missing_keys:
            for key in auth_missing_keys[:5]:  # Show first 5
                print(f"  - {key}")
            if len(auth_missing_keys) > 5:
                print(f"  ... and {len(auth_missing_keys) - 5} more")
        
        print(f"Extra auth keys: {len(auth_extra_keys)}")
        if auth_extra_keys:
            for key in auth_extra_keys[:5]:  # Show first 5
                print(f"  + {key}")
            if len(auth_extra_keys) > 5:
                print(f"  ... and {len(auth_extra_keys) - 5} more")
        
        # Determine overall status
        high_completion_langs = [lang for lang, rate in completion_rates.items() if rate >= 97]
        auth_issues_resolved = len(auth_missing_keys) == 0 and len(auth_extra_keys) == 0
        
        print(f"\n📈 SUMMARY:")
        print("-" * 20)
        print(f"Languages with 97%+ completion: {len(high_completion_langs)}/8")
        print(f"Auth validation issues: {'✅ RESOLVED' if auth_issues_resolved else '❌ REMAINING'}")
        
        if len(high_completion_langs) >= 6 and len(auth_missing_keys) <= 5 and len(auth_extra_keys) <= 5:
            print(f"\n🎉 EXCELLENT PROGRESS!")
            print(f"✅ Most languages have high completion rates")
            print(f"✅ Auth validation issues are minimal or resolved")
            print(f"✅ Browser console should be much cleaner")
            
            print(f"\n🎯 EXPECTED BROWSER CONSOLE RESULTS:")
            print(f"• Significantly fewer translation validation warnings")
            print(f"• No or minimal 'Missing keys' for auth.errors.*")
            print(f"• No or minimal 'Extra keys' for auth.login.errors.*")
            print(f"• Access & Security page should work perfectly")
            
            print(f"\n🚀 NEXT STEPS:")
            print(f"1. Restart the frontend development server")
            print(f"2. Clear browser cache")
            print(f"3. Check browser console - should be much cleaner")
            print(f"4. Test Access & Security page functionality")
            print(f"5. If satisfied, can extend fixes to remaining languages")
            
            return True
        else:
            print(f"\n⚠️ PARTIAL PROGRESS")
            print(f"Some issues remain, but significant improvement achieved")
            return False
            
    except Exception as e:
        print(f"Error running validation test: {str(e)}")
        return False

if __name__ == "__main__":
    success = check_auth_validation_status()
    sys.exit(0 if success else 1)
