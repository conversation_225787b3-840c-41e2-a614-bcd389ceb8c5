#!/usr/bin/env python3
"""
Final status report for all language files
"""

import sys
import os
import subprocess

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_completion_rates():
    """Get current completion rates"""
    try:
        result = subprocess.run(['python', 'debug_translation_validation.py'], 
                              capture_output=True, text=True, cwd='.')
        output = result.stdout
        
        completion_rates = {}
        auth_issues = {'missing': [], 'extra': []}
        
        for line in output.split('\n'):
            # Get completion rates
            if ' - ' in line and '% complete' in line:
                parts = line.split(' - ')
                if len(parts) == 2:
                    lang = parts[0].strip()
                    completion = parts[1].split('%')[0].strip()
                    try:
                        completion_rates[lang] = float(completion)
                    except ValueError:
                        pass
            
            # Check for auth-related issues
            if 'Missing keys' in line and 'auth.' in line:
                auth_keys = [key.strip("'") for key in line.split('[')[1].split(']')[0].split(',') 
                           if 'auth.' in key.strip("'")]
                auth_issues['missing'].extend(auth_keys)
            
            if 'Extra keys' in line and 'auth.' in line:
                auth_keys = [key.strip("'") for key in line.split('[')[1].split(']')[0].split(',') 
                           if 'auth.' in key.strip("'")]
                auth_issues['extra'].extend(auth_keys)
        
        return completion_rates, auth_issues
        
    except Exception as e:
        return {}, {'missing': [], 'extra': []}

def check_syntax_status():
    """Check if all files have clean syntax using ESLint"""
    try:
        # Run ESLint on the i18n locales directory
        result = subprocess.run(['npx', 'eslint', 'src/i18n/locales', '--ext', '.ts'],
                              capture_output=True, text=True, cwd='../frontend')

        # If ESLint returns 0, all files are clean
        if result.returncode == 0:
            return 8, 8  # All 8 files are clean
        else:
            # Parse ESLint output to count files with issues
            output = result.stdout
            files_with_issues = set()

            for line in output.split('\n'):
                if 'common.ts' in line and ('error' in line or 'warning' in line):
                    # Extract filename and add to set
                    if '/locales/' in line:
                        lang_part = line.split('/locales/')[1].split('/')[0]
                        files_with_issues.add(lang_part)

            clean_count = 8 - len(files_with_issues)
            return clean_count, 8

    except Exception:
        # Fallback to basic syntax checking
        languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
        clean_count = 0

        for lang in languages:
            try:
                with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
                    content = f.read()

                # Check for basic syntax issues (excluding brace counting which is unreliable)
                has_double_commas = ',,' in content
                has_double_braces = '}},' in content and not '{{' in content  # Avoid false positives from interpolation

                # Check file structure
                proper_start = content.startswith('export default {')
                proper_end = content.rstrip().endswith('};')

                if not has_double_commas and not has_double_braces and proper_start and proper_end:
                    clean_count += 1

            except Exception:
                pass

        return clean_count, len(languages)

def generate_final_report():
    """Generate final status report"""
    print("🎯 FINAL TRANSLATION SYSTEM STATUS REPORT")
    print("=" * 60)
    
    # Get completion rates
    completion_rates, auth_issues = get_completion_rates()
    
    # Get syntax status
    clean_syntax, total_files = check_syntax_status()
    
    print(f"\n📊 SYNTAX STATUS:")
    print("-" * 20)
    print(f"Files with clean syntax: {clean_syntax}/{total_files}")
    if clean_syntax == total_files:
        print("✅ All files have clean TypeScript syntax")
        print("✅ Frontend should compile successfully")
    else:
        print(f"⚠️ {total_files - clean_syntax} file(s) may have syntax issues")
    
    print(f"\n📈 TRANSLATION COMPLETION:")
    print("-" * 30)
    if completion_rates:
        high_completion = 0
        for lang, rate in sorted(completion_rates.items()):
            if rate >= 98:
                status = "🟢"
                high_completion += 1
            elif rate >= 95:
                status = "🟡"
                high_completion += 1
            else:
                status = "🔴"
            print(f"{status} {lang}: {rate}%")
        
        print(f"\nLanguages with 95%+ completion: {high_completion}/{len(completion_rates)}")
    else:
        print("❌ Could not retrieve completion data")
    
    print(f"\n🔐 AUTH VALIDATION STATUS:")
    print("-" * 30)
    missing_auth = list(set(auth_issues['missing']))
    extra_auth = list(set(auth_issues['extra']))
    
    print(f"Missing auth keys: {len(missing_auth)}")
    print(f"Extra auth keys: {len(extra_auth)}")
    
    if len(missing_auth) == 0 and len(extra_auth) == 0:
        print("✅ No auth-related validation issues")
        print("✅ Browser console should be clean of auth warnings")
    elif len(missing_auth) <= 5 and len(extra_auth) <= 5:
        print("🟡 Minimal auth-related validation issues")
        print("🟡 Browser console should be mostly clean")
    else:
        print("🔴 Significant auth-related validation issues")
    
    print(f"\n🎯 OVERALL ASSESSMENT:")
    print("-" * 25)
    
    # Calculate overall score
    syntax_score = (clean_syntax / total_files) * 100
    completion_score = sum(completion_rates.values()) / len(completion_rates) if completion_rates else 0
    auth_score = 100 if (len(missing_auth) == 0 and len(extra_auth) == 0) else max(0, 100 - (len(missing_auth) + len(extra_auth)) * 5)
    
    overall_score = (syntax_score + completion_score + auth_score) / 3
    
    print(f"Syntax Score: {syntax_score:.1f}%")
    print(f"Completion Score: {completion_score:.1f}%")
    print(f"Auth Validation Score: {auth_score:.1f}%")
    print(f"Overall Score: {overall_score:.1f}%")
    
    if overall_score >= 90:
        print(f"\n🎉 EXCELLENT STATUS!")
        print(f"✅ Translation system is working very well")
        print(f"✅ Access & Security page should work perfectly")
        print(f"✅ Browser console should be clean")
        
        print(f"\n🚀 READY FOR USE!")
        print(f"• Frontend compiles successfully")
        print(f"• Translations display properly")
        print(f"• Minimal validation warnings")
        print(f"• Core functionality working")
        
    elif overall_score >= 75:
        print(f"\n🟡 GOOD STATUS!")
        print(f"✅ Translation system is mostly working")
        print(f"✅ Core functionality should work")
        print(f"⚠️ Some minor issues remain")
        
        print(f"\n🎯 RECOMMENDATIONS:")
        print(f"• Test Access & Security page functionality")
        print(f"• Monitor browser console for warnings")
        print(f"• Consider addressing remaining translation gaps")
        
    else:
        print(f"\n🔴 NEEDS IMPROVEMENT")
        print(f"❌ Significant issues remain")
        print(f"❌ May affect functionality")
        
        print(f"\n🔧 NEXT STEPS:")
        print(f"• Address syntax errors")
        print(f"• Fix auth structure issues")
        print(f"• Complete missing translations")
    
    return overall_score >= 75

if __name__ == "__main__":
    success = generate_final_report()
    sys.exit(0 if success else 1)
