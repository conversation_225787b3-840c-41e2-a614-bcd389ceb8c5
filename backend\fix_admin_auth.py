#!/usr/bin/env python3
"""
Fix Admin Authentication Issues
1. Check/fix admin users in database
2. Fix IP management endpoint authentication
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.admin import AdminUser
from werkzeug.security import generate_password_hash

def check_admin_users():
    """Check existing admin users"""
    print("🔍 CHECKING ADMIN USERS IN DATABASE")
    print("="*50)
    
    admins = AdminUser.query.all()
    print(f"Total admin users found: {len(admins)}")
    
    for admin in admins:
        print(f"\nAdmin ID: {admin.id}")
        print(f"Username: {admin.username}")
        print(f"Is Super Admin: {admin.is_super_admin}")
        print(f"Is Active: {admin.is_active}")
        print(f"Created At: {admin.created_at}")
        print(f"Last Login: {admin.last_login}")
        
        # Test password verification
        test_passwords = ["admin123", "12345", "admin"]
        for pwd in test_passwords:
            if admin.check_password(pwd):
                print(f"✅ Password '{pwd}' works for {admin.username}")
                break
        else:
            print(f"❌ None of the test passwords work for {admin.username}")

def fix_super_admin():
    """Fix super admin credentials"""
    print("\n🔧 FIXING SUPER ADMIN CREDENTIALS")
    print("="*50)
    
    # Check if super admin exists
    super_admin = AdminUser.query.filter_by(username='admin').first()
    
    if super_admin:
        print(f"Found existing super admin: {super_admin.username}")
        # Update password to admin123
        super_admin.password_hash = generate_password_hash('admin123')
        super_admin.is_super_admin = True
        super_admin.is_active = True
        db.session.commit()
        print("✅ Updated super admin password to 'admin123'")
    else:
        print("Creating new super admin...")
        super_admin = AdminUser(
            username='admin',
            password='admin123',
            is_super_admin=True,
            is_active=True
        )
        db.session.add(super_admin)
        db.session.commit()
        print("✅ Created new super admin: admin/admin123")
    
    return super_admin

def fix_limited_admin():
    """Ensure limited admin exists"""
    print("\n🔧 CHECKING LIMITED ADMIN")
    print("="*50)
    
    limited_admin = AdminUser.query.filter_by(username='limited_admin').first()
    
    if limited_admin:
        print(f"✅ Limited admin exists: {limited_admin.username}")
        # Ensure password is correct
        limited_admin.password_hash = generate_password_hash('limited123')
        limited_admin.is_super_admin = False
        limited_admin.is_active = True
        db.session.commit()
        print("✅ Updated limited admin password to 'limited123'")
    else:
        print("Creating limited admin...")
        limited_admin = AdminUser(
            username='limited_admin',
            password='limited123',
            is_super_admin=False,
            is_active=True
        )
        db.session.add(limited_admin)
        db.session.commit()
        print("✅ Created limited admin: limited_admin/limited123")
    
    return limited_admin

def check_ip_management_routes():
    """Check IP management route decorators"""
    print("\n🔍 CHECKING IP MANAGEMENT ROUTES")
    print("="*50)
    
    # Check if IP management blueprint exists and is registered
    from app.api.ip_management import ip_mgmt_bp
    
    print(f"IP Management Blueprint: {ip_mgmt_bp.name}")
    
    # List all routes in the blueprint
    rules = []
    for rule in ip_mgmt_bp.deferred_functions:
        print(f"Deferred function: {rule}")
    
    print("IP management routes should be accessible at /api/admin/ip/*")

def test_admin_login_after_fix():
    """Test admin login after fixes"""
    print("\n🧪 TESTING ADMIN LOGIN AFTER FIXES")
    print("="*50)
    
    import requests
    
    base_url = "http://127.0.0.1:5000"
    
    # Test super admin
    print("Testing super admin login...")
    response = requests.post(f"{base_url}/api/admin/login", json={
        "username": "admin",
        "password": "admin123"
    })
    
    if response.status_code == 200:
        print("✅ Super admin login successful")
        data = response.json()
        token = data.get('access_token')
        
        # Test token verification
        headers = {"Authorization": f"Bearer {token}"}
        verify_response = requests.get(f"{base_url}/api/admin/verify-token", headers=headers)
        
        if verify_response.status_code == 200:
            print("✅ Super admin token verification successful")
        else:
            print(f"❌ Super admin token verification failed: {verify_response.status_code}")
    else:
        print(f"❌ Super admin login failed: {response.status_code} - {response.text}")
    
    # Test limited admin
    print("\nTesting limited admin login...")
    response = requests.post(f"{base_url}/api/admin/login", json={
        "username": "limited_admin",
        "password": "limited123"
    })
    
    if response.status_code == 200:
        print("✅ Limited admin login successful")
    else:
        print(f"❌ Limited admin login failed: {response.status_code} - {response.text}")

def main():
    """Run all fixes"""
    app = create_app()
    
    with app.app_context():
        print("🚀 ADMIN AUTHENTICATION FIX SCRIPT")
        print(f"Database URI: {app.config.get('SQLALCHEMY_DATABASE_URI', 'Not set')}")
        
        # Check current state
        check_admin_users()
        
        # Fix issues
        fix_super_admin()
        fix_limited_admin()
        
        # Verify fixes
        print("\n✅ FIXES APPLIED - VERIFYING...")
        check_admin_users()
        
        # Check routes
        check_ip_management_routes()
        
        print("\n🎯 FIX SCRIPT COMPLETE")
        print("\nNow test the admin login again:")
        print("1. Super Admin: admin / admin123")
        print("2. Limited Admin: limited_admin / limited123")

if __name__ == "__main__":
    main()
