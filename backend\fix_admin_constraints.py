#!/usr/bin/env python3
"""
Fix Admin Constraints Script
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.admin import AdminUser, AdminAction
from werkzeug.security import generate_password_hash

def fix_admin_constraints():
    """Fix admin constraints and clean up data"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔧 FIXING ADMIN CONSTRAINTS...")
            print("="*50)
            
            # First, let's check what admin_actions are causing issues
            print("Checking admin_action records...")
            
            # Find admin_actions with null admin_id
            null_actions = AdminAction.query.filter(AdminAction.admin_id.is_(None)).all()
            print(f"Found {len(null_actions)} admin_action records with null admin_id")
            
            # Delete these problematic records
            for action in null_actions:
                print(f"Deleting admin_action record ID: {action.id}")
                db.session.delete(action)
            
            # Find admin_actions referencing the limited_admin we want to delete
            limited_admin = AdminUser.query.filter_by(username='limited_admin').first()
            if limited_admin:
                print(f"Found limited_admin with ID: {limited_admin.id}")
                
                # Find actions by this admin
                limited_actions = AdminAction.query.filter_by(admin_id=limited_admin.id).all()
                print(f"Found {len(limited_actions)} actions by limited_admin")
                
                # Delete these actions first
                for action in limited_actions:
                    print(f"Deleting action ID: {action.id} by limited_admin")
                    db.session.delete(action)
                
                # Now we can safely delete the limited_admin
                print(f"Deleting limited_admin user")
                db.session.delete(limited_admin)
            
            # Commit the deletions first
            db.session.commit()
            print("✅ Cleaned up problematic records")
            
            # Now fix passwords for remaining admins
            print("\n🔑 UPDATING ADMIN PASSWORDS...")
            print("="*50)
            
            admins = AdminUser.query.all()
            for admin in admins:
                print(f"Updating password for: {admin.username}")
                admin.set_password('admin123')
                admin.is_active = True
                
                # Ensure proper admin privileges
                if admin.username in ['<EMAIL>', '<EMAIL>', '<EMAIL>']:
                    admin.is_super_admin = True
            
            # Commit password updates
            db.session.commit()
            print("✅ All admin passwords updated!")
            
            # Verify
            print("\n🧪 VERIFICATION:")
            print("="*50)
            
            final_admins = AdminUser.query.all()
            for admin in final_admins:
                print(f"✅ {admin.username}")
                print(f"   - ID: {admin.id}")
                print(f"   - Super Admin: {admin.is_super_admin}")
                print(f"   - Active: {admin.is_active}")
                
                # Test password
                if admin.check_password('admin123'):
                    print(f"   - Password: ✅ 'admin123' works")
                else:
                    print(f"   - Password: ❌ 'admin123' does not work")
                print()
            
            print("🎯 FINAL ADMIN CREDENTIALS:")
            print("="*50)
            for admin in final_admins:
                print(f"Email: {admin.username}")
                print(f"Password: admin123")
                print(f"Type: {'Super Admin' if admin.is_super_admin else 'Limited Admin'}")
                print("-" * 40)
            
            return True
            
        except Exception as e:
            print(f"❌ Error fixing admin constraints: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    success = fix_admin_constraints()
    
    if success:
        print("\n🎉 ADMIN CONSTRAINTS FIXED SUCCESSFULLY!")
        print("\nYou can now login with:")
        print("- <EMAIL> / admin123")
        print("- <EMAIL> / admin123") 
        print("- <EMAIL> / admin123")
    else:
        print("\n❌ FAILED TO FIX ADMIN CONSTRAINTS!")
