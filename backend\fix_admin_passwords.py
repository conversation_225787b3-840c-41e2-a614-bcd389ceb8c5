#!/usr/bin/env python3
"""
Fix Admin Passwords Script
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.admin import AdminUser
from werkzeug.security import generate_password_hash

def fix_admin_passwords():
    """Fix admin passwords and remove duplicate limited_admin"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔧 FIXING ADMIN PASSWORDS...")
            print("="*50)
            
            # Get all admin users
            admins = AdminUser.query.all()
            
            # Remove the duplicate limited_admin account
            limited_admin = AdminUser.query.filter_by(username='limited_admin').first()
            if limited_admin:
                print(f"🗑️  Removing duplicate limited_admin account (ID: {limited_admin.id})")
                db.session.delete(limited_admin)
            
            # Fix passwords for all remaining admins
            for admin in admins:
                if admin.username != 'limited_admin':  # Skip the one we're deleting
                    print(f"🔑 Updating password for: {admin.username}")
                    admin.set_password('admin123')
                    admin.is_active = True
                    
                    # Ensure proper admin privileges
                    if admin.username in ['<EMAIL>', '<EMAIL>', '<EMAIL>']:
                        admin.is_super_admin = True
            
            # Commit changes
            db.session.commit()
            print("✅ All admin passwords updated!")
            
            # Verify
            print("\n🧪 VERIFICATION:")
            print("="*50)
            
            updated_admins = AdminUser.query.all()
            for admin in updated_admins:
                print(f"✅ {admin.username}")
                print(f"   - Super Admin: {admin.is_super_admin}")
                print(f"   - Active: {admin.is_active}")
                
                # Test password
                if admin.check_password('admin123'):
                    print(f"   - Password: ✅ 'admin123' works")
                else:
                    print(f"   - Password: ❌ 'admin123' does not work")
                print()
            
            print("🎯 FINAL ADMIN CREDENTIALS:")
            print("="*50)
            for admin in updated_admins:
                print(f"Email: {admin.username}")
                print(f"Password: admin123")
                print(f"Type: {'Super Admin' if admin.is_super_admin else 'Limited Admin'}")
                print("-" * 40)
            
            return True
            
        except Exception as e:
            print(f"❌ Error fixing admin passwords: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    success = fix_admin_passwords()
    
    if success:
        print("\n🎉 ADMIN PASSWORDS FIXED SUCCESSFULLY!")
    else:
        print("\n❌ FAILED TO FIX ADMIN PASSWORDS!")
