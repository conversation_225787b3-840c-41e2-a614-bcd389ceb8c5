#!/usr/bin/env python3
"""
Comprehensive script to fix all syntax errors in language files
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_syntax_errors_comprehensive(lang):
    """Fix all syntax errors in a language file"""
    print(f"  Fixing {lang.upper()}:")
    
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        fixes_applied = []
        
        # Fix 1: Remove double commas
        if ',,' in content:
            content = re.sub(r',,+', ',', content)
            fixes_applied.append("Double commas")
        
        # Fix 2: Fix double closing braces
        # Pattern: }}, should be },
        if '}},' in content:
            content = re.sub(r'}},', '},', content)
            fixes_applied.append("Double closing braces")
        
        # Fix 3: Fix lines ending with }}, (without comma after)
        # Pattern: }}\n should be }\n
        content = re.sub(r'}}(\s*\n)', r'}\1', content)
        
        # Fix 4: Add missing commas after closing braces followed by quotes
        # Pattern: }"key" should be },"key"
        content = re.sub(r'}(\s*)"', r'},\1"', content)
        
        # Fix 5: Fix specific problematic patterns
        # Pattern: {{tier}}, should be {{tier}}
        content = re.sub(r'{{([^}]+)}},', r'{{\1}}', content)
        
        # Fix 6: Ensure proper comma placement in object properties
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # Skip empty lines and comments
            if not line.strip() or line.strip().startswith('//'):
                fixed_lines.append(line)
                continue
            
            # Check if this line has a key-value pair without a comma
            if ('"' in line and '":' in line and 
                not line.strip().endswith(',') and 
                not line.strip().endswith('{') and
                not line.strip().endswith('}') and
                not line.strip().endswith('};') and
                not line.strip().endswith('},')):
                
                # Look ahead to see if next non-empty line starts with a key or closing brace
                next_line_idx = i + 1
                while next_line_idx < len(lines) and not lines[next_line_idx].strip():
                    next_line_idx += 1
                
                if next_line_idx < len(lines):
                    next_line = lines[next_line_idx].strip()
                    # If next line starts with a quote (new key) or closing brace, add comma
                    if next_line.startswith('"') or next_line.startswith('}'):
                        line = line.rstrip() + ','
                        if "Missing commas on key-value pairs" not in fixes_applied:
                            fixes_applied.append("Missing commas on key-value pairs")
            
            fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
        # Fix 7: Remove trailing comma before final closing brace
        # Pattern: ,\n  }; should be \n  };
        content = re.sub(r',(\s*}\s*;\s*$)', r'\1', content, flags=re.MULTILINE)
        
        # Fix 8: Ensure file ends properly
        if not content.rstrip().endswith('};'):
            if content.rstrip().endswith('}'):
                content = content.rstrip() + ';\n'
            fixes_applied.append("File ending")
        
        # Write back if changes were made
        if content != original_content:
            with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"     [SUCCESS] Applied fixes: {', '.join(fixes_applied)}")
            return True
        else:
            print(f"     [CLEAN] No fixes needed")
            return False
            
    except Exception as e:
        print(f"     [ERROR] Could not fix file: {str(e)}")
        return False

def validate_syntax_after_fix(lang):
    """Validate syntax after fixes"""
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        errors = []
        
        # Check for remaining issues
        if ',,' in content:
            errors.append("Double commas still present")
        
        if '}},' in content:
            errors.append("Double closing braces still present")
        
        # Check brace balance
        open_braces = content.count('{')
        close_braces = content.count('}')
        if open_braces != close_braces:
            errors.append(f"Brace mismatch: {open_braces} open, {close_braces} close")
        
        # Check file structure
        if not content.startswith('export default {'):
            errors.append("File should start with 'export default {'")
        
        if not content.rstrip().endswith('};'):
            errors.append("File should end with '};'")
        
        return len(errors) == 0, errors
        
    except Exception as e:
        return False, [f"Could not validate: {str(e)}"]

def run_comprehensive_syntax_fix():
    """Run comprehensive syntax fix on all language files"""
    print("COMPREHENSIVE SYNTAX ERROR FIX")
    print("=" * 60)
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    print("\n1. FIXING SYNTAX ERRORS:")
    print("-" * 30)
    fixed_count = 0
    for lang in languages:
        if fix_syntax_errors_comprehensive(lang):
            fixed_count += 1
    
    print("\n2. VALIDATING AFTER FIX:")
    print("-" * 30)
    valid_count = 0
    for lang in languages:
        is_valid, errors = validate_syntax_after_fix(lang)
        if is_valid:
            print(f"✅ {lang.upper()}: Valid syntax")
            valid_count += 1
        else:
            print(f"❌ {lang.upper()}: {len(errors)} remaining error(s)")
            for error in errors[:2]:
                print(f"   - {error}")
    
    print("\n" + "=" * 60)
    print("COMPREHENSIVE SYNTAX FIX SUMMARY")
    print("=" * 60)
    print(f"Total files processed: {len(languages)}")
    print(f"Files with fixes applied: {fixed_count}")
    print(f"Files passing validation: {valid_count}")
    
    if valid_count == len(languages):
        print("\n✅ ALL SYNTAX ERRORS FIXED!")
        print("✅ All files pass validation")
        print("✅ Frontend should compile successfully")
        
        print("\n🎯 RESULTS:")
        print("• Double commas removed")
        print("• Double closing braces fixed")
        print("• Missing commas added")
        print("• Proper TypeScript object structure restored")
        print("• File endings corrected")
        
        print("\n🎯 NEXT STEPS:")
        print("1. Frontend should automatically recompile")
        print("2. Vite error overlay should disappear")
        print("3. Run translation completion test")
        print("4. Test Access & Security page functionality")
        return True
    elif valid_count >= len(languages) * 0.8:  # 80% success rate
        print(f"\n🟡 MOSTLY SUCCESSFUL!")
        print(f"✅ {valid_count}/{len(languages)} files have valid syntax")
        print(f"⚠️ {len(languages) - valid_count} files may need manual review")
        print("\n🎯 NEXT STEPS:")
        print("1. Check frontend compilation")
        print("2. Address remaining syntax issues if needed")
        return False
    else:
        print(f"\n❌ NEEDS MORE WORK")
        print(f"Only {valid_count}/{len(languages)} files have valid syntax")
        print("Manual review and fixing required")
        return False

if __name__ == "__main__":
    success = run_comprehensive_syntax_fix()
    sys.exit(0 if success else 1)
