#!/usr/bin/env python3
"""
Script to fix auth key placement - move from auth.login to auth root level
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_auth_key_placement(lang):
    """Fix auth key placement for a specific language"""
    print(f"  Processing {lang.upper()}:")
    
    # Define the translations for auth sections at root level
    auth_translations = {
        'es': {
            'errors': {
                'emailInvalid': 'Por favor ingresa una dirección de email válida',
                'emailRequired': 'Email es requerido',
                'invalidCredentials': 'Email o contraseña inválidos',
                'passwordRequired': 'Contraseña es requerida',
                'passwordTooShort': 'La contraseña debe tener al menos 8 caracteres',
                'passwordsNotMatch': 'Las contraseñas no coinciden',
                'termsRequired': 'Debes aceptar los términos y condiciones'
            },
            'passwordRequirements': {
                'title': 'Requisitos de Contraseña',
                'length': 'Al menos 8 caracteres',
                'uppercase': 'Una letra mayúscula',
                'lowercase': 'Una letra minúscula',
                'number': 'Un número',
                'special': 'Un carácter especial'
            }
        },
        'pt': {
            'errors': {
                'emailInvalid': 'Por favor, insira um endereço de email válido',
                'emailRequired': 'Email é obrigatório',
                'invalidCredentials': 'Email ou senha inválidos',
                'passwordRequired': 'Senha é obrigatória',
                'passwordTooShort': 'A senha deve ter pelo menos 8 caracteres',
                'passwordsNotMatch': 'As senhas não coincidem',
                'termsRequired': 'Você deve concordar com os termos e condições'
            },
            'passwordRequirements': {
                'title': 'Requisitos de Senha',
                'length': 'Pelo menos 8 caracteres',
                'uppercase': 'Uma letra maiúscula',
                'lowercase': 'Uma letra minúscula',
                'number': 'Um número',
                'special': 'Um caractere especial'
            }
        },
        'ko': {
            'errors': {
                'emailInvalid': '유효한 이메일 주소를 입력해주세요',
                'emailRequired': '이메일이 필요합니다',
                'invalidCredentials': '유효하지 않은 이메일 또는 비밀번호',
                'passwordRequired': '비밀번호가 필요합니다',
                'passwordTooShort': '비밀번호는 최소 8자 이상이어야 합니다',
                'passwordsNotMatch': '비밀번호가 일치하지 않습니다',
                'termsRequired': '이용약관에 동의해야 합니다'
            },
            'passwordRequirements': {
                'title': '비밀번호 요구사항',
                'length': '최소 8자',
                'uppercase': '대문자 하나',
                'lowercase': '소문자 하나',
                'number': '숫자 하나',
                'special': '특수문자 하나'
            }
        },
        'ja': {
            'errors': {
                'emailInvalid': '有効なメールアドレスを入力してください',
                'emailRequired': 'メールが必要です',
                'invalidCredentials': '無効なメールまたはパスワード',
                'passwordRequired': 'パスワードが必要です',
                'passwordTooShort': 'パスワードは8文字以上である必要があります',
                'passwordsNotMatch': 'パスワードが一致しません',
                'termsRequired': '利用規約に同意する必要があります'
            },
            'passwordRequirements': {
                'title': 'パスワード要件',
                'length': '8文字以上',
                'uppercase': '大文字1文字',
                'lowercase': '小文字1文字',
                'number': '数字1文字',
                'special': '特殊文字1文字'
            }
        },
        'de': {
            'errors': {
                'emailInvalid': 'Bitte geben Sie eine gültige E-Mail-Adresse ein',
                'emailRequired': 'E-Mail ist erforderlich',
                'invalidCredentials': 'Ungültige E-Mail oder Passwort',
                'passwordRequired': 'Passwort ist erforderlich',
                'passwordTooShort': 'Passwort muss mindestens 8 Zeichen haben',
                'passwordsNotMatch': 'Passwörter stimmen nicht überein',
                'termsRequired': 'Sie müssen den Geschäftsbedingungen zustimmen'
            },
            'passwordRequirements': {
                'title': 'Passwort-Anforderungen',
                'length': 'Mindestens 8 Zeichen',
                'uppercase': 'Ein Großbuchstabe',
                'lowercase': 'Ein Kleinbuchstabe',
                'number': 'Eine Zahl',
                'special': 'Ein Sonderzeichen'
            }
        },
        'fr': {
            'errors': {
                'emailInvalid': 'Veuillez saisir une adresse email valide',
                'emailRequired': 'Email requis',
                'invalidCredentials': 'Email ou mot de passe invalide',
                'passwordRequired': 'Mot de passe requis',
                'passwordTooShort': 'Le mot de passe doit contenir au moins 8 caractères',
                'passwordsNotMatch': 'Les mots de passe ne correspondent pas',
                'termsRequired': 'Vous devez accepter les termes et conditions'
            },
            'passwordRequirements': {
                'title': 'Exigences du mot de passe',
                'length': 'Au moins 8 caractères',
                'uppercase': 'Une lettre majuscule',
                'lowercase': 'Une lettre minuscule',
                'number': 'Un chiffre',
                'special': 'Un caractère spécial'
            }
        },
        'zh': {
            'errors': {
                'emailInvalid': '请输入有效的邮箱地址',
                'emailRequired': '邮箱必填',
                'invalidCredentials': '无效的邮箱或密码',
                'passwordRequired': '密码必填',
                'passwordTooShort': '密码必须至少8个字符',
                'passwordsNotMatch': '密码不匹配',
                'termsRequired': '您必须同意条款和条件'
            },
            'passwordRequirements': {
                'title': '密码要求',
                'length': '至少8个字符',
                'uppercase': '一个大写字母',
                'lowercase': '一个小写字母',
                'number': '一个数字',
                'special': '一个特殊字符'
            }
        }
    }
    
    if lang not in auth_translations:
        print(f"     [SKIP] No auth translations for {lang}")
        return True
    
    try:
        # Read the current .ts file
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove any existing auth.login.errors and auth.login.passwordRequirements sections
        # that were incorrectly placed
        content = re.sub(
            r'\s*"errors":\s*{\s*"emailInvalid":[^}]+},?\s*',
            '',
            content,
            flags=re.DOTALL
        )
        content = re.sub(
            r'\s*"passwordRequirements":\s*{\s*"title":[^}]+},?\s*',
            '',
            content,
            flags=re.DOTALL
        )
        
        # Find the auth section
        auth_match = re.search(r'"auth":\s*{([^}]+(?:{[^}]*}[^}]*)*)}', content, re.DOTALL)
        if not auth_match:
            print(f"     [ERROR] Could not find auth section")
            return False
        
        auth_content = auth_match.group(1)
        
        # Check if we need to add the sections at the root level
        needs_errors = '"errors":' not in auth_content or 'auth.login.errors' in content
        needs_pwd_req = '"passwordRequirements":' not in auth_content or 'auth.login.passwordRequirements' in content
        
        if needs_errors or needs_pwd_req:
            # Build the new auth content
            new_auth_lines = []
            
            # Add existing content (login, register sections)
            existing_lines = auth_content.split('\n')
            for line in existing_lines:
                # Skip any incorrectly nested errors or passwordRequirements
                if ('"errors":' in line and 'emailInvalid' in line) or \
                   ('"passwordRequirements":' in line and 'title' in line):
                    continue
                new_auth_lines.append(line)
            
            # Add the correct sections at root level
            if needs_errors:
                new_auth_lines.append('    "errors": {')
                for key, value in auth_translations[lang]['errors'].items():
                    new_auth_lines.append(f'      "{key}": "{value}",')
                # Remove last comma
                if new_auth_lines[-1].endswith(','):
                    new_auth_lines[-1] = new_auth_lines[-1][:-1]
                new_auth_lines.append('    },')
            
            if needs_pwd_req:
                new_auth_lines.append('    "passwordRequirements": {')
                for key, value in auth_translations[lang]['passwordRequirements'].items():
                    new_auth_lines.append(f'      "{key}": "{value}",')
                # Remove last comma
                if new_auth_lines[-1].endswith(','):
                    new_auth_lines[-1] = new_auth_lines[-1][:-1]
                new_auth_lines.append('    },')
            
            # Remove trailing comma from last section
            for i in range(len(new_auth_lines) - 1, -1, -1):
                if new_auth_lines[i].strip().endswith(','):
                    new_auth_lines[i] = new_auth_lines[i][:-1]
                    break
            
            new_auth_content = '\n'.join(new_auth_lines)
            
            # Replace the auth section in the file
            new_content = re.sub(
                r'"auth":\s*{([^}]+(?:{[^}]*}[^}]*)*)}',
                f'"auth": {{{new_auth_content}}}',
                content,
                flags=re.DOTALL
            )
            
            # Write back to file
            with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"     [SUCCESS] Fixed auth key placement")
            return True
        else:
            print(f"     [SKIP] Auth structure already correct")
            return True
        
    except Exception as e:
        print(f"     [ERROR] {str(e)}")
        return False

def run_auth_placement_fix():
    """Run the auth key placement fix process"""
    print("FIXING AUTH KEY PLACEMENT")
    print("=" * 60)
    
    languages = ['es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    success_count = 0
    failed_count = 0
    
    for lang in languages:
        try:
            if fix_auth_key_placement(lang):
                success_count += 1
            else:
                failed_count += 1
        except Exception as e:
            print(f"  [ERROR] Unexpected error processing {lang}: {str(e)}")
            failed_count += 1
    
    print("\n" + "=" * 60)
    print("AUTH PLACEMENT FIX SUMMARY")
    print("=" * 60)
    print(f"Languages processed: {len(languages)}")
    print(f"Successful: {success_count}")
    print(f"Failed: {failed_count}")
    
    if failed_count == 0:
        print("\n✅ AUTH KEY PLACEMENT FIXED FOR ALL LANGUAGES!")
        print("✅ Keys moved from auth.login to auth root level")
        print("✅ Structure now matches English reference")
        
        print("\n🎯 EXPECTED RESULTS:")
        print("• No more 'Extra keys' warnings for auth.login.errors")
        print("• No more 'Missing keys' warnings for auth.errors")
        print("• Translation validation should show higher completion %")
        print("• Browser console should be much cleaner")
        
        print("\n🎯 NEXT STEPS:")
        print("1. Restart the frontend development server")
        print("2. Check browser console - auth validation warnings should be gone")
        print("3. Translation percentages should improve significantly")
        return True
    else:
        print(f"\n❌ {failed_count} language file(s) failed to fix")
        return False

if __name__ == "__main__":
    success = run_auth_placement_fix()
    sys.exit(0 if success else 1)
