#!/usr/bin/env python3
"""
Script to fix brace mismatches in language files
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def count_braces(content):
    """Count opening and closing braces"""
    open_braces = content.count('{')
    close_braces = content.count('}')
    return open_braces, close_braces

def fix_brace_mismatch(lang):
    """Fix brace mismatch in a language file"""
    print(f"  Fixing {lang.upper()}:")
    
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        open_braces, close_braces = count_braces(content)
        print(f"     Before: {open_braces} open, {close_braces} close")
        
        if open_braces > close_braces:
            # Need to add closing braces
            missing_braces = open_braces - close_braces
            
            # Check if file ends properly
            content = content.rstrip()
            
            # If file doesn't end with }; add the missing braces before the final };
            if content.endswith('};'):
                # Remove the final };
                content = content[:-2].rstrip()
                
                # Add missing closing braces
                for _ in range(missing_braces):
                    content += '\n  }'
                
                # Add back the final };
                content += '\n};\n'
            elif content.endswith('}'):
                # Add missing closing braces
                for _ in range(missing_braces):
                    content += '\n  }'
                
                # Add final semicolon
                content += ';\n'
            else:
                # Add all missing braces and proper ending
                for _ in range(missing_braces):
                    content += '\n  }'
                content += '\n};\n'
            
            # Write back
            with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Verify fix
            new_open, new_close = count_braces(content)
            print(f"     After: {new_open} open, {new_close} close")
            
            if new_open == new_close:
                print(f"     [SUCCESS] Brace mismatch fixed")
                return True
            else:
                print(f"     [PARTIAL] Still mismatched")
                return False
        
        elif open_braces < close_braces:
            print(f"     [ERROR] Too many closing braces - manual review needed")
            return False
        else:
            print(f"     [CLEAN] Braces already balanced")
            return True
            
    except Exception as e:
        print(f"     [ERROR] {str(e)}")
        return False

def run_brace_fix():
    """Run brace fix on all language files"""
    print("BRACE MISMATCH FIX")
    print("=" * 40)
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    fixed_count = 0
    for lang in languages:
        if fix_brace_mismatch(lang):
            fixed_count += 1
    
    print("\n" + "=" * 40)
    print("BRACE FIX SUMMARY")
    print("=" * 40)
    print(f"Languages processed: {len(languages)}")
    print(f"Successfully fixed: {fixed_count}")
    
    if fixed_count == len(languages):
        print("\n✅ ALL BRACE MISMATCHES FIXED!")
        print("✅ All files should now have balanced braces")
        print("✅ Frontend should compile successfully")
        
        print("\n🎯 NEXT STEPS:")
        print("1. Frontend should automatically recompile")
        print("2. Check that Vite error overlay disappears")
        print("3. Run comprehensive test again")
        return True
    else:
        print(f"\n⚠️ {len(languages) - fixed_count} file(s) may need manual review")
        return False

if __name__ == "__main__":
    success = run_brace_fix()
    sys.exit(0 if success else 1)
