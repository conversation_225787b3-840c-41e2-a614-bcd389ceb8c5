#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to comprehensively fix the Chinese translation file
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_chinese_file():
    """Fix all issues in the Chinese translation file"""
    print("FIXING CHINESE TRANSLATION FILE")
    print("=" * 50)
    
    try:
        # Read the current Chinese file
        with open('../frontend/src/i18n/locales/zh/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("1. Fixing basic syntax issues...")
        
        # Fix 1: Remove double commas
        content = re.sub(r',,+', ',', content)
        
        # Fix 2: Fix trailing commas in wrong places
        # Remove trailing commas before closing braces in object definitions
        content = re.sub(r',(\s*})', r'\1', content)
        
        # Fix 3: Fix template literal issues
        # Fix {{tier}}, to {{tier}}
        content = re.sub(r'{{([^}]+)}},', r'{{\1}}', content)
        
        # Fix 4: Fix {{error}}, to {{error}}
        content = re.sub(r'{{error}},', r'{{error}}', content)
        
        # Fix 5: Fix {{count}}, to {{count}}
        content = re.sub(r'{{count}},', r'{{count}}', content)
        
        # Fix 6: Fix {{balance}}, to {{balance}}
        content = re.sub(r'{{balance}},', r'{{balance}}', content)
        
        # Fix 7: Fix {{app}}, to {{app}}
        content = re.sub(r'{{app}},', r'{{app}}', content)
        
        # Fix 8: Fix {{external}}, to {{external}}
        content = re.sub(r'{{external}},', r'{{external}}', content)
        
        print("2. Ensuring proper comma placement...")
        
        # Fix comma placement for object properties
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # Skip empty lines and comments
            if not line.strip() or line.strip().startswith('//'):
                fixed_lines.append(line)
                continue
            
            # Check if this line has a key-value pair
            if ('"' in line and '":' in line and 
                not line.strip().endswith('{') and
                not line.strip().endswith('}') and
                not line.strip().endswith('};')):
                
                # Look ahead to see if next non-empty line starts with a key or closing brace
                next_line_idx = i + 1
                while next_line_idx < len(lines) and not lines[next_line_idx].strip():
                    next_line_idx += 1
                
                if next_line_idx < len(lines):
                    next_line = lines[next_line_idx].strip()
                    # If next line starts with a quote (new key) or closing brace, ensure comma
                    if next_line.startswith('"') or next_line.startswith('}'):
                        if not line.rstrip().endswith(','):
                            line = line.rstrip() + ','
                    # If this is the last property in an object, remove comma
                    elif next_line.startswith('}') and line.rstrip().endswith(','):
                        line = line.rstrip()[:-1]
            
            fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
        print("3. Fixing file structure...")
        
        # Ensure proper file ending
        content = content.rstrip()
        if not content.endswith('};'):
            if content.endswith('}'):
                content += ';'
            else:
                content += '\n};'
        content += '\n'
        
        print("4. Writing fixed file...")
        
        # Write back the fixed content
        with open('../frontend/src/i18n/locales/zh/common.ts', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Chinese file fixed successfully!")
        
        # Validate the fix
        print("5. Validating fix...")
        
        # Check brace balance
        open_braces = content.count('{')
        close_braces = content.count('}')
        
        print(f"   Brace balance: {open_braces} open, {close_braces} close")
        
        if open_braces == close_braces:
            print("   ✅ Braces are balanced")
        else:
            print(f"   ❌ Brace mismatch: {abs(open_braces - close_braces)} difference")
        
        # Check for common issues
        issues = []
        if ',,' in content:
            issues.append("Double commas found")
        if '}},' in content:
            issues.append("Template literal comma issues found")
        
        if issues:
            print(f"   ⚠️ Remaining issues: {', '.join(issues)}")
        else:
            print("   ✅ No common syntax issues found")
        
        return len(issues) == 0 and open_braces == close_braces
        
    except Exception as e:
        print(f"❌ Error fixing Chinese file: {str(e)}")
        return False

def run_chinese_fix():
    """Run the Chinese file fix"""
    success = fix_chinese_file()
    
    print("\n" + "=" * 50)
    print("CHINESE FILE FIX SUMMARY")
    print("=" * 50)
    
    if success:
        print("✅ Chinese translation file fixed successfully!")
        print("✅ All syntax issues resolved")
        print("✅ File structure corrected")
        
        print("\n🎯 NEXT STEPS:")
        print("1. Frontend should automatically recompile")
        print("2. Check that Vite error overlay disappears")
        print("3. Run translation validation test")
        print("4. Test Chinese language functionality")
        
        return True
    else:
        print("❌ Some issues remain in Chinese file")
        print("Manual review may be required")
        return False

if __name__ == "__main__":
    success = run_chinese_fix()
    sys.exit(0 if success else 1)
