#!/usr/bin/env python3
"""
Fix test_admin password
"""

from app import create_app, db
from app.models.admin import AdminUser

def fix_test_admin():
    app = create_app()
    with app.app_context():
        admin = AdminUser.query.filter_by(username='test_admin').first()
        if admin:
            print(f'Found test_admin: {admin.username}')
            admin.set_password('admin123')
            db.session.commit()
            print('Password updated to admin123')
        else:
            print('test_admin not found')

if __name__ == "__main__":
    fix_test_admin()
