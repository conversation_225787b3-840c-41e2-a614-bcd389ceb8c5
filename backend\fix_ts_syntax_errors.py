#!/usr/bin/env python3
"""
Script to fix TypeScript syntax errors in translation files
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_syntax_errors(lang):
    """Fix syntax errors in a .ts file"""
    print(f"  Fixing {lang.upper()}:")
    
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        fixes_applied = []
        
        # Fix 1: Remove double commas
        if ',,' in content:
            content = re.sub(r',,+', ',', content)
            fixes_applied.append("Double commas")
        
        # Fix 2: Add missing commas after closing braces followed by quotes
        # This pattern: }"key" should be: },"key"
        pattern = r'}\s*"'
        if re.search(pattern, content):
            content = re.sub(r'}\s*"', '},"', content)
            fixes_applied.append("Missing commas after closing braces")
        
        # Fix 3: Remove trailing commas before final closing brace
        # This pattern: ,} should be: }
        pattern = r',(\s*}\s*;?\s*$)'
        if re.search(pattern, content):
            content = re.sub(pattern, r'\1', content)
            fixes_applied.append("Trailing commas before final brace")
        
        # Fix 4: Ensure proper comma placement in object properties
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # Skip empty lines and comments
            if not line.strip() or line.strip().startswith('//'):
                fixed_lines.append(line)
                continue
            
            # Check if this line has a key-value pair without a comma
            if ('"' in line and '":' in line and 
                not line.strip().endswith(',') and 
                not line.strip().endswith('{') and
                not line.strip().endswith('}') and
                not line.strip().endswith('};')):
                
                # Look ahead to see if next non-empty line starts with a key or closing brace
                next_line_idx = i + 1
                while next_line_idx < len(lines) and not lines[next_line_idx].strip():
                    next_line_idx += 1
                
                if next_line_idx < len(lines):
                    next_line = lines[next_line_idx].strip()
                    # If next line starts with a quote (new key) or closing brace, add comma
                    if next_line.startswith('"') or next_line.startswith('}'):
                        line = line.rstrip() + ','
                        if "Missing commas on key-value pairs" not in fixes_applied:
                            fixes_applied.append("Missing commas on key-value pairs")
            
            fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
        # Write back if changes were made
        if content != original_content:
            with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"     [FIXED] Applied fixes: {', '.join(fixes_applied)}")
            return True
        else:
            print(f"     [CLEAN] No fixes needed")
            return False
            
    except Exception as e:
        print(f"     [ERROR] Could not fix file: {str(e)}")
        return False

def validate_syntax(lang):
    """Validate TypeScript syntax after fixes"""
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Basic validation checks
        errors = []
        
        # Check brace balance
        open_braces = content.count('{')
        close_braces = content.count('}')
        if open_braces != close_braces:
            errors.append(f"Brace mismatch: {open_braces} open, {close_braces} close")
        
        # Check for double commas
        if ',,' in content:
            errors.append("Double commas found")
        
        # Check for basic structure
        if not content.startswith('export default {'):
            errors.append("File should start with 'export default {'")
        
        if not content.rstrip().endswith('};'):
            errors.append("File should end with '};'")
        
        return len(errors) == 0, errors
        
    except Exception as e:
        return False, [f"Could not validate: {str(e)}"]

def run_syntax_fix():
    """Run syntax fix on all language files"""
    print("TYPESCRIPT SYNTAX ERROR FIX")
    print("=" * 60)
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    fixed_count = 0
    clean_count = 0
    error_count = 0
    
    for lang in languages:
        if fix_syntax_errors(lang):
            fixed_count += 1
        
        # Validate after fix
        is_valid, errors = validate_syntax(lang)
        if is_valid:
            clean_count += 1
        else:
            error_count += 1
            print(f"     [VALIDATION FAILED] {lang}: {', '.join(errors)}")
    
    print("\n" + "=" * 60)
    print("SYNTAX FIX SUMMARY")
    print("=" * 60)
    print(f"Total files processed: {len(languages)}")
    print(f"Files fixed: {fixed_count}")
    print(f"Files passing validation: {clean_count}")
    print(f"Files with remaining errors: {error_count}")
    
    if error_count == 0:
        print("\n✅ ALL TYPESCRIPT SYNTAX ERRORS FIXED!")
        print("✅ All files pass validation")
        print("✅ Frontend should compile successfully")
        
        print("\n🎯 RESULTS:")
        print("• Double commas removed")
        print("• Missing commas after closing braces added")
        print("• Key-value pair commas fixed")
        print("• Proper TypeScript object structure restored")
        
        print("\n🎯 NEXT STEPS:")
        print("1. Frontend should automatically recompile")
        print("2. Vite error overlay should disappear")
        print("3. Access & Security page should load properly")
        print("4. Translation validation should work correctly")
        return True
    else:
        print(f"\n❌ {error_count} file(s) still have validation errors")
        print("Manual review may be required for complex issues")
        return False

if __name__ == "__main__":
    success = run_syntax_fix()
    sys.exit(0 if success else 1)
