#!/usr/bin/env python3
"""
DeepTrade Secret Keys Generator

This script generates cryptographically secure secret keys for the DeepTrade application.
These keys are essential for securing user sessions and JWT tokens.

Usage:
    python generate_keys.py

Output:
    Two secure keys that should be copied to your .env file
"""

import secrets
import sys

def generate_secure_key(length=32):
    """Generate a cryptographically secure random key."""
    return secrets.token_urlsafe(length)

def main():
    print("=" * 50)
    print("🔐 DeepTrade Secret Keys Generator")
    print("=" * 50)
    print()
    
    # Generate the keys
    flask_secret = generate_secure_key(32)
    jwt_secret = generate_secure_key(32)
    
    print("✅ Generated secure keys for your DeepTrade application:")
    print()
    print("📋 Copy these lines to your .env file:")
    print("-" * 50)
    print(f"SECRET_KEY={flask_secret}")
    print(f"JWT_SECRET_KEY={jwt_secret}")
    print("-" * 50)
    print()
    
    print("🔍 Key Information:")
    print(f"   • Flask SECRET_KEY: {len(flask_secret)} characters")
    print(f"   • JWT SECRET_KEY: {len(jwt_secret)} characters")
    print(f"   • Entropy: 256 bits per key")
    print()
    
    print("⚠️  IMPORTANT SECURITY NOTES:")
    print("   • Keep these keys SECRET - never share them publicly")
    print("   • Use DIFFERENT keys for development and production")
    print("   • Never commit these keys to version control (Git)")
    print("   • Store production keys in secure environment variables")
    print("   • Regenerate keys if you suspect they may be compromised")
    print()
    
    print("🚀 Next Steps:")
    print("   1. Copy the keys above to your .env file")
    print("   2. Configure your Google OAuth credentials")
    print("   3. Set up your database connection")
    print("   4. Run: docker-compose up -d")
    print()
    
    print("=" * 50)
    print("✨ Keys generated successfully!")
    print("=" * 50)

if __name__ == "__main__":
    main()