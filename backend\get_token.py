#!/usr/bin/env python3
"""
Simple script to help get the access token from browser localStorage.
"""

print("🔑 How to get your access token:")
print()
print("1. Open your browser and go to: http://localhost:5173/tier")
print("2. Press F12 to open Developer Tools")
print("3. Go to the 'Application' tab (or 'Storage' in Firefox)")
print("4. In the left sidebar, expand 'Local Storage'")
print("5. Click on 'http://localhost:5173'")
print("6. Look for the key 'access_token'")
print("7. Copy the entire value (it should start with 'eyJ')")
print("8. Paste it into the test_api_calls.py script")
print()
print("The token should look something like:")
print("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2U...")
print()
print("Once you have the token, run:")
print("python test_api_calls.py")
