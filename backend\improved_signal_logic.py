#!/usr/bin/env python3
"""
IMPROVED SIGNAL LOGIC: Elite ML + Chart Confirmation
This shows how to add confirmation logic between Elite ML and Chart systems
"""

def generate_confirmed_signals(self, symbol: str = 'BTCUSDT', timeframe: str = '1h') -> Dict:
    """
    Enhanced signal generation with Elite ML + Chart confirmation
    Both systems must agree on direction for high-confidence trades
    """
    try:
        # Fetch market data (same for both systems)
        market_data = self._fetch_market_data(symbol, timeframe)
        
        if market_data is None or len(market_data) < 500:
            return {'error': 'Insufficient market data'}
        
        # 1. GET ELITE ML SIGNAL
        elite_signal = None
        elite_direction = 'HOLD'
        elite_confidence = 0.0
        
        if self.elite_predictor is not None:
            elite_result = self.elite_predictor.generate_elite_signal(market_data)
            if 'error' not in elite_result:
                elite_signal = elite_result
                elite_direction = elite_result.get('signal', 'HOLD')
                elite_confidence = elite_result.get('confidence', 0.0)
        
        # 2. GET CHART FORECAST DIRECTION
        forecast_data = self._get_ml_forecast(market_data, symbol)
        chart_direction = 'HOLD'
        chart_confidence = 0.0
        
        if forecast_data:
            forecast, highest_price, lowest_price, forecast_result = forecast_data
            current_price = float(market_data['close'].iloc[-1])
            
            # Determine chart direction based on forecast
            if len(forecast) > 0:
                # Look at next 6-12 hours of forecast
                short_term_forecast = forecast[:12]  # Next 12 hours
                avg_forecast_price = np.mean(short_term_forecast)
                
                price_change_pct = (avg_forecast_price - current_price) / current_price
                
                if price_change_pct > 0.005:  # +0.5% threshold
                    chart_direction = 'BUY'
                    chart_confidence = min(abs(price_change_pct) * 20, 0.95)  # Scale confidence
                elif price_change_pct < -0.005:  # -0.5% threshold
                    chart_direction = 'SELL'
                    chart_confidence = min(abs(price_change_pct) * 20, 0.95)
        
        # 3. CONFIRMATION LOGIC
        return self._apply_confirmation_logic(
            elite_direction, elite_confidence, elite_signal,
            chart_direction, chart_confidence,
            market_data, symbol
        )
        
    except Exception as e:
        return {'error': f'Signal generation failed: {str(e)}'}

def _apply_confirmation_logic(self, elite_dir, elite_conf, elite_signal, 
                            chart_dir, chart_conf, market_data, symbol) -> Dict:
    """Apply confirmation logic between Elite ML and Chart systems"""
    
    # CASE 1: BOTH SYSTEMS AGREE (HIGH CONFIDENCE)
    if elite_dir == chart_dir and elite_dir != 'HOLD':
        return {
            'signal': elite_dir,
            'confidence': min(elite_conf + 0.1, 0.99),  # Boost confidence when both agree
            'confirmation': 'BOTH_AGREE',
            'elite_confidence': elite_conf,
            'chart_confidence': chart_conf,
            'reason': f'Both Elite ML and Chart agree on {elite_dir}',
            'risk_level': 'LOW',  # Both systems agree = lower risk
            **self._convert_elite_signal_to_trading_signal(elite_signal, market_data, symbol)
        }
    
    # CASE 2: SYSTEMS DISAGREE (REQUIRE HIGHER CONFIDENCE)
    elif elite_dir != chart_dir and elite_dir != 'HOLD' and chart_dir != 'HOLD':
        # Only trade if Elite ML has VERY high confidence (95%+)
        if elite_conf >= 0.95:
            return {
                'signal': elite_dir,
                'confidence': elite_conf * 0.9,  # Reduce confidence due to disagreement
                'confirmation': 'ELITE_OVERRIDE',
                'elite_confidence': elite_conf,
                'chart_confidence': chart_conf,
                'reason': f'Elite ML override: {elite_dir} (95%+ confidence) vs Chart: {chart_dir}',
                'risk_level': 'HIGH',  # Disagreement = higher risk
                'warning': 'Systems disagree - Elite ML overriding with very high confidence',
                **self._convert_elite_signal_to_trading_signal(elite_signal, market_data, symbol)
            }
        else:
            return {
                'signal': 'HOLD',
                'confidence': 0.5,
                'confirmation': 'DISAGREEMENT_HOLD',
                'elite_confidence': elite_conf,
                'chart_confidence': chart_conf,
                'reason': f'Systems disagree: Elite ML: {elite_dir} ({elite_conf:.2f}) vs Chart: {chart_dir} ({chart_conf:.2f})',
                'risk_level': 'HIGH',
                'warning': 'Systems disagree and Elite ML confidence < 95% - Holding position'
            }
    
    # CASE 3: ONLY ELITE ML HAS SIGNAL
    elif elite_dir != 'HOLD' and chart_dir == 'HOLD':
        return {
            'signal': elite_dir,
            'confidence': elite_conf,
            'confirmation': 'ELITE_ONLY',
            'elite_confidence': elite_conf,
            'chart_confidence': chart_conf,
            'reason': f'Elite ML signal: {elite_dir}, Chart neutral',
            'risk_level': 'MEDIUM',
            **self._convert_elite_signal_to_trading_signal(elite_signal, market_data, symbol)
        }
    
    # CASE 4: ONLY CHART HAS SIGNAL
    elif elite_dir == 'HOLD' and chart_dir != 'HOLD':
        # Use legacy system logic
        signal_data = self._analyze_trading_conditions(
            market_data, forecast, swing_points, heikin_ashi,
            highest_price, lowest_price
        )
        
        return {
            **signal_data,
            'confirmation': 'CHART_ONLY',
            'elite_confidence': elite_conf,
            'chart_confidence': chart_conf,
            'reason': f'Chart-based signal: {chart_dir}, Elite ML neutral',
            'risk_level': 'MEDIUM'
        }
    
    # CASE 5: BOTH SYSTEMS NEUTRAL
    else:
        return {
            'signal': 'HOLD',
            'confidence': 0.5,
            'confirmation': 'BOTH_NEUTRAL',
            'elite_confidence': elite_conf,
            'chart_confidence': chart_conf,
            'reason': 'Both systems neutral - no clear signal',
            'risk_level': 'LOW'
        }
