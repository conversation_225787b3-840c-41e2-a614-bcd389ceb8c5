#!/usr/bin/env python3
"""
Manual Paper Trading Setup Script

This script manually adds the paper trading functionality to the database
when migrations fail due to existing database issues.
"""

import os
import sys
from sqlalchemy import text

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db

def manual_setup():
    """Manually set up paper trading tables and column."""
    app = create_app()
    
    with app.app_context():
        try:
            print("🚀 Starting manual paper trading setup...")
            
            # Check if paper_trading_mode column exists
            print("🔍 Checking if paper_trading_mode column exists...")
            try:
                with db.engine.connect() as conn:
                    result = conn.execute(text("SHOW COLUMNS FROM users LIKE 'paper_trading_mode'"))
                    if result.fetchone():
                        print("ℹ️  paper_trading_mode column already exists")
                    else:
                        print("➕ Adding paper_trading_mode column...")
                        conn.execute(text("ALTER TABLE users ADD COLUMN paper_trading_mode BOOLEAN NOT NULL DEFAULT FALSE"))
                        conn.commit()
                        print("✅ Added paper_trading_mode column")
            except Exception as e:
                print(f"⚠️  Error with paper_trading_mode column: {e}")
            
            # Create paper trading tables
            print("📊 Creating paper trading tables...")
            
            # Create paper_trading_accounts table
            try:
                with db.engine.connect() as conn:
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS paper_trading_accounts (
                        id VARCHAR(36) PRIMARY KEY,
                        user_id VARCHAR(36) NOT NULL UNIQUE,
                        virtual_balance DECIMAL(20,8) NOT NULL DEFAULT 10000.********,
                        initial_balance DECIMAL(20,8) NOT NULL DEFAULT 10000.********,
                        total_pnl DECIMAL(20,8) DEFAULT 0.********,
                        total_trades_count INTEGER DEFAULT 0,
                        winning_trades_count INTEGER DEFAULT 0,
                        losing_trades_count INTEGER DEFAULT 0,
                        win_rate DECIMAL(5,2) DEFAULT 0.00,
                        reset_count INTEGER DEFAULT 0,
                        last_reset_at DATETIME NULL,
                        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                    )
                    """))
                    conn.commit()
                print("✅ Created paper_trading_accounts table")
            except Exception as e:
                print(f"⚠️  paper_trading_accounts table: {e}")
            
            # Create paper_trades table
            try:
                with db.engine.connect() as conn:
                    conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS paper_trades (
                        id VARCHAR(36) PRIMARY KEY,
                        user_id VARCHAR(36) NOT NULL,
                        paper_account_id VARCHAR(36) NOT NULL,
                        session_id VARCHAR(36) NULL,
                        symbol VARCHAR(20) NOT NULL,
                        side ENUM('buy', 'sell', 'long', 'short') NOT NULL,
                        quantity DECIMAL(20,8) NOT NULL,
                        source VARCHAR(20) NOT NULL DEFAULT 'app',
                        entry_price DECIMAL(20,8) NOT NULL,
                        exit_price DECIMAL(20,8) NULL,
                        stop_loss DECIMAL(20,8) NULL,
                        take_profit DECIMAL(20,8) NULL,
                        pnl DECIMAL(20,8) NULL,
                        simulated_fee DECIMAL(20,8) DEFAULT 0,
                        status ENUM('open', 'closed', 'cancelled') NOT NULL DEFAULT 'open',
                        exit_reason VARCHAR(100) NULL,
                        archived BOOLEAN DEFAULT FALSE,
                        entry_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        exit_time DATETIME NULL,
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                        FOREIGN KEY (paper_account_id) REFERENCES paper_trading_accounts(id) ON DELETE CASCADE
                    )
                    """))
                    conn.commit()
                print("✅ Created paper_trades table")
            except Exception as e:
                print(f"⚠️  paper_trades table: {e}")
            
            # Create paper_trading_sessions table
            try:
                db.engine.execute(text("""
                    CREATE TABLE IF NOT EXISTS paper_trading_sessions (
                        id VARCHAR(36) PRIMARY KEY,
                        user_id VARCHAR(36) NOT NULL,
                        paper_account_id VARCHAR(36) NOT NULL,
                        symbol VARCHAR(20) NOT NULL,
                        status ENUM('active', 'paused', 'ended') NOT NULL DEFAULT 'active',
                        initial_balance DECIMAL(20,8) NOT NULL,
                        current_balance DECIMAL(20,8) NOT NULL,
                        leverage INTEGER DEFAULT 1,
                        investment_percentage INTEGER DEFAULT 0,
                        total_trades INTEGER DEFAULT 0,
                        winning_trades INTEGER DEFAULT 0,
                        total_pnl DECIMAL(20,8) DEFAULT 0,
                        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        ended_at DATETIME NULL,
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                        FOREIGN KEY (paper_account_id) REFERENCES paper_trading_accounts(id) ON DELETE CASCADE
                    )
                """))
                print("✅ Created paper_trading_sessions table")
            except Exception as e:
                print(f"⚠️  paper_trading_sessions table: {e}")
            
            # Create paper_balance_snapshots table
            try:
                db.engine.execute(text("""
                    CREATE TABLE IF NOT EXISTS paper_balance_snapshots (
                        id VARCHAR(36) PRIMARY KEY,
                        paper_account_id VARCHAR(36) NOT NULL,
                        balance DECIMAL(20,8) NOT NULL,
                        pnl_change DECIMAL(20,8) DEFAULT 0,
                        transaction_type VARCHAR(20) NOT NULL,
                        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (paper_account_id) REFERENCES paper_trading_accounts(id) ON DELETE CASCADE
                    )
                """))
                print("✅ Created paper_balance_snapshots table")
            except Exception as e:
                print(f"⚠️  paper_balance_snapshots table: {e}")
            
            # Create indexes
            print("📈 Creating indexes...")
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_paper_trades_user_id ON paper_trades(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_paper_trades_account_id ON paper_trades(paper_account_id)",
                "CREATE INDEX IF NOT EXISTS idx_paper_trades_status ON paper_trades(status)",
                "CREATE INDEX IF NOT EXISTS idx_paper_trades_symbol ON paper_trades(symbol)",
                "CREATE INDEX IF NOT EXISTS idx_paper_trades_entry_time ON paper_trades(entry_time)",
                "CREATE INDEX IF NOT EXISTS idx_paper_sessions_user_id ON paper_trading_sessions(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_paper_sessions_account_id ON paper_trading_sessions(paper_account_id)",
                "CREATE INDEX IF NOT EXISTS idx_paper_sessions_status ON paper_trading_sessions(status)",
                "CREATE INDEX IF NOT EXISTS idx_paper_snapshots_account_id ON paper_balance_snapshots(paper_account_id)",
                "CREATE INDEX IF NOT EXISTS idx_paper_snapshots_created_at ON paper_balance_snapshots(created_at)",
                "CREATE INDEX IF NOT EXISTS idx_paper_snapshots_transaction_type ON paper_balance_snapshots(transaction_type)"
            ]
            
            for index_sql in indexes:
                try:
                    db.engine.execute(text(index_sql))
                except Exception as e:
                    print(f"⚠️  Index warning: {e}")
            
            print("✅ Created indexes")
            
            print("🎉 Manual paper trading setup completed successfully!")
            print("\n📋 Summary:")
            print("   - Added paper_trading_mode column to users table")
            print("   - Created paper_trading_accounts table")
            print("   - Created paper_trades table")
            print("   - Created paper_trading_sessions table")
            print("   - Created paper_balance_snapshots table")
            print("   - Created performance indexes")
            print("\n🚀 Paper trading system is ready to use!")
            
            return True
            
        except Exception as e:
            print(f"❌ Manual setup failed: {e}")
            return False

if __name__ == "__main__":
    print("🎯 DeepTrade Manual Paper Trading Setup")
    print("=" * 50)
    
    if manual_setup():
        print("\n🎉 Paper trading system is fully operational!")
        print("\n📖 Next steps:")
        print("   1. Restart your Flask application")
        print("   2. Test the paper trading toggle in the dashboard")
        print("   3. Enable auto-trading in paper mode to test the system")
    else:
        print("\n❌ Manual setup failed")
        sys.exit(1)
