#!/usr/bin/env python3
"""
Database migration script to add email change verification and soft delete fields
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db

def migrate_database():
    """Apply database schema migrations for email change and soft delete."""
    app = create_app()
    
    with app.app_context():
        try:
            # Get database engine
            engine = db.engine
            
            with engine.connect() as conn:
                # Check if email_verification_token column exists
                result = conn.execute(text("""
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'users' 
                    AND COLUMN_NAME = 'email_verification_token'
                """))
                
                email_verification_token_exists = result.fetchone() is not None
                
                if not email_verification_token_exists:
                    print("Adding email_verification_token column to users table...")
                    conn.execute(text("""
                        ALTER TABLE users 
                        ADD COLUMN email_verification_token VARCHAR(255) NULL AFTER verification_token_expires_at
                    """))
                    print("✓ email_verification_token column added")
                else:
                    print("✓ email_verification_token column already exists")
                
                # Check if email_verification_token_expires_at column exists
                result = conn.execute(text("""
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'users' 
                    AND COLUMN_NAME = 'email_verification_token_expires_at'
                """))
                
                email_verification_expires_exists = result.fetchone() is not None
                
                if not email_verification_expires_exists:
                    print("Adding email_verification_token_expires_at column to users table...")
                    conn.execute(text("""
                        ALTER TABLE users 
                        ADD COLUMN email_verification_token_expires_at DATETIME NULL AFTER email_verification_token
                    """))
                    print("✓ email_verification_token_expires_at column added")
                else:
                    print("✓ email_verification_token_expires_at column already exists")
                
                # Check if pending_email column exists
                result = conn.execute(text("""
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'users' 
                    AND COLUMN_NAME = 'pending_email'
                """))
                
                pending_email_exists = result.fetchone() is not None
                
                if not pending_email_exists:
                    print("Adding pending_email column to users table...")
                    conn.execute(text("""
                        ALTER TABLE users 
                        ADD COLUMN pending_email VARCHAR(255) NULL AFTER email_verification_token_expires_at
                    """))
                    print("✓ pending_email column added")
                else:
                    print("✓ pending_email column already exists")
                
                # Check if deleted_at column exists
                result = conn.execute(text("""
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'users' 
                    AND COLUMN_NAME = 'deleted_at'
                """))
                
                deleted_at_exists = result.fetchone() is not None
                
                if not deleted_at_exists:
                    print("Adding deleted_at column to users table...")
                    conn.execute(text("""
                        ALTER TABLE users 
                        ADD COLUMN deleted_at DATETIME NULL AFTER is_active
                    """))
                    print("✓ deleted_at column added")
                else:
                    print("✓ deleted_at column already exists")
                
                # Commit the transaction
                conn.commit()
                print("\n✅ Database migration completed successfully!")
                
        except SQLAlchemyError as e:
            print(f"❌ Database migration failed: {str(e)}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error during migration: {str(e)}")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 Starting database migration for email change and soft delete fields...")
    success = migrate_database()
    if success:
        print("🎉 Migration completed successfully!")
        sys.exit(0)
    else:
        print("💥 Migration failed!")
        sys.exit(1)
