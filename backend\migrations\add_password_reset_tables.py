#!/usr/bin/env python3
"""
Migration script to add password reset and 2FA reset request tables.
Run this script to create the new tables for password reset functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models.password_reset import PasswordResetToken, TwoFAResetRequest

def run_migration():
    """Create password reset and 2FA reset request tables."""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 Creating password reset and 2FA reset request tables...")
            
            # Create the tables
            db.create_all()
            
            print("✅ Successfully created tables:")
            print("   - password_reset_tokens")
            print("   - twofa_reset_requests")
            print("\n📋 Table structures:")
            
            # Show table info
            print("\n🔑 PasswordResetToken fields:")
            print("   - id (UUID, Primary Key)")
            print("   - user_id (Foreign Key to users)")
            print("   - token (Unique reset token)")
            print("   - expires_at (Token expiration)")
            print("   - is_used (Boolean flag)")
            print("   - created_at, used_at (Timestamps)")
            print("   - ip_address, user_agent (Security tracking)")
            
            print("\n🔐 TwoFAResetRequest fields:")
            print("   - id (UUID, Primary Key)")
            print("   - user_id (Foreign Key to users)")
            print("   - reason (User's reason for reset)")
            print("   - Security verification fields:")
            print("     * full_name_provided, email_provided")
            print("     * last_login_date_provided, account_creation_date_provided")
            print("     * recent_activity_description")
            print("     * security_question_1/2/3 and answers")
            print("   - Admin workflow fields:")
            print("     * status (pending/approved/rejected/completed)")
            print("     * admin_id, admin_notes, reviewed_at, completed_at")
            print("   - Security assessment:")
            print("     * risk_level (low/medium/high)")
            print("     * requires_additional_verification")
            print("   - Tracking: created_at, ip_address, user_agent")
            
            print("\n🎉 Migration completed successfully!")
            
        except Exception as e:
            print(f"❌ Migration failed: {str(e)}")
            return False
    
    return True

if __name__ == "__main__":
    success = run_migration()
    if not success:
        sys.exit(1)
