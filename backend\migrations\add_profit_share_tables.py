"""
Database migration script to add profit share tracking tables.
Run this script to create the new tables for the profit share system.
"""

import os
from app import create_app, db
from app.models import (
    UserBalanceTracker, BalanceSnapshot, 
    Referral, ReferralEarning, ReferrerProfile,
    SolanaPayment, MembershipBilling, SolanaWalletConfig
)

def create_profit_share_tables():
    """Create all new tables for the profit share system."""
    app = create_app()
    
    with app.app_context():
        print("Creating profit share tracking tables...")
        
        # Create all tables
        db.create_all()
        
        # Initialize Solana wallet configuration
        existing_config = SolanaWalletConfig.query.first()
        if not existing_config:
            # Get treasury wallet from environment variable
            treasury_wallet = os.getenv('SOLANA_TREASURY_WALLET')
            if not treasury_wallet:
                print("WARNING: SOLANA_TREASURY_WALLET environment variable not set. Using default wallet.")
                treasury_wallet = "9tveNp6FvLn857ZSMNhAJhzG3vDmkDXABgFDdX7iQiPD"  # Default from .env

            config = SolanaWalletConfig(
                treasury_wallet_address=treasury_wallet,
                usdt_token_mint="Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",
                usdt_decimals=6,
                rpc_endpoint="https://api.mainnet-beta.solana.com",
                network="mainnet",
                default_confirmation_count=12,
                max_retry_attempts=3
            )
            db.session.add(config)
            db.session.commit()
            print("✓ Solana wallet configuration initialized")
        
        print("✓ All profit share tables created successfully!")
        print("\nNew tables created:")
        print("- user_balance_tracker: Track user balance and profit calculations")
        print("- balance_snapshots: Historical balance snapshots")
        print("- referrals: Referral relationships between users")
        print("- referral_earnings: Individual referral earnings")
        print("- referrer_profiles: Extended profiles for referrers")
        print("- solana_payments: USDT payments on Solana blockchain")
        print("- membership_billing: Monthly billing for Tier 2 users")
        print("- solana_wallet_config: Solana integration configuration")

if __name__ == "__main__":
    create_profit_share_tables()
