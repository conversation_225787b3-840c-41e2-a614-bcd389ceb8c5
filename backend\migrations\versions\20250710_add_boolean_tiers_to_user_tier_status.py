"""Add boolean tier fields to user_tier_status and remove old tier column

Revision ID: 20250710_add_boolean_tiers
Revises: 
Create Date: 2025-07-10

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '20250710_add_boolean_tiers'
down_revision = '1095b8c61ab5'
branch_labels = None
depends_on = None

def upgrade():
    with op.batch_alter_table('user_tier_status') as batch_op:
        batch_op.add_column(sa.Column('tier_1', sa.<PERSON>(), nullable=False, server_default=sa.sql.expression.true()))
        batch_op.add_column(sa.Column('tier_2', sa.<PERSON>(), nullable=False, server_default=sa.sql.expression.false()))
        batch_op.add_column(sa.Column('tier_3', sa.<PERSON>(), nullable=False, server_default=sa.sql.expression.false()))
        batch_op.drop_column('tier')

def downgrade():
    with op.batch_alter_table('user_tier_status') as batch_op:
        batch_op.add_column(sa.Column('tier', sa.Integer(), nullable=False, server_default='1'))
        batch_op.drop_column('tier_1')
        batch_op.drop_column('tier_2')
        batch_op.drop_column('tier_3')