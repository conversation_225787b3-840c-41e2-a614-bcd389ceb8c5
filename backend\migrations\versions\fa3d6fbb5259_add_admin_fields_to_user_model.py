"""Add admin fields to User model

Revision ID: fa3d6fbb5259
Revises: 568e0210d745
Create Date: 2025-07-24 13:35:06.858628

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fa3d6fbb5259'
down_revision = '568e0210d745'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Skip foreign key constraint changes as they're causing issues
    # The admin columns already exist in the database

    # Add admin columns only if they don't exist
    try:
        with op.batch_alter_table('users', schema=None) as batch_op:
            # Check if columns exist before adding
            connection = op.get_bind()
            result = connection.execute(sa.text("SHOW COLUMNS FROM users LIKE 'is_admin'"))
            if not result.fetchone():
                batch_op.add_column(sa.Column('is_admin', sa.Boolean(), nullable=False, server_default='0'))

            result = connection.execute(sa.text("SHOW COLUMNS FROM users LIKE 'is_super_admin'"))
            if not result.fetchone():
                batch_op.add_column(sa.Column('is_super_admin', sa.Boolean(), nullable=False, server_default='0'))
    except Exception as e:
        print(f"Admin columns may already exist: {e}")

    # Skip dropping indexes that may not exist or cause issues
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Skip downgrade operations to avoid issues
    pass
    # ### end Alembic commands ###
