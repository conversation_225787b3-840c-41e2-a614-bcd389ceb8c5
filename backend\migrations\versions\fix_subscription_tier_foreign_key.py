"""Fix subscription tier foreign key

Revision ID: fix_subscription_tier_fk
Revises: 
Create Date: 2025-07-10 13:40:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'fix_subscription_tier_fk'
down_revision = '20250710_add_boolean_tiers'
branch_labels = None
depends_on = None

def upgrade():
    # Drop the foreign key constraint
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.drop_constraint('subscriptions_tier_fkey', type_='foreignkey')
    
    # Recreate the column without the foreign key
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.alter_column('tier',
                            existing_type=sa.INTEGER(),
                            nullable=False,
                            existing_server_default=sa.text('1'))

def downgrade():
    # Re-add the foreign key constraint
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.create_foreign_key('subscriptions_tier_fkey',
                                  'user_tier_status',
                                  ['tier'],
                                  ['tier'])
