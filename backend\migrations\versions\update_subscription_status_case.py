"""Update subscription status values to uppercase

Revision ID: update_subscription_status_case
Revises: 1095b8c61ab5
Create Date: 2025-07-10 04:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.orm import Session
from app.models.subscription import SubscriptionStatus

# revision identifiers, used by Alembic.
revision = 'update_subscription_status_case'
down_revision = 'fix_subscription_tier_fk'
branch_labels = None
depends_on = None


def upgrade():
    """Update all subscription status values to uppercase."""
    # Create a session
    bind = op.get_bind()
    session = Session(bind=bind)
    
    try:
        # Get all subscriptions
        subscriptions = session.execute(
            sa.text("SELECT id, status FROM subscriptions")
        ).fetchall()
        
        # Update each subscription status to uppercase
        for sub_id, status in subscriptions:
            if status and status != status.upper():
                session.execute(
                    sa.text(
                        "UPDATE subscriptions SET status = :new_status WHERE id = :id"
                    ).bindparams(
                        new_status=status.upper(),
                        id=sub_id
                    )
                )
        
        # Commit the changes
        session.commit()
        print(f"Updated {len(subscriptions)} subscription statuses to uppercase")
        
    except Exception as e:
        session.rollback()
        print(f"Error updating subscription statuses: {str(e)}")
        raise
    finally:
        session.close()


def downgrade():
    # This is a one-way migration.
    # We don't provide a downgrade as we can't reliably determine the original case.
    pass
