#!/usr/bin/env python3
"""
Test frequency of original legacy system
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_original_legacy_frequency():
    """Test how many signals the original legacy generates per day"""
    print("📊 Original Legacy Frequency Test")
    print("=" * 60)
    
    try:
        # Import Flask app and create application context
        from app import create_app
        app = create_app()
        
        with app.app_context():
            # Import required services
            from app.services.market_data import BinanceMarketData
            from app.services.trading_signals import TradingSignalGenerator
            
            # Initialize services
            market_service = BinanceMarketData()
            signal_generator = TradingSignalGenerator("test_user", market_service)
            
            print("✅ Services initialized successfully")
            
            # Test different market scenarios
            scenarios = [
                ("Bull Trend", 0.8, 0.3),      # Strong uptrend with moderate volatility
                ("Bear Trend", -0.6, 0.4),     # Downtrend with higher volatility
                ("Sideways", 0.1, 0.2),        # Sideways with low volatility
                ("Volatile Bull", 1.2, 0.8),   # Strong uptrend with high volatility
                ("Volatile Bear", -0.9, 0.7)   # Strong downtrend with high volatility
            ]
            
            total_signals = 0
            total_days = len(scenarios)
            
            for scenario_name, trend_per_hour, volatility in scenarios:
                print(f"\n🎯 Testing {scenario_name} Market...")
                print("-" * 40)
                
                # Generate 24 hours of data (1 day)
                hours = 24
                base_price = 65000
                
                # Create market data with trend and volatility
                prices = []
                for i in range(hours):
                    trend_component = trend_per_hour * i
                    noise = np.random.normal(0, volatility * 100)  # Volatility in dollars
                    price = base_price + trend_component + noise
                    prices.append(max(price, 50000))  # Floor at $50k
                
                # Create OHLC data
                mock_data = pd.DataFrame({
                    'timestamp': [1640995200000 + (i * 3600000) for i in range(hours)],
                    'open': prices,
                    'high': [p * (1 + np.random.uniform(0.002, 0.008)) for p in prices],
                    'low': [p * (1 - np.random.uniform(0.002, 0.008)) for p in prices],
                    'close': [p * (1 + np.random.uniform(-0.003, 0.003)) for p in prices],
                    'volume': [np.random.uniform(4000, 8000) for _ in range(hours)]
                })
                
                # Ensure OHLC relationships
                for i in range(len(mock_data)):
                    open_price = mock_data.loc[i, 'open']
                    close_price = mock_data.loc[i, 'close']
                    mock_data.loc[i, 'high'] = max(mock_data.loc[i, 'high'], open_price, close_price)
                    mock_data.loc[i, 'low'] = min(mock_data.loc[i, 'low'], open_price, close_price)
                
                print(f"   📊 Price Range: ${mock_data['close'].min():,.0f} - ${mock_data['close'].max():,.0f}")
                print(f"   📈 Total Change: {((mock_data['close'].iloc[-1] / mock_data['close'].iloc[0]) - 1) * 100:+.1f}%")
                
                # Test every 4 hours (6 tests per day)
                signals_today = 0
                test_hours = [4, 8, 12, 16, 20, 23]  # 6 tests per day
                
                for test_hour in test_hours:
                    # Use data up to test hour
                    current_data = mock_data.iloc[:test_hour+1].copy()
                    current_price = float(current_data['close'].iloc[-1])
                    
                    # Create realistic forecast
                    if trend_per_hour > 0:  # Bull market
                        forecast = np.array([current_price * (1 + np.random.uniform(0.005, 0.025)) for _ in range(24)])
                    elif trend_per_hour < -0.3:  # Bear market
                        forecast = np.array([current_price * (1 + np.random.uniform(-0.025, -0.005)) for _ in range(24)])
                    else:  # Sideways
                        forecast = np.array([current_price * (1 + np.random.uniform(-0.01, 0.01)) for _ in range(24)])
                    
                    highest_price = np.max(forecast)
                    lowest_price = np.min(forecast)
                    
                    # Create swing points
                    swing_points = {
                        'swing_high': current_price * np.random.uniform(1.015, 1.035),
                        'swing_low': current_price * np.random.uniform(0.965, 0.985)
                    }
                    
                    # Calculate Heikin-Ashi
                    heikin_ashi = signal_generator._calculate_heikin_ashi(current_data)
                    
                    # Test the original legacy system
                    try:
                        result = signal_generator._analyze_trading_conditions(
                            market_data=current_data,
                            forecast=forecast,
                            swing_points=swing_points,
                            heikin_ashi=heikin_ashi,
                            highest_price=highest_price,
                            lowest_price=lowest_price
                        )
                        
                        signal = result.get('signal', 'HOLD')
                        confidence = result.get('confidence', 0)
                        
                        if signal != 'HOLD':
                            signals_today += 1
                            print(f"   ⚡ Hour {test_hour}: {signal} ({confidence:.1f}%)")
                        
                    except Exception as e:
                        print(f"   ❌ Error at hour {test_hour}: {e}")
                
                print(f"   🎯 Signals Today: {signals_today}")
                total_signals += signals_today
            
            # Calculate average
            avg_signals_per_day = total_signals / total_days
            
            print(f"\n📈 ORIGINAL LEGACY FREQUENCY RESULTS:")
            print("=" * 50)
            print(f"   📊 Total Signals: {total_signals}")
            print(f"   📅 Total Days Tested: {total_days}")
            print(f"   🎯 Average Signals/Day: {avg_signals_per_day:.1f}")
            
            # Frequency assessment
            if avg_signals_per_day == 0:
                assessment = "❌ TOO CONSERVATIVE - No signals"
            elif avg_signals_per_day < 1:
                assessment = "⚠️ VERY CONSERVATIVE - Less than 1/day"
            elif avg_signals_per_day <= 3:
                assessment = "✅ BALANCED - Good frequency"
            elif avg_signals_per_day <= 6:
                assessment = "⚡ ACTIVE - High frequency"
            else:
                assessment = "🚨 TOO AGGRESSIVE - Very high frequency"
            
            print(f"   📋 Assessment: {assessment}")
            
            # Compare with optimized legacy
            print(f"\n🔄 Comparison:")
            print(f"   📊 Optimized Legacy: 0 signals/day (too conservative)")
            print(f"   📊 Original Legacy: {avg_signals_per_day:.1f} signals/day")
            print(f"   📈 Improvement: {'+∞' if avg_signals_per_day > 0 else '0'}x better")
            
            return avg_signals_per_day
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 0

if __name__ == "__main__":
    print("🚀 Original Legacy Frequency Test")
    print("=" * 50)
    
    frequency = test_original_legacy_frequency()
    
    if frequency > 0:
        print(f"\n🎉 ORIGINAL LEGACY GENERATES {frequency:.1f} SIGNALS/DAY!")
        print(f"\n💡 This is much better than the optimized version (0/day)")
        print(f"   The original simple conditions are more practical!")
    else:
        print(f"\n❌ ORIGINAL LEGACY STILL NOT GENERATING SIGNALS")
