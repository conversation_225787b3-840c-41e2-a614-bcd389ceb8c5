{"start_time": "2025-07-28T10:43:12.312098", "test_suites": {"Take Profit Logic": {"status": "COMPLETED", "tests": {"long_take_profit_logic": {"status": "PASSED", "message": "Long take profit logic passed", "scenarios": [{"scenario": "At take profit", "current_price": 105000, "take_profit": 105000, "expected": true, "actual": true, "passed": true}, {"scenario": "Above take profit", "current_price": 106000, "take_profit": 105000, "expected": true, "actual": true, "passed": true}, {"scenario": "Below take profit", "current_price": 104000, "take_profit": 105000, "expected": false, "actual": false, "passed": true}, {"scenario": "No take profit set", "current_price": 105000, "take_profit": null, "expected": false, "actual": false, "passed": true}]}, "short_take_profit_logic": {"status": "PASSED", "message": "Short take profit logic passed", "scenarios": [{"scenario": "At take profit", "current_price": 95000, "take_profit": 95000, "expected": true, "actual": true, "passed": true}, {"scenario": "Below take profit", "current_price": 94000, "take_profit": 95000, "expected": true, "actual": true, "passed": true}, {"scenario": "Above take profit", "current_price": 96000, "take_profit": 95000, "expected": false, "actual": false, "passed": true}, {"scenario": "No take profit set", "current_price": 95000, "take_profit": null, "expected": false, "actual": false, "passed": true}]}}, "total_tests": 2, "passed_tests": 2, "failed_tests": 0}, "Stop Loss Logic": {"status": "COMPLETED", "tests": {"long_stop_loss_logic": {"status": "PASSED", "message": "Long stop loss logic passed", "scenarios": [{"scenario": "At stop loss", "current_price": 95000, "stop_loss": 95000, "expected": true, "actual": true, "passed": true}, {"scenario": "Below stop loss", "current_price": 94000, "stop_loss": 95000, "expected": true, "actual": true, "passed": true}, {"scenario": "Above stop loss", "current_price": 96000, "stop_loss": 95000, "expected": false, "actual": false, "passed": true}, {"scenario": "No stop loss set", "current_price": 95000, "stop_loss": null, "expected": false, "actual": false, "passed": true}]}, "short_stop_loss_logic": {"status": "PASSED", "message": "Short stop loss logic passed", "scenarios": [{"scenario": "At stop loss", "current_price": 105000, "stop_loss": 105000, "expected": true, "actual": true, "passed": true}, {"scenario": "Above stop loss", "current_price": 106000, "stop_loss": 105000, "expected": true, "actual": true, "passed": true}, {"scenario": "Below stop loss", "current_price": 104000, "stop_loss": 105000, "expected": false, "actual": false, "passed": true}, {"scenario": "No stop loss set", "current_price": 105000, "stop_loss": null, "expected": false, "actual": false, "passed": true}]}}, "total_tests": 2, "passed_tests": 2, "failed_tests": 0}, "Position Monitoring Components": {"status": "COMPLETED", "tests": {"trading_container_monitoring": {"status": "PASSED", "message": "Trading container monitoring methods available", "methods": {"has_start_position_monitoring": true, "has_start_paper_monitoring": true, "has_get_current_price": true, "has_close_position": true, "has_close_paper_position": true, "has_close_trade": true}}}, "total_tests": 1, "passed_tests": 1, "failed_tests": 0}, "Exit Strategy Performance": {"status": "COMPLETED", "tests": {"exit_decision_speed": {"status": "PASSED", "message": "Exit decision performance: avg 0.0000ms, max 0.0000ms", "performance_data": {"average_decision_time_ms": 0.0, "max_decision_time_ms": 0.0, "total_decisions": 700, "performance_acceptable": true}}}, "total_tests": 1, "passed_tests": 1, "failed_tests": 0}, "Trade Closure Workflow": {"status": "COMPLETED", "tests": {"trade_closure_workflow": {"status": "PASSED", "message": "Trade closure workflow completed successfully", "workflow_steps": {"price_monitoring": true, "exit_condition_check": true, "position_closure": true, "pnl_calculation": true, "database_update": true, "profit_share_processing": true, "notification_sending": true, "cleanup_operations": true}}}, "total_tests": 1, "passed_tests": 1, "failed_tests": 0}}, "summary": {"total_tests": 7, "passed_tests": 7, "failed_tests": 0, "skipped_tests": 0}, "end_time": "2025-07-28T10:43:12.322134"}