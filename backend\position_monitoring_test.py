#!/usr/bin/env python3
"""
Comprehensive Position Monitoring and Exit Strategy Testing Framework
Tests take profit, stop loss, and position monitoring functionality
"""

import sys
import os
import time
import threading
import json
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional
import logging

# Add backend to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PositionMonitoringTester:
    """Comprehensive testing framework for position monitoring and exit strategies"""
    
    def __init__(self):
        self.test_results = {}
        self.mock_trades = []
        self.monitoring_threads = []
        
        logger.info("🎯 Initializing Position Monitoring Testing Framework")
    
    def run_all_position_tests(self) -> Dict:
        """Execute all position monitoring test suites"""
        logger.info("=" * 80)
        logger.info("🔍 STARTING COMPREHENSIVE POSITION MONITORING TESTS")
        logger.info("=" * 80)
        
        test_suites = [
            ("Take Profit Logic Testing", self.test_take_profit_logic),
            ("Stop Loss Logic Testing", self.test_stop_loss_logic),
            ("Position Monitoring Threads", self.test_position_monitoring_threads),
            ("Paper Trading Exit Logic", self.test_paper_trading_exits),
            ("Live Trading Exit Logic", self.test_live_trading_exits),
            ("Trailing Stop Loss", self.test_trailing_stop_loss),
            ("Exit Strategy Performance", self.test_exit_strategy_performance),
            ("Multi-Position Monitoring", self.test_multi_position_monitoring)
        ]
        
        overall_results = {
            'start_time': datetime.now().isoformat(),
            'test_suites': {},
            'summary': {
                'total_tests': 0,
                'passed_tests': 0,
                'failed_tests': 0,
                'skipped_tests': 0
            }
        }
        
        for suite_name, test_function in test_suites:
            logger.info(f"\n📋 RUNNING TEST SUITE: {suite_name}")
            logger.info("-" * 60)
            
            try:
                suite_results = test_function()
                overall_results['test_suites'][suite_name] = suite_results
                
                # Update summary
                overall_results['summary']['total_tests'] += suite_results.get('total_tests', 0)
                overall_results['summary']['passed_tests'] += suite_results.get('passed_tests', 0)
                overall_results['summary']['failed_tests'] += suite_results.get('failed_tests', 0)
                overall_results['summary']['skipped_tests'] += suite_results.get('skipped_tests', 0)
                
            except Exception as e:
                logger.error(f"❌ Test suite '{suite_name}' failed with error: {e}")
                overall_results['test_suites'][suite_name] = {
                    'error': str(e),
                    'status': 'FAILED'
                }
                overall_results['summary']['failed_tests'] += 1
        
        overall_results['end_time'] = datetime.now().isoformat()
        self._print_position_monitoring_summary(overall_results)
        return overall_results
    
    def test_take_profit_logic(self) -> Dict:
        """Test take profit trigger logic for both long and short positions"""
        logger.info("💰 Testing Take Profit Logic")
        
        results = {
            'status': 'RUNNING',
            'tests': {},
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'skipped_tests': 0
        }
        
        # Test 1: Long Position Take Profit
        test_name = "long_position_take_profit"
        results['total_tests'] += 1
        try:
            from app.models.trade import Trade
            
            # Create mock long trade
            mock_trade = Trade(
                user_id="test_user",
                symbol="BTCUSDT",
                side="BUY",
                quantity=0.1,
                entry_price=100000.0,
                take_profit=105000.0,  # 5% profit target
                stop_loss=95000.0     # 5% stop loss
            )
            mock_trade.status = 'OPEN'
            
            # Test scenarios
            test_scenarios = [
                (104000.0, False, "Below take profit"),
                (105000.0, True, "At take profit"),
                (106000.0, True, "Above take profit")
            ]
            
            scenario_results = []
            for price, expected, description in test_scenarios:
                result = mock_trade.should_take_profit(price)
                scenario_results.append({
                    'price': price,
                    'expected': expected,
                    'actual': result,
                    'passed': result == expected,
                    'description': description
                })
            
            all_passed = all(s['passed'] for s in scenario_results)
            
            results['tests'][test_name] = {
                'status': 'PASSED' if all_passed else 'FAILED',
                'message': f'Long position take profit logic {"passed" if all_passed else "failed"}',
                'scenarios': scenario_results
            }
            
            if all_passed:
                results['passed_tests'] += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                results['failed_tests'] += 1
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            results['tests'][test_name] = {
                'status': 'FAILED',
                'message': f'Long position take profit test failed: {str(e)}'
            }
            results['failed_tests'] += 1
            logger.error(f"❌ {test_name}: FAILED - {str(e)}")
        
        # Test 2: Short Position Take Profit
        test_name = "short_position_take_profit"
        results['total_tests'] += 1
        try:
            from app.models.trade import Trade
            
            # Create mock short trade
            mock_trade = Trade(
                user_id="test_user",
                symbol="BTCUSDT",
                side="SELL",
                quantity=0.1,
                entry_price=100000.0,
                take_profit=95000.0,   # 5% profit target (price goes down)
                stop_loss=105000.0     # 5% stop loss (price goes up)
            )
            mock_trade.status = 'OPEN'
            
            # Test scenarios for short position
            test_scenarios = [
                (96000.0, False, "Above take profit"),
                (95000.0, True, "At take profit"),
                (94000.0, True, "Below take profit")
            ]
            
            scenario_results = []
            for price, expected, description in test_scenarios:
                result = mock_trade.should_take_profit(price)
                scenario_results.append({
                    'price': price,
                    'expected': expected,
                    'actual': result,
                    'passed': result == expected,
                    'description': description
                })
            
            all_passed = all(s['passed'] for s in scenario_results)
            
            results['tests'][test_name] = {
                'status': 'PASSED' if all_passed else 'FAILED',
                'message': f'Short position take profit logic {"passed" if all_passed else "failed"}',
                'scenarios': scenario_results
            }
            
            if all_passed:
                results['passed_tests'] += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                results['failed_tests'] += 1
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            results['tests'][test_name] = {
                'status': 'FAILED',
                'message': f'Short position take profit test failed: {str(e)}'
            }
            results['failed_tests'] += 1
            logger.error(f"❌ {test_name}: FAILED - {str(e)}")
        
        results['status'] = 'COMPLETED'
        return results
    
    def test_stop_loss_logic(self) -> Dict:
        """Test stop loss trigger logic for both long and short positions"""
        logger.info("🛑 Testing Stop Loss Logic")
        
        results = {
            'status': 'RUNNING',
            'tests': {},
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'skipped_tests': 0
        }
        
        # Test 1: Long Position Stop Loss
        test_name = "long_position_stop_loss"
        results['total_tests'] += 1
        try:
            from app.models.trade import Trade
            
            # Create mock long trade
            mock_trade = Trade(
                user_id="test_user",
                symbol="BTCUSDT",
                side="BUY",
                quantity=0.1,
                entry_price=100000.0,
                take_profit=105000.0,
                stop_loss=95000.0     # 5% stop loss
            )
            mock_trade.status = 'OPEN'
            
            # Test scenarios
            test_scenarios = [
                (96000.0, False, "Above stop loss"),
                (95000.0, True, "At stop loss"),
                (94000.0, True, "Below stop loss")
            ]
            
            scenario_results = []
            for price, expected, description in test_scenarios:
                result = mock_trade.should_stop_loss(price)
                scenario_results.append({
                    'price': price,
                    'expected': expected,
                    'actual': result,
                    'passed': result == expected,
                    'description': description
                })
            
            all_passed = all(s['passed'] for s in scenario_results)
            
            results['tests'][test_name] = {
                'status': 'PASSED' if all_passed else 'FAILED',
                'message': f'Long position stop loss logic {"passed" if all_passed else "failed"}',
                'scenarios': scenario_results
            }
            
            if all_passed:
                results['passed_tests'] += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                results['failed_tests'] += 1
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            results['tests'][test_name] = {
                'status': 'FAILED',
                'message': f'Long position stop loss test failed: {str(e)}'
            }
            results['failed_tests'] += 1
            logger.error(f"❌ {test_name}: FAILED - {str(e)}")
        
        # Test 2: Short Position Stop Loss
        test_name = "short_position_stop_loss"
        results['total_tests'] += 1
        try:
            from app.models.trade import Trade
            
            # Create mock short trade
            mock_trade = Trade(
                user_id="test_user",
                symbol="BTCUSDT",
                side="SELL",
                quantity=0.1,
                entry_price=100000.0,
                take_profit=95000.0,
                stop_loss=105000.0     # 5% stop loss (price goes up)
            )
            mock_trade.status = 'OPEN'
            
            # Test scenarios for short position
            test_scenarios = [
                (104000.0, False, "Below stop loss"),
                (105000.0, True, "At stop loss"),
                (106000.0, True, "Above stop loss")
            ]
            
            scenario_results = []
            for price, expected, description in test_scenarios:
                result = mock_trade.should_stop_loss(price)
                scenario_results.append({
                    'price': price,
                    'expected': expected,
                    'actual': result,
                    'passed': result == expected,
                    'description': description
                })
            
            all_passed = all(s['passed'] for s in scenario_results)
            
            results['tests'][test_name] = {
                'status': 'PASSED' if all_passed else 'FAILED',
                'message': f'Short position stop loss logic {"passed" if all_passed else "failed"}',
                'scenarios': scenario_results
            }
            
            if all_passed:
                results['passed_tests'] += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                results['failed_tests'] += 1
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            results['tests'][test_name] = {
                'status': 'FAILED',
                'message': f'Short position stop loss test failed: {str(e)}'
            }
            results['failed_tests'] += 1
            logger.error(f"❌ {test_name}: FAILED - {str(e)}")
        
        results['status'] = 'COMPLETED'
        return results

    def test_position_monitoring_threads(self) -> Dict:
        """Test position monitoring thread functionality"""
        logger.info("🔄 Testing Position Monitoring Threads")

        results = {
            'status': 'RUNNING',
            'tests': {},
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'skipped_tests': 0
        }

        # Test 1: Thread Creation and Management
        test_name = "monitoring_thread_creation"
        results['total_tests'] += 1
        try:
            from app.services.trading_container import UserTradingContainer

            # Create container
            container = UserTradingContainer(user_id="test_user")

            # Test thread management capabilities
            thread_tests = {
                'container_created': isinstance(container, UserTradingContainer),
                'has_monitoring_methods': hasattr(container, '_start_position_monitoring'),
                'has_paper_monitoring': hasattr(container, '_start_paper_position_monitoring'),
                'has_price_getter': hasattr(container, '_get_current_price'),
                'has_close_methods': hasattr(container, '_close_position')
            }

            all_passed = all(thread_tests.values())

            results['tests'][test_name] = {
                'status': 'PASSED' if all_passed else 'FAILED',
                'message': f'Position monitoring thread methods {"available" if all_passed else "missing"}',
                'thread_capabilities': thread_tests
            }

            if all_passed:
                results['passed_tests'] += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                results['failed_tests'] += 1
                logger.error(f"❌ {test_name}: FAILED")

        except Exception as e:
            results['tests'][test_name] = {
                'status': 'FAILED',
                'message': f'Thread creation test failed: {str(e)}'
            }
            results['failed_tests'] += 1
            logger.error(f"❌ {test_name}: FAILED - {str(e)}")

        results['status'] = 'COMPLETED'
        return results

    def test_paper_trading_exits(self) -> Dict:
        """Test paper trading exit logic"""
        logger.info("📄 Testing Paper Trading Exit Logic")

        results = {
            'status': 'RUNNING',
            'tests': {},
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'skipped_tests': 0
        }

        # Test 1: Paper Trade Exit Methods
        test_name = "paper_trade_exit_methods"
        results['total_tests'] += 1
        try:
            from app.models.paper_trading import PaperTrade, PaperTradeStatus

            # Create mock paper trade
            paper_trade = PaperTrade(
                user_id="test_user",
                symbol="BTCUSDT",
                side="buy",
                quantity=Decimal('0.1'),
                entry_price=Decimal('100000.0'),
                take_profit=Decimal('105000.0'),
                stop_loss=Decimal('95000.0'),
                status=PaperTradeStatus.OPEN
            )

            # Test available methods
            method_tests = {
                'has_close_method': hasattr(paper_trade, 'close_trade'),
                'has_pnl_calculation': hasattr(paper_trade, 'calculate_pnl'),
                'has_is_long_method': hasattr(paper_trade, 'is_long'),
                'has_is_short_method': hasattr(paper_trade, 'is_short'),
                'status_is_open': paper_trade.status == PaperTradeStatus.OPEN
            }

            all_passed = all(method_tests.values())

            results['tests'][test_name] = {
                'status': 'PASSED' if all_passed else 'FAILED',
                'message': f'Paper trade exit methods {"available" if all_passed else "missing"}',
                'method_availability': method_tests
            }

            if all_passed:
                results['passed_tests'] += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                results['failed_tests'] += 1
                logger.error(f"❌ {test_name}: FAILED")

        except Exception as e:
            results['tests'][test_name] = {
                'status': 'FAILED',
                'message': f'Paper trade exit methods test failed: {str(e)}'
            }
            results['failed_tests'] += 1
            logger.error(f"❌ {test_name}: FAILED - {str(e)}")

        results['status'] = 'COMPLETED'
        return results

    def test_live_trading_exits(self) -> Dict:
        """Test live trading exit logic"""
        logger.info("💼 Testing Live Trading Exit Logic")

        results = {
            'status': 'RUNNING',
            'tests': {},
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'skipped_tests': 0
        }

        # Test 1: Live Trade Exit Methods
        test_name = "live_trade_exit_methods"
        results['total_tests'] += 1
        try:
            from app.models.trade import Trade, TradeStatus

            # Create mock live trade
            live_trade = Trade(
                user_id="test_user",
                symbol="BTCUSDT",
                side="BUY",
                quantity=0.1,
                entry_price=100000.0,
                take_profit=105000.0,
                stop_loss=95000.0,
                status=TradeStatus.OPEN
            )

            # Test available methods
            method_tests = {
                'has_close_method': hasattr(live_trade, 'close_trade'),
                'has_should_stop_loss': hasattr(live_trade, 'should_stop_loss'),
                'has_should_take_profit': hasattr(live_trade, 'should_take_profit'),
                'has_calculate_pnl': hasattr(live_trade, 'calculate_pnl'),
                'has_is_long_method': hasattr(live_trade, 'is_long'),
                'status_is_open': live_trade.status == TradeStatus.OPEN
            }

            all_passed = all(method_tests.values())

            results['tests'][test_name] = {
                'status': 'PASSED' if all_passed else 'FAILED',
                'message': f'Live trade exit methods {"available" if all_passed else "missing"}',
                'method_availability': method_tests
            }

            if all_passed:
                results['passed_tests'] += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                results['failed_tests'] += 1
                logger.error(f"❌ {test_name}: FAILED")

        except Exception as e:
            results['tests'][test_name] = {
                'status': 'FAILED',
                'message': f'Live trade exit methods test failed: {str(e)}'
            }
            results['failed_tests'] += 1
            logger.error(f"❌ {test_name}: FAILED - {str(e)}")

        results['status'] = 'COMPLETED'
        return results

    def test_trailing_stop_loss(self) -> Dict:
        """Test trailing stop loss functionality"""
        logger.info("📈 Testing Trailing Stop Loss")

        results = {
            'status': 'RUNNING',
            'tests': {},
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'skipped_tests': 0
        }

        # Test 1: Trailing Stop Logic
        test_name = "trailing_stop_logic"
        results['total_tests'] += 1
        try:
            # Simulate trailing stop loss logic from trade.py
            def update_trailing_stop(trade, current_price, trailing_offset=0.02):
                """Simulate trailing stop loss update"""
                if trade['side'] == 'BUY':
                    # For long positions, stop loss moves up with price
                    new_stop_loss = current_price - (current_price * trailing_offset)
                    if new_stop_loss > trade['stop_loss']:
                        trade['stop_loss'] = new_stop_loss
                        return True
                elif trade['side'] == 'SELL':
                    # For short positions, stop loss moves down with price
                    new_stop_loss = current_price + (current_price * trailing_offset)
                    if new_stop_loss < trade['stop_loss']:
                        trade['stop_loss'] = new_stop_loss
                        return True
                return False

            # Test scenarios
            test_scenarios = [
                {
                    'trade': {'side': 'BUY', 'entry_price': 100000, 'stop_loss': 95000},
                    'price_movements': [102000, 104000, 103000, 105000],
                    'expected_updates': [True, True, False, True]
                },
                {
                    'trade': {'side': 'SELL', 'entry_price': 100000, 'stop_loss': 105000},
                    'price_movements': [98000, 96000, 97000, 94000],
                    'expected_updates': [True, True, False, True]
                }
            ]

            scenario_results = []
            for scenario in test_scenarios:
                trade = scenario['trade'].copy()
                actual_updates = []

                for price in scenario['price_movements']:
                    updated = update_trailing_stop(trade, price)
                    actual_updates.append(updated)

                scenario_results.append({
                    'scenario': scenario,
                    'actual_updates': actual_updates,
                    'expected_updates': scenario['expected_updates'],
                    'passed': actual_updates == scenario['expected_updates']
                })

            all_passed = all(s['passed'] for s in scenario_results)

            results['tests'][test_name] = {
                'status': 'PASSED' if all_passed else 'FAILED',
                'message': f'Trailing stop loss logic {"passed" if all_passed else "failed"}',
                'scenarios': scenario_results
            }

            if all_passed:
                results['passed_tests'] += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                results['failed_tests'] += 1
                logger.error(f"❌ {test_name}: FAILED")

        except Exception as e:
            results['tests'][test_name] = {
                'status': 'FAILED',
                'message': f'Trailing stop loss test failed: {str(e)}'
            }
            results['failed_tests'] += 1
            logger.error(f"❌ {test_name}: FAILED - {str(e)}")

        results['status'] = 'COMPLETED'
        return results

    def test_exit_strategy_performance(self) -> Dict:
        """Test exit strategy performance and timing"""
        logger.info("⚡ Testing Exit Strategy Performance")

        results = {
            'status': 'RUNNING',
            'tests': {},
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'skipped_tests': 0
        }

        # Test 1: Exit Decision Speed
        test_name = "exit_decision_speed"
        results['total_tests'] += 1
        try:
            from app.models.trade import Trade, TradeStatus
            import time

            # Create test trade
            test_trade = Trade(
                user_id="test_user",
                symbol="BTCUSDT",
                side="BUY",
                quantity=0.1,
                entry_price=100000.0,
                take_profit=105000.0,
                stop_loss=95000.0,
                status=TradeStatus.OPEN
            )

            # Test exit decision performance
            test_prices = [94000, 95000, 96000, 104000, 105000, 106000]
            decision_times = []

            for price in test_prices:
                start_time = time.time()

                # Test both stop loss and take profit decisions
                should_stop = test_trade.should_stop_loss(price)
                should_take = test_trade.should_take_profit(price)

                end_time = time.time()
                decision_time = (end_time - start_time) * 1000  # Convert to milliseconds
                decision_times.append(decision_time)

            avg_decision_time = sum(decision_times) / len(decision_times)
            max_decision_time = max(decision_times)

            # Performance should be very fast (< 1ms per decision)
            performance_acceptable = avg_decision_time < 1.0 and max_decision_time < 5.0

            results['tests'][test_name] = {
                'status': 'PASSED' if performance_acceptable else 'FAILED',
                'message': f'Exit decision performance: avg {avg_decision_time:.3f}ms, max {max_decision_time:.3f}ms',
                'performance_data': {
                    'average_decision_time_ms': round(avg_decision_time, 3),
                    'max_decision_time_ms': round(max_decision_time, 3),
                    'total_decisions': len(test_prices) * 2,
                    'performance_acceptable': performance_acceptable
                }
            }

            if performance_acceptable:
                results['passed_tests'] += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                results['failed_tests'] += 1
                logger.error(f"❌ {test_name}: FAILED")

        except Exception as e:
            results['tests'][test_name] = {
                'status': 'FAILED',
                'message': f'Exit decision speed test failed: {str(e)}'
            }
            results['failed_tests'] += 1
            logger.error(f"❌ {test_name}: FAILED - {str(e)}")

        results['status'] = 'COMPLETED'
        return results

    def test_multi_position_monitoring(self) -> Dict:
        """Test monitoring multiple positions simultaneously"""
        logger.info("🔀 Testing Multi-Position Monitoring")

        results = {
            'status': 'RUNNING',
            'tests': {},
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'skipped_tests': 0
        }

        # Test 1: Multiple Position Logic
        test_name = "multiple_position_logic"
        results['total_tests'] += 1
        try:
            from app.models.trade import Trade, TradeStatus

            # Create multiple test trades
            trades = [
                Trade(
                    user_id="test_user",
                    symbol="BTCUSDT",
                    side="BUY",
                    quantity=0.1,
                    entry_price=100000.0,
                    take_profit=105000.0,
                    stop_loss=95000.0,
                    status=TradeStatus.OPEN
                ),
                Trade(
                    user_id="test_user",
                    symbol="ETHUSDT",
                    side="SELL",
                    quantity=1.0,
                    entry_price=3000.0,
                    take_profit=2850.0,
                    stop_loss=3150.0,
                    status=TradeStatus.OPEN
                )
            ]

            # Test current prices
            test_scenarios = [
                {'BTCUSDT': 105000, 'ETHUSDT': 2850},  # Both hit take profit
                {'BTCUSDT': 95000, 'ETHUSDT': 3150},   # Both hit stop loss
                {'BTCUSDT': 102000, 'ETHUSDT': 2950}   # Both hold
            ]

            scenario_results = []
            for prices in test_scenarios:
                btc_trade, eth_trade = trades

                btc_should_stop = btc_trade.should_stop_loss(prices['BTCUSDT'])
                btc_should_take = btc_trade.should_take_profit(prices['BTCUSDT'])
                eth_should_stop = eth_trade.should_stop_loss(prices['ETHUSDT'])
                eth_should_take = eth_trade.should_take_profit(prices['ETHUSDT'])

                scenario_results.append({
                    'prices': prices,
                    'btc_actions': {'stop_loss': btc_should_stop, 'take_profit': btc_should_take},
                    'eth_actions': {'stop_loss': eth_should_stop, 'take_profit': eth_should_take},
                    'total_actions': sum([btc_should_stop, btc_should_take, eth_should_stop, eth_should_take])
                })

            # All scenarios should produce logical results
            all_logical = True
            for scenario in scenario_results:
                # Each trade should only trigger one action at most
                btc_actions = sum(scenario['btc_actions'].values())
                eth_actions = sum(scenario['eth_actions'].values())
                if btc_actions > 1 or eth_actions > 1:
                    all_logical = False
                    break

            results['tests'][test_name] = {
                'status': 'PASSED' if all_logical else 'FAILED',
                'message': f'Multi-position monitoring logic {"passed" if all_logical else "failed"}',
                'scenarios': scenario_results
            }

            if all_logical:
                results['passed_tests'] += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                results['failed_tests'] += 1
                logger.error(f"❌ {test_name}: FAILED")

        except Exception as e:
            results['tests'][test_name] = {
                'status': 'FAILED',
                'message': f'Multi-position monitoring test failed: {str(e)}'
            }
            results['failed_tests'] += 1
            logger.error(f"❌ {test_name}: FAILED - {str(e)}")

        results['status'] = 'COMPLETED'
        return results

    def _print_position_monitoring_summary(self, results: Dict):
        """Print comprehensive position monitoring test summary"""
        logger.info("\n" + "=" * 80)
        logger.info("📊 POSITION MONITORING TEST SUMMARY")
        logger.info("=" * 80)

        summary = results['summary']
        duration = results.get('duration', 'Unknown')

        logger.info(f"⏱️  Total Duration: {duration}")
        logger.info(f"📋 Total Tests: {summary['total_tests']}")
        logger.info(f"✅ Passed: {summary['passed_tests']}")
        logger.info(f"❌ Failed: {summary['failed_tests']}")
        logger.info(f"⏭️  Skipped: {summary['skipped_tests']}")

        success_rate = (summary['passed_tests'] / summary['total_tests'] * 100) if summary['total_tests'] > 0 else 0
        logger.info(f"📈 Success Rate: {success_rate:.1f}%")

        # Print detailed results for each test suite
        for suite_name, suite_results in results['test_suites'].items():
            logger.info(f"\n📋 {suite_name}:")
            if 'tests' in suite_results:
                for test_name, test_result in suite_results['tests'].items():
                    status_icon = "✅" if test_result['status'] == 'PASSED' else "❌"
                    logger.info(f"   {status_icon} {test_name}: {test_result['message']}")

        # Overall assessment
        logger.info("\n🎯 POSITION MONITORING ASSESSMENT:")
        if success_rate >= 90:
            logger.info("🎉 EXCELLENT: Position monitoring system is highly reliable")
        elif success_rate >= 75:
            logger.info("✅ GOOD: Position monitoring system is mostly functional")
        elif success_rate >= 50:
            logger.info("⚠️  FAIR: Position monitoring system has issues that need attention")
        else:
            logger.info("🚨 POOR: Position monitoring system has critical issues")

        logger.info("=" * 80)


def main():
    """Main execution function"""
    print("🎯 Starting Position Monitoring and Exit Strategy Testing")
    print("=" * 70)

    try:
        from app import create_app

        app = create_app()

        with app.app_context():
            # Create tester instance
            tester = PositionMonitoringTester()

            # Run all tests
            results = tester.run_all_position_tests()

            # Save results to file
            results_file = f"position_monitoring_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)

            print(f"\n💾 Position monitoring test results saved to: {results_file}")

            # Return exit code based on results
            success_rate = (results['summary']['passed_tests'] / results['summary']['total_tests'] * 100) if results['summary']['total_tests'] > 0 else 0

            print(f"\n📊 FINAL POSITION MONITORING ASSESSMENT:")
            print(f"   Success Rate: {success_rate:.1f}%")

            if success_rate >= 75:
                print("🎉 Position monitoring system is working correctly!")
                return 0
            else:
                print("⚠️  Position monitoring system needs attention")
                return 1

    except Exception as e:
        logger.error(f"❌ Critical error during position monitoring testing: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
