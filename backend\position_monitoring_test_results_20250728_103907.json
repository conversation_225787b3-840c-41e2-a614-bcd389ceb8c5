{"start_time": "2025-07-28T10:39:07.755880", "test_suites": {"Take Profit Logic Testing": {"status": "COMPLETED", "tests": {"long_position_take_profit": {"status": "PASSED", "message": "Long position take profit logic passed", "scenarios": [{"price": 104000.0, "expected": false, "actual": false, "passed": true, "description": "Below take profit"}, {"price": 105000.0, "expected": true, "actual": true, "passed": true, "description": "At take profit"}, {"price": 106000.0, "expected": true, "actual": true, "passed": true, "description": "Above take profit"}]}, "short_position_take_profit": {"status": "PASSED", "message": "Short position take profit logic passed", "scenarios": [{"price": 96000.0, "expected": false, "actual": false, "passed": true, "description": "Above take profit"}, {"price": 95000.0, "expected": true, "actual": true, "passed": true, "description": "At take profit"}, {"price": 94000.0, "expected": true, "actual": true, "passed": true, "description": "Below take profit"}]}}, "total_tests": 2, "passed_tests": 2, "failed_tests": 0, "skipped_tests": 0}, "Stop Loss Logic Testing": {"status": "COMPLETED", "tests": {"long_position_stop_loss": {"status": "PASSED", "message": "Long position stop loss logic passed", "scenarios": [{"price": 96000.0, "expected": false, "actual": false, "passed": true, "description": "Above stop loss"}, {"price": 95000.0, "expected": true, "actual": true, "passed": true, "description": "At stop loss"}, {"price": 94000.0, "expected": true, "actual": true, "passed": true, "description": "Below stop loss"}]}, "short_position_stop_loss": {"status": "PASSED", "message": "Short position stop loss logic passed", "scenarios": [{"price": 104000.0, "expected": false, "actual": false, "passed": true, "description": "Below stop loss"}, {"price": 105000.0, "expected": true, "actual": true, "passed": true, "description": "At stop loss"}, {"price": 106000.0, "expected": true, "actual": true, "passed": true, "description": "Above stop loss"}]}}, "total_tests": 2, "passed_tests": 2, "failed_tests": 0, "skipped_tests": 0}, "Position Monitoring Threads": {"status": "COMPLETED", "tests": {"monitoring_thread_creation": {"status": "PASSED", "message": "Position monitoring thread methods available", "thread_capabilities": {"container_created": true, "has_monitoring_methods": true, "has_paper_monitoring": true, "has_price_getter": true, "has_close_methods": true}}}, "total_tests": 1, "passed_tests": 1, "failed_tests": 0, "skipped_tests": 0}, "Paper Trading Exit Logic": {"status": "COMPLETED", "tests": {"paper_trade_exit_methods": {"status": "FAILED", "message": "Paper trade exit methods test failed: __init__() got an unexpected keyword argument 'status'"}}, "total_tests": 1, "passed_tests": 0, "failed_tests": 1, "skipped_tests": 0}, "Live Trading Exit Logic": {"status": "COMPLETED", "tests": {"live_trade_exit_methods": {"status": "FAILED", "message": "Live trade exit methods test failed: __init__() got an unexpected keyword argument 'status'"}}, "total_tests": 1, "passed_tests": 0, "failed_tests": 1, "skipped_tests": 0}, "Trailing Stop Loss": {"status": "COMPLETED", "tests": {"trailing_stop_logic": {"status": "PASSED", "message": "Trailing stop loss logic passed", "scenarios": [{"scenario": {"trade": {"side": "BUY", "entry_price": 100000, "stop_loss": 95000}, "price_movements": [102000, 104000, 103000, 105000], "expected_updates": [true, true, false, true]}, "actual_updates": [true, true, false, true], "expected_updates": [true, true, false, true], "passed": true}, {"scenario": {"trade": {"side": "SELL", "entry_price": 100000, "stop_loss": 105000}, "price_movements": [98000, 96000, 97000, 94000], "expected_updates": [true, true, false, true]}, "actual_updates": [true, true, false, true], "expected_updates": [true, true, false, true], "passed": true}]}}, "total_tests": 1, "passed_tests": 1, "failed_tests": 0, "skipped_tests": 0}, "Exit Strategy Performance": {"status": "COMPLETED", "tests": {"exit_decision_speed": {"status": "FAILED", "message": "Exit decision speed test failed: __init__() got an unexpected keyword argument 'status'"}}, "total_tests": 1, "passed_tests": 0, "failed_tests": 1, "skipped_tests": 0}, "Multi-Position Monitoring": {"status": "COMPLETED", "tests": {"multiple_position_logic": {"status": "FAILED", "message": "Multi-position monitoring test failed: __init__() got an unexpected keyword argument 'status'"}}, "total_tests": 1, "passed_tests": 0, "failed_tests": 1, "skipped_tests": 0}}, "summary": {"total_tests": 10, "passed_tests": 6, "failed_tests": 4, "skipped_tests": 0}, "end_time": "2025-07-28T10:39:07.767850"}