#!/usr/bin/env python3
"""
Quick test to check payment status directly from database.
This bypasses the API and checks the database directly.
"""

import sys
import os
from datetime import datetime

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_payment_flow():
    """Test the payment flow directly using the database."""
    try:
        # Import Flask app and models
        from app import create_app
        from app.models.user_tier_status import UserTierStatus
        from app.models.user import User
        from app.models.solana_payment import SolanaPayment, SolanaPaymentType, SolanaPaymentStatus
        from app import db
        from decimal import Decimal
        import uuid

        # Create Flask app context
        app = create_app()
        
        with app.app_context():
            print("🧪 Direct Database Payment Flow Test")
            print("=" * 50)
            
            # Find a test user (you can replace with specific email)
            test_user = User.query.filter_by(email='<EMAIL>').first()
            if not test_user:
                print("❌ Test user not found. Please update the email in the script.")
                return
            
            print(f"👤 Testing with user: {test_user.email} (ID: {test_user.id})")
            
            # Get or create tier status
            tier_status = UserTierStatus.query.filter_by(user_id=test_user.id).first()
            if not tier_status:
                print("❌ No tier status found for user")
                return
            
            print(f"\n📊 BEFORE PAYMENT:")
            print(f"   - Profit Share Owed: ${tier_status.profit_share_owed}")
            print(f"   - Payment Status: {tier_status.payment_status}")
            print(f"   - Account Disabled: {tier_status.account_disabled}")
            print(f"   - Data Type: {type(tier_status.profit_share_owed)}")
            
            # If no debt, add some test debt
            if tier_status.profit_share_owed <= 0:
                print(f"\n💰 Adding test debt of $0.01...")
                tier_status.profit_share_owed = 0.01
                tier_status.payment_status = 'unpaid'
                db.session.commit()
                print(f"   - New debt: ${tier_status.profit_share_owed}")
            
            # Record the debt amount before payment
            original_debt = float(tier_status.profit_share_owed)
            print(f"\n💳 Making payment for: ${original_debt}")
            
            # Create a simulated payment record
            simulated_payment = SolanaPayment(
                user_id=test_user.id,
                payment_type=SolanaPaymentType.PROFIT_SHARE,
                amount=Decimal(str(original_debt)),
                to_address='SIMULATED_TREASURY_WALLET',
                from_address='SIMULATED_USER_WALLET',
                transaction_signature=f'SIMULATED_TX_{uuid.uuid4().hex[:16]}'
            )
            simulated_payment.status = SolanaPaymentStatus.CONFIRMED
            simulated_payment.processed_at = datetime.utcnow()
            
            db.session.add(simulated_payment)
            
            # Process the payment using clear_debt method
            print(f"🔄 Processing payment using clear_debt method...")
            tier_status.clear_debt(original_debt)
            
            # Commit the changes
            db.session.commit()
            
            # Refresh the object from database
            db.session.refresh(tier_status)
            
            print(f"\n📊 AFTER PAYMENT:")
            print(f"   - Profit Share Owed: ${tier_status.profit_share_owed}")
            print(f"   - Payment Status: {tier_status.payment_status}")
            print(f"   - Account Disabled: {tier_status.account_disabled}")
            print(f"   - Data Type: {type(tier_status.profit_share_owed)}")
            
            # Verification
            print(f"\n🔍 VERIFICATION:")
            print("=" * 30)
            
            if tier_status.profit_share_owed == 0:
                print("✅ SUCCESS: Debt properly cleared to $0.00!")
            else:
                print(f"❌ FAILURE: Debt not cleared! Still owe: ${tier_status.profit_share_owed}")
            
            if tier_status.payment_status == 'paid':
                print("✅ SUCCESS: Payment status updated to 'paid'!")
            else:
                print(f"❌ FAILURE: Payment status not updated! Status: {tier_status.payment_status}")
            
            if not tier_status.account_disabled:
                print("✅ SUCCESS: Account is enabled!")
            else:
                print("❌ FAILURE: Account is still disabled!")
            
            # Test the API response format
            print(f"\n🌐 API Response Format Test:")
            tier_dict = tier_status.to_dict()
            print(f"   - to_dict() profit_share_owed: {tier_dict['profit_share_owed']} ({type(tier_dict['profit_share_owed'])})")
            print(f"   - to_dict() payment_status: {tier_dict['payment_status']}")
            
            # Test what the tier status endpoint would return
            response_data = {
                'profit_share_owed': str(tier_status.profit_share_owed),
                'payment_status': tier_status.payment_status,
                'account_disabled': tier_status.account_disabled
            }
            print(f"\n📡 Simulated API Response:")
            print(f"   - profit_share_owed (str): '{response_data['profit_share_owed']}'")
            print(f"   - payment_status: '{response_data['payment_status']}'")
            print(f"   - account_disabled: {response_data['account_disabled']}")
            
            return tier_status.profit_share_owed == 0 and tier_status.payment_status == 'paid'
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_payment_flow()
    if success:
        print(f"\n🎉 OVERALL RESULT: PAYMENT FLOW WORKING CORRECTLY!")
    else:
        print(f"\n💥 OVERALL RESULT: PAYMENT FLOW HAS ISSUES!")
