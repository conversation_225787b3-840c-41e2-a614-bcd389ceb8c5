#!/usr/bin/env python3
"""
<PERSON>ript to rebuild the Chinese file with proper structure
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_english_structure():
    """Get the structure from English file"""
    try:
        with open('../frontend/src/i18n/locales/en/common.ts', 'r', encoding='utf-8') as f:
            en_content = f.read()
        
        # Extract main sections
        sections = []
        
        # Find all top-level sections
        pattern = r'"([^"]+)":\s*{'
        matches = re.finditer(pattern, en_content)
        
        for match in matches:
            section_name = match.group(1)
            sections.append(section_name)
        
        return sections
    except Exception as e:
        print(f"Error reading English file: {str(e)}")
        return []

def fix_chinese_structure():
    """Fix the Chinese file structure"""
    print("REBUILDING CHINESE FILE STRUCTURE")
    print("=" * 50)
    
    try:
        # Read current Chinese file
        with open('../frontend/src/i18n/locales/zh/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("1. Analyzing current structure...")
        
        # Count braces
        open_braces = content.count('{')
        close_braces = content.count('}')
        print(f"   Current: {open_braces} open, {close_braces} close")
        
        if open_braces == close_braces:
            print("   ✅ Braces are already balanced")
            return True
        
        print("2. Fixing brace balance...")
        
        # Remove the problematic ending
        content = content.rstrip()
        
        # Find the last meaningful content (not just closing braces)
        lines = content.split('\n')
        
        # Remove trailing empty closing braces
        while lines and lines[-1].strip() in ['', '}', '};']:
            lines.pop()
        
        # Find the last line with actual content
        last_content_line = -1
        for i in range(len(lines) - 1, -1, -1):
            line = lines[i].strip()
            if line and not line.startswith('}') and line != '};':
                last_content_line = i
                break
        
        if last_content_line >= 0:
            # Keep content up to the last meaningful line
            content = '\n'.join(lines[:last_content_line + 1])
            
            # Count braces in the meaningful content
            open_braces = content.count('{')
            close_braces = content.count('}')
            
            print(f"   After cleanup: {open_braces} open, {close_braces} close")
            
            # Add the required closing braces
            missing_braces = open_braces - close_braces
            
            if missing_braces > 0:
                print(f"   Adding {missing_braces} closing braces...")
                
                # Add closing braces with proper indentation
                for i in range(missing_braces):
                    if i == missing_braces - 1:
                        # Last brace should be the export default closing
                        content += '\n};'
                    else:
                        # Add intermediate closing braces
                        content += '\n  }'
            else:
                content += '\n};'
            
            content += '\n'
        
        print("3. Writing fixed file...")
        
        # Write the fixed content
        with open('../frontend/src/i18n/locales/zh/common.ts', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("4. Validating fix...")
        
        # Validate the fix
        final_open = content.count('{')
        final_close = content.count('}')
        
        print(f"   Final: {final_open} open, {final_close} close")
        
        if final_open == final_close:
            print("   ✅ Braces are now balanced")
            return True
        else:
            print(f"   ❌ Still unbalanced: {abs(final_open - final_close)} difference")
            return False
        
    except Exception as e:
        print(f"❌ Error fixing structure: {str(e)}")
        return False

def run_rebuild():
    """Run the rebuild process"""
    success = fix_chinese_structure()
    
    print("\n" + "=" * 50)
    print("CHINESE FILE REBUILD SUMMARY")
    print("=" * 50)
    
    if success:
        print("✅ Chinese file structure fixed!")
        print("✅ Braces are balanced")
        print("✅ File should compile successfully")
        
        print("\n🎯 NEXT STEPS:")
        print("1. Check that frontend compiles")
        print("2. Verify Vite error overlay disappears")
        print("3. Test Chinese language functionality")
        
        return True
    else:
        print("❌ Structure fix failed")
        print("Manual intervention may be required")
        return False

if __name__ == "__main__":
    success = run_rebuild()
    sys.exit(0 if success else 1)
