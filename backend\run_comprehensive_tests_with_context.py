#!/usr/bin/env python3
"""
Run comprehensive trading tests with proper Flask application context
"""

import sys
import os
from datetime import datetime

# Add backend to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def run_tests_with_context():
    """Run comprehensive tests with Flask application context"""
    print("🚀 RUNNING COMPREHENSIVE TESTS WITH FLASK CONTEXT")
    print("=" * 60)
    
    try:
        # Import Flask app
        print("📦 Importing Flask application...")
        from app import create_app
        
        # Create app instance
        print("🏗️  Creating Flask application...")
        app = create_app()
        
        # Import test framework
        print("🧪 Importing test framework...")
        from comprehensive_trading_test import TradingWorkflowTester
        
        print("✅ All imports successful!")
        print()
        
        # Run tests within application context
        print("🎯 Running tests within Flask application context...")
        with app.app_context():
            # Create tester instance
            tester = TradingWorkflowTester()
            
            # Run all tests
            print("🚀 Starting comprehensive test execution...")
            results = tester.run_all_tests()
            
            # Save results
            results_file = f"trading_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            import json
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            print(f"\n💾 Test results saved to: {results_file}")
            
            # Print summary
            summary = results['summary']
            success_rate = (summary['passed_tests'] / summary['total_tests'] * 100) if summary['total_tests'] > 0 else 0
            
            print(f"\n📊 FINAL SUMMARY:")
            print(f"   Total Tests: {summary['total_tests']}")
            print(f"   Passed: {summary['passed_tests']}")
            print(f"   Failed: {summary['failed_tests']}")
            print(f"   Success Rate: {success_rate:.1f}%")
            
            if success_rate >= 75:
                print(f"🎉 SUCCESS: Test suite passed with {success_rate:.1f}% success rate!")
                return 0
            else:
                print(f"⚠️  WARNING: Test suite has issues ({success_rate:.1f}% success rate)")
                return 1
                
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("   Make sure you're running from the backend directory")
        print("   and all dependencies are installed")
        return 1
        
    except Exception as e:
        print(f"❌ Execution Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

def run_individual_test_suites():
    """Run individual test suites for debugging"""
    print("🔍 RUNNING INDIVIDUAL TEST SUITES FOR DEBUGGING")
    print("=" * 60)
    
    try:
        from app import create_app
        from comprehensive_trading_test import TradingWorkflowTester
        
        app = create_app()
        
        with app.app_context():
            tester = TradingWorkflowTester()
            
            # Test suites to run individually
            test_suites = [
                ("Signal Generation", tester.test_signal_generation),
                ("ML Forecast Integration", tester.test_ml_forecast_integration),
                ("Trade Execution Pipeline", tester.test_trade_execution_pipeline),
                ("Integration Workflow", tester.test_integration_workflow),
                ("Error Handling", tester.test_error_handling),
                ("Performance Testing", tester.test_performance)
            ]
            
            overall_results = {
                'individual_suites': {},
                'summary': {'total_tests': 0, 'passed_tests': 0, 'failed_tests': 0}
            }
            
            for suite_name, test_function in test_suites:
                print(f"\n🧪 Testing: {suite_name}")
                print("-" * 40)
                
                try:
                    suite_results = test_function()
                    overall_results['individual_suites'][suite_name] = suite_results
                    
                    # Update summary
                    overall_results['summary']['total_tests'] += suite_results.get('total_tests', 0)
                    overall_results['summary']['passed_tests'] += suite_results.get('passed_tests', 0)
                    overall_results['summary']['failed_tests'] += suite_results.get('failed_tests', 0)
                    
                    # Print suite summary
                    passed = suite_results.get('passed_tests', 0)
                    total = suite_results.get('total_tests', 0)
                    print(f"   Result: {passed}/{total} tests passed")
                    
                except Exception as e:
                    print(f"   ❌ Suite failed: {e}")
                    overall_results['individual_suites'][suite_name] = {'error': str(e)}
                    overall_results['summary']['failed_tests'] += 1
            
            # Final summary
            summary = overall_results['summary']
            success_rate = (summary['passed_tests'] / summary['total_tests'] * 100) if summary['total_tests'] > 0 else 0
            
            print(f"\n📊 INDIVIDUAL SUITE TESTING SUMMARY:")
            print(f"   Total Tests: {summary['total_tests']}")
            print(f"   Passed: {summary['passed_tests']}")
            print(f"   Failed: {summary['failed_tests']}")
            print(f"   Success Rate: {success_rate:.1f}%")
            
            return 0 if success_rate >= 50 else 1
            
    except Exception as e:
        print(f"❌ Individual testing failed: {e}")
        return 1

def main():
    """Main execution function"""
    print(f"🚀 DEEPTRADE COMPREHENSIVE TESTING WITH FLASK CONTEXT")
    print(f"Timestamp: {datetime.now()}")
    print("=" * 60)
    
    # Check command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == '--individual':
        return run_individual_test_suites()
    else:
        return run_tests_with_context()

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
