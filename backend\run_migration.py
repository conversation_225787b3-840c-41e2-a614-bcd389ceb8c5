#!/usr/bin/env python3
"""
Simple migration script to add risk settings fields to users table
Run this from the backend directory: python run_migration.py
"""

import os
import sys
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
import mysql.connector
from mysql.connector import Error

# Database configuration - update these with your actual values
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'deeptrade',
    'user': 'deeptrade_user',
    'password': '123456'
}

def run_migration():
    """Run the migration using direct MySQL connection"""
    connection = None
    cursor = None
    
    try:
        print("🔄 Connecting to MySQL database...")
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("✅ Connected successfully!")
        
        # Check if columns already exist
        print("🔍 Checking existing columns...")
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = %s 
            AND table_name = 'users' 
            AND column_name IN ('investment_percentage', 'leverage_multiplier', 'risk_settings_configured', 'selected_exchange', 'account_type')
        """, (DB_CONFIG['database'],))
        
        existing_columns = [row[0] for row in cursor.fetchall()]
        
        if existing_columns:
            print(f"⚠️  Some columns already exist: {existing_columns}")
            print("❌ Skipping migration to avoid conflicts.")
            return
        
        print("📝 Adding new columns to users table...")
        
        # Add new columns one by one
        migrations = [
            "ALTER TABLE users ADD COLUMN investment_percentage DECIMAL(5,2) DEFAULT 0.00 NOT NULL",
            "ALTER TABLE users ADD COLUMN leverage_multiplier DECIMAL(3,1) DEFAULT 1.0 NOT NULL", 
            "ALTER TABLE users ADD COLUMN risk_settings_configured BOOLEAN DEFAULT FALSE NOT NULL",
            "ALTER TABLE users ADD COLUMN selected_exchange VARCHAR(50) DEFAULT 'binance' NOT NULL",
            "ALTER TABLE users ADD COLUMN account_type VARCHAR(20) DEFAULT 'SPOT' NOT NULL"
        ]
        
        for i, migration in enumerate(migrations, 1):
            print(f"  {i}/5: {migration}")
            cursor.execute(migration)
        
        print("📊 Adding indexes...")
        
        # Add indexes
        indexes = [
            "CREATE INDEX idx_users_risk_configured ON users(risk_settings_configured)",
            "CREATE INDEX idx_users_selected_exchange ON users(selected_exchange)"
        ]
        
        for i, index in enumerate(indexes, 1):
            print(f"  {i}/2: {index}")
            try:
                cursor.execute(index)
            except Error as e:
                if "Duplicate key name" in str(e):
                    print(f"    ⚠️  Index already exists, skipping...")
                else:
                    raise
        
        # Commit all changes
        connection.commit()
        
        print("\n✅ Migration completed successfully!")
        print("📋 New fields added:")
        print("  - investment_percentage (DECIMAL(5,2), default 0.00)")
        print("  - leverage_multiplier (DECIMAL(3,1), default 1.0)")
        print("  - risk_settings_configured (BOOLEAN, default FALSE)")
        print("  - selected_exchange (VARCHAR(50), default 'binance')")
        print("  - account_type (VARCHAR(20), default 'SPOT')")
        print("\n🚀 You can now restart your Flask application!")
        
    except Error as e:
        print(f"❌ Migration failed: {e}")
        if connection:
            connection.rollback()
        sys.exit(1)
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
            print("🔌 Database connection closed.")

if __name__ == "__main__":
    print("🚀 Starting DeepTrade Risk Settings Migration")
    print("=" * 50)
    run_migration()
