import sys
import os
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from app.extensions import db
from app.models.user_tier_status import UserTierStatus

def check_and_fix_tier_references():
    app = create_app()
    with app.app_context():
        # Check if tier 1 exists in user_tier_status
        tier1 = UserTierStatus.query.filter_by(tier=1).first()
        
        if not tier1:
            print("Tier 1 not found in user_tier_status. Creating...")
            try:
                tier1 = UserTierStatus(
                    tier=1,
                    name="Free",
                    description="Free tier with basic features",
                    max_trading_volume=1000,
                    max_open_trades=5,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                db.session.add(tier1)
                db.session.commit()
                print("Created Tier 1 in user_tier_status")
            except Exception as e:
                db.session.rollback()
                print(f"Error creating Tier 1: {e}")
        
        # Check for other tiers (2 and 3)
        for tier_num in [2, 3]:
            tier = UserTierStatus.query.filter_by(tier=tier_num).first()
            if not tier:
                print(f"Tier {tier_num} not found in user_tier_status. Creating...")
                try:
                    new_tier = UserTierStatus(
                        tier=tier_num,
                        name=f"Tier {tier_num}",
                        description=f"Tier {tier_num} subscription",
                        max_trading_volume=1000 * tier_num,
                        max_open_trades=5 * tier_num,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    db.session.add(new_tier)
                    db.session.commit()
                    print(f"Created Tier {tier_num} in user_tier_status")
                except Exception as e:
                    db.session.rollback()
                    print(f"Error creating Tier {tier_num}: {e}")
        
        print("Tier reference check complete.")

if __name__ == "__main__":
    check_and_fix_tier_references()
