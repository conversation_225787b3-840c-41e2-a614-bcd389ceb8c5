#!/usr/bin/env python3
"""
Simple Admin Fix Script - Run with Flask shell
"""

# Run this in Flask shell: flask shell
# Then: exec(open('simple_admin_fix.py').read())

from app.models.admin import AdminUser
from app import db
from werkzeug.security import generate_password_hash

print("🔍 CHECKING ADMIN USERS...")

# Check existing admins
admins = AdminUser.query.all()
print(f"Found {len(admins)} admin users:")

for admin in admins:
    print(f"- ID: {admin.id}, Username: {admin.username}, Super: {admin.is_super_admin}, Active: {admin.is_active}")

# Fix super admin
print("\n🔧 FIXING SUPER ADMIN...")
super_admin = AdminUser.query.filter_by(username='admin').first()

if super_admin:
    print(f"Updating existing super admin: {super_admin.username}")
    super_admin.password_hash = generate_password_hash('admin123')
    super_admin.is_super_admin = True
    super_admin.is_active = True
else:
    print("Creating new super admin...")
    super_admin = AdminUser(
        username='admin',
        password='admin123',
        is_super_admin=True,
        is_active=True
    )
    db.session.add(super_admin)

# Fix limited admin
print("\n🔧 FIXING LIMITED ADMIN...")
limited_admin = AdminUser.query.filter_by(username='limited_admin').first()

if limited_admin:
    print(f"Updating existing limited admin: {limited_admin.username}")
    limited_admin.password_hash = generate_password_hash('limited123')
    limited_admin.is_super_admin = False
    limited_admin.is_active = True
else:
    print("Creating new limited admin...")
    limited_admin = AdminUser(
        username='limited_admin',
        password='limited123',
        is_super_admin=False,
        is_active=True
    )
    db.session.add(limited_admin)

# Commit changes
db.session.commit()
print("\n✅ ADMIN USERS FIXED!")

# Verify
print("\n🧪 VERIFICATION:")
admins = AdminUser.query.all()
for admin in admins:
    print(f"- {admin.username}: Super={admin.is_super_admin}, Active={admin.is_active}")
    
    # Test passwords
    if admin.username == 'admin' and admin.check_password('admin123'):
        print(f"  ✅ Password 'admin123' works for {admin.username}")
    elif admin.username == 'limited_admin' and admin.check_password('limited123'):
        print(f"  ✅ Password 'limited123' works for {admin.username}")

print("\n🎯 READY TO TEST:")
print("1. Super Admin: admin / admin123")
print("2. Limited Admin: limited_admin / limited123")
