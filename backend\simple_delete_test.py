#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_delete():
    try:
        from app import create_app, db
        from app.models.admin import AdminUser, AdminAction, CouponCode
        
        app = create_app()
        
        with app.app_context():
            print("Testing simple admin deletion...")
            
            # Create a test admin
            test_admin = AdminUser(
                username='test_delete_admin',
                password='test123',
                is_super_admin=False
            )
            db.session.add(test_admin)
            db.session.commit()
            
            admin_id = test_admin.id
            print(f"Created test admin with ID: {admin_id}")
            
            # Try to delete it
            print("Attempting deletion...")
            db.session.delete(test_admin)
            db.session.commit()
            
            # Check if deleted
            deleted_admin = AdminUser.query.get(admin_id)
            if deleted_admin is None:
                print("✅ SUCCESS: Admin deleted successfully")
                return True
            else:
                print("❌ FAILED: Admin still exists")
                return False
                
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_simple_delete()
    print(f"Test result: {'PASSED' if success else 'FAILED'}")
