#!/usr/bin/env python3
"""
Simple test to force a signal from optimized legacy
"""

import sys
import os
import pandas as pd
import numpy as np

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simple_signal_test():
    """Create perfect conditions that should generate a signal"""
    print("🎯 Simple Signal Test - Perfect Conditions")
    print("=" * 60)
    
    try:
        # Import Flask app and create application context
        from app import create_app
        app = create_app()
        
        with app.app_context():
            # Import required services
            from app.services.market_data import BinanceMarketData
            from app.services.trading_signals import TradingSignalGenerator
            
            # Initialize services
            market_service = BinanceMarketData()
            signal_generator = TradingSignalGenerator("test_user", market_service)
            
            print("✅ Services initialized successfully")
            
            # Create PERFECT bullish scenario
            print(f"\n🚀 Creating PERFECT Bullish Scenario...")
            
            # Generate 100 hours of perfect uptrend
            hours = 100
            base_price = 60000
            
            # Perfect uptrend - steady increase
            prices = [base_price + (i * 50) for i in range(hours)]  # $50/hour = $1200/day
            
            # Create perfect OHLC with consistent uptrend
            mock_data = pd.DataFrame({
                'timestamp': [1640995200000 + (i * 3600000) for i in range(hours)],  # Hourly timestamps
                'open': prices,
                'high': [p * 1.015 for p in prices],  # 1.5% higher
                'low': [p * 0.995 for p in prices],   # 0.5% lower  
                'close': [p * 1.008 for p in prices], # 0.8% higher than open
                'volume': [8000 + (i * 50) for i in range(hours)]  # Increasing volume
            })
            
            current_price = float(mock_data['close'].iloc[-1])
            print(f"   💰 Current Price: ${current_price:,.2f}")
            print(f"   📈 Total Gain: +${current_price - base_price:,.2f}")
            
            # Create VERY optimistic forecast
            forecast = np.array([current_price * (1 + 0.05 + i*0.001) for i in range(24)])  # 5%+ forecast
            highest_price = np.max(forecast)
            lowest_price = np.min(forecast)
            
            print(f"   📈 Forecast High: ${highest_price:,.2f} (+{((highest_price/current_price)-1)*100:.1f}%)")
            
            # Perfect swing points
            swing_points = {
                'swing_high': current_price * 1.08,  # 8% above (very high)
                'swing_low': current_price * 0.90    # 10% below
            }
            
            print(f"   🔺 Swing High: ${swing_points['swing_high']:,.2f}")
            print(f"   🔻 Swing Low: ${swing_points['swing_low']:,.2f}")
            
            # Calculate Heikin-Ashi
            heikin_ashi = signal_generator._calculate_heikin_ashi(mock_data)
            print(f"   🕯️ HA Color: {heikin_ashi.get('ha_color', 'N/A')}")
            
            # Test the system
            result = signal_generator._analyze_trading_conditions(
                market_data=mock_data,
                forecast=forecast,
                swing_points=swing_points,
                heikin_ashi=heikin_ashi,
                highest_price=highest_price,
                lowest_price=lowest_price
            )
            
            signal = result.get('signal', 'UNKNOWN')
            confidence = result.get('confidence', 0)
            
            print(f"\n🎯 PERFECT SCENARIO RESULT:")
            print(f"   Signal: {signal}")
            print(f"   Confidence: {confidence:.1f}%")
            
            if signal == 'BUY':
                print(f"   ✅ SUCCESS! BUY signal generated!")
                
                # Show what worked
                if 'optimized_indicators' in result:
                    opt_ind = result['optimized_indicators']
                    ma = opt_ind.get('moving_averages', {})
                    mom = opt_ind.get('momentum', {})
                    vol = opt_ind.get('volume', {})
                    
                    print(f"\n📊 What Worked:")
                    print(f"   📈 MA Bullish Alignment: {ma.get('bullish_alignment', False)}")
                    print(f"   ⚡ RSI: {mom.get('rsi', 0):.1f}")
                    print(f"   📊 Volume Ratio: {vol.get('ratio', 0):.1f}x")
                
            else:
                print(f"   ❌ FAILED! Still getting {signal}")
                
                # Show what's blocking
                if 'optimized_indicators' in result:
                    opt_ind = result['optimized_indicators']
                    ma = opt_ind.get('moving_averages', {})
                    mom = opt_ind.get('momentum', {})
                    macd = opt_ind.get('macd', {})
                    vol = opt_ind.get('volume', {})
                    pm = opt_ind.get('price_momentum', {})
                    
                    print(f"\n❌ What's Blocking:")
                    print(f"   📈 MA Bullish Alignment: {ma.get('bullish_alignment', False)}")
                    print(f"   📉 MA Bearish Alignment: {ma.get('bearish_alignment', False)}")
                    print(f"   ⚡ RSI: {mom.get('rsi', 0):.1f} (OS: {mom.get('rsi_oversold', False)}, Bull: {mom.get('rsi_bullish_momentum', False)})")
                    print(f"   📊 MACD Bull Cross: {macd.get('bullish_cross', False)}")
                    print(f"   📊 MACD > Signal: {macd.get('macd', 0):.4f} > {macd.get('signal', 0):.4f} = {macd.get('macd', 0) > macd.get('signal', 0)}")
                    print(f"   📊 Volume Surge: {vol.get('surge', False)} (Ratio: {vol.get('ratio', 0):.1f}x)")
                    print(f"   🚀 4H Momentum: {pm.get('4h_change', 0):.2f}%")
                    
                    # Calculate potential moves
                    potential_up = (highest_price - current_price) / current_price
                    print(f"   📈 Potential Up: {potential_up*100:.2f}% (need >1.2%)")
            
            # Try with even more extreme conditions
            print(f"\n🔥 Trying EXTREME Bullish Conditions...")
            
            # Extreme scenario
            extreme_data = mock_data.copy()
            
            # Boost last 20 hours dramatically
            for i in range(-20, 0):
                multiplier = 1 + (abs(i) * 0.01)  # 1% per hour boost
                extreme_data.iloc[i, extreme_data.columns.get_loc('close')] *= multiplier
                extreme_data.iloc[i, extreme_data.columns.get_loc('high')] *= multiplier
                extreme_data.iloc[i, extreme_data.columns.get_loc('volume')] *= 3  # 3x volume
            
            current_price = float(extreme_data['close'].iloc[-1])
            
            # Extreme forecast
            forecast = np.array([current_price * (1 + 0.08 + i*0.002) for i in range(24)])  # 8%+ forecast
            highest_price = np.max(forecast)
            
            # Extreme swing points
            swing_points = {
                'swing_high': current_price * 1.15,  # 15% above
                'swing_low': current_price * 0.85    # 15% below
            }
            
            heikin_ashi = signal_generator._calculate_heikin_ashi(extreme_data)
            
            result = signal_generator._analyze_trading_conditions(
                market_data=extreme_data,
                forecast=forecast,
                swing_points=swing_points,
                heikin_ashi=heikin_ashi,
                highest_price=highest_price,
                lowest_price=np.min(forecast)
            )
            
            signal = result.get('signal', 'UNKNOWN')
            confidence = result.get('confidence', 0)
            
            print(f"   💰 Extreme Price: ${current_price:,.2f}")
            print(f"   📈 Extreme Forecast: +{((highest_price/current_price)-1)*100:.1f}%")
            print(f"   🎯 Extreme Result: {signal} ({confidence:.1f}%)")
            
            if signal != 'HOLD':
                print(f"   ✅ EXTREME CONDITIONS WORKED!")
                return True
            else:
                print(f"   ❌ Even extreme conditions failed!")
                print(f"\n💡 The system needs fundamental adjustments:")
                print(f"   • Reduce ALL confirmation requirements")
                print(f"   • Lower potential move threshold to 0.8%")
                print(f"   • Make volume requirements optional")
                print(f"   • Simplify trend detection")
                return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Simple Signal Test")
    print("=" * 40)
    
    success = simple_signal_test()
    
    if success:
        print(f"\n🎉 SIGNAL GENERATION WORKING!")
    else:
        print(f"\n❌ SYSTEM TOO CONSERVATIVE - NEEDS ADJUSTMENT")
