#!/usr/bin/env python3
"""
Script to sync missing translation keys from English to all other languages
"""

import sys
import os
import re
import json

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def extract_common_section_from_english():
    """Extract the common section from English .ts file"""
    try:
        with open('../frontend/src/i18n/locales/en/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the common section
        common_match = re.search(r'"common":\s*{([^}]+(?:{[^}]*}[^}]*)*)}', content, re.DOTALL)
        if not common_match:
            print("Could not find common section in English file")
            return None
        
        common_content = common_match.group(1)
        
        # Parse the common keys
        common_keys = {}
        lines = common_content.split('\n')
        
        for line in lines:
            line = line.strip()
            if line and '"' in line and '":' in line and not ':{' in line:
                key_match = re.search(r'"([^"]+)":\s*"([^"]*)"', line)
                if key_match:
                    key = key_match.group(1)
                    value = key_match.group(2)
                    common_keys[key] = value
        
        return common_keys
        
    except Exception as e:
        print(f"Error extracting common section: {str(e)}")
        return None

def get_language_translations():
    """Get translations for common keys in different languages"""
    translations = {
        'es': {
            'info': 'Información',
            'ok': 'OK',
            'apply': 'Aplicar',
            'clear': 'Limpiar',
            'copy': 'Copiar',
            'copied': '¡Copiado!',
            'download': 'Descargar',
            'upload': 'Subir',
            'select': 'Seleccionar',
            'print': 'Imprimir',
            'and': 'y',
            'disable': 'Deshabilitar',
            'enable': 'Habilitar',
            'close': 'Cerrar',
            'back': 'Atrás',
            'next': 'Siguiente',
            'previous': 'Anterior',
            'confirm': 'Confirmar',
            'length': 'longitud',
            'special': 'especial',
            'lowercase': 'minúscula',
            'uppercase': 'mayúscula',
            'emailInvalid': 'Email inválido'
        },
        'pt': {
            'info': 'Informação',
            'ok': 'OK',
            'apply': 'Aplicar',
            'clear': 'Limpar',
            'copy': 'Copiar',
            'copied': 'Copiado!',
            'download': 'Baixar',
            'upload': 'Enviar',
            'select': 'Selecionar',
            'print': 'Imprimir',
            'and': 'e',
            'disable': 'Desabilitar',
            'enable': 'Habilitar',
            'close': 'Fechar',
            'back': 'Voltar',
            'next': 'Próximo',
            'previous': 'Anterior',
            'confirm': 'Confirmar',
            'length': 'comprimento',
            'special': 'especial',
            'lowercase': 'minúscula',
            'uppercase': 'maiúscula',
            'emailInvalid': 'Email inválido'
        },
        'ko': {
            'info': '정보',
            'ok': '확인',
            'apply': '적용',
            'clear': '지우기',
            'copy': '복사',
            'copied': '복사됨!',
            'download': '다운로드',
            'upload': '업로드',
            'select': '선택',
            'print': '인쇄',
            'and': '그리고',
            'disable': '비활성화',
            'enable': '활성화',
            'close': '닫기',
            'back': '뒤로',
            'next': '다음',
            'previous': '이전',
            'confirm': '확인',
            'length': '길이',
            'special': '특수',
            'lowercase': '소문자',
            'uppercase': '대문자',
            'emailInvalid': '유효하지 않은 이메일'
        },
        'ja': {
            'info': '情報',
            'ok': 'OK',
            'apply': '適用',
            'clear': 'クリア',
            'copy': 'コピー',
            'copied': 'コピーしました！',
            'download': 'ダウンロード',
            'upload': 'アップロード',
            'select': '選択',
            'print': '印刷',
            'and': 'と',
            'disable': '無効化',
            'enable': '有効化',
            'close': '閉じる',
            'back': '戻る',
            'next': '次へ',
            'previous': '前へ',
            'confirm': '確認',
            'length': '長さ',
            'special': '特殊',
            'lowercase': '小文字',
            'uppercase': '大文字',
            'emailInvalid': '無効なメール'
        },
        'de': {
            'info': 'Information',
            'ok': 'OK',
            'apply': 'Anwenden',
            'clear': 'Löschen',
            'copy': 'Kopieren',
            'copied': 'Kopiert!',
            'download': 'Herunterladen',
            'upload': 'Hochladen',
            'select': 'Auswählen',
            'print': 'Drucken',
            'and': 'und',
            'disable': 'Deaktivieren',
            'enable': 'Aktivieren',
            'close': 'Schließen',
            'back': 'Zurück',
            'next': 'Weiter',
            'previous': 'Vorherige',
            'confirm': 'Bestätigen',
            'length': 'Länge',
            'special': 'Sonderzeichen',
            'lowercase': 'Kleinbuchstabe',
            'uppercase': 'Großbuchstabe',
            'emailInvalid': 'Ungültige E-Mail'
        },
        'fr': {
            'info': 'Information',
            'ok': 'OK',
            'apply': 'Appliquer',
            'clear': 'Effacer',
            'copy': 'Copier',
            'copied': 'Copié !',
            'download': 'Télécharger',
            'upload': 'Téléverser',
            'select': 'Sélectionner',
            'print': 'Imprimer',
            'and': 'et',
            'disable': 'Désactiver',
            'enable': 'Activer',
            'close': 'Fermer',
            'back': 'Retour',
            'next': 'Suivant',
            'previous': 'Précédent',
            'confirm': 'Confirmer',
            'length': 'longueur',
            'special': 'spécial',
            'lowercase': 'minuscule',
            'uppercase': 'majuscule',
            'emailInvalid': 'Email invalide'
        },
        'zh': {
            'info': '信息',
            'ok': '确定',
            'apply': '应用',
            'clear': '清除',
            'copy': '复制',
            'copied': '已复制！',
            'download': '下载',
            'upload': '上传',
            'select': '选择',
            'print': '打印',
            'and': '和',
            'disable': '禁用',
            'enable': '启用',
            'close': '关闭',
            'back': '返回',
            'next': '下一个',
            'previous': '上一个',
            'confirm': '确认',
            'length': '长度',
            'special': '特殊',
            'lowercase': '小写',
            'uppercase': '大写',
            'emailInvalid': '无效邮箱'
        }
    }
    return translations

def add_missing_keys_to_language(lang):
    """Add missing common keys to a language file"""
    print(f"  Processing {lang.upper()}:")
    
    try:
        # Read the current .ts file
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Get the English common keys
        en_common = extract_common_section_from_english()
        if not en_common:
            return False
        
        # Get translations for this language
        lang_translations = get_language_translations()
        if lang not in lang_translations:
            print(f"     [ERROR] No translations available for {lang}")
            return False
        
        # Find the common section in the current language file
        common_match = re.search(r'"common":\s*{([^}]+(?:{[^}]*}[^}]*)*)}', content, re.DOTALL)
        if not common_match:
            print(f"     [ERROR] Could not find common section")
            return False
        
        current_common = common_match.group(1)
        
        # Extract existing keys
        existing_keys = set()
        lines = current_common.split('\n')
        for line in lines:
            line = line.strip()
            if line and '"' in line and '":' in line and not ':{' in line:
                key_match = re.search(r'"([^"]+)":', line)
                if key_match:
                    existing_keys.add(key_match.group(1))
        
        # Find missing keys
        missing_keys = []
        for key in en_common:
            if key not in existing_keys:
                if key in lang_translations[lang]:
                    missing_keys.append((key, lang_translations[lang][key]))
                else:
                    # Use English as fallback
                    missing_keys.append((key, en_common[key]))
        
        if not missing_keys:
            print(f"     [SKIP] No missing keys")
            return True
        
        # Add missing keys to the common section
        new_lines = []
        lines = current_common.split('\n')
        
        for line in lines:
            new_lines.append(line)
            # Add missing keys before the last line (which should be closing)
            if line.strip() and not line.strip().startswith('}'):
                # Ensure comma at end if not present
                if not line.strip().endswith(',') and '"' in line and '":' in line:
                    new_lines[-1] = line.rstrip() + ','
        
        # Add missing keys before the last line
        for key, value in missing_keys:
            new_lines.insert(-1, f'    "{key}": "{value}",')
        
        # Remove trailing comma from last key
        for i in range(len(new_lines) - 2, -1, -1):
            if new_lines[i].strip() and '"' in new_lines[i] and '":' in new_lines[i]:
                new_lines[i] = new_lines[i].rstrip().rstrip(',')
                break
        
        new_common = '\n'.join(new_lines)
        
        # Replace the common section in the file
        new_content = re.sub(
            r'"common":\s*{([^}]+(?:{[^}]*}[^}]*)*)}',
            f'"common": {{{new_common}}}',
            content,
            flags=re.DOTALL
        )
        
        # Write back to file
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"     [SUCCESS] Added {len(missing_keys)} missing keys")
        return True
        
    except Exception as e:
        print(f"     [ERROR] {str(e)}")
        return False

def run_sync():
    """Run the sync process"""
    print("SYNCING MISSING TRANSLATION KEYS")
    print("=" * 60)
    
    languages = ['es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    success_count = 0
    failed_count = 0
    
    for lang in languages:
        try:
            if add_missing_keys_to_language(lang):
                success_count += 1
            else:
                failed_count += 1
        except Exception as e:
            print(f"  [ERROR] Unexpected error processing {lang}: {str(e)}")
            failed_count += 1
    
    print("\n" + "=" * 60)
    print("SYNC SUMMARY")
    print("=" * 60)
    print(f"Languages processed: {len(languages)}")
    print(f"Successful: {success_count}")
    print(f"Failed: {failed_count}")
    
    if failed_count == 0:
        print("\n✅ ALL MISSING KEYS SYNCED SUCCESSFULLY!")
        print("✅ Translation validation issues should be resolved")
        print("\n🎯 NEXT STEPS:")
        print("1. Restart the frontend development server")
        print("2. Check browser console - validation warnings should be gone")
        print("3. All translation percentages should be 100%")
        return True
    else:
        print(f"\n❌ {failed_count} language file(s) failed to sync")
        return False

if __name__ == "__main__":
    success = run_sync()
    sys.exit(0 if success else 1)
