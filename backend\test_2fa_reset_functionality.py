#!/usr/bin/env python3
"""
Test script to verify the 2FA reset functionality in admin panel
"""
import requests
import json
import sys

# Configuration
BASE_URL = "http://127.0.0.1:5000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "12345"

def get_admin_token():
    """Get admin authentication token"""
    try:
        response = requests.post(f"{BASE_URL}/api/admin/login", json={
            "username": ADMIN_USERNAME,
            "password": ADMIN_PASSWORD
        })
        
        if response.status_code == 200:
            data = response.json()
            return data.get('access_token')
        else:
            print(f"❌ Admin login failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error during admin login: {e}")
        return None

def test_2fa_reset_endpoint(token):
    """Test the new 2FA reset endpoint"""
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # First, get list of users to find a test user
    print("📋 Getting user list...")
    response = requests.get(f"{BASE_URL}/api/admin/users", headers=headers)
    
    if response.status_code != 200:
        print(f"❌ Failed to get users: {response.status_code}")
        print(f"Response: {response.text}")
        return False
    
    users_data = response.json()
    users = users_data.get('users', [])
    
    if not users:
        print("❌ No users found to test with")
        return False
    
    # Find a user to test with (not the admin)
    test_user = None
    for user in users:
        if user.get('email') != f"{ADMIN_USERNAME}@deeptrade.com":
            test_user = user
            break
    
    if not test_user:
        print("❌ No suitable test user found")
        return False
    
    user_id = test_user['id']
    user_email = test_user['email']
    
    print(f"🧪 Testing 2FA reset for user: {user_email}")
    
    # Get user profile to check current 2FA status
    print("🔍 Checking current user profile...")
    profile_response = requests.get(
        f"{BASE_URL}/api/admin/users/{user_id}/profile",
        headers=headers
    )
    
    if profile_response.status_code != 200:
        print(f"❌ Failed to get user profile: {profile_response.status_code}")
        return False
    
    profile_data = profile_response.json()
    current_2fa_status = profile_data['user_info'].get('two_fa_enabled', False)
    
    print(f"   Current 2FA status: {'Enabled' if current_2fa_status else 'Disabled'}")
    
    # Test the 2FA reset endpoint
    print("🔄 Testing 2FA reset endpoint...")
    response = requests.post(
        f"{BASE_URL}/api/admin/users/{user_id}/reset-2fa",
        headers=headers,
        json={"reason": "Testing 2FA reset functionality"}
    )
    
    if current_2fa_status:
        # User has 2FA enabled, reset should work
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 2FA reset successful!")
            print(f"   Message: {result.get('message')}")
            
            # Verify the change by getting user profile again
            print("🔍 Verifying 2FA was reset...")
            profile_response = requests.get(
                f"{BASE_URL}/api/admin/users/{user_id}/profile",
                headers=headers
            )
            
            if profile_response.status_code == 200:
                profile_data = profile_response.json()
                new_2fa_status = profile_data['user_info'].get('two_fa_enabled', False)
                
                if not new_2fa_status:
                    print(f"✅ 2FA successfully disabled for user")
                else:
                    print(f"❌ 2FA still enabled after reset")
                    return False
            else:
                print(f"❌ Failed to verify 2FA reset: {profile_response.status_code}")
                return False
            
            return True
        else:
            print(f"❌ 2FA reset failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    else:
        # User doesn't have 2FA enabled, should get error
        if response.status_code == 400:
            result = response.json()
            if "does not have 2FA enabled" in result.get('message', ''):
                print(f"✅ Correctly handled user without 2FA")
                print(f"   Message: {result.get('message')}")
                return True
            else:
                print(f"❌ Unexpected error message: {result.get('message')}")
                return False
        else:
            print(f"❌ Expected 400 error for user without 2FA, got: {response.status_code}")
            return False

def test_admin_action_logging(token):
    """Test that admin actions are properly logged"""
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    print("📊 Checking admin action logs...")
    response = requests.get(f"{BASE_URL}/api/admin/actions", headers=headers)
    
    if response.status_code == 200:
        actions = response.json().get('actions', [])
        
        # Look for recent 2FA reset actions
        recent_2fa_actions = [
            action for action in actions 
            if action.get('action_type') == 'user_2fa_reset'
        ]
        
        if recent_2fa_actions:
            print(f"✅ Found {len(recent_2fa_actions)} 2FA reset action(s) in logs")
            latest_action = recent_2fa_actions[0]
            print(f"   Latest action: {latest_action.get('description')}")
        else:
            print("ℹ️  No 2FA reset actions found in recent logs")
        
        return True
    else:
        print(f"❌ Failed to get admin actions: {response.status_code}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing 2FA Reset Functionality")
    print("=" * 50)
    
    # Get admin token
    print("🔐 Getting admin authentication token...")
    token = get_admin_token()
    
    if not token:
        print("❌ Failed to get admin token")
        sys.exit(1)
    
    print("✅ Admin token obtained")
    
    # Test the 2FA reset endpoint
    success = test_2fa_reset_endpoint(token)
    
    if success:
        # Test admin action logging
        test_admin_action_logging(token)
        
        print("\n🎉 All tests passed!")
        print("✅ 2FA reset endpoint is working correctly")
        print("✅ Admin actions are being logged properly")
        print("✅ User profile shows 2FA status correctly")
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
