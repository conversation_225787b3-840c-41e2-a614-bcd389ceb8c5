#!/usr/bin/env python3

import requests
import json
import traceback

BASE_URL = 'http://127.0.0.1:5000'

def test_user_profile_endpoint():
    """Test the user profile endpoint to verify exchange connections are included."""

    print("🔍 Testing User Profile Endpoint with Exchange Connections")
    print("=" * 60)

    try:
        # Step 1: Admin login
        print("\n1️⃣ Attempting admin login...")
        login_response = requests.post(f'{BASE_URL}/api/admin/login',
            json={'username': '<EMAIL>', 'password': 'admin123'})

        print(f"   Login Status: {login_response.status_code}")

        if login_response.status_code != 200:
            print(f"   ❌ Admin login failed: {login_response.text}")
            return False

        token = login_response.json()['access_token']
        print(f"   ✅ Admin login successful")

        # Step 2: Test user profile endpoint
        print("\n2️⃣ Testing user profile endpoint...")
        user_id = 'ba44c046-6d57-403e-b69a-a646ca37542c'  # User from 2FA request

        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f'{BASE_URL}/api/admin/users/{user_id}/profile',
                               headers=headers)

        print(f"   Status Code: {response.status_code}")

        if response.status_code == 200:
            print("   ✅ User profile endpoint working!")
            data = response.json()
            print(f"   Response keys: {list(data.keys())}")

            # Check if api_credentials is included
            if 'api_credentials' in data:
                print(f"   ✅ API credentials found: {len(data['api_credentials'])} connections")
                for cred in data['api_credentials']:
                    print(f"      - {cred['exchange']}: {'Active' if cred['is_active'] else 'Inactive'}")
            else:
                print("   ⚠️ API credentials not found in response")

            return True
        else:
            print(f"   ❌ User profile endpoint failed!")
            print(f"   Response Text: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Test failed with exception: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_2fa_view_endpoint():
    """Test the specific 2FA reset request view endpoint that's failing."""

    print("🔍 Testing 2FA Reset Request View Endpoint")
    print("=" * 50)
    
    try:
        # Step 1: Admin login
        print("\n1️⃣ Attempting admin login...")
        login_response = requests.post(f'{BASE_URL}/api/admin/login',
            json={'username': '<EMAIL>', 'password': 'admin123'})
        
        print(f"   Login Status: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"   ❌ Admin login failed: {login_response.text}")
            return False
            
        token = login_response.json()['access_token']
        print(f"   ✅ Admin login successful")
        
        # Step 2: Test the failing endpoint
        print("\n2️⃣ Testing 2FA reset request view endpoint...")
        request_id = '0365c08e-0773-4591-baf9-3f05ff2ddcf1'  # The failing ID from browser
        
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f'{BASE_URL}/api/admin/2fa-reset-requests/{request_id}', 
                               headers=headers)
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("   ✅ Endpoint working!")
            data = response.json()
            print(f"   Response keys: {list(data.keys())}")
            return True
        else:
            print(f"   ❌ Endpoint failed!")
            print(f"   Response Text: {response.text}")
            
            # Try to get more details about the error
            try:
                error_data = response.json()
                print(f"   Error JSON: {json.dumps(error_data, indent=2)}")
            except:
                print("   (Response is not valid JSON)")
            
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_database_record():
    """Check if the 2FA reset request record exists in the database."""
    
    print("\n🗄️ Checking Database Record")
    print("=" * 30)
    
    try:
        from app import create_app
        from app.models.password_reset import TwoFAResetRequest
        
        app = create_app()
        with app.app_context():
            request_id = '0365c08e-0773-4591-baf9-3f05ff2ddcf1'
            reset_request = TwoFAResetRequest.query.get(request_id)
            
            if reset_request:
                print(f"   ✅ Record found!")
                print(f"   ID: {reset_request.id}")
                print(f"   User ID: {reset_request.user_id}")
                print(f"   Email: {reset_request.email_provided}")
                print(f"   Status: {reset_request.status}")
                print(f"   Created: {reset_request.created_at}")
                
                # Check if user exists
                user = reset_request.user
                if user:
                    print(f"   User exists: {user.email}")
                else:
                    print(f"   ⚠️ User not found for user_id: {reset_request.user_id}")
                
                return True
            else:
                print(f"   ❌ Record not found for ID: {request_id}")
                return False
                
    except Exception as e:
        print(f"❌ Database check failed: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    print("🧪 Admin Dashboard Enhancement Test")
    print("=" * 40)

    # Test database record first
    db_ok = test_database_record()

    # Test the 2FA endpoint
    endpoint_ok = test_2fa_view_endpoint()

    # Test the user profile endpoint with exchange connections
    profile_ok = test_user_profile_endpoint()

    print("\n📊 Test Results:")
    print(f"   Database Record: {'✅ OK' if db_ok else '❌ FAIL'}")
    print(f"   2FA View Endpoint: {'✅ OK' if endpoint_ok else '❌ FAIL'}")
    print(f"   User Profile Endpoint: {'✅ OK' if profile_ok else '❌ FAIL'}")

    if not endpoint_ok:
        print("\n💡 Next steps for 2FA endpoint:")
        print("   1. Check Flask backend logs for detailed error")
        print("   2. Verify admin authentication is working")
        print("   3. Check if the endpoint route is properly registered")
        print("   4. Verify database relationships are intact")

    if profile_ok:
        print("\n🎉 Exchange connections should now be visible in admin user profiles!")
