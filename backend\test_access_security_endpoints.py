#!/usr/bin/env python3
"""
Test script for Access Security endpoints
Tests the functionality of the security endpoints that the frontend is calling.
"""

import sys
import os
import requests
import json
from datetime import datetime, timed<PERSON><PERSON>

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_endpoints():
    """Test the access security endpoints"""
    print("🧪 TESTING ACCESS SECURITY ENDPOINTS...")
    print("="*50)
    
    base_url = "http://localhost:5000"
    
    # Test endpoints without authentication first
    endpoints = [
        "/api/users/security/access-logs?limit=5",
        "/api/users/security/stats", 
        "/api/users/security/current-session"
    ]
    
    print("1. Testing endpoint availability (without auth):")
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            print(f"   {endpoint}: Status {response.status_code}")
            if response.status_code == 401:
                print(f"     ✅ Correctly requires authentication")
            elif response.status_code == 404:
                print(f"     ❌ Endpoint not found")
            else:
                print(f"     ⚠️  Unexpected status: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"   {endpoint}: ❌ Connection failed - Backend not running?")
        except Exception as e:
            print(f"   {endpoint}: ❌ Error: {e}")
    
    print("\n2. Testing with mock authentication:")
    # This would require a valid JWT token, which we don't have in this simple test
    print("   ⚠️  Skipping authenticated tests - would need valid JWT token")
    
    print("\n3. Backend endpoint structure check:")
    try:
        # Check if the backend files exist and have the right structure
        from app.api.user_routes import user_bp
        print("   ✅ user_routes.py imported successfully")
        
        # Check if the routes are registered
        routes = [rule.rule for rule in user_bp.url_map.iter_rules()]
        security_routes = [r for r in routes if 'security' in r]
        print(f"   Security routes found: {security_routes}")
        
    except Exception as e:
        print(f"   ❌ Error checking backend structure: {e}")

if __name__ == "__main__":
    test_endpoints()
