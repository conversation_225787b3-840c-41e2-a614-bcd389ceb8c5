#!/usr/bin/env python3
"""
Test script to verify AccessSecurity data display fixes
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_backend_endpoints():
    """Test backend endpoint import error handling"""
    print("Testing Backend Endpoint Import Handling...")
    
    try:
        with open('app/api/user_routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for access logs endpoint with error handling
        print("  1. Testing access logs endpoint:")
        
        access_logs_patterns = [
            "@user_bp.route('/security/access-logs', methods=['GET'])",
            "@jwt_required()",
            "def get_user_access_logs():",
            "try:",
            "from app.models.ip_tracking import IPAccessLog",
            "except ImportError:",
            "logs_data = []",
            "total_logs = 0"
        ]
        
        for pattern in access_logs_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        # Test 2: Check for security stats endpoint with error handling
        print("  2. Testing security stats endpoint:")
        
        stats_patterns = [
            "@user_bp.route('/security/stats', methods=['GET'])",
            "def get_user_security_stats():",
            "total_logins = 0",
            "successful_logins = 0",
            "failed_logins = 0",
            "unique_ips = 0",
            "last_login = None"
        ]
        
        for pattern in stats_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        # Test 3: Check for current session endpoint with error handling
        print("  3. Testing current session endpoint:")
        
        session_patterns = [
            "@user_bp.route('/security/current-session', methods=['GET'])",
            "def get_current_session():",
            "recent_login = None"
        ]
        
        for pattern in session_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_frontend_translations():
    """Test frontend translation implementation"""
    print("Testing Frontend Translation Implementation...")
    
    try:
        with open('../frontend/src/pages/AccessSecurity.tsx', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for translation usage
        print("  1. Testing translation usage:")
        
        translation_patterns = [
            "t('accessSecurity.title')",
            "t('accessSecurity.subtitle')",
            "t('accessSecurity.refresh')",
            "t('accessSecurity.totalLogins')",
            "t('accessSecurity.successRate')",
            "t('accessSecurity.uniqueIPs')",
            "t('accessSecurity.twoFactorStatus')",
            "t('accessSecurity.enabled')",
            "t('accessSecurity.disabled')",
            "t('accessSecurity.currentSession')",
            "t('accessSecurity.recentLoginActivity')",
            "t('accessSecurity.successful')",
            "t('accessSecurity.failed')"
        ]
        
        for pattern in translation_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_translation_files():
    """Test translation files for all languages"""
    print("Testing Translation Files...")
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    for lang in languages:
        print(f"  Testing {lang.upper()} translations:")
        
        try:
            with open(f'../frontend/src/i18n/locales/{lang}/common.json', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for accessSecurity section
            if '"accessSecurity"' in content:
                print(f"     [PASS] accessSecurity section found")
                
                # Check for key translations
                key_translations = [
                    '"title"',
                    '"subtitle"',
                    '"totalLogins"',
                    '"successRate"',
                    '"uniqueIPs"',
                    '"twoFactorStatus"',
                    '"enabled"',
                    '"disabled"',
                    '"currentSession"',
                    '"recentLoginActivity"',
                    '"successful"',
                    '"failed"'
                ]
                
                missing_keys = []
                for key in key_translations:
                    if key not in content:
                        missing_keys.append(key)
                
                if missing_keys:
                    print(f"     [FAIL] Missing keys: {', '.join(missing_keys)}")
                    return False
                else:
                    print(f"     [PASS] All required keys found")
            else:
                print(f"     [FAIL] accessSecurity section missing")
                return False
                
        except Exception as e:
            print(f"     [ERROR] Error reading {lang} file: {str(e)}")
            return False
    
    return True

def test_data_fetching_logic():
    """Test data fetching logic in frontend"""
    print("Testing Data Fetching Logic...")
    
    try:
        with open('../frontend/src/pages/AccessSecurity.tsx', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for proper API endpoints
        print("  1. Testing API endpoints:")
        
        api_patterns = [
            "'/api/users/security/access-logs?limit=5'",
            "'/api/users/security/stats'",
            "'/api/users/security/current-session'"
        ]
        
        for pattern in api_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        # Test 2: Check for error handling
        print("  2. Testing error handling:")
        
        error_patterns = [
            "catch (error)",
            "console.error",
            "finally {",
            "setLoading(false)",
            "setRefreshing(false)"
        ]
        
        for pattern in error_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        # Test 3: Check for state management
        print("  3. Testing state management:")
        
        state_patterns = [
            "setAccessLogs(logsData.access_logs)",
            "setSecurityStats(statsData)",
            "setCurrentSession(sessionData)"
        ]
        
        for pattern in state_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_conditional_rendering():
    """Test conditional rendering logic"""
    print("Testing Conditional Rendering...")
    
    try:
        with open('../frontend/src/pages/AccessSecurity.tsx', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for loading states
        print("  1. Testing loading states:")
        
        loading_patterns = [
            "refreshing ?",
            "accessLogs.length === 0",
            "setLoading"
        ]
        
        for pattern in loading_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        # Test 2: Check for data display conditions
        print("  2. Testing data display conditions:")
        
        display_patterns = [
            "securityStats &&",
            "currentSession &&",
            "t('accessSecurity.noLoginActivity')"
        ]
        
        for pattern in display_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def run_all_tests():
    """Run all AccessSecurity fix tests"""
    print("ACCESS SECURITY DATA DISPLAY FIX TEST")
    print("=" * 60)
    
    tests = [
        ("Backend Endpoints", test_backend_endpoints),
        ("Frontend Translations", test_frontend_translations),
        ("Translation Files", test_translation_files),
        ("Data Fetching Logic", test_data_fetching_logic),
        ("Conditional Rendering", test_conditional_rendering)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"  [PASS] {test_name} completed successfully")
            else:
                failed += 1
                print(f"  [FAIL] {test_name} failed")
        except Exception as e:
            failed += 1
            print(f"  [ERROR] {test_name} error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("ACCESS SECURITY FIX TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {len(tests)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed/len(tests)*100):.1f}%")
    
    if failed == 0:
        print("\nALL TESTS PASSED! AccessSecurity fixes implemented successfully.")
        print("\nFix Details:")
        print("- Added import error handling for IPAccessLog model")
        print("- Fixed backend endpoints to handle missing IP tracking gracefully")
        print("- Added comprehensive translations for all supported languages")
        print("- Updated frontend to use translation keys instead of hardcoded text")
        print("- Maintained proper error handling and loading states")
        print("- Fixed sidebar title and all UI text to be translatable")
        return True
    else:
        print(f"\n{failed} test(s) failed. Please review implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
