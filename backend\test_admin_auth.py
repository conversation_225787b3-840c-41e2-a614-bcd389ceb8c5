#!/usr/bin/env python3
"""
Admin Authentication Test Script
Tests admin login and token verification to diagnose 401 issues
"""

import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5000"
ADMIN_CREDENTIALS = {
    "super_admin": {"username": "admin", "password": "admin123"},
    "limited_admin": {"username": "limited_admin", "password": "limited123"}
}

def test_admin_login(admin_type):
    """Test admin login and return token"""
    print(f"\n{'='*50}")
    print(f"TESTING {admin_type.upper()} LOGIN")
    print(f"{'='*50}")
    
    credentials = ADMIN_CREDENTIALS[admin_type]
    login_url = f"{BASE_URL}/api/admin/login"
    
    print(f"1. Attempting login with: {credentials['username']}")
    print(f"   URL: {login_url}")
    
    try:
        response = requests.post(login_url, json=credentials, timeout=10)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            admin_info = data.get('admin', {})
            
            print(f"   ✅ LOGIN SUCCESSFUL")
            print(f"   Admin ID: {admin_info.get('id')}")
            print(f"   Username: {admin_info.get('username')}")
            print(f"   Is Super Admin: {admin_info.get('is_super_admin')}")
            print(f"   Token Length: {len(token) if token else 0}")
            print(f"   Token Preview: {token[:50]}..." if token else "   No token received")
            
            return token
        else:
            print(f"   ❌ LOGIN FAILED")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ LOGIN ERROR: {str(e)}")
        return None

def test_token_verification(token, admin_type):
    """Test token verification endpoint"""
    print(f"\n2. Testing token verification...")
    
    if not token:
        print("   ❌ No token to verify")
        return False
    
    verify_url = f"{BASE_URL}/api/admin/verify-token"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"   URL: {verify_url}")
    print(f"   Authorization Header: Bearer {token[:30]}...")
    
    try:
        response = requests.get(verify_url, headers=headers, timeout=10)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ TOKEN VERIFICATION SUCCESSFUL")
            print(f"   Valid: {data.get('valid')}")
            admin_info = data.get('admin', {})
            print(f"   Admin ID: {admin_info.get('id')}")
            print(f"   Username: {admin_info.get('username')}")
            print(f"   Is Super Admin: {admin_info.get('is_super_admin')}")
            return True
        else:
            print(f"   ❌ TOKEN VERIFICATION FAILED")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ TOKEN VERIFICATION ERROR: {str(e)}")
        return False

def test_ip_management_access(token, admin_type):
    """Test IP management endpoints"""
    print(f"\n3. Testing IP management access...")
    
    if not token:
        print("   ❌ No token to test")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test endpoints that should work for both admin types
    test_endpoints = [
        "/api/admin/ip/stats?days=30",
        "/api/admin/ip/access-logs?page=1&per_page=10",
        "/api/admin/ip/blacklist"
    ]
    
    for endpoint in test_endpoints:
        url = f"{BASE_URL}{endpoint}"
        print(f"   Testing: {endpoint}")
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                print(f"     ✅ SUCCESS (200)")
            elif response.status_code == 401:
                print(f"     ❌ UNAUTHORIZED (401)")
            elif response.status_code == 403:
                print(f"     ⚠️  FORBIDDEN (403) - Expected for limited admin on some endpoints")
            else:
                print(f"     ❓ UNEXPECTED ({response.status_code})")
                
        except Exception as e:
            print(f"     ❌ ERROR: {str(e)}")

def decode_jwt_payload(token):
    """Decode JWT payload for inspection"""
    print(f"\n4. Analyzing JWT token...")
    
    if not token:
        print("   ❌ No token to analyze")
        return
    
    try:
        import base64
        
        # Split token into parts
        parts = token.split('.')
        if len(parts) != 3:
            print(f"   ❌ Invalid JWT format - expected 3 parts, got {len(parts)}")
            return
        
        # Decode payload (second part)
        payload_b64 = parts[1]
        # Add padding if needed
        payload_b64 += '=' * (4 - len(payload_b64) % 4)
        
        payload_bytes = base64.urlsafe_b64decode(payload_b64)
        payload = json.loads(payload_bytes.decode('utf-8'))
        
        print(f"   ✅ JWT PAYLOAD DECODED:")
        print(f"   Identity: {payload.get('sub')}")
        print(f"   Is Admin: {payload.get('is_admin')}")
        print(f"   Is Super Admin: {payload.get('is_super_admin')}")
        print(f"   Admin ID: {payload.get('admin_id')}")
        print(f"   Admin Username: {payload.get('admin_username')}")
        print(f"   Issued At: {datetime.fromtimestamp(payload.get('iat', 0))}")
        print(f"   Expires At: {datetime.fromtimestamp(payload.get('exp', 0))}")
        
        # Check if token is expired
        exp = payload.get('exp', 0)
        now = datetime.utcnow().timestamp()
        if exp < now:
            print(f"   ❌ TOKEN IS EXPIRED!")
        else:
            print(f"   ✅ Token is valid for {int((exp - now) / 60)} more minutes")
            
    except Exception as e:
        print(f"   ❌ JWT DECODE ERROR: {str(e)}")

def main():
    """Run all admin authentication tests"""
    print("🔍 ADMIN AUTHENTICATION DIAGNOSTIC SCRIPT")
    print(f"Testing against: {BASE_URL}")
    print(f"Timestamp: {datetime.now()}")
    
    # Test both admin types
    for admin_type in ["super_admin", "limited_admin"]:
        token = test_admin_login(admin_type)
        
        if token:
            decode_jwt_payload(token)
            test_token_verification(token, admin_type)
            test_ip_management_access(token, admin_type)
        
        print(f"\n{'-'*50}")
    
    print("\n🎯 DIAGNOSTIC COMPLETE")
    print("\nIf you see 401 errors above, the issue is in:")
    print("1. JWT token generation/format")
    print("2. Admin authentication decorators")
    print("3. Database admin user setup")

if __name__ == "__main__":
    main()
