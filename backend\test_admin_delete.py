#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.admin import AdminUser, AdminAction, CouponCode
from app.models.ip_tracking import IPAccessLog, IPBan

def test_admin_delete():
    """Test admin deletion to identify foreign key issues"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔍 TESTING ADMIN DELETION...")
            print("="*50)
            
            # Find an admin to test with (not the current one)
            admins = AdminUser.query.all()
            print(f"Found {len(admins)} admins:")
            for admin in admins:
                print(f"  - ID: {admin.id}, Username: {admin.username}, Super: {admin.is_super_admin}")
            
            if len(admins) < 2:
                print("❌ Need at least 2 admins to test deletion")
                return False
            
            # Use the last admin for testing
            test_admin = admins[-1]
            current_admin = admins[0]  # Use first admin as "current"
            
            print(f"\n🎯 Testing deletion of admin: {test_admin.username} (ID: {test_admin.id})")
            print(f"Current admin: {current_admin.username} (ID: {current_admin.id})")
            
            # Check foreign key references
            print(f"\n🔍 Checking foreign key references for admin {test_admin.id}:")
            
            # AdminAction references
            admin_actions = AdminAction.query.filter_by(admin_id=test_admin.id).count()
            print(f"  - AdminActions: {admin_actions}")
            
            # CouponCode references
            coupon_codes = CouponCode.query.filter_by(created_by=test_admin.id).count()
            print(f"  - CouponCodes: {coupon_codes}")
            
            # AdminUser self-references
            created_admins = AdminUser.query.filter_by(created_by=test_admin.id).count()
            print(f"  - Created Admins: {created_admins}")
            
            # IP tracking references
            try:
                ip_access_logs = IPAccessLog.query.filter_by(admin_id=test_admin.id).count()
                print(f"  - IPAccessLogs: {ip_access_logs}")
            except Exception as e:
                print(f"  - IPAccessLogs: Error - {e}")
            
            try:
                ip_bans = IPBan.query.filter_by(banned_by_admin_id=test_admin.id).count()
                print(f"  - IPBans: {ip_bans}")
            except Exception as e:
                print(f"  - IPBans: Error - {e}")
            
            print(f"\n🧹 Cleaning up foreign key references...")
            
            # Clean up foreign keys
            AdminAction.query.filter_by(admin_id=test_admin.id).update({'admin_id': None})
            CouponCode.query.filter_by(created_by=test_admin.id).update({'created_by': current_admin.id})
            AdminUser.query.filter_by(created_by=test_admin.id).update({'created_by': current_admin.id})
            
            try:
                IPAccessLog.query.filter_by(admin_id=test_admin.id).update({'admin_id': None})
            except Exception as e:
                print(f"  - IPAccessLog cleanup error: {e}")
            
            try:
                IPBan.query.filter_by(banned_by_admin_id=test_admin.id).update({'banned_by_admin_id': current_admin.id})
            except Exception as e:
                print(f"  - IPBan cleanup error: {e}")
            
            print("✅ Foreign key cleanup completed")
            
            # Try to delete the admin
            print(f"\n🗑️ Attempting to delete admin {test_admin.username}...")
            db.session.delete(test_admin)
            db.session.commit()
            
            print("✅ Admin deleted successfully!")
            
            # Verify deletion
            deleted_admin = AdminUser.query.get(test_admin.id)
            if deleted_admin is None:
                print("✅ Verification: Admin completely removed from database")
                return True
            else:
                print("❌ Verification: Admin still exists in database")
                return False
                
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error during admin deletion test: {str(e)}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            return False

if __name__ == "__main__":
    success = test_admin_delete()
    if success:
        print("\n🎉 Admin deletion test PASSED")
    else:
        print("\n💥 Admin deletion test FAILED")
