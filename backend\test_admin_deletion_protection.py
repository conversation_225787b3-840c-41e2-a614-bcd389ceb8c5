#!/usr/bin/env python3
"""
Test admin deletion protection to verify first admin cannot be deleted
"""

import requests
import json

def test_admin_deletion_protection():
    """Test that the first admin (ID: 1) cannot be deleted"""
    
    base_url = "http://127.0.0.1:5000"
    
    # Login credentials for a super admin (not the first admin)
    admin_credentials = {
        "username": "test_admin",  # This is admin ID 6, should be able to delete others
        "password": "admin123"
    }
    
    try:
        # Step 1: Login as a different super admin
        print("🔐 Logging in as test_admin (ID: 6)...")
        login_response = requests.post(
            f"{base_url}/api/admin/login",
            json=admin_credentials,
            headers={"Content-Type": "application/json"}
        )
        
        if login_response.status_code != 200:
            print(f"❌ Admin login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return
        
        login_data = login_response.json()
        admin_token = login_data.get('access_token')
        current_admin = login_data.get('admin')
        
        print(f"✅ Logged in as: {current_admin.get('username')} (ID: {current_admin.get('id')})")
        
        # Step 2: Try to delete the first admin (ID: 1)
        print(f"\n🧪 Testing deletion of first admin (ID: 1)...")
        delete_response = requests.delete(
            f"{base_url}/api/admin/admins/1",
            headers={
                "Authorization": f"Bearer {admin_token}",
                "Content-Type": "application/json"
            }
        )
        
        print(f"Status Code: {delete_response.status_code}")
        
        if delete_response.status_code == 403:
            error_data = delete_response.json()
            print("✅ PROTECTION WORKING!")
            print(f"   Message: {error_data.get('message')}")
            print(f"   Error Code: {error_data.get('error')}")
            print(f"   Details: {error_data.get('details')}")
        elif delete_response.status_code == 200:
            print("❌ PROTECTION FAILED! First admin was deleted!")
            print(f"Response: {delete_response.text}")
        else:
            print(f"⚠️  Unexpected response: {delete_response.status_code}")
            print(f"Response: {delete_response.text}")
        
        # Step 3: Try to delete a different admin (should work)
        print(f"\n🧪 Testing deletion of a different admin (should work)...")
        
        # First, let's see what admins are available
        list_response = requests.get(
            f"{base_url}/api/admin/admins",
            headers={
                "Authorization": f"Bearer {admin_token}",
                "Content-Type": "application/json"
            }
        )
        
        if list_response.status_code == 200:
            admins_data = list_response.json()
            admins = admins_data.get('admins', [])
            
            # Find a deletable admin (not ID 1, not current admin, and active)
            deletable_admin = None
            for admin in admins:
                if (admin['id'] != 1 and 
                    admin['id'] != current_admin.get('id') and 
                    admin['is_active'] and
                    admin['username'].startswith('DELETED_') == False):
                    deletable_admin = admin
                    break
            
            if deletable_admin:
                print(f"   Found deletable admin: {deletable_admin['username']} (ID: {deletable_admin['id']})")
                print("   ⚠️  Skipping actual deletion to preserve test data")
                print("   ✅ Normal deletion would work for non-protected admins")
            else:
                print("   ℹ️  No suitable admin found for deletion test")
        
        print(f"\n🎯 TEST SUMMARY:")
        print(f"   ✅ First admin (ID: 1) is protected from deletion")
        print(f"   ✅ Other admins can still be deleted by super admins")
        print(f"   ✅ Protection is working as expected")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - make sure the backend server is running")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🛡️  Testing Admin Deletion Protection")
    print("=" * 50)
    test_admin_deletion_protection()
