#!/usr/bin/env python3
"""
Test script to simulate the exact admin deletion route logic
This will identify any issues in the admin route before web testing
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_admin_deletion_logic():
    """Test the exact logic used in admin deletion route"""
    print("🧪 Testing admin deletion route logic...")
    
    try:
        from app import create_app, db
        from flask import current_app
        
        app = create_app()
        
        with app.app_context():
            # Test user ID (use the test user we created)
            user_id = "ac25356a-a33e-4dbf-a04f-beb72e463f00"
            
            print(f"🔍 Testing deletion logic for user: {user_id}")
            
            # Import all models that have foreign keys to users (with error handling)
            try:
                from app.models.security_log import LoginAttempt, SecurityLog, APICredential
                from app.models.subscription import Subscription
                from app.models.payment import Payment
                from app.models.trade import Trade, TradingSession
                from app.models.fee_calculation import FeeCalculation
                from app.models.user import User2FABackupCode, User2FAEmailCode
                from app.models.admin import CouponUsage
                from app.models.referral import Referral, ReferralEarning, ReferrerProfile
                from app.models.solana_payment import SolanaPayment, MembershipBilling
                print("✅ All model imports successful in admin context")
            except ImportError as e:
                print(f"❌ Import error in comprehensive deletion: {e}")
                return False
            
            # Test the exact deletion steps from admin route
            print("\n📊 Checking data counts...")
            
            # 1. Delete 2FA related data
            backup_codes_count = User2FABackupCode.query.filter_by(user_id=user_id).count()
            print(f"   🔒 2FA backup codes: {backup_codes_count}")
            
            email_codes_count = User2FAEmailCode.query.filter_by(user_id=user_id).count()
            print(f"   📧 2FA email codes: {email_codes_count}")

            # 2. Delete security and login data
            login_attempts_count = LoginAttempt.query.filter_by(user_id=user_id).count()
            print(f"   🔐 Login attempts: {login_attempts_count}")

            security_logs_count = SecurityLog.query.filter_by(user_id=user_id).count()
            print(f"   🛡️ Security logs: {security_logs_count}")

            # 3. Delete API credentials
            api_creds_count = APICredential.query.filter_by(user_id=user_id).count()
            print(f"   🔑 API credentials: {api_creds_count}")

            # 4. Delete trading data
            trades_count = Trade.query.filter_by(user_id=user_id).count()
            print(f"   📊 Trades: {trades_count}")
            
            sessions_count = TradingSession.query.filter_by(user_id=user_id).count()
            print(f"   🎯 Trading sessions: {sessions_count}")

            # 5. Delete fee calculations
            fees_count = FeeCalculation.query.filter_by(user_id=user_id).count()
            print(f"   💸 Fee calculations: {fees_count}")

            # 6. Delete coupon usage
            coupon_usage_count = CouponUsage.query.filter_by(user_id=user_id).count()
            print(f"   🎫 Coupon usage: {coupon_usage_count}")

            # 7. Delete referral data (handle complex relationships)
            user_referrals = Referral.query.filter_by(referrer_id=user_id).all()
            earnings_count = 0
            for referral in user_referrals:
                earnings = ReferralEarning.query.filter_by(referral_id=referral.id).all()
                earnings_count += len(earnings)
            print(f"   💰 Referral earnings: {earnings_count}")
            
            referrals_as_referrer = Referral.query.filter_by(referrer_id=user_id).count()
            print(f"   👥 Referrals as referrer: {referrals_as_referrer}")
            
            referrals_as_referee = Referral.query.filter_by(referee_id=user_id).count()
            print(f"   👥 Referrals as referee: {referrals_as_referee}")
            
            profile_count = ReferrerProfile.query.filter_by(user_id=user_id).count()
            print(f"   👤 Referrer profiles: {profile_count}")

            # 8. Delete Solana payment data
            solana_payments_count = SolanaPayment.query.filter_by(user_id=user_id).count()
            print(f"   ⚡ Solana payments: {solana_payments_count}")
            
            billing_count = MembershipBilling.query.filter_by(user_id=user_id).count()
            print(f"   📄 Membership billing: {billing_count}")

            # 9. Delete payments
            payments_count = Payment.query.filter_by(user_id=user_id).count()
            print(f"   💳 Payments: {payments_count}")

            # 10. Delete subscriptions
            subscriptions_count = Subscription.query.filter_by(user_id=user_id).count()
            print(f"   📋 Subscriptions: {subscriptions_count}")
            
            print("\n✅ Admin deletion logic test completed successfully!")
            print("   All model queries work correctly")
            print("   No variable scope issues detected")
            
            return True
            
    except Exception as e:
        print(f"\n❌ Admin deletion logic test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_actual_deletion():
    """Test the actual deletion process"""
    print("\n🧪 Testing actual deletion process...")
    
    try:
        from app import create_app, db
        from app.models.user import User
        
        app = create_app()
        
        with app.app_context():
            # Create a new test user for deletion
            test_user = User(
                email="<EMAIL>",
                full_name="Test Actual Deletion User",
                password="testpassword123"
            )
            
            db.session.add(test_user)
            db.session.commit()
            
            user_id = test_user.id
            print(f"✅ Created test user for deletion: {user_id}")
            
            # Now test the comprehensive deletion
            from app.models.security_log import LoginAttempt, SecurityLog, APICredential
            from app.models.subscription import Subscription
            from app.models.payment import Payment
            from app.models.trade import Trade, TradingSession
            from app.models.fee_calculation import FeeCalculation
            from app.models.user import User2FABackupCode, User2FAEmailCode
            from app.models.admin import CouponUsage
            from app.models.referral import Referral, ReferralEarning, ReferrerProfile
            from app.models.solana_payment import SolanaPayment, MembershipBilling
            from app.models.paper_trading import PaperTradingAccount, PaperTrade, PaperTradingSession, PaperBalanceSnapshot
            from app.models.user_balance_tracker import UserBalanceTracker, BalanceSnapshot
            from app.models.user_tier_status import UserTierStatus
            
            # Simulate the comprehensive deletion process
            print("   🔄 Simulating comprehensive deletion...")
            
            # Delete all foreign key references (same order as admin route)
            User2FABackupCode.query.filter_by(user_id=user_id).delete()
            User2FAEmailCode.query.filter_by(user_id=user_id).delete()
            LoginAttempt.query.filter_by(user_id=user_id).delete()
            SecurityLog.query.filter_by(user_id=user_id).delete()
            APICredential.query.filter_by(user_id=user_id).delete()
            Trade.query.filter_by(user_id=user_id).delete()
            TradingSession.query.filter_by(user_id=user_id).delete()
            FeeCalculation.query.filter_by(user_id=user_id).delete()
            CouponUsage.query.filter_by(user_id=user_id).delete()
            
            # Handle referrals
            user_referrals = Referral.query.filter_by(referrer_id=user_id).all()
            for referral in user_referrals:
                ReferralEarning.query.filter_by(referral_id=referral.id).delete()
            Referral.query.filter_by(referrer_id=user_id).delete()
            Referral.query.filter_by(referee_id=user_id).delete()
            ReferrerProfile.query.filter_by(user_id=user_id).delete()
            
            SolanaPayment.query.filter_by(user_id=user_id).delete()
            MembershipBilling.query.filter_by(user_id=user_id).delete()
            Payment.query.filter_by(user_id=user_id).delete()
            Subscription.query.filter_by(user_id=user_id).delete()
            
            # Handle paper trading
            paper_accounts = PaperTradingAccount.query.filter_by(user_id=user_id).all()
            for account in paper_accounts:
                PaperBalanceSnapshot.query.filter_by(paper_account_id=account.id).delete()
            PaperTrade.query.filter_by(user_id=user_id).delete()
            PaperTradingSession.query.filter_by(user_id=user_id).delete()
            PaperTradingAccount.query.filter_by(user_id=user_id).delete()
            
            # Handle balance tracking
            balance_trackers = UserBalanceTracker.query.filter_by(user_id=user_id).all()
            for tracker in balance_trackers:
                BalanceSnapshot.query.filter_by(tracker_id=tracker.id).delete()
            UserBalanceTracker.query.filter_by(user_id=user_id).delete()
            
            UserTierStatus.query.filter_by(user_id=user_id).delete()
            
            # Finally delete the user
            User.query.filter_by(id=user_id).delete()
            
            db.session.commit()
            
            # Verify deletion
            remaining_user = User.query.filter_by(id=user_id).first()
            if remaining_user:
                print("   ❌ User still exists after deletion!")
                return False
            else:
                print("   ✅ User successfully deleted!")
                return True
                
    except Exception as e:
        print(f"\n❌ Actual deletion test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 Starting comprehensive admin deletion tests...")
    print("=" * 60)
    
    # Test 1: Logic test
    logic_success = test_admin_deletion_logic()
    
    # Test 2: Actual deletion test
    if logic_success:
        deletion_success = test_actual_deletion()
    else:
        deletion_success = False
    
    print("\n" + "=" * 60)
    if logic_success and deletion_success:
        print("🎉 ALL ADMIN DELETION TESTS PASSED!")
        print("   The admin route should work correctly on the web.")
    else:
        print("💥 ADMIN DELETION TESTS FAILED!")
        print("   Issues need to be fixed before web testing.")
        
    print("=" * 60)
