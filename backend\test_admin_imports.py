#!/usr/bin/env python3
"""
Test script to verify all imports in admin routes work correctly
This will identify any import issues before testing on the web
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_admin_imports():
    """Test all imports used in admin routes"""
    print("🧪 Testing admin route imports...")
    
    try:
        from app import create_app, db
        print("✅ Flask app imports successful")
        
        # Test basic model imports
        from app.models.user import User, User2FABackupCode, User2FAEmailCode
        print("✅ User models imported successfully")
        
        from app.models.security_log import LoginAttempt, SecurityLog, APICredential
        print("✅ Security models imported successfully")
        
        from app.models.subscription import Subscription
        print("✅ Subscription model imported successfully")
        
        from app.models.payment import Payment
        print("✅ Payment model imported successfully")
        
        # Test the problematic Trade import
        try:
            from app.models.trade import Trade, TradingSession
            print("✅ Trade models imported successfully")
        except Exception as e:
            print(f"❌ Trade models import failed: {e}")
            return False
        
        from app.models.fee_calculation import FeeCalculation
        print("✅ Fee calculation model imported successfully")
        
        from app.models.admin import CouponUsage
        print("✅ Admin models imported successfully")
        
        from app.models.referral import Referral, ReferralEarning, ReferrerProfile
        print("✅ Referral models imported successfully")
        
        from app.models.solana_payment import SolanaPayment, MembershipBilling
        print("✅ Solana payment models imported successfully")
        
        from app.models.paper_trading import PaperTradingAccount, PaperTrade, PaperTradingSession, PaperBalanceSnapshot
        print("✅ Paper trading models imported successfully")
        
        from app.models.user_balance_tracker import UserBalanceTracker, BalanceSnapshot
        print("✅ Balance tracker models imported successfully")
        
        from app.models.user_tier_status import UserTierStatus
        print("✅ Tier status model imported successfully")
        
        print("\n🎉 ALL IMPORTS SUCCESSFUL!")
        return True
        
    except Exception as e:
        print(f"\n❌ Import test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_model_queries():
    """Test that we can query all models without errors"""
    print("\n🧪 Testing model queries...")
    
    try:
        from app import create_app, db
        from app.models.user import User, User2FABackupCode, User2FAEmailCode
        from app.models.security_log import LoginAttempt, SecurityLog, APICredential
        from app.models.subscription import Subscription
        from app.models.payment import Payment
        from app.models.trade import Trade, TradingSession
        from app.models.fee_calculation import FeeCalculation
        from app.models.admin import CouponUsage
        from app.models.referral import Referral, ReferralEarning, ReferrerProfile
        from app.models.solana_payment import SolanaPayment, MembershipBilling
        from app.models.paper_trading import PaperTradingAccount, PaperTrade, PaperTradingSession, PaperBalanceSnapshot
        from app.models.user_balance_tracker import UserBalanceTracker, BalanceSnapshot
        from app.models.user_tier_status import UserTierStatus
        
        app = create_app()
        
        with app.app_context():
            # Test basic queries on all models
            test_user_id = "test-user-id"
            
            print("   Testing User queries...")
            User.query.filter_by(id=test_user_id).count()
            
            print("   Testing 2FA queries...")
            User2FABackupCode.query.filter_by(user_id=test_user_id).count()
            User2FAEmailCode.query.filter_by(user_id=test_user_id).count()
            
            print("   Testing Security queries...")
            LoginAttempt.query.filter_by(user_id=test_user_id).count()
            SecurityLog.query.filter_by(user_id=test_user_id).count()
            APICredential.query.filter_by(user_id=test_user_id).count()
            
            print("   Testing Subscription queries...")
            Subscription.query.filter_by(user_id=test_user_id).count()
            
            print("   Testing Payment queries...")
            Payment.query.filter_by(user_id=test_user_id).count()
            
            print("   Testing Trade queries...")
            Trade.query.filter_by(user_id=test_user_id).count()
            TradingSession.query.filter_by(user_id=test_user_id).count()
            
            print("   Testing Fee calculation queries...")
            FeeCalculation.query.filter_by(user_id=test_user_id).count()
            
            print("   Testing Admin queries...")
            CouponUsage.query.filter_by(user_id=test_user_id).count()
            
            print("   Testing Referral queries...")
            Referral.query.filter_by(referrer_id=test_user_id).count()
            Referral.query.filter_by(referee_id=test_user_id).count()
            ReferrerProfile.query.filter_by(user_id=test_user_id).count()
            
            print("   Testing Solana payment queries...")
            SolanaPayment.query.filter_by(user_id=test_user_id).count()
            MembershipBilling.query.filter_by(user_id=test_user_id).count()
            
            print("   Testing Paper trading queries...")
            PaperTradingAccount.query.filter_by(user_id=test_user_id).count()
            PaperTrade.query.filter_by(user_id=test_user_id).count()
            PaperTradingSession.query.filter_by(user_id=test_user_id).count()
            
            print("   Testing Balance tracker queries...")
            UserBalanceTracker.query.filter_by(user_id=test_user_id).count()
            
            print("   Testing Tier status queries...")
            UserTierStatus.query.filter_by(user_id=test_user_id).count()
            
            print("\n✅ ALL MODEL QUERIES SUCCESSFUL!")
            return True
            
    except Exception as e:
        print(f"\n❌ Model query test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 Starting comprehensive admin import tests...")
    print("=" * 60)
    
    # Test 1: Import test
    import_success = test_admin_imports()
    
    # Test 2: Query test
    if import_success:
        query_success = test_model_queries()
    else:
        query_success = False
    
    print("\n" + "=" * 60)
    if import_success and query_success:
        print("🎉 ALL TESTS PASSED! Admin routes should work correctly.")
    else:
        print("💥 TESTS FAILED! Issues need to be fixed before web testing.")
        
    print("=" * 60)
