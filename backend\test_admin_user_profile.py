#!/usr/bin/env python3
"""
Comprehensive test script for Admin User Profile page functionality.
Tests all components shown in the user profile interface.
"""
import requests
import json
import sys
import time
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5000"

class AdminProfileTester:
    def __init__(self):
        self.admin_token = None
        self.test_user_id = "c37b40a8-c3e4-4fb8-94ef-4b856971c258"  # From screenshot
        
    def print_separator(self, title):
        """Print a separator with title."""
        print(f"\n{'='*70}")
        print(f"  {title}")
        print(f"{'='*70}")
        
    def print_subsection(self, title):
        """Print a subsection separator."""
        print(f"\n{'-'*50}")
        print(f"  {title}")
        print(f"{'-'*50}")

    def get_admin_token(self):
        """Get admin authentication token"""
        try:
            login_data = {
                "username": "test_admin",
                "password": "test123"
            }
            
            response = requests.post(f"{BASE_URL}/api/admin/login", json=login_data)
            
            if response.status_code == 200:
                data = response.json()
                self.admin_token = data.get('access_token')
                print("✅ Admin authentication successful")
                return True
            else:
                print(f"❌ Admin login failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error getting admin token: {e}")
            return False

    def get_admin_headers(self):
        """Get headers with admin authorization token"""
        return {
            'Authorization': f'Bearer {self.admin_token}',
            'Content-Type': 'application/json'
        }

    def test_user_profile_data(self):
        """Test the main user profile endpoint"""
        self.print_subsection("Testing User Profile Data Fetch")
        
        try:
            response = requests.get(
                f"{BASE_URL}/api/admin/users/{self.test_user_id}/profile",
                headers=self.get_admin_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                print("✅ User profile data fetched successfully")
                
                # Verify all expected sections exist
                expected_sections = [
                    'user_info', 'tier_status', 'trading_info', 
                    'financial_info', 'trades', 'payments', 'api_credentials'
                ]
                
                missing_sections = []
                for section in expected_sections:
                    if section not in data:
                        missing_sections.append(section)
                
                if missing_sections:
                    print(f"❌ Missing sections: {missing_sections}")
                    return False
                else:
                    print("✅ All expected data sections present")
                
                # Test specific data from screenshot
                user_info = data.get('user_info', {})
                print(f"📧 Email: {user_info.get('email', 'N/A')}")
                print(f"🆔 User ID: {user_info.get('id', 'N/A')}")
                print(f"📅 Created: {user_info.get('created_at', 'N/A')}")
                print(f"🔒 2FA Enabled: {user_info.get('two_fa_enabled', 'N/A')}")
                print(f"🤖 Auto Trading: {user_info.get('auto_trading_enabled', 'N/A')}")
                
                tier_status = data.get('tier_status', {})
                print(f"🏆 Current Tier: {tier_status.get('current_tier', 'N/A')}")
                print(f"💰 Profit Share Owed: ${tier_status.get('profit_share_owed', 'N/A')}")
                
                trading_info = data.get('trading_info', {})
                print(f"📊 Total Trades: {trading_info.get('total_trades', 'N/A')}")
                
                financial_info = data.get('financial_info', {})
                print(f"💵 Total Profit: ${financial_info.get('total_profit', 'N/A')}")
                print(f"💳 Total Payments: {financial_info.get('total_payments', 'N/A')}")
                
                return data
                
            else:
                print(f"❌ Failed to fetch user profile: {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error testing user profile: {e}")
            return None

    def test_reset_2fa_functionality(self):
        """Test the Reset 2FA button functionality and UI logic"""
        self.print_subsection("Testing Reset 2FA Functionality")

        try:
            # First, get user profile to check 2FA status
            profile_response = requests.get(
                f"{BASE_URL}/api/admin/users/{self.test_user_id}/profile",
                headers=self.get_admin_headers()
            )

            if profile_response.status_code != 200:
                print("❌ Could not fetch user profile for 2FA test")
                return False

            profile_data = profile_response.json()
            user_has_2fa = profile_data.get('user_info', {}).get('two_fa_enabled', False)

            print(f"👤 User 2FA Status: {'Enabled' if user_has_2fa else 'Disabled'}")

            # Test the Reset 2FA endpoint
            response = requests.post(
                f"{BASE_URL}/api/admin/users/{self.test_user_id}/reset-2fa",
                headers=self.get_admin_headers(),
                json={"reason": "Admin test reset"}
            )
            
            if user_has_2fa:
                # User has 2FA enabled - button should be visible and functional
                print("🔍 Testing Reset 2FA for user WITH 2FA enabled...")

                if response.status_code == 200:
                    data = response.json()
                    print("✅ Reset 2FA request successful")
                    print(f"📝 Message: {data.get('message', 'N/A')}")

                    # Verify user data in response
                    user_data = data.get('user', {})
                    if user_data:
                        print(f"👤 User Email: {user_data.get('email', 'N/A')}")
                        print(f"🔒 2FA Status After Reset: {user_data.get('two_fa_enabled', 'N/A')}")
                        print("✅ UI Logic: Reset 2FA button should be VISIBLE for this user")
                        return True
                    else:
                        print("❌ No user data in reset 2FA response")
                        return False
                else:
                    print(f"❌ Reset 2FA failed: {response.status_code}")
                    print(f"Response: {response.text}")
                    return False
            else:
                # User does NOT have 2FA enabled - button should be hidden
                print("🔍 Testing Reset 2FA for user WITHOUT 2FA enabled...")

                if response.status_code == 400:
                    data = response.json()
                    message = data.get('message', '')
                    if 'does not have 2FA enabled' in message:
                        print("✅ Backend correctly rejects reset for non-2FA users")
                        print("✅ UI Logic: Reset 2FA button should be HIDDEN for this user")
                        print("✅ Reset 2FA functionality is working correctly")
                        return True
                    else:
                        print(f"❌ Unexpected 400 error: {message}")
                        return False
                elif response.status_code == 403:
                    print("❌ Reset 2FA failed: Insufficient permissions (need super admin)")
                    return False
                else:
                    print(f"❌ Unexpected response for non-2FA user: {response.status_code}")
                    print(f"Response: {response.text}")
                    return False
                
        except Exception as e:
            print(f"❌ Error testing reset 2FA: {e}")
            return False

    def test_trading_container_status(self):
        """Test trading container status data"""
        self.print_subsection("Testing Trading Container Status")
        
        try:
            # Get trading containers from admin endpoint
            response = requests.get(
                f"{BASE_URL}/api/admin/trading-bot/containers",
                headers=self.get_admin_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Trading containers data fetched successfully")
                
                containers = data.get('containers', [])
                print(f"📦 Total active containers: {len(containers)}")
                
                # Look for our test user's container
                user_container = None
                for container in containers:
                    if container.get('user_id') == self.test_user_id:
                        user_container = container
                        break
                
                if user_container:
                    print(f"✅ Found container for test user")
                    print(f"📊 Container Status: {user_container.get('status', 'N/A')}")
                    print(f"🤖 Auto Trading: {user_container.get('auto_trading_enabled', 'N/A')}")
                    print(f"📈 Current Trade: {user_container.get('current_trade', 'None')}")
                    print(f"⚖️ Risk %: {user_container.get('risk_percentage', 'N/A')}")
                    print(f"🔢 Leverage: {user_container.get('leverage', 'N/A')}")
                else:
                    print(f"ℹ️  No active container found for test user (expected if auto-trading disabled)")
                
                return True
                
            else:
                print(f"❌ Failed to fetch trading containers: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing trading container status: {e}")
            return False

    def test_refresh_status_functionality(self):
        """Test the refresh status functionality by making multiple calls"""
        self.print_subsection("Testing Refresh Status Functionality")
        
        try:
            print("🔄 Testing multiple profile data fetches...")
            
            # Make 3 consecutive calls to simulate refresh
            for i in range(3):
                print(f"📡 Fetch attempt {i+1}/3...")
                
                response = requests.get(
                    f"{BASE_URL}/api/admin/users/{self.test_user_id}/profile",
                    headers=self.get_admin_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    print(f"✅ Fetch {i+1} successful at {timestamp}")
                    
                    # Check if data is consistent
                    user_info = data.get('user_info', {})
                    print(f"   📧 Email: {user_info.get('email', 'N/A')}")
                    print(f"   🤖 Auto Trading: {user_info.get('auto_trading_enabled', 'N/A')}")
                    
                else:
                    print(f"❌ Fetch {i+1} failed: {response.status_code}")
                    return False
                
                if i < 2:  # Don't sleep after last iteration
                    time.sleep(1)
            
            print("✅ Refresh functionality working correctly")
            return True
            
        except Exception as e:
            print(f"❌ Error testing refresh functionality: {e}")
            return False

    def test_data_consistency(self, profile_data):
        """Test data consistency and validate expected values"""
        self.print_subsection("Testing Data Consistency")

        try:
            issues = []

            # Check user info consistency
            user_info = profile_data.get('user_info', {})
            if not user_info.get('email'):
                issues.append("Missing user email")
            if not user_info.get('id'):
                issues.append("Missing user ID")

            # Check tier status consistency
            tier_status = profile_data.get('tier_status', {})
            current_tier = tier_status.get('current_tier')
            if current_tier not in [1, 2, 3]:
                issues.append(f"Invalid tier value: {current_tier}")

            # Check financial info consistency
            financial_info = profile_data.get('financial_info', {})
            profit_share_owed = financial_info.get('profit_share_owed', 0)
            if not isinstance(profit_share_owed, (int, float)):
                issues.append("Profit share owed is not a number")

            # Check trading info consistency
            trading_info = profile_data.get('trading_info', {})
            total_trades = trading_info.get('total_trades', 0)
            if not isinstance(total_trades, int):
                issues.append("Total trades is not an integer")

            # Check arrays are present
            if not isinstance(profile_data.get('trades', []), list):
                issues.append("Trades data is not an array")
            if not isinstance(profile_data.get('payments', []), list):
                issues.append("Payments data is not an array")
            if not isinstance(profile_data.get('api_credentials', []), list):
                issues.append("API credentials data is not an array")

            if issues:
                print("❌ Data consistency issues found:")
                for issue in issues:
                    print(f"   • {issue}")
                return False
            else:
                print("✅ All data consistency checks passed")
                return True

        except Exception as e:
            print(f"❌ Error testing data consistency: {e}")
            return False

    def test_ui_data_mapping(self, profile_data):
        """Test that data matches what's shown in the UI screenshot"""
        self.print_subsection("Testing UI Data Mapping")

        try:
            print("🖥️  Verifying data matches UI components...")

            user_info = profile_data.get('user_info', {})
            tier_status = profile_data.get('tier_status', {})
            trading_info = profile_data.get('trading_info', {})
            financial_info = profile_data.get('financial_info', {})

            # Header section data
            print(f"📧 Email (Header): {user_info.get('email', 'N/A')}")
            print(f"🏆 Tier Badge: Tier {tier_status.get('current_tier', 'N/A')}")
            print(f"✅ Status Badge: {'Active' if user_info.get('is_active') else 'Inactive'}")

            # User details section
            print(f"🆔 User ID: {user_info.get('id', 'N/A')}")
            print(f"📅 Created: {user_info.get('created_at', 'N/A')}")
            print(f"🤖 Auto Trading: {'Enabled' if user_info.get('auto_trading_enabled') else 'Disabled'}")
            print(f"🔒 2FA Status: {'Enabled' if user_info.get('two_fa_enabled') else 'Disabled'}")

            # Financial cards
            print(f"📊 Total Trades: {trading_info.get('total_trades', 0)}")
            print(f"💵 Total Profit: ${financial_info.get('total_profit', 0)}")
            print(f"💰 Profit Share Owed: ${tier_status.get('profit_share_owed', 0)}")
            print(f"💳 Total Payments: {financial_info.get('total_payments', 0)}")

            # Active Trading Container section
            active_session = trading_info.get('active_session')
            if active_session:
                print(f"📦 Container Status: Active")
                print(f"📈 Current Trade: {active_session.get('symbol', 'None')}")
            else:
                print(f"📦 Container Status: Inactive")
                print(f"📈 Current Trade: None")

            # Verify data types and ranges
            checks_passed = 0
            total_checks = 0

            # Check tier is valid
            total_checks += 1
            if tier_status.get('current_tier') in [1, 2, 3]:
                checks_passed += 1
                print("✅ Tier value is valid")
            else:
                print("❌ Invalid tier value")

            # Check profit share is numeric
            total_checks += 1
            if isinstance(tier_status.get('profit_share_owed', 0), (int, float)):
                checks_passed += 1
                print("✅ Profit share owed is numeric")
            else:
                print("❌ Profit share owed is not numeric")

            # Check total trades is integer
            total_checks += 1
            if isinstance(trading_info.get('total_trades', 0), int):
                checks_passed += 1
                print("✅ Total trades is integer")
            else:
                print("❌ Total trades is not integer")

            success_rate = (checks_passed / total_checks) * 100
            print(f"📈 UI Data Mapping: {checks_passed}/{total_checks} checks passed ({success_rate:.1f}%)")

            return checks_passed == total_checks

        except Exception as e:
            print(f"❌ Error testing UI data mapping: {e}")
            return False

    def run_comprehensive_test(self):
        """Run all tests"""
        self.print_separator("ADMIN USER PROFILE COMPREHENSIVE TEST")
        
        print("🚀 Starting comprehensive admin user profile test...")
        print(f"📡 Backend URL: {BASE_URL}")
        print(f"👤 Test User ID: {self.test_user_id}")
        
        # Step 1: Authenticate as admin
        self.print_separator("STEP 1: Admin Authentication")
        if not self.get_admin_token():
            print("❌ Cannot proceed without admin authentication")
            return False
        
        # Step 2: Test user profile data fetch
        self.print_separator("STEP 2: User Profile Data")
        profile_data = self.test_user_profile_data()
        if not profile_data:
            print("❌ Cannot proceed without profile data")
            return False
        
        # Step 3: Test data consistency
        self.print_separator("STEP 3: Data Consistency")
        consistency_ok = self.test_data_consistency(profile_data)

        # Step 4: Test UI data mapping
        self.print_separator("STEP 4: UI Data Mapping")
        ui_mapping_ok = self.test_ui_data_mapping(profile_data)

        # Step 5: Test Reset 2FA functionality
        self.print_separator("STEP 5: Reset 2FA Functionality")
        reset_2fa_ok = self.test_reset_2fa_functionality()

        # Step 6: Test trading container status
        self.print_separator("STEP 6: Trading Container Status")
        container_ok = self.test_trading_container_status()

        # Step 7: Test refresh functionality
        self.print_separator("STEP 7: Refresh Status Functionality")
        refresh_ok = self.test_refresh_status_functionality()
        
        # Final results
        self.print_separator("TEST RESULTS SUMMARY")
        
        results = {
            "Profile Data Fetch": profile_data is not None,
            "Data Consistency": consistency_ok,
            "UI Data Mapping": ui_mapping_ok,
            "Reset 2FA": reset_2fa_ok,
            "Container Status": container_ok,
            "Refresh Functionality": refresh_ok
        }
        
        all_passed = True
        for test_name, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{status} {test_name}")
            if not passed:
                all_passed = False
        
        print(f"\n🎯 OVERALL RESULT:")
        if all_passed:
            print("🎉 ALL TESTS PASSED - Admin User Profile is working correctly!")
        else:
            print("💥 SOME TESTS FAILED - Check the issues above")
        
        return all_passed

def main():
    """Main function"""
    tester = AdminProfileTester()
    success = tester.run_comprehensive_test()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
