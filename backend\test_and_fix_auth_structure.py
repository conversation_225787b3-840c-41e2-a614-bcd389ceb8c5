#!/usr/bin/env python3
"""
Comprehensive test script to diagnose and fix auth structure issues
"""

import sys
import os
import re
import json

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_auth_structure(lang):
    """Analyze the current auth structure in a language file"""
    print(f"  Analyzing {lang.upper()}:")
    
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the auth section
        auth_match = re.search(r'"auth":\s*{([^}]+(?:{[^}]*}[^}]*)*)}', content, re.DOTALL)
        if not auth_match:
            print(f"     [ERROR] No auth section found")
            return None
        
        auth_content = auth_match.group(1)
        
        # Check for different patterns
        has_root_errors = bool(re.search(r'"errors":\s*{[^}]*"emailInvalid"', auth_content))
        has_root_pwd_req = bool(re.search(r'"passwordRequirements":\s*{[^}]*"title"', auth_content))
        has_login_errors = bool(re.search(r'"login":\s*{[^}]*"errors":\s*{[^}]*"emailInvalid"', auth_content, re.DOTALL))
        has_login_pwd_req = bool(re.search(r'"login":\s*{[^}]*"passwordRequirements":\s*{[^}]*"title"', auth_content, re.DOTALL))
        
        structure = {
            'has_root_errors': has_root_errors,
            'has_root_pwd_req': has_root_pwd_req,
            'has_login_errors': has_login_errors,
            'has_login_pwd_req': has_login_pwd_req,
            'auth_content_preview': auth_content[:200] + '...' if len(auth_content) > 200 else auth_content
        }
        
        print(f"     Root level errors: {'✅' if has_root_errors else '❌'}")
        print(f"     Root level passwordRequirements: {'✅' if has_root_pwd_req else '❌'}")
        print(f"     Login nested errors: {'❌' if has_login_errors else '✅'} (should be false)")
        print(f"     Login nested passwordRequirements: {'❌' if has_login_pwd_req else '✅'} (should be false)")
        
        return structure
        
    except Exception as e:
        print(f"     [ERROR] {str(e)}")
        return None

def fix_auth_structure_definitively(lang):
    """Definitively fix the auth structure by rebuilding it correctly"""
    print(f"  Fixing {lang.upper()}:")
    
    # Define correct translations
    translations = {
        'es': {
            'errors': {
                'emailInvalid': 'Por favor ingresa una dirección de email válida',
                'emailRequired': 'Email es requerido',
                'invalidCredentials': 'Email o contraseña inválidos',
                'passwordRequired': 'Contraseña es requerida',
                'passwordTooShort': 'La contraseña debe tener al menos 8 caracteres',
                'passwordsNotMatch': 'Las contraseñas no coinciden',
                'termsRequired': 'Debes aceptar los términos y condiciones'
            },
            'passwordRequirements': {
                'title': 'Requisitos de Contraseña',
                'length': 'Al menos 8 caracteres',
                'uppercase': 'Una letra mayúscula',
                'lowercase': 'Una letra minúscula',
                'number': 'Un número',
                'special': 'Un carácter especial'
            }
        },
        'pt': {
            'errors': {
                'emailInvalid': 'Por favor, insira um endereço de email válido',
                'emailRequired': 'Email é obrigatório',
                'invalidCredentials': 'Email ou senha inválidos',
                'passwordRequired': 'Senha é obrigatória',
                'passwordTooShort': 'A senha deve ter pelo menos 8 caracteres',
                'passwordsNotMatch': 'As senhas não coincidem',
                'termsRequired': 'Você deve concordar com os termos e condições'
            },
            'passwordRequirements': {
                'title': 'Requisitos de Senha',
                'length': 'Pelo menos 8 caracteres',
                'uppercase': 'Uma letra maiúscula',
                'lowercase': 'Uma letra minúscula',
                'number': 'Um número',
                'special': 'Um caractere especial'
            }
        },
        'ko': {
            'errors': {
                'emailInvalid': '유효한 이메일 주소를 입력해주세요',
                'emailRequired': '이메일이 필요합니다',
                'invalidCredentials': '유효하지 않은 이메일 또는 비밀번호',
                'passwordRequired': '비밀번호가 필요합니다',
                'passwordTooShort': '비밀번호는 최소 8자 이상이어야 합니다',
                'passwordsNotMatch': '비밀번호가 일치하지 않습니다',
                'termsRequired': '이용약관에 동의해야 합니다'
            },
            'passwordRequirements': {
                'title': '비밀번호 요구사항',
                'length': '최소 8자',
                'uppercase': '대문자 하나',
                'lowercase': '소문자 하나',
                'number': '숫자 하나',
                'special': '특수문자 하나'
            }
        },
        'ja': {
            'errors': {
                'emailInvalid': '有効なメールアドレスを入力してください',
                'emailRequired': 'メールが必要です',
                'invalidCredentials': '無効なメールまたはパスワード',
                'passwordRequired': 'パスワードが必要です',
                'passwordTooShort': 'パスワードは8文字以上である必要があります',
                'passwordsNotMatch': 'パスワードが一致しません',
                'termsRequired': '利用規約に同意する必要があります'
            },
            'passwordRequirements': {
                'title': 'パスワード要件',
                'length': '8文字以上',
                'uppercase': '大文字1文字',
                'lowercase': '小文字1文字',
                'number': '数字1文字',
                'special': '特殊文字1文字'
            }
        },
        'de': {
            'errors': {
                'emailInvalid': 'Bitte geben Sie eine gültige E-Mail-Adresse ein',
                'emailRequired': 'E-Mail ist erforderlich',
                'invalidCredentials': 'Ungültige E-Mail oder Passwort',
                'passwordRequired': 'Passwort ist erforderlich',
                'passwordTooShort': 'Passwort muss mindestens 8 Zeichen haben',
                'passwordsNotMatch': 'Passwörter stimmen nicht überein',
                'termsRequired': 'Sie müssen den Geschäftsbedingungen zustimmen'
            },
            'passwordRequirements': {
                'title': 'Passwort-Anforderungen',
                'length': 'Mindestens 8 Zeichen',
                'uppercase': 'Ein Großbuchstabe',
                'lowercase': 'Ein Kleinbuchstabe',
                'number': 'Eine Zahl',
                'special': 'Ein Sonderzeichen'
            }
        },
        'fr': {
            'errors': {
                'emailInvalid': 'Veuillez saisir une adresse email valide',
                'emailRequired': 'Email requis',
                'invalidCredentials': 'Email ou mot de passe invalide',
                'passwordRequired': 'Mot de passe requis',
                'passwordTooShort': 'Le mot de passe doit contenir au moins 8 caractères',
                'passwordsNotMatch': 'Les mots de passe ne correspondent pas',
                'termsRequired': 'Vous devez accepter les termes et conditions'
            },
            'passwordRequirements': {
                'title': 'Exigences du mot de passe',
                'length': 'Au moins 8 caractères',
                'uppercase': 'Une lettre majuscule',
                'lowercase': 'Une lettre minuscule',
                'number': 'Un chiffre',
                'special': 'Un caractère spécial'
            }
        },
        'zh': {
            'errors': {
                'emailInvalid': '请输入有效的邮箱地址',
                'emailRequired': '邮箱必填',
                'invalidCredentials': '无效的邮箱或密码',
                'passwordRequired': '密码必填',
                'passwordTooShort': '密码必须至少8个字符',
                'passwordsNotMatch': '密码不匹配',
                'termsRequired': '您必须同意条款和条件'
            },
            'passwordRequirements': {
                'title': '密码要求',
                'length': '至少8个字符',
                'uppercase': '一个大写字母',
                'lowercase': '一个小写字母',
                'number': '一个数字',
                'special': '一个特殊字符'
            }
        }
    }
    
    if lang not in translations:
        print(f"     [SKIP] No translations for {lang}")
        return False
    
    try:
        # Read the file
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and extract the auth section
        auth_match = re.search(r'"auth":\s*{([^}]+(?:{[^}]*}[^}]*)*)}', content, re.DOTALL)
        if not auth_match:
            print(f"     [ERROR] No auth section found")
            return False
        
        # Extract existing login and register sections (preserve them)
        auth_content = auth_match.group(1)
        
        # Extract login section
        login_match = re.search(r'"login":\s*{([^}]+(?:{[^}]*}[^}]*)*)}', auth_content, re.DOTALL)
        login_section = login_match.group(0) if login_match else None
        
        # Extract register section  
        register_match = re.search(r'"register":\s*{([^}]+(?:{[^}]*}[^}]*)*)}', auth_content, re.DOTALL)
        register_section = register_match.group(0) if register_match else None
        
        # Build new auth section with correct structure
        new_auth_parts = []
        
        # Add login section (clean it of nested errors/passwordRequirements)
        if login_section:
            # Remove nested errors and passwordRequirements from login
            clean_login = re.sub(r',?\s*"errors":\s*{[^}]*}', '', login_section)
            clean_login = re.sub(r',?\s*"passwordRequirements":\s*{[^}]*}', '', clean_login)
            new_auth_parts.append(clean_login)
        
        # Add register section
        if register_section:
            new_auth_parts.append(register_section)
        
        # Add root level passwordRequirements
        pwd_req_lines = ['    "passwordRequirements": {']
        for key, value in translations[lang]['passwordRequirements'].items():
            pwd_req_lines.append(f'      "{key}": "{value}",')
        # Remove last comma
        pwd_req_lines[-1] = pwd_req_lines[-1][:-1]
        pwd_req_lines.append('    }')
        new_auth_parts.append('\n'.join(pwd_req_lines))
        
        # Add root level errors
        errors_lines = ['    "errors": {']
        for key, value in translations[lang]['errors'].items():
            errors_lines.append(f'      "{key}": "{value}",')
        # Remove last comma
        errors_lines[-1] = errors_lines[-1][:-1]
        errors_lines.append('    }')
        new_auth_parts.append('\n'.join(errors_lines))
        
        # Combine all parts
        new_auth_content = ',\n'.join(new_auth_parts)
        
        # Replace the auth section
        new_content = re.sub(
            r'"auth":\s*{([^}]+(?:{[^}]*}[^}]*)*)}',
            f'"auth": {{\n{new_auth_content}\n  }}',
            content,
            flags=re.DOTALL
        )
        
        # Write back
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"     [SUCCESS] Auth structure rebuilt correctly")
        return True
        
    except Exception as e:
        print(f"     [ERROR] {str(e)}")
        return False

def test_auth_structure_after_fix(lang):
    """Test the auth structure after fix"""
    structure = analyze_auth_structure(lang)
    if not structure:
        return False
    
    # Check if structure is correct
    correct = (structure['has_root_errors'] and 
               structure['has_root_pwd_req'] and 
               not structure['has_login_errors'] and 
               not structure['has_login_pwd_req'])
    
    if correct:
        print(f"     [TEST PASSED] ✅ Auth structure is correct")
    else:
        print(f"     [TEST FAILED] ❌ Auth structure still incorrect")
    
    return correct

def run_comprehensive_auth_fix():
    """Run comprehensive auth structure analysis and fix"""
    print("COMPREHENSIVE AUTH STRUCTURE TEST & FIX")
    print("=" * 60)
    
    languages = ['es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    print("\n1. ANALYZING CURRENT STRUCTURE:")
    print("-" * 40)
    structures = {}
    for lang in languages:
        structures[lang] = analyze_auth_structure(lang)
    
    print("\n2. FIXING AUTH STRUCTURES:")
    print("-" * 40)
    fixed_count = 0
    for lang in languages:
        if fix_auth_structure_definitively(lang):
            fixed_count += 1
    
    print("\n3. TESTING AFTER FIX:")
    print("-" * 40)
    passed_count = 0
    for lang in languages:
        if test_auth_structure_after_fix(lang):
            passed_count += 1
    
    print("\n" + "=" * 60)
    print("COMPREHENSIVE FIX SUMMARY")
    print("=" * 60)
    print(f"Languages processed: {len(languages)}")
    print(f"Structures fixed: {fixed_count}")
    print(f"Tests passed: {passed_count}")
    
    if passed_count == len(languages):
        print("\n✅ ALL AUTH STRUCTURES FIXED AND TESTED!")
        print("✅ All languages now have correct auth.errors and auth.passwordRequirements")
        print("✅ No more nested auth.login.errors or auth.login.passwordRequirements")
        
        print("\n🎯 EXPECTED BROWSER CONSOLE RESULTS:")
        print("• No more 'Missing keys' warnings for auth.errors.*")
        print("• No more 'Missing keys' warnings for auth.passwordRequirements.*")
        print("• No more 'Extra keys' warnings for auth.login.errors.*")
        print("• No more 'Extra keys' warnings for auth.login.passwordRequirements.*")
        print("• Translation completion should improve significantly")
        
        print("\n🎯 NEXT STEPS:")
        print("1. Restart the frontend development server")
        print("2. Clear browser cache")
        print("3. Check browser console - auth warnings should be gone")
        print("4. Test Access & Security page functionality")
        return True
    else:
        print(f"\n❌ {len(languages) - passed_count} language(s) still have incorrect structure")
        return False

if __name__ == "__main__":
    success = run_comprehensive_auth_fix()
    sys.exit(0 if success else 1)
