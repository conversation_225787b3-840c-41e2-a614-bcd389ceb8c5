#!/usr/bin/env python3
"""
Test Elite ML API endpoints
"""

import requests
import json

def test_elite_ml_status():
    """Test the elite ML status endpoint"""
    try:
        url = "http://127.0.0.1:5000/api/elite-ml/status"
        
        print(f"🔍 Testing: {url}")
        response = requests.get(url)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Elite ML Status API working!")
            print(json.dumps(data, indent=2))
            return True
        elif response.status_code == 401:
            print("⚠️  Authentication required (expected for protected endpoint)")
            return True
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

def test_server_health():
    """Test if server is running"""
    try:
        url = "http://127.0.0.1:5000/"
        response = requests.get(url, timeout=5)
        
        if response.status_code in [200, 404]:  # 404 is fine, means server is running
            print("✅ Server is running")
            return True
        else:
            print(f"⚠️  Server responded with status: {response.status_code}")
            return True
            
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running")
        return False
    except Exception as e:
        print(f"❌ Error checking server: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Elite ML API Integration")
    print("=" * 50)
    
    # Test server health
    if test_server_health():
        # Test elite ML status endpoint
        test_elite_ml_status()
    else:
        print("❌ Server is not running. Please start with 'python run.py'")
