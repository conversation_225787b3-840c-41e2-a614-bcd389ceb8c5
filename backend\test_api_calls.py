#!/usr/bin/env python3
"""
Test script to simulate the payment flow via API calls.
This will test the exact same flow that the frontend uses.
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:5000"
# You'll need to get a valid token from the browser's localStorage
# Go to browser dev tools -> Application -> Local Storage -> access_token
# For now, we'll use a placeholder - you need to replace this with the real token
ACCESS_TOKEN = "REPLACE_WITH_REAL_TOKEN_FROM_BROWSER"

def get_headers():
    """Get headers with authorization token."""
    return {
        'Authorization': f'Bearer {ACCESS_TOKEN}',
        'Content-Type': 'application/json'
    }

def print_separator(title):
    """Print a separator with title."""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def check_tier_status():
    """Check current tier status (same as frontend fetchTierAndPaymentData)."""
    print("\n🔍 Checking tier status...")

    response = requests.get(f"{BASE_URL}/api/trading/tier/status", headers=get_headers())

    if response.status_code == 200:
        data = response.json()
        print(f"✅ Tier Status Response:")
        print(f"   - Profit Share Owed: ${data.get('profit_share_owed', 'N/A')}")
        print(f"   - Tier: {data.get('tier', 'N/A')}")

        # Check tier_status nested object
        tier_status = data.get('tier_status', {})
        if tier_status:
            print(f"   - Tier Status Object:")
            print(f"     * profit_share_owed: ${tier_status.get('profit_share_owed', 'N/A')}")
            print(f"     * payment_status: {tier_status.get('payment_status', 'N/A')}")
            print(f"     * account_disabled: {tier_status.get('account_disabled', 'N/A')}")

        return data
    else:
        print(f"❌ Failed to get tier status: {response.status_code}")
        print(f"   Response: {response.text}")
        return None

def check_payday_status():
    """Check payday status."""
    print("\n📅 Checking payday status...")

    response = requests.get(f"{BASE_URL}/api/trading/payday/status", headers=get_headers())

    if response.status_code == 200:
        data = response.json()
        print(f"✅ Payday Status Response:")
        print(f"   - Account Disabled: {data.get('account_disabled', 'N/A')}")
        print(f"   - Profit Share Owed: ${data.get('profit_share_owed', 'N/A')}")
        print(f"   - Payment Status: {data.get('payment_status', 'N/A')}")
        print(f"   - Is Past Deadline: {data.get('is_past_payday_deadline', 'N/A')}")
        return data
    else:
        print(f"❌ Failed to get payday status: {response.status_code}")
        print(f"   Response: {response.text}")
        return None

def add_test_debt(amount=0.01):
    """Add test debt."""
    print(f"\n💰 Adding test debt: ${amount}")

    response = requests.post(
        f"{BASE_URL}/api/trading/test/add-profit-share-debt",
        headers=get_headers(),
        json={"amount": amount}
    )

    if response.status_code == 200:
        data = response.json()
        print(f"✅ Test debt added successfully:")
        print(f"   - Message: {data.get('message', 'N/A')}")
        print(f"   - Total Owed: ${data.get('total_owed', 'N/A')}")
        return True
    else:
        print(f"❌ Failed to add test debt: {response.status_code}")
        print(f"   Response: {response.text}")
        return False

def make_test_payment(amount):
    """Make a test payment (same as frontend Test Backend Payment button)."""
    print(f"\n💳 Making test payment: ${amount}")

    response = requests.post(
        f"{BASE_URL}/api/trading/test/simulate-profit-share-payment",
        headers=get_headers(),
        json={"amount": amount}
    )

    if response.status_code == 200:
        data = response.json()
        print(f"✅ Test payment successful:")
        print(f"   - Message: {data.get('message', 'N/A')}")

        payment_info = data.get('payment', {})
        print(f"   - Payment ID: {payment_info.get('id', 'N/A')}")
        print(f"   - Payment Amount: ${payment_info.get('amount', 'N/A')}")
        print(f"   - Transaction Signature: {payment_info.get('transaction_signature', 'N/A')}")

        debt_info = data.get('debt_info', {})
        print(f"   - Original Debt: ${debt_info.get('original_debt', 'N/A')}")
        print(f"   - Remaining Debt: ${debt_info.get('remaining_debt', 'N/A')}")
        print(f"   - Payment Status: {debt_info.get('payment_status', 'N/A')}")
        print(f"   - Account Enabled: {debt_info.get('account_enabled', 'N/A')}")

        return data
    else:
        print(f"❌ Test payment failed: {response.status_code}")
        print(f"   Response: {response.text}")
        return None

def clear_all_debt():
    """Clear all debt for clean testing."""
    print(f"\n🧹 Clearing all debt...")

    response = requests.post(
        f"{BASE_URL}/api/trading/test/clear-profit-share-debt",
        headers=get_headers()
    )

    if response.status_code == 200:
        data = response.json()
        print(f"✅ All debt cleared: {data.get('message', 'N/A')}")
        return True
    else:
        print(f"❌ Failed to clear debt: {response.status_code}")
        print(f"   Response: {response.text}")
        return False

def main():
    """Main test function."""
    print_separator("DeepTrade Payment Flow API Test")

    print("🚀 Starting payment flow test...")
    print(f"📡 Backend URL: {BASE_URL}")
    print(f"🔑 Using token: {ACCESS_TOKEN[:20]}...")

    # Step 1: Clear any existing debt for clean test
    print_separator("STEP 1: Clean Slate")
    clear_all_debt()

    # Step 2: Check initial status
    print_separator("STEP 2: Initial Status Check")
    initial_tier_status = check_tier_status()
    initial_payday_status = check_payday_status()

    # Step 3: Add test debt
    print_separator("STEP 3: Add Test Debt")
    if add_test_debt(0.01):
        time.sleep(1)  # Give database time to update

        # Step 4: Check status after adding debt
        print_separator("STEP 4: Status After Adding Debt")
        debt_tier_status = check_tier_status()
        debt_payday_status = check_payday_status()

        if debt_tier_status and debt_tier_status.get('profit_share_owed') != '0':
            debt_amount = float(debt_tier_status.get('profit_share_owed', 0))

            # Step 5: Make payment
            print_separator("STEP 5: Make Test Payment")
            payment_result = make_test_payment(debt_amount)

            if payment_result:
                time.sleep(1)  # Give database time to update

                # Step 6: Check final status
                print_separator("STEP 6: Final Status Check")
                final_tier_status = check_tier_status()
                final_payday_status = check_payday_status()

                # Step 7: Verification
                print_separator("VERIFICATION RESULTS")

                if final_tier_status:
                    final_debt = float(final_tier_status.get('profit_share_owed', -1))
                    tier_status_obj = final_tier_status.get('tier_status', {})
                    final_payment_status = tier_status_obj.get('payment_status', 'unknown')
                    final_account_disabled = tier_status_obj.get('account_disabled', True)

                    print(f"💰 DEBT STATUS:")
                    if final_debt == 0:
                        print(f"   ✅ SUCCESS: Debt cleared to $0.00")
                    else:
                        print(f"   ❌ FAILURE: Debt not cleared! Still owe: ${final_debt}")

                    print(f"📋 PAYMENT STATUS:")
                    if final_payment_status == 'paid':
                        print(f"   ✅ SUCCESS: Payment status is 'paid'")
                    else:
                        print(f"   ❌ FAILURE: Payment status is '{final_payment_status}' (expected 'paid')")

                    print(f"🔓 ACCOUNT STATUS:")
                    if not final_account_disabled:
                        print(f"   ✅ SUCCESS: Account is enabled")
                    else:
                        print(f"   ❌ FAILURE: Account is still disabled")

                    # Overall result
                    success = (final_debt == 0 and
                             final_payment_status == 'paid' and
                             not final_account_disabled)

                    print(f"\n🎯 OVERALL RESULT:")
                    if success:
                        print(f"   🎉 PAYMENT FLOW WORKING CORRECTLY!")
                    else:
                        print(f"   💥 PAYMENT FLOW HAS ISSUES!")
                        print(f"   🔧 Check backend logs for debugging information")

                    return success
                else:
                    print(f"❌ Could not get final tier status")
                    return False
            else:
                print(f"❌ Payment failed")
                return False
        else:
            print(f"❌ No debt found after adding test debt")
            return False
    else:
        print(f"❌ Failed to add test debt")
        return False

if __name__ == "__main__":
    print("⚠️  IMPORTANT: Update ACCESS_TOKEN with a valid token from browser localStorage!")
    print("   1. Go to browser dev tools (F12)")
    print("   2. Application tab -> Local Storage -> localhost:5173")
    print("   3. Copy the 'access_token' value")
    print("   4. Replace ACCESS_TOKEN in this script")
    print("   5. Run the script again")
    print()

    # Check if token looks valid (basic check)
    if len(ACCESS_TOKEN) < 100 or "eyJ" not in ACCESS_TOKEN:
        print("❌ ACCESS_TOKEN appears to be invalid or placeholder")
        print("   Please update it with a real token from the browser")
        exit(1)

    success = main()
    exit(0 if success else 1)