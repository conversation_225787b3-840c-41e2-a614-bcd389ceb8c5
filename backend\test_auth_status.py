#!/usr/bin/env python3
"""
Test script to check authentication status and create a test login
"""

import sys
import os
import requests
import json

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_auth_status():
    """Test authentication and create a test login if needed"""
    print("🔐 TESTING AUTHENTICATION STATUS...")
    print("="*50)
    
    base_url = "http://localhost:5000"
    
    # Test login with the existing user
    print("1. Attempting to login with test user...")
    try:
        login_response = requests.post(f"{base_url}/api/auth/login", 
                                     json={
                                         "email": "<EMAIL>",
                                         "password": "testpassword123"  # You might need to adjust this
                                     },
                                     timeout=10)
        
        print(f"   Login response status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_data = login_response.json()
            print("   ✅ Login successful!")
            print(f"   User: {login_data.get('user', {}).get('email', 'Unknown')}")
            
            access_token = login_data.get('access_token')
            if access_token:
                print(f"   Access token: {access_token[:20]}...")
                
                # Test the security endpoints with this token
                print("\n2. Testing security endpoints with token...")
                
                headers = {
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json'
                }
                
                # Test access logs
                logs_response = requests.get(f"{base_url}/api/users/security/access-logs?limit=5", 
                                           headers=headers, timeout=10)
                print(f"   Access logs: {logs_response.status_code}")
                if logs_response.status_code == 200:
                    logs_data = logs_response.json()
                    print(f"   Found {len(logs_data.get('access_logs', []))} access logs")
                
                # Test security stats
                stats_response = requests.get(f"{base_url}/api/users/security/stats", 
                                            headers=headers, timeout=10)
                print(f"   Security stats: {stats_response.status_code}")
                if stats_response.status_code == 200:
                    stats_data = stats_response.json()
                    print(f"   Total logins: {stats_data.get('statistics', {}).get('total_logins', 0)}")
                
                # Test current session
                session_response = requests.get(f"{base_url}/api/users/security/current-session", 
                                              headers=headers, timeout=10)
                print(f"   Current session: {session_response.status_code}")
                
                print(f"\n✅ Use this token in your browser localStorage:")
                print(f"   localStorage.setItem('access_token', '{access_token}');")
                
            else:
                print("   ❌ No access token in response")
        else:
            error_data = login_response.text
            print(f"   ❌ Login failed: {error_data}")
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Connection failed - Backend not running?")
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    test_auth_status()
