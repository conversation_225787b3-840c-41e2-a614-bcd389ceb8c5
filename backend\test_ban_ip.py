#!/usr/bin/env python3
"""
Test script for Ban IP Address functionality
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ban_ip_button():
    """Test Ban IP button implementation"""
    print("Testing Ban IP Button Implementation...")
    
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for ban IP button
        print("  1. Testing ban IP button:")
        
        if 'id="ban-ip-btn"' in content and 'Ban IP Address' in content:
            print("     [PASS] Ban IP button found")
        else:
            print("     [FAIL] Ban IP button missing")
            return False
        
        # Test 2: Check button styling
        print("  2. Testing button styling:")
        
        button_styles = [
            'bg-red-600 hover:bg-red-700'
        ]
        
        for style in button_styles:
            if style in content:
                print(f"     [PASS] {style} found")
            else:
                print(f"     [FAIL] {style} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_ban_ip_modal():
    """Test Ban IP modal implementation"""
    print("Testing Ban IP Modal Implementation...")
    
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for modal function
        print("  1. Testing modal function:")
        
        if 'showBanIPModal' in content:
            print("     [PASS] showBanIPModal function found")
        else:
            print("     [FAIL] showBanIPModal function missing")
            return False
        
        # Test 2: Check for modal elements
        print("  2. Testing modal elements:")
        
        modal_elements = [
            'ban-ip-input',
            'ban-reason-input',
            'ban-expiry-input',
            'confirm-ban-btn',
            'cancel-ban-btn'
        ]
        
        for element in modal_elements:
            if element in content:
                print(f"     [PASS] {element} found")
            else:
                print(f"     [FAIL] {element} missing")
                return False
        
        # Test 3: Check for form validation
        print("  3. Testing form validation:")
        
        validation_checks = [
            'ipAddress.*trim()',
            'reason.*trim()',
            'Please enter both IP address and reason'
        ]
        
        for check in validation_checks:
            if re.search(check, content):
                print(f"     [PASS] {check} found")
            else:
                print(f"     [FAIL] {check} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_ban_ip_javascript():
    """Test Ban IP JavaScript functionality"""
    print("Testing Ban IP JavaScript Functionality...")
    
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for performIPBan function
        print("  1. Testing performIPBan function:")
        
        js_functions = [
            'async function performIPBan',
            '/api/admin/ip/ban',
            'method.*POST',
            'Authorization.*Bearer'
        ]
        
        for func in js_functions:
            if re.search(func, content):
                print(f"     [PASS] {func} found")
            else:
                print(f"     [FAIL] {func} missing")
                return False
        
        # Test 2: Check for event listener
        print("  2. Testing event listener:")
        
        if 'ban-ip-btn.*addEventListener.*showBanIPModal' in content or 'getElementById(\'ban-ip-btn\').addEventListener' in content:
            print("     [PASS] Event listener found")
        else:
            print("     [FAIL] Event listener missing")
            return False
        
        # Test 3: Check for success/error handling
        print("  3. Testing success/error handling:")
        
        handling_elements = [
            'showToast.*success',
            'showToast.*error',
            'loadIPBlacklist',
            'loadIPStats'
        ]
        
        for element in handling_elements:
            if re.search(element, content):
                print(f"     [PASS] {element} found")
            else:
                print(f"     [FAIL] {element} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_ban_ip_backend():
    """Test Ban IP backend endpoint"""
    print("Testing Ban IP Backend Endpoint...")
    
    try:
        with open('app/api/admin_routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for endpoint definition
        print("  1. Testing endpoint definition:")
        
        endpoint_patterns = [
            "@admin_bp.route('/ip/ban', methods=['POST'])",
            "@super_admin_required",
            "def ban_ip_address():",
            "Ban an IP address"
        ]
        
        for pattern in endpoint_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        # Test 2: Check for functionality
        print("  2. Testing functionality:")
        
        functionality_patterns = [
            'ip_address = data.get',
            'reason = data.get',
            'ipaddress.ip_address',
            'IPBan.query.filter_by',
            'is_active=True'
        ]
        
        for pattern in functionality_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        # Test 3: Check for validation
        print("  3. Testing validation:")
        
        validation_patterns = [
            'IP address and reason are required',
            'Invalid IP address format',
            'IP address is already banned'
        ]
        
        for pattern in validation_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_security_features():
    """Test security features of Ban IP functionality"""
    print("Testing Security Features...")
    
    try:
        with open('app/api/admin_routes.py', 'r', encoding='utf-8') as f:
            backend_content = f.read()
        
        # Test 1: Check for super admin requirement
        print("  1. Testing super admin requirement:")
        
        if '@super_admin_required' in backend_content and 'ban_ip_address' in backend_content:
            print("     [PASS] Super admin requirement found")
        else:
            print("     [FAIL] Super admin requirement missing")
            return False
        
        # Test 2: Check for IP validation
        print("  2. Testing IP validation:")
        
        validation_patterns = [
            'import ipaddress',
            'ipaddress.ip_address',
            'ValueError'
        ]
        
        for pattern in validation_patterns:
            if pattern in backend_content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        # Test 3: Check for admin action logging
        print("  3. Testing admin action logging:")
        
        logging_patterns = [
            'AdminAction.log_action',
            'action_type.*ban_ip',
            'target_type.*ip_address'
        ]
        
        for pattern in logging_patterns:
            if re.search(pattern, backend_content):
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def run_all_tests():
    """Run all Ban IP tests"""
    print("BAN IP ADDRESS FUNCTIONALITY TEST")
    print("=" * 60)
    
    tests = [
        ("Ban IP Button", test_ban_ip_button),
        ("Ban IP Modal", test_ban_ip_modal),
        ("Ban IP JavaScript", test_ban_ip_javascript),
        ("Ban IP Backend", test_ban_ip_backend),
        ("Security Features", test_security_features)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"  [PASS] {test_name} completed successfully")
            else:
                failed += 1
                print(f"  [FAIL] {test_name} failed")
        except Exception as e:
            failed += 1
            print(f"  [ERROR] {test_name} error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("BAN IP TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {len(tests)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed/len(tests)*100):.1f}%")
    
    if failed == 0:
        print("\nALL TESTS PASSED! Ban IP functionality implemented successfully.")
        print("\nImplemented Features:")
        print("- Ban IP button in IP Address Management section")
        print("- Modal with IP address, reason, and expiry fields")
        print("- Backend API endpoint with super admin authentication")
        print("- IP address format validation")
        print("- Duplicate ban prevention")
        print("- Admin action logging for audit trail")
        print("- Success/error toast notifications")
        return True
    else:
        print(f"\n{failed} test(s) failed. Please review implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
