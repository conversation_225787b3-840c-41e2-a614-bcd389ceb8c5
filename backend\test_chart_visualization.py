#!/usr/bin/env python3
"""
Test Chart Visualization with Entry, SL, and TP markers
Shows how the ML system predictions appear on the chart
"""

import sys
import os
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_chart_with_signals():
    """Create a test chart showing ML signals with Entry, SL, and TP"""
    print("📊 Creating Chart Visualization Test")
    print("=" * 50)
    
    try:
        # Import Flask app and create application context
        from app import create_app
        app = create_app()
        
        with app.app_context():
            # Import required services
            from app.services.trading_signals import TradingSignalGenerator
            from app.services.sl_tp_ml_predictors import SLTPMLManager
            from app.models.user import User
            from app import db
            
            # Create or get test user
            test_user = User.query.filter_by(email='<EMAIL>').first()
            if not test_user:
                test_user = User(
                    email='<EMAIL>',
                    full_name='Test User',
                    password='test_password'
                )
                test_user.is_active = True
                db.session.add(test_user)
                db.session.commit()
            
            # Initialize services
            signal_generator = TradingSignalGenerator(
                user_id=str(test_user.id),
                exchange_service=None,
                admin_monitoring_mode=True
            )
            
            sl_tp_manager = SLTPMLManager()
            
            print("✅ Services initialized")
            
            # Generate realistic market data
            print("📈 Generating market data...")
            dates = pd.date_range(start='2024-01-01', periods=200, freq='1H')
            
            # Create realistic BTC price movement
            base_price = 65000
            price_data = []
            current_price = base_price
            
            for i in range(len(dates)):
                # Add some trend and volatility
                trend = 0.0001 * i  # Slight upward trend
                volatility = np.random.normal(0, 0.01)  # 1% volatility
                
                current_price = current_price * (1 + trend + volatility)
                
                # Create OHLC data
                open_price = current_price
                high_price = open_price * (1 + abs(np.random.normal(0, 0.005)))
                low_price = open_price * (1 - abs(np.random.normal(0, 0.005)))
                close_price = open_price + np.random.normal(0, open_price * 0.003)
                
                # Ensure OHLC relationships
                high_price = max(high_price, open_price, close_price)
                low_price = min(low_price, open_price, close_price)
                
                price_data.append({
                    'timestamp': dates[i],
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': np.random.uniform(4000, 8000)
                })
                
                current_price = close_price
            
            df = pd.DataFrame(price_data)
            print(f"✅ Generated {len(df)} price points")
            
            # Generate signals at specific points
            signal_points = []
            
            # Test different scenarios
            test_scenarios = [
                {'index': 50, 'signal': 'BUY', 'scenario': 'Strong Bull Market'},
                {'index': 100, 'signal': 'SELL', 'scenario': 'Bear Reversal'},
                {'index': 150, 'signal': 'BUY', 'scenario': 'Recovery Signal'}
            ]
            
            print("\n🎯 Generating ML signals...")
            
            for scenario in test_scenarios:
                idx = scenario['index']
                signal_type = scenario['signal']
                scenario_name = scenario['scenario']
                
                # Get market data up to this point
                market_data = df.iloc[:idx+1].copy()
                entry_price = float(market_data['close'].iloc[-1])
                
                print(f"\n📊 {scenario_name} at ${entry_price:,.2f}")
                
                # Get ML-based SL/TP
                sl_tp_result = sl_tp_manager.get_optimal_sl_tp(market_data, entry_price, signal_type)
                
                if sl_tp_result:
                    signal_points.append({
                        'timestamp': market_data['timestamp'].iloc[-1],
                        'entry_price': entry_price,
                        'signal': signal_type,
                        'scenario': scenario_name,
                        'stop_loss': sl_tp_result['sl_result']['sl_price'],
                        'take_profit': sl_tp_result['tp_result']['tp_price'],
                        'sl_confidence': sl_tp_result['sl_result']['confidence'],
                        'tp_confidence': sl_tp_result['tp_result']['confidence'],
                        'risk_reward': sl_tp_result['final_risk_reward'],
                        'system_status': sl_tp_result['system_status']
                    })
                    
                    print(f"   🎯 Entry: ${entry_price:,.2f}")
                    print(f"   🛡️ Stop Loss: ${sl_tp_result['sl_result']['sl_price']:,.2f} ({sl_tp_result['sl_result']['confidence']:.1f}%)")
                    print(f"   💰 Take Profit: ${sl_tp_result['tp_result']['tp_price']:,.2f} ({sl_tp_result['tp_result']['confidence']:.1f}%)")
                    print(f"   📈 Risk-Reward: 1:{sl_tp_result['final_risk_reward']:.2f}")
                    print(f"   🤖 Status: {sl_tp_result['system_status']}")
                else:
                    print(f"   ❌ Failed to generate SL/TP for {scenario_name}")
            
            # Create the chart
            print(f"\n📊 Creating interactive chart with {len(signal_points)} signals...")
            
            fig = make_subplots(
                rows=2, cols=1,
                shared_xaxes=True,
                vertical_spacing=0.1,
                subplot_titles=('BTC/USDT Price with ML Signals', 'Volume'),
                row_heights=[0.8, 0.2]
            )
            
            # Add candlestick chart
            fig.add_trace(
                go.Candlestick(
                    x=df['timestamp'],
                    open=df['open'],
                    high=df['high'],
                    low=df['low'],
                    close=df['close'],
                    name='BTC/USDT',
                    increasing_line_color='#00ff88',
                    decreasing_line_color='#ff4444'
                ),
                row=1, col=1
            )
            
            # Add volume
            fig.add_trace(
                go.Bar(
                    x=df['timestamp'],
                    y=df['volume'],
                    name='Volume',
                    marker_color='rgba(158,202,225,0.6)',
                    showlegend=False
                ),
                row=2, col=1
            )
            
            # Add signal markers
            colors = {'BUY': '#00ff88', 'SELL': '#ff4444'}
            
            for i, signal in enumerate(signal_points):
                color = colors[signal['signal']]
                
                # Entry point
                fig.add_trace(
                    go.Scatter(
                        x=[signal['timestamp']],
                        y=[signal['entry_price']],
                        mode='markers',
                        marker=dict(
                            symbol='circle',
                            size=15,
                            color=color,
                            line=dict(width=3, color='white')
                        ),
                        name=f"{signal['signal']} Entry",
                        hovertemplate=f"<b>{signal['scenario']}</b><br>" +
                                    f"Signal: {signal['signal']}<br>" +
                                    f"Entry: ${signal['entry_price']:,.2f}<br>" +
                                    f"Status: {signal['system_status']}<br>" +
                                    f"Risk-Reward: 1:{signal['risk_reward']:.2f}<extra></extra>",
                        showlegend=True if i == 0 else False
                    ),
                    row=1, col=1
                )
                
                # Stop Loss line
                fig.add_trace(
                    go.Scatter(
                        x=[signal['timestamp'], signal['timestamp'] + timedelta(hours=24)],
                        y=[signal['stop_loss'], signal['stop_loss']],
                        mode='lines',
                        line=dict(color='red', width=2, dash='dash'),
                        name=f"Stop Loss ({signal['sl_confidence']:.0f}%)",
                        hovertemplate=f"<b>Stop Loss</b><br>" +
                                    f"Price: ${signal['stop_loss']:,.2f}<br>" +
                                    f"Confidence: {signal['sl_confidence']:.1f}%<extra></extra>",
                        showlegend=True if i == 0 else False
                    ),
                    row=1, col=1
                )
                
                # Take Profit line
                fig.add_trace(
                    go.Scatter(
                        x=[signal['timestamp'], signal['timestamp'] + timedelta(hours=24)],
                        y=[signal['take_profit'], signal['take_profit']],
                        mode='lines',
                        line=dict(color='green', width=2, dash='dash'),
                        name=f"Take Profit ({signal['tp_confidence']:.0f}%)",
                        hovertemplate=f"<b>Take Profit</b><br>" +
                                    f"Price: ${signal['take_profit']:,.2f}<br>" +
                                    f"Confidence: {signal['tp_confidence']:.1f}%<extra></extra>",
                        showlegend=True if i == 0 else False
                    ),
                    row=1, col=1
                )
                
                # Add text annotations
                fig.add_annotation(
                    x=signal['timestamp'],
                    y=signal['entry_price'],
                    text=f"{signal['signal']}<br>1:{signal['risk_reward']:.1f}",
                    showarrow=True,
                    arrowhead=2,
                    arrowsize=1,
                    arrowwidth=2,
                    arrowcolor=color,
                    ax=0,
                    ay=-40 if signal['signal'] == 'BUY' else 40,
                    font=dict(size=10, color=color),
                    bgcolor='rgba(255,255,255,0.8)',
                    bordercolor=color,
                    borderwidth=1
                )
            
            # Update layout
            fig.update_layout(
                title={
                    'text': '🤖 DeepTrade ML System - Entry, SL & TP Visualization',
                    'x': 0.5,
                    'font': {'size': 20}
                },
                xaxis_title='Time',
                yaxis_title='Price (USDT)',
                template='plotly_dark',
                height=800,
                showlegend=True,
                legend=dict(
                    yanchor="top",
                    y=0.99,
                    xanchor="left",
                    x=0.01
                )
            )
            
            # Remove range slider for cleaner look
            fig.update_layout(xaxis_rangeslider_visible=False)
            
            # Save the chart
            chart_filename = f"ml_signals_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            fig.write_html(chart_filename)
            
            print(f"✅ Chart saved as: {chart_filename}")
            print(f"📊 Chart contains {len(signal_points)} ML signals with SL/TP levels")
            
            # Display summary
            print(f"\n🏆 CHART SUMMARY:")
            print("=" * 40)
            
            for signal in signal_points:
                print(f"📍 {signal['scenario']}:")
                print(f"   🎯 {signal['signal']} at ${signal['entry_price']:,.2f}")
                print(f"   🛡️ SL: ${signal['stop_loss']:,.2f} ({signal['sl_confidence']:.1f}%)")
                print(f"   💰 TP: ${signal['take_profit']:,.2f} ({signal['tp_confidence']:.1f}%)")
                print(f"   📈 R:R: 1:{signal['risk_reward']:.2f}")
                print(f"   🤖 Status: {signal['system_status']}")
                print()
            
            return {
                'success': True,
                'chart_file': chart_filename,
                'signals_generated': len(signal_points),
                'signals': signal_points
            }
            
    except Exception as e:
        print(f"❌ Chart visualization failed: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    print("🚀 DeepTrade Chart Visualization Test")
    print("=" * 45)
    
    results = create_test_chart_with_signals()
    
    if results and results['success']:
        print(f"\n🎉 CHART VISUALIZATION COMPLETE!")
        print(f"   📊 Chart File: {results['chart_file']}")
        print(f"   🎯 Signals Generated: {results['signals_generated']}")
        print(f"   ✅ Ready to view in browser!")
    else:
        error = results.get('error', 'Unknown error') if results else 'Test failed'
        print(f"\n❌ Chart visualization failed: {error}")
