#!/usr/bin/env python3
"""
Test script to verify the cleaned admin route works correctly
This tests the admin route after removing all duplicate code
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_cleaned_admin_route():
    """Test the cleaned admin route deletion"""
    print("🧪 Testing cleaned admin route deletion...")
    
    try:
        from app import create_app, db
        from app.models.user import User
        from app.models.subscription import Subscription, SubscriptionTier
        from app.models.user_tier_status import UserTierStatus
        from app.models.security_log import LoginAttempt, SecurityLog
        
        app = create_app()
        
        with app.app_context():
            # Create test user with comprehensive data
            import uuid
            unique_id = str(uuid.uuid4())[:8]
            test_user = User(
                email=f"cleaned.admin.{unique_id}@example.com",
                full_name="Cleaned Admin Test User",
                password="testpassword123"
            )
            
            db.session.add(test_user)
            db.session.flush()
            
            user_id = test_user.id
            print(f"✅ Created test user: {user_id}")
            
            # Create subscription
            subscription = Subscription(user_id=user_id, tier=SubscriptionTier.TIER_1)
            db.session.add(subscription)
            
            # Create tier status
            tier_status = UserTierStatus(user_id=user_id, tier_1=True, tier_2=False, tier_3=False)
            db.session.add(tier_status)
            
            # Create login attempt
            login_attempt = LoginAttempt(ip_address="127.0.0.1", user_agent="Test Browser", user_id=user_id)
            login_attempt.mark_success()
            db.session.add(login_attempt)
            
            # Create security log
            security_log = SecurityLog(user_id=user_id, event_type="test_event", ip_address="127.0.0.1")
            db.session.add(security_log)
            
            db.session.commit()
            print("✅ Created comprehensive test data")
            
            # Verify data exists before deletion
            print("\n📊 Before deletion:")
            user_count = User.query.filter_by(id=user_id).count()
            sub_count = Subscription.query.filter_by(user_id=user_id).count()
            tier_count = UserTierStatus.query.filter_by(user_id=user_id).count()
            login_count = LoginAttempt.query.filter_by(user_id=user_id).count()
            security_count = SecurityLog.query.filter_by(user_id=user_id).count()
            
            print(f"   Users: {user_count}")
            print(f"   Subscriptions: {sub_count}")
            print(f"   Tier status: {tier_count}")
            print(f"   Login attempts: {login_count}")
            print(f"   Security logs: {security_count}")
            
            # Now test the cleaned comprehensive deletion logic
            print(f"\n🔄 Testing cleaned comprehensive deletion...")
            
            # Import all models (same as in admin route)
            from app.models.security_log import LoginAttempt, SecurityLog, APICredential
            from app.models.subscription import Subscription
            from app.models.payment import Payment
            from app.models.trade import Trade, TradingSession
            from app.models.fee_calculation import FeeCalculation
            from app.models.user import User2FABackupCode, User2FAEmailCode
            from app.models.admin import CouponUsage
            from app.models.referral import Referral, ReferralEarning, ReferrerProfile
            from app.models.solana_payment import SolanaPayment, MembershipBilling
            from app.models.paper_trading import PaperTradingAccount, PaperTrade, PaperTradingSession, PaperBalanceSnapshot
            from app.models.user_balance_tracker import UserBalanceTracker, BalanceSnapshot
            from app.models.user_tier_status import UserTierStatus
            
            # Execute the EXACT deletion sequence from the cleaned admin route
            
            # 1. Delete 2FA related data
            User2FABackupCode.query.filter_by(user_id=user_id).delete()
            User2FAEmailCode.query.filter_by(user_id=user_id).delete()

            # 2. Delete security and login data
            LoginAttempt.query.filter_by(user_id=user_id).delete()
            SecurityLog.query.filter_by(user_id=user_id).delete()

            # 3. Delete API credentials
            APICredential.query.filter_by(user_id=user_id).delete()

            # 4. Delete trading data
            Trade.query.filter_by(user_id=user_id).delete()
            TradingSession.query.filter_by(user_id=user_id).delete()

            # 5. Delete fee calculations
            FeeCalculation.query.filter_by(user_id=user_id).delete()

            # 6. Delete coupon usage
            CouponUsage.query.filter_by(user_id=user_id).delete()

            # 7. Delete referral data (handle complex relationships)
            user_referrals = Referral.query.filter_by(referrer_id=user_id).all()
            for referral in user_referrals:
                earnings = ReferralEarning.query.filter_by(referral_id=referral.id).all()
                for earning in earnings:
                    db.session.delete(earning)
            
            Referral.query.filter_by(referrer_id=user_id).delete()
            Referral.query.filter_by(referee_id=user_id).delete()
            ReferrerProfile.query.filter_by(user_id=user_id).delete()

            # 8. Delete Solana payment data
            SolanaPayment.query.filter_by(user_id=user_id).delete()
            MembershipBilling.query.filter_by(user_id=user_id).delete()

            # 9. Delete payments
            Payment.query.filter_by(user_id=user_id).delete()

            # 10. Delete subscriptions
            Subscription.query.filter_by(user_id=user_id).delete()
            
            # Handle remaining models
            # Paper trading cleanup
            paper_accounts = PaperTradingAccount.query.filter_by(user_id=user_id).all()
            for account in paper_accounts:
                PaperBalanceSnapshot.query.filter_by(paper_account_id=account.id).delete()
            PaperTrade.query.filter_by(user_id=user_id).delete()
            PaperTradingSession.query.filter_by(user_id=user_id).delete()
            PaperTradingAccount.query.filter_by(user_id=user_id).delete()
            
            # Balance tracking cleanup
            balance_trackers = UserBalanceTracker.query.filter_by(user_id=user_id).all()
            for tracker in balance_trackers:
                BalanceSnapshot.query.filter_by(tracker_id=tracker.id).delete()
            UserBalanceTracker.query.filter_by(user_id=user_id).delete()
            
            # Tier status cleanup
            UserTierStatus.query.filter_by(user_id=user_id).delete()
            
            # Finally delete the user using raw SQL
            from sqlalchemy import text
            result = db.session.execute(text(
                "DELETE FROM users WHERE id = :user_id"
            ), {"user_id": user_id})
            
            # Commit all changes
            db.session.commit()
            print("   ✅ All deletion steps completed successfully")
            
            # Verify complete deletion
            print("\n🔍 After deletion:")
            user_count = User.query.filter_by(id=user_id).count()
            sub_count = Subscription.query.filter_by(user_id=user_id).count()
            tier_count = UserTierStatus.query.filter_by(user_id=user_id).count()
            login_count = LoginAttempt.query.filter_by(user_id=user_id).count()
            security_count = SecurityLog.query.filter_by(user_id=user_id).count()
            
            print(f"   Users: {user_count}")
            print(f"   Subscriptions: {sub_count}")
            print(f"   Tier status: {tier_count}")
            print(f"   Login attempts: {login_count}")
            print(f"   Security logs: {security_count}")
            
            if user_count == 0 and sub_count == 0 and tier_count == 0 and login_count == 0 and security_count == 0:
                print("   ✅ Complete deletion successful!")
                return True
            else:
                print("   ❌ Deletion incomplete!")
                return False
                
    except Exception as e:
        print(f"\n❌ Cleaned admin route test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 Starting cleaned admin route test...")
    print("=" * 60)
    
    success = test_cleaned_admin_route()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 CLEANED ADMIN ROUTE TEST PASSED!")
        print("   ✅ No duplicate code conflicts")
        print("   ✅ All imports work correctly")
        print("   ✅ Comprehensive deletion successful")
        print("   ✅ Ready for web testing!")
    else:
        print("💥 CLEANED ADMIN ROUTE TEST FAILED!")
        print("   ❌ Issues still need to be fixed")
        
    print("=" * 60)
