#!/usr/bin/env python3
"""
Test script for IP Access Logs Clear functionality
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_clear_logs_button():
    """Test Clear Logs button implementation"""
    print("Testing Clear Logs Button Implementation...")
    
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for clear logs button
        print("  1. Testing clear logs button:")
        
        if 'id="clear-ip-logs-btn"' in content and 'Clear Logs' in content:
            print("     [PASS] Clear Logs button found")
        else:
            print("     [FAIL] Clear Logs button missing")
            return False
        
        # Test 2: Check button styling
        print("  2. Testing button styling:")
        
        button_styles = [
            'bg-orange-600 hover:bg-orange-700',
            'dark:bg-orange-500 dark:hover:bg-orange-600'
        ]
        
        for style in button_styles:
            if style in content:
                print(f"     [PASS] {style} found")
            else:
                print(f"     [FAIL] {style} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_clear_logs_modal():
    """Test Clear Logs modal implementation"""
    print("Testing Clear Logs Modal Implementation...")
    
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for modal function
        print("  1. Testing modal function:")
        
        if 'showClearIPLogsModal' in content:
            print("     [PASS] showClearIPLogsModal function found")
        else:
            print("     [FAIL] showClearIPLogsModal function missing")
            return False
        
        # Test 2: Check for modal elements
        print("  2. Testing modal elements:")
        
        modal_elements = [
            'Clear IP Access Logs',
            'clear-logs-period',
            'clear-logs-confirm-checkbox',
            'executeClearIPLogs'
        ]
        
        for element in modal_elements:
            if element in content:
                print(f"     [PASS] {element} found")
            else:
                print(f"     [FAIL] {element} missing")
                return False
        
        # Test 3: Check for time period options
        print("  3. Testing time period options:")
        
        period_options = [
            '7 days',
            '30 days',
            '90 days',
            '6 months',
            '1 year',
            'All logs (Use with caution)'
        ]
        
        for option in period_options:
            if option in content:
                print(f"     [PASS] {option} found")
            else:
                print(f"     [FAIL] {option} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_clear_logs_javascript():
    """Test Clear Logs JavaScript functionality"""
    print("Testing Clear Logs JavaScript Functionality...")
    
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for execute function
        print("  1. Testing execute function:")
        
        js_functions = [
            'async function executeClearIPLogs()',
            'clear-logs-period',
            'clear-logs-confirm-checkbox',
            '/api/admin/ip/clear-logs'
        ]
        
        for func in js_functions:
            if re.search(func, content):
                print(f"     [PASS] {func} found")
            else:
                print(f"     [FAIL] {func} missing")
                return False
        
        # Test 2: Check for event listener
        print("  2. Testing event listener:")
        
        if 'clear-ip-logs-btn.*addEventListener.*showClearIPLogsModal' in content or 'getElementById(\'clear-ip-logs-btn\').addEventListener' in content:
            print("     [PASS] Event listener found")
        else:
            print("     [FAIL] Event listener missing")
            return False
        
        # Test 3: Check for API call structure
        print("  3. Testing API call structure:")
        
        api_elements = [
            'days_to_keep.*parseInt',
            'Authorization.*Bearer',
            'Content-Type.*application/json',
            'loadIPAccessLogs.*currentIPLogsPerPage'
        ]
        
        for element in api_elements:
            if re.search(element, content):
                print(f"     [PASS] {element} found")
            else:
                print(f"     [FAIL] {element} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_clear_logs_backend():
    """Test Clear Logs backend endpoint"""
    print("Testing Clear Logs Backend Endpoint...")
    
    try:
        with open('app/api/admin_routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for endpoint definition
        print("  1. Testing endpoint definition:")
        
        endpoint_patterns = [
            "@admin_bp.route('/ip/clear-logs', methods=['POST'])",
            "@super_admin_required",
            "def clear_ip_access_logs():",
            "Clear IP access logs older than specified days"
        ]
        
        for pattern in endpoint_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        # Test 2: Check for functionality
        print("  2. Testing functionality:")
        
        functionality_patterns = [
            'days_to_keep = data.get',
            'cutoff_date = datetime.utcnow() - timedelta',
            'IPAccessLog.query.filter',
            'login_timestamp < cutoff_date',
            'deleted_count',
            'db.session.commit()'
        ]
        
        for pattern in functionality_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        # Test 3: Check for admin action logging
        print("  3. Testing admin action logging:")
        
        logging_patterns = [
            'AdminAction.log_action',
            'action_type.*clear_ip_logs',
            'target_type.*ip_logs',
            'deleted_count.*deleted_count'
        ]
        
        for pattern in logging_patterns:
            if re.search(pattern, content):
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_security_features():
    """Test security features of Clear Logs functionality"""
    print("Testing Security Features...")
    
    try:
        with open('app/api/admin_routes.py', 'r', encoding='utf-8') as f:
            backend_content = f.read()
        
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            frontend_content = f.read()
        
        # Test 1: Check for super admin requirement
        print("  1. Testing super admin requirement:")
        
        if '@super_admin_required' in backend_content and 'clear_ip_access_logs' in backend_content:
            print("     [PASS] Super admin requirement found")
        else:
            print("     [FAIL] Super admin requirement missing")
            return False
        
        # Test 2: Check for confirmation checkbox
        print("  2. Testing confirmation requirement:")

        if 'clear-logs-confirm-checkbox' in frontend_content and 'checked' in frontend_content:
            print("     [PASS] Confirmation checkbox validation found")
        else:
            print("     [FAIL] Confirmation checkbox validation missing")
            return False
        
        # Test 3: Check for input validation
        print("  3. Testing input validation:")
        
        validation_patterns = [
            'isinstance(days_to_keep, int)',
            'days_to_keep < 0',
            'Invalid days_to_keep parameter'
        ]
        
        for pattern in validation_patterns:
            if pattern in backend_content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        # Test 4: Check for warning message
        print("  4. Testing warning message:")
        
        if 'This action cannot be undone' in frontend_content:
            print("     [PASS] Warning message found")
        else:
            print("     [FAIL] Warning message missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def run_all_tests():
    """Run all Clear Logs tests"""
    print("CLEAR IP LOGS FUNCTIONALITY TEST")
    print("=" * 60)
    
    tests = [
        ("Clear Logs Button", test_clear_logs_button),
        ("Clear Logs Modal", test_clear_logs_modal),
        ("Clear Logs JavaScript", test_clear_logs_javascript),
        ("Clear Logs Backend", test_clear_logs_backend),
        ("Security Features", test_security_features)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"  [PASS] {test_name} completed successfully")
            else:
                failed += 1
                print(f"  [FAIL] {test_name} failed")
        except Exception as e:
            failed += 1
            print(f"  [ERROR] {test_name} error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("CLEAR LOGS TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {len(tests)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed/len(tests)*100):.1f}%")
    
    if failed == 0:
        print("\nALL TESTS PASSED! Clear Logs functionality implemented successfully.")
        print("\nImplemented Features:")
        print("- Clear Logs button in IP Address Management section")
        print("- Modal confirmation with multiple time period options")
        print("- Backend API endpoint with super admin authentication")
        print("- Comprehensive input validation and error handling")
        print("- Admin action logging for audit trail")
        print("- Safety features: confirmation checkbox and warning messages")
        return True
    else:
        print(f"\n{failed} test(s) failed. Please review implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
