#!/usr/bin/env python3
"""
Complete System Test - All Features Integration
Tests: Hourly Training, Chart Visualization, TP-Chart Sync, Legacy Cleanup
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_system():
    """Test all system components working together"""
    print("🚀 DeepTrade Complete System Test")
    print("=" * 50)
    
    results = {
        'hourly_training': False,
        'chart_visualization': False,
        'tp_chart_sync': False,
        'legacy_cleanup': False,
        'overall_success': False
    }
    
    try:
        # Import Flask app and create application context
        from app import create_app
        app = create_app()
        
        with app.app_context():
            print("✅ Flask application context created")
            
            # Test 1: Hourly Training System
            print("\n📚 Testing Hourly Training System...")
            try:
                from app.tasks.ml_training_scheduler import run_hourly_ml_training
                
                # Run a quick training test
                training_results = run_hourly_ml_training()
                
                if training_results and training_results.get('total_success'):
                    print("✅ Hourly training system working")
                    results['hourly_training'] = True
                else:
                    print("⚠️ Hourly training system has issues")
                    
            except Exception as e:
                print(f"❌ Hourly training test failed: {e}")
            
            # Test 2: Chart Visualization
            print("\n📊 Testing Chart Visualization...")
            try:
                from test_chart_visualization import create_test_chart_with_signals
                
                chart_results = create_test_chart_with_signals()
                
                if chart_results and chart_results.get('success'):
                    print(f"✅ Chart visualization working - {chart_results['signals_generated']} signals")
                    results['chart_visualization'] = True
                else:
                    print("❌ Chart visualization failed")
                    
            except Exception as e:
                print(f"❌ Chart visualization test failed: {e}")
            
            # Test 3: TP-Chart Sync
            print("\n🎯 Testing TP-Chart Sync...")
            try:
                from app.services.sl_tp_ml_predictors import SLTPMLManager
                from app.services.market_data import ml_service
                
                # Generate test market data
                dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
                test_data = []
                base_price = 65000
                
                for i, date in enumerate(dates):
                    price = base_price + np.random.normal(0, 500)
                    test_data.append({
                        'timestamp': date,
                        'open': price,
                        'high': price * 1.01,
                        'low': price * 0.99,
                        'close': price,
                        'volume': np.random.uniform(4000, 8000)
                    })
                
                df = pd.DataFrame(test_data)
                
                # Test SL/TP with chart sync
                sl_tp_manager = SLTPMLManager()
                entry_price = 65000.0
                signal = 'BUY'
                
                result = sl_tp_manager.get_optimal_sl_tp(df, entry_price, signal)
                
                if result:
                    print(f"✅ TP-Chart sync working")
                    print(f"   Entry: ${entry_price:,.2f}")
                    print(f"   SL: ${result['sl_result']['sl_price']:,.2f}")
                    print(f"   TP: ${result['tp_result']['tp_price']:,.2f}")
                    print(f"   R:R: 1:{result['final_risk_reward']:.2f}")
                    results['tp_chart_sync'] = True
                else:
                    print("❌ TP-Chart sync failed")
                    
            except Exception as e:
                print(f"❌ TP-Chart sync test failed: {e}")
            
            # Test 4: Legacy Cleanup Verification
            print("\n🧹 Testing Legacy Cleanup...")
            try:
                from app.services.trading_signals import TradingSignalGenerator
                import inspect
                
                # Check if old methods are removed
                signal_gen = TradingSignalGenerator(user_id="test", exchange_service=None)
                
                # Check if _calculate_take_profit_levels method is removed
                if hasattr(signal_gen, '_calculate_take_profit_levels'):
                    print("❌ Legacy method _calculate_take_profit_levels still exists")
                else:
                    print("✅ Legacy method _calculate_take_profit_levels removed")
                    results['legacy_cleanup'] = True
                    
            except Exception as e:
                print(f"❌ Legacy cleanup test failed: {e}")
            
            # Overall Success
            success_count = sum(results.values())
            total_tests = len(results) - 1  # Exclude overall_success
            
            results['overall_success'] = success_count >= 3  # At least 3/4 tests pass
            
            print(f"\n🏆 COMPLETE SYSTEM TEST RESULTS:")
            print("=" * 40)
            print(f"📚 Hourly Training: {'✅' if results['hourly_training'] else '❌'}")
            print(f"📊 Chart Visualization: {'✅' if results['chart_visualization'] else '❌'}")
            print(f"🎯 TP-Chart Sync: {'✅' if results['tp_chart_sync'] else '❌'}")
            print(f"🧹 Legacy Cleanup: {'✅' if results['legacy_cleanup'] else '❌'}")
            print(f"\n📈 Success Rate: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
            
            if results['overall_success']:
                print("🎉 SYSTEM STATUS: 🏆 EXCELLENT - All major features working!")
            else:
                print("⚠️ SYSTEM STATUS: PARTIAL - Some features need attention")
            
            return results
            
    except Exception as e:
        print(f"❌ Complete system test failed: {e}")
        import traceback
        traceback.print_exc()
        return results

def test_individual_components():
    """Test individual components separately"""
    print("\n🔧 Individual Component Tests")
    print("=" * 30)
    
    # Test ML Training Scheduler
    print("1. Testing ML Training Scheduler...")
    try:
        from app.tasks.ml_training_scheduler import retrain_elite_ml_models, retrain_sl_tp_ml_models
        
        print("   - Elite ML training: Available ✅")
        print("   - SL/TP ML training: Available ✅")
        
    except ImportError as e:
        print(f"   - Training scheduler import failed: {e}")
    
    # Test Chart Visualization
    print("2. Testing Chart Visualization...")
    try:
        import plotly.graph_objects as go
        print("   - Plotly available: ✅")
        
        from test_chart_visualization import create_test_chart_with_signals
        print("   - Chart test function: Available ✅")
        
    except ImportError as e:
        print(f"   - Chart visualization import failed: {e}")
    
    # Test SL/TP ML System
    print("3. Testing SL/TP ML System...")
    try:
        from app.services.sl_tp_ml_predictors import SLTPMLManager, StopLossMLPredictor, TakeProfitMLPredictor
        
        print("   - SL ML Predictor: Available ✅")
        print("   - TP ML Predictor: Available ✅")
        print("   - SL/TP Manager: Available ✅")
        
    except ImportError as e:
        print(f"   - SL/TP ML system import failed: {e}")
    
    # Test Chart Forecast Integration
    print("4. Testing Chart Forecast Integration...")
    try:
        from app.services.market_data import ml_service
        print("   - ML Service: Available ✅")
        
    except ImportError as e:
        print(f"   - Chart forecast integration failed: {e}")

if __name__ == "__main__":
    print("🚀 DeepTrade Complete System Test Suite")
    print("=" * 50)
    
    # Run individual component tests first
    test_individual_components()
    
    # Run complete system test
    results = test_complete_system()
    
    if results and results['overall_success']:
        print(f"\n🎉 ALL SYSTEMS OPERATIONAL!")
        print("✅ Ready for production deployment")
        sys.exit(0)
    else:
        print(f"\n⚠️ SYSTEM NEEDS ATTENTION")
        print("❌ Some components require fixes")
        sys.exit(1)
