#!/usr/bin/env python3
"""
Test script for admin panel container spacing
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_container_spacing():
    """Test that admin panel containers have consistent spacing"""
    print("🧪 TESTING ADMIN PANEL CONTAINER SPACING...")
    print("="*50)
    
    try:
        # Read the admin panel HTML
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find all container divs with management sections
        container_pattern = r'<!-- (.+?) Management -->\s*\n\s*<div class="([^"]*)"'
        containers = re.findall(container_pattern, content, re.MULTILINE)
        
        print("Found containers:")
        spacing_consistent = True
        
        for container_name, css_classes in containers:
            has_margin = 'mb-8' in css_classes
            print(f"  - {container_name} Management: {'✅ Has mb-8 spacing' if has_margin else '❌ Missing mb-8 spacing'}")
            
            if not has_margin and container_name not in ['Active Trading']:  # Active Trading might be different
                spacing_consistent = False
        
        # Check for symmetric spacing between IP and Coupon management
        ip_to_coupon_pattern = r'<!-- IP Management -->.*?</div>\s*\n\s*<!-- Coupons Management -->'
        ip_to_coupon_match = re.search(ip_to_coupon_pattern, content, re.DOTALL)
        
        if ip_to_coupon_match:
            # Check if there's no extra spacing div between them
            extra_spacing_pattern = r'<!-- IP Management -->.*?</div>\s*\n\s*<!-- Spacing between containers -->'
            has_extra_spacing = re.search(extra_spacing_pattern, content, re.DOTALL)
            
            if has_extra_spacing:
                print("❌ Found extra spacing div between containers")
                spacing_consistent = False
            else:
                print("✅ No extra spacing divs found - using consistent mb-8 margins")
        
        # Verify all main containers have consistent structure
        main_containers = [
            'Users Management',
            'IP Management', 
            'Coupons Management'
        ]
        
        for container in main_containers:
            pattern = f'<!-- {container} -->\s*\n\s*<div class="([^"]*)"'
            match = re.search(pattern, content)
            if match:
                classes = match.group(1)
                required_classes = ['bg-white', 'dark:bg-gray-800', 'rounded-lg', 'shadow-lg', 'p-6', 'mb-8']
                missing_classes = [cls for cls in required_classes if cls not in classes]
                
                if missing_classes:
                    print(f"❌ {container} missing classes: {missing_classes}")
                    spacing_consistent = False
                else:
                    print(f"✅ {container} has all required spacing classes")
        
        if spacing_consistent:
            print("\n🎉 CONTAINER SPACING TEST PASSED!")
            print("All containers have consistent symmetric spacing")
            return True
        else:
            print("\n❌ CONTAINER SPACING TEST FAILED!")
            print("Some containers are missing consistent spacing")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_visual_spacing():
    """Test visual spacing recommendations"""
    print("\n📐 VISUAL SPACING RECOMMENDATIONS...")
    print("="*50)
    
    print("✅ Using mb-8 (2rem/32px) for consistent container spacing")
    print("✅ Containers have symmetric gaps between them")
    print("✅ No extra spacing divs needed - using Tailwind margin classes")
    print("✅ Responsive spacing maintained across screen sizes")
    
    return True

if __name__ == "__main__":
    success = test_container_spacing()
    if success:
        test_visual_spacing()
        print("\n✅ All spacing tests completed successfully!")
    else:
        print("\n❌ Spacing tests failed!")
        sys.exit(1)
