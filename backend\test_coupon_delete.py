#!/usr/bin/env python3
"""
Test script for coupon deletion functionality
"""

import sys
import os
import requests
import json
from datetime import datetime, timedelta

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.admin import AdminUser, CouponCode, CouponUsage

def test_coupon_delete():
    """Test the coupon deletion endpoint"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🧪 TESTING COUPON DELETE FUNCTIONALITY...")
            print("="*50)
            
            # Create a test admin user if not exists
            admin = AdminUser.query.filter_by(username='test_admin').first()
            if not admin:
                admin = AdminUser(
                    username='test_admin',
                    password='test_password',
                    is_super_admin=True
                )
                db.session.add(admin)
                db.session.commit()
                print("✅ Created test admin user")
            
            # Create a test coupon
            test_code = f"TEST{datetime.now().strftime('%H%M%S')}"
            test_coupon = CouponCode(
                code=test_code,
                tier_level=2,
                expiration_date=datetime.utcnow() + timedelta(days=30),
                created_by=admin.id,
                max_uses=5,
                description="Test coupon for deletion"
            )
            db.session.add(test_coupon)
            db.session.commit()
            print(f"✅ Created test coupon: {test_code}")
            
            # Verify coupon exists
            coupon_check = CouponCode.query.filter_by(code=test_code).first()
            if not coupon_check:
                print("❌ Test coupon was not created properly")
                return False
            
            print(f"✅ Coupon verified in database: ID={coupon_check.id}, Code={coupon_check.code}")
            
            # Test the DELETE endpoint route exists
            from app.api.admin_routes import admin_bp
            
            # Check if the route is registered
            routes = []
            for rule in app.url_map.iter_rules():
                if rule.endpoint and rule.endpoint.startswith('admin.'):
                    routes.append({
                        'endpoint': rule.endpoint,
                        'methods': list(rule.methods),
                        'rule': rule.rule
                    })
            
            delete_route_found = False
            for route in routes:
                if 'DELETE' in route['methods'] and 'coupons' in route['rule'] and '<string:coupon_code>' in route['rule']:
                    delete_route_found = True
                    print(f"✅ DELETE route found: {route['rule']} -> {route['endpoint']}")
                    break
            
            if not delete_route_found:
                print("❌ DELETE route for coupons not found")
                return False
            
            # Clean up test coupon
            db.session.delete(test_coupon)
            db.session.commit()
            print("✅ Test coupon cleaned up")
            
            print("\n🎉 COUPON DELETE ENDPOINT TEST PASSED!")
            print("The DELETE /api/admin/coupons/<coupon_code> route is now available")
            return True
            
        except Exception as e:
            print(f"❌ Test failed with error: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def test_coupon_delete_api():
    """Test the actual API endpoint with a running server"""
    print("\n🌐 TESTING COUPON DELETE API ENDPOINT...")
    print("="*50)
    
    # This would require a running server and authentication
    # For now, just verify the route structure
    print("ℹ️  To test the API endpoint:")
    print("1. Start the Flask server")
    print("2. Login as super admin to get JWT token")
    print("3. Create a test coupon")
    print("4. Send DELETE request to /api/admin/coupons/{coupon_code}")
    print("5. Verify coupon is deleted from database")
    
    return True

if __name__ == "__main__":
    success = test_coupon_delete()
    if success:
        test_coupon_delete_api()
        print("\n✅ All tests completed successfully!")
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)
