#!/usr/bin/env python3
"""
Test the specific current-signal endpoint that the frontend calls
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"

def login_admin():
    """Login as admin and get token"""
    login_data = {
        "username": ADMIN_USERNAME,
        "password": ADMIN_PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/api/admin/login", json=login_data)
    
    if response.status_code == 200:
        data = response.json()
        token = data.get('access_token')
        print(f"✅ Login successful")
        return token
    else:
        print(f"❌ Login failed: {response.status_code}")
        return None

def test_current_signal_endpoint(token):
    """Test the current signal endpoint specifically"""
    print(f"\n🧪 TESTING: Current Signal Endpoint")
    print(f"URL: {BASE_URL}/api/admin/trading-bot/current-signal")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{BASE_URL}/api/admin/trading-bot/current-signal", headers=headers)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SUCCESS")
            print(f"Response data:")
            print(json.dumps(data, indent=2))
            
            # Check if it has the expected fields for frontend
            expected_fields = ['signal', 'confidence', 'current_price', 'timestamp']
            missing_fields = [field for field in expected_fields if field not in data]
            
            if missing_fields:
                print(f"⚠️  Missing expected fields: {missing_fields}")
            else:
                print(f"✅ All expected fields present")
            
            return True
        else:
            print(f"❌ FAILED")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

def main():
    print("🔍 TESTING CURRENT SIGNAL ENDPOINT")
    print(f"Testing against: {BASE_URL}")
    print(f"Timestamp: {datetime.now()}")
    print()
    
    # Login first
    token = login_admin()
    if not token:
        print("❌ Cannot proceed without admin token")
        return
    
    # Test the current signal endpoint
    success = test_current_signal_endpoint(token)
    
    if success:
        print("\n🎉 Current signal endpoint is working correctly!")
        print("The Trading Bot Monitoring should now load properly in the admin dashboard.")
    else:
        print("\n❌ Current signal endpoint has issues.")

if __name__ == "__main__":
    main()
