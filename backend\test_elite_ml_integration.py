#!/usr/bin/env python3
"""
Test Elite ML Integration
Verify that the elite ML system is properly integrated with DeepTrade
"""

import os
import sys
import requests
import pandas as pd
from datetime import datetime

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

def fetch_test_data():
    """Fetch test data for signal generation"""
    try:
        endpoint = 'https://fapi.binance.com/fapi/v1/klines'
        params = {
            'symbol': 'BTCUSDT',
            'interval': '1h',
            'limit': 1000
        }
        
        response = requests.get(endpoint, params=params)
        data = response.json()
        
        if isinstance(data, list) and len(data) > 0:
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert to proper data types
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            return df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
        else:
            return None
            
    except Exception as e:
        print(f"❌ Error fetching test data: {e}")
        return None

def test_elite_ml_service():
    """Test the elite ML service directly"""
    print("🧪 Testing Elite ML Service...")
    
    try:
        from app.services.elite_ml_service import Elite96PercentPredictor
        
        # Initialize predictor
        elite_predictor = Elite96PercentPredictor()
        
        # Test model loading
        print("📂 Testing model loading...")
        models_loaded = elite_predictor.load_models()
        
        if models_loaded:
            print("✅ Models loaded successfully")
            
            # Get model status
            status = elite_predictor.get_model_status()
            print(f"   Models available: {status['models_loaded']}")
            print(f"   Regimes: {list(status['regime_names'].values())}")
            
            # Test signal generation
            print("\n🎯 Testing signal generation...")
            test_data = fetch_test_data()
            
            if test_data is not None and len(test_data) >= 500:
                signal = elite_predictor.generate_elite_signal(test_data)
                
                if 'error' not in signal:
                    print("✅ Elite signal generated successfully:")
                    print(f"   Signal: {signal.get('signal', 'N/A')}")
                    print(f"   Confidence: {signal.get('confidence', 0):.4f}")
                    print(f"   Regime: {signal.get('regime', 'N/A')}")
                    print(f"   Model: {signal.get('model_type', 'N/A')}")
                    return True
                else:
                    print(f"❌ Signal generation failed: {signal.get('error', 'Unknown')}")
                    return False
            else:
                print("❌ Insufficient test data")
                return False
        else:
            print("❌ Failed to load models")
            return False
            
    except Exception as e:
        print(f"❌ Elite ML service test failed: {e}")
        return False

def test_trading_signals_integration():
    """Test the integration with trading signals"""
    print("\n🔗 Testing Trading Signals Integration...")
    
    try:
        from app.services.trading_signals import TradingSignalGenerator
        from app.services.market_data import BinanceMarketData

        # Create market data service for testing (same pattern as existing code)
        market_service = BinanceMarketData()

        # Test signal generation with elite ML
        signal_generator = TradingSignalGenerator("test_user", market_service)
        
        # Check if elite ML is initialized
        if hasattr(signal_generator, 'elite_predictor') and signal_generator.elite_predictor is not None:
            print("✅ Elite ML predictor initialized in trading signals")
            
            # Test the conversion method
            if hasattr(signal_generator, '_convert_elite_signal_to_trading_signal'):
                print("✅ Elite signal conversion method available")
                return True
            else:
                print("❌ Elite signal conversion method missing")
                return False
        else:
            print("⚠️  Elite ML predictor not initialized (may be disabled)")
            return True  # Not necessarily an error if disabled
            
    except Exception as e:
        print(f"❌ Trading signals integration test failed: {e}")
        return False

def test_environment_configuration():
    """Test environment configuration"""
    print("\n⚙️  Testing Environment Configuration...")
    
    try:
        # Check elite ML environment variables
        elite_ml_enabled = os.getenv('ELITE_ML_ENABLED', 'false').lower() == 'true'
        min_data_points = int(os.getenv('ELITE_ML_MIN_DATA_POINTS', '500'))
        target_accuracy = float(os.getenv('ELITE_ML_TARGET_ACCURACY', '0.96'))
        risk_reward_ratio = float(os.getenv('ELITE_ML_RISK_REWARD_RATIO', '2.0'))
        
        print(f"✅ Elite ML Enabled: {elite_ml_enabled}")
        print(f"✅ Min Data Points: {min_data_points}")
        print(f"✅ Target Accuracy: {target_accuracy*100:.1f}%")
        print(f"✅ Risk/Reward Ratio: 1:{risk_reward_ratio}")
        
        # Check confidence thresholds
        thresholds = {
            'Bull': float(os.getenv('ELITE_ML_CONFIDENCE_THRESHOLD_BULL', '0.90')),
            'Bear': float(os.getenv('ELITE_ML_CONFIDENCE_THRESHOLD_BEAR', '0.88')),
            'Sideways Low': float(os.getenv('ELITE_ML_CONFIDENCE_THRESHOLD_SIDEWAYS_LOW', '0.92')),
            'Sideways High': float(os.getenv('ELITE_ML_CONFIDENCE_THRESHOLD_SIDEWAYS_HIGH', '0.91'))
        }
        
        print("✅ Confidence Thresholds:")
        for regime, threshold in thresholds.items():
            print(f"   {regime}: {threshold*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Environment configuration test failed: {e}")
        return False

def test_model_files():
    """Test that model files exist and are accessible"""
    print("\n📁 Testing Model Files...")
    
    try:
        model_path = 'app/models/elite_ml/'
        
        if os.path.exists(model_path):
            print(f"✅ Model directory exists: {model_path}")
            
            # List model files
            model_files = [f for f in os.listdir(model_path) if f.endswith('.joblib')]
            
            if model_files:
                print(f"✅ Found {len(model_files)} model files:")
                for file in model_files:
                    print(f"   {file}")
                return True
            else:
                print("⚠️  No model files found (run training first)")
                return False
        else:
            print(f"❌ Model directory not found: {model_path}")
            return False
            
    except Exception as e:
        print(f"❌ Model files test failed: {e}")
        return False

def main():
    """Run all integration tests"""
    print("=" * 80)
    print("🧪 DEEPTRADE ELITE ML INTEGRATION TESTS")
    print("=" * 80)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Environment Configuration", test_environment_configuration),
        ("Model Files", test_model_files),
        ("Elite ML Service", test_elite_ml_service),
        ("Trading Signals Integration", test_trading_signals_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"🔍 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
                
        except Exception as e:
            print(f"💥 {test_name} test CRASHED: {e}")
            results.append((test_name, False))
        
        print()
    
    # Summary
    print("=" * 80)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print()
    print(f"🏆 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Elite ML integration is working correctly.")
        print()
        print("📋 Next Steps:")
        print("1. Start your DeepTrade backend server")
        print("2. Elite ML will automatically activate for trading signals")
        print("3. Monitor logs for 'ELITE_ML' entries")
        print("4. Test API endpoints: /api/elite-ml/status")
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
        print()
        print("🔧 Common fixes:")
        print("1. Run 'python train_elite_ml.py' to train models")
        print("2. Check .env configuration")
        print("3. Ensure all dependencies are installed")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
