#!/usr/bin/env python3
"""
Test Elite ML + Chart prediction combination
(Test BOTH_AGREE and ELITE_OVERRIDE scenarios)
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_elite_ml_with_chart():
    """Test Elite ML system when combined with chart prediction"""
    print("🤖 Elite ML + Chart Prediction Combined Test")
    print("=" * 60)
    
    try:
        # Import Flask app and create application context
        from app import create_app
        app = create_app()
        
        with app.app_context():
            # Import required services
            from app.services.market_data import BinanceMarketData
            from app.services.trading_signals import TradingSignalGenerator
            
            # Initialize services
            market_service = BinanceMarketData()
            signal_generator = TradingSignalGenerator("test_user", market_service)
            
            print("✅ Services initialized successfully")
            
            # Test different Elite ML + Chart scenarios
            scenarios = [
                # BOTH_AGREE scenarios (highest confidence)
                ("Elite BUY + Chart UP", "BUY", 96, "UP", 85),
                ("Elite SELL + Chart DOWN", "SELL", 94, "DOWN", 82),
                
                # ELITE_OVERRIDE scenarios (Elite >95%, Chart disagrees)
                ("Elite BUY + Chart DOWN", "BUY", 97, "DOWN", 78),
                ("Elite SELL + Chart UP", "SELL", 96, "UP", 80),
                
                # DISAGREEMENT_HOLD scenarios (Elite <95%, Chart disagrees)
                ("Elite BUY + Chart DOWN (Low)", "BUY", 92, "DOWN", 75),
                ("Elite SELL + Chart UP (Low)", "SELL", 89, "UP", 77),
                
                # ELITE_ONLY scenarios (Elite confident, Chart neutral)
                ("Elite BUY + Chart NEUTRAL", "BUY", 93, "NEUTRAL", 65),
                ("Elite SELL + Chart NEUTRAL", "SELL", 91, "NEUTRAL", 62)
            ]
            
            total_signals = 0
            scenario_results = []
            
            for scenario_name, elite_signal, elite_conf, chart_dir, chart_conf in scenarios:
                print(f"\n🎯 Testing {scenario_name}...")
                print("-" * 50)
                
                # Generate realistic market data
                hours = 48
                base_price = 65000
                
                # Create trend based on Elite ML signal
                if elite_signal == "BUY":
                    trend_per_hour = 0.5  # Moderate uptrend
                elif elite_signal == "SELL":
                    trend_per_hour = -0.4  # Moderate downtrend
                else:
                    trend_per_hour = 0.1  # Sideways
                
                # Create market data
                prices = []
                for i in range(hours):
                    trend_component = trend_per_hour * i
                    noise = np.random.normal(0, 200)  # $200 volatility
                    price = base_price + trend_component + noise
                    prices.append(max(price, 50000))
                
                mock_data = pd.DataFrame({
                    'timestamp': [1640995200000 + (i * 3600000) for i in range(hours)],
                    'open': prices,
                    'high': [p * (1 + np.random.uniform(0.002, 0.008)) for p in prices],
                    'low': [p * (1 - np.random.uniform(0.002, 0.008)) for p in prices],
                    'close': [p * (1 + np.random.uniform(-0.003, 0.003)) for p in prices],
                    'volume': [np.random.uniform(4000, 8000) for _ in range(hours)]
                })
                
                # Ensure OHLC relationships
                for i in range(len(mock_data)):
                    open_price = mock_data.loc[i, 'open']
                    close_price = mock_data.loc[i, 'close']
                    mock_data.loc[i, 'high'] = max(mock_data.loc[i, 'high'], open_price, close_price)
                    mock_data.loc[i, 'low'] = min(mock_data.loc[i, 'low'], open_price, close_price)
                
                current_price = float(mock_data['close'].iloc[-1])
                print(f"   💰 Final Price: ${current_price:,.0f}")
                print(f"   🤖 Elite ML: {elite_signal} ({elite_conf}%)")
                print(f"   📊 Chart: {chart_dir} ({chart_conf}%)")
                
                # Test the confirmation system
                signals_generated = 0
                test_hours = [30, 36, 42, 47]  # Test multiple times
                
                for test_hour in test_hours:
                    current_data = mock_data.iloc[:test_hour+1].copy()
                    
                    try:
                        # Create forecast data based on chart direction
                        if chart_dir == "UP":
                            forecast_change = 2.5
                        elif chart_dir == "DOWN":
                            forecast_change = -2.2
                        else:  # NEUTRAL
                            forecast_change = 0.3
                        
                        forecast_data = (forecast_change, chart_conf)
                        
                        # Mock Elite ML signal
                        elite_direction = elite_signal if elite_signal != "HOLD" else "NEUTRAL"
                        elite_mock = {
                            'signal': elite_signal,
                            'confidence': elite_conf,
                            'reason': f'High confidence {elite_signal} signal'
                        }
                        
                        # Test the confirmation logic
                        result = signal_generator._apply_confirmation_logic(
                            elite_dir=elite_direction,
                            elite_conf=elite_conf / 100.0,  # Convert to decimal
                            elite_signal=elite_mock,
                            chart_dir=chart_dir,
                            chart_conf=chart_conf / 100.0,  # Convert to decimal
                            market_data=current_data,
                            symbol="BTCUSDT",
                            forecast_data=forecast_data
                        )
                        
                        signal = result.get('signal', 'HOLD')
                        confidence = result.get('confidence', 0)
                        confirmation = result.get('confirmation', 'UNKNOWN')
                        risk_level = result.get('risk_level', 'UNKNOWN')
                        
                        if signal != 'HOLD':
                            signals_generated += 1
                            print(f"   ⚡ Hour {test_hour}: {signal} ({confidence:.1f}%) - {confirmation} [{risk_level}]")
                            break  # Only count first signal per scenario
                        
                    except Exception as e:
                        print(f"   ❌ Error at hour {test_hour}: {e}")
                
                print(f"   🎯 Signals Generated: {signals_generated}")
                
                # Store result
                scenario_results.append({
                    'scenario': scenario_name,
                    'elite_signal': elite_signal,
                    'elite_conf': elite_conf,
                    'chart_dir': chart_dir,
                    'chart_conf': chart_conf,
                    'signals': signals_generated
                })
                
                total_signals += signals_generated
            
            # Calculate results
            total_scenarios = len(scenarios)
            avg_signals_per_scenario = total_signals / total_scenarios
            
            print(f"\n🤖 ELITE ML + CHART COMBINED RESULTS:")
            print("=" * 60)
            print(f"   📊 Total Signals: {total_signals}")
            print(f"   📅 Total Scenarios: {total_scenarios}")
            print(f"   🎯 Average Signals/Scenario: {avg_signals_per_scenario:.1f}")
            
            # Analyze by confirmation type
            print(f"\n📋 Results by Confirmation Type:")
            both_agree = sum(1 for r in scenario_results if 
                           (r['elite_signal'] == 'BUY' and r['chart_dir'] == 'UP') or
                           (r['elite_signal'] == 'SELL' and r['chart_dir'] == 'DOWN'))
            
            elite_override = sum(1 for r in scenario_results if 
                               r['elite_conf'] >= 95 and
                               ((r['elite_signal'] == 'BUY' and r['chart_dir'] == 'DOWN') or
                                (r['elite_signal'] == 'SELL' and r['chart_dir'] == 'UP')))
            
            disagreement = sum(1 for r in scenario_results if 
                             r['elite_conf'] < 95 and
                             ((r['elite_signal'] == 'BUY' and r['chart_dir'] == 'DOWN') or
                              (r['elite_signal'] == 'SELL' and r['chart_dir'] == 'UP')))
            
            elite_only = sum(1 for r in scenario_results if r['chart_dir'] == 'NEUTRAL')
            
            print(f"   ✅ BOTH_AGREE scenarios: {both_agree}")
            print(f"   ⚠️ ELITE_OVERRIDE scenarios: {elite_override}")
            print(f"   🛑 DISAGREEMENT_HOLD scenarios: {disagreement}")
            print(f"   🔵 ELITE_ONLY scenarios: {elite_only}")
            
            # Compare with previous tests
            print(f"\n🔄 Frequency Comparison:")
            print(f"   📊 Legacy Only: 0.4 signals/day")
            print(f"   📊 Legacy + Chart: 1.0 signals/day")
            print(f"   📊 Elite ML + Chart: {avg_signals_per_scenario:.1f} signals/scenario")
            
            # Estimate daily frequency (assuming 2-3 Elite ML signals per day)
            estimated_daily = avg_signals_per_scenario * 2.5  # Assume 2.5 Elite ML signals/day
            print(f"   📊 Elite ML + Chart (estimated daily): {estimated_daily:.1f} signals/day")
            
            if estimated_daily > 1:
                print(f"   ✅ High frequency - Good for active trading")
            elif estimated_daily > 0.5:
                print(f"   ⚖️ Moderate frequency - Balanced trading")
            else:
                print(f"   ⚠️ Low frequency - Conservative trading")
            
            return avg_signals_per_scenario
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 0

if __name__ == "__main__":
    print("🚀 Elite ML + Chart Prediction Test")
    print("=" * 50)
    
    frequency = test_elite_ml_with_chart()
    
    if frequency > 0:
        estimated_daily = frequency * 2.5
        print(f"\n🎉 ELITE ML + CHART SUCCESS!")
        print(f"   📊 {frequency:.1f} signals per scenario")
        print(f"   📊 ~{estimated_daily:.1f} estimated signals/day")
        
        if estimated_daily >= 2:
            print(f"   ✅ Excellent frequency for active trading!")
        elif estimated_daily >= 1:
            print(f"   ✅ Good frequency for regular trading!")
        else:
            print(f"   ⚠️ Conservative but working")
    else:
        print(f"\n❌ ELITE ML + CHART NOT GENERATING SIGNALS")
