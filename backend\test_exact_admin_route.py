#!/usr/bin/env python3
"""
Test script to simulate the EXACT admin route call
This will identify the specific issue causing the UPDATE error
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_exact_admin_route_call():
    """Test the exact admin route deletion call"""
    print("🧪 Testing EXACT admin route deletion call...")
    
    try:
        from app import create_app, db
        from app.models.user import User
        from app.models.subscription import Subscription, SubscriptionTier
        from app.models.user_tier_status import UserTierStatus
        from app.models.security_log import LoginAttempt, SecurityLog
        from flask import current_app
        
        app = create_app()
        
        with app.app_context():
            # Create test user with comprehensive data
            import uuid
            unique_id = str(uuid.uuid4())[:8]
            test_user = User(
                email=f"exact.admin.{unique_id}@example.com",
                full_name="Exact Admin Test User",
                password="testpassword123"
            )
            
            db.session.add(test_user)
            db.session.flush()
            
            user_id = test_user.id
            print(f"✅ Created test user: {user_id}")
            
            # Create subscription
            subscription = Subscription(user_id=user_id, tier=SubscriptionTier.TIER_1)
            db.session.add(subscription)
            
            # Create tier status
            tier_status = UserTierStatus(user_id=user_id, tier_1=True, tier_2=False, tier_3=False)
            db.session.add(tier_status)
            
            # Create login attempt
            login_attempt = LoginAttempt(ip_address="127.0.0.1", user_agent="Test Browser", user_id=user_id)
            login_attempt.mark_success()
            db.session.add(login_attempt)
            
            # Create security log
            security_log = SecurityLog(user_id=user_id, event_type="test_event", ip_address="127.0.0.1")
            db.session.add(security_log)
            
            db.session.commit()
            print("✅ Created comprehensive test data")
            
            # Now execute the EXACT admin route deletion logic
            print(f"\n🔄 Executing EXACT admin route deletion logic...")
            
            try:
                # This is the EXACT code from the admin route
                current_app.logger.info(f"Starting comprehensive user deletion for user {user_id}")
                
                # Import all models that have foreign keys to users (with error handling)
                try:
                    from app.models.security_log import LoginAttempt, SecurityLog, APICredential
                    from app.models.subscription import Subscription
                    from app.models.payment import Payment
                    from app.models.trade import Trade, TradingSession
                    from app.models.fee_calculation import FeeCalculation
                    from app.models.user import User2FABackupCode, User2FAEmailCode
                    from app.models.admin import CouponUsage
                    from app.models.referral import Referral, ReferralEarning, ReferrerProfile
                    from app.models.solana_payment import SolanaPayment, MembershipBilling
                    print("✅ All model imports successful")
                except ImportError as e:
                    current_app.logger.error(f"Import error in comprehensive deletion: {e}")
                    print(f"❌ Import error: {e}")
                    return False
                
                # Delete in order to respect foreign key dependencies
                
                # 1. Delete 2FA related data
                backup_codes_count = User2FABackupCode.query.filter_by(user_id=user_id).count()
                if backup_codes_count > 0:
                    User2FABackupCode.query.filter_by(user_id=user_id).delete()
                    current_app.logger.info(f"Deleted {backup_codes_count} 2FA backup codes for user {user_id}")
                    print(f"   ✅ Deleted {backup_codes_count} 2FA backup codes")
                    
                email_codes_count = User2FAEmailCode.query.filter_by(user_id=user_id).count()
                if email_codes_count > 0:
                    User2FAEmailCode.query.filter_by(user_id=user_id).delete()
                    current_app.logger.info(f"Deleted {email_codes_count} 2FA email codes for user {user_id}")
                    print(f"   ✅ Deleted {email_codes_count} 2FA email codes")

                # 2. Delete security and login data
                login_attempts_count = LoginAttempt.query.filter_by(user_id=user_id).count()
                if login_attempts_count > 0:
                    LoginAttempt.query.filter_by(user_id=user_id).delete()
                    current_app.logger.info(f"Deleted {login_attempts_count} login attempts for user {user_id}")
                    print(f"   ✅ Deleted {login_attempts_count} login attempts")

                security_logs_count = SecurityLog.query.filter_by(user_id=user_id).count()
                if security_logs_count > 0:
                    SecurityLog.query.filter_by(user_id=user_id).delete()
                    current_app.logger.info(f"Deleted {security_logs_count} security logs for user {user_id}")
                    print(f"   ✅ Deleted {security_logs_count} security logs")

                # 3. Delete API credentials
                api_creds_count = APICredential.query.filter_by(user_id=user_id).count()
                if api_creds_count > 0:
                    APICredential.query.filter_by(user_id=user_id).delete()
                    current_app.logger.info(f"Deleted {api_creds_count} API credentials for user {user_id}")
                    print(f"   ✅ Deleted {api_creds_count} API credentials")

                # 4. Delete trading data
                trades_count = Trade.query.filter_by(user_id=user_id).count()
                if trades_count > 0:
                    Trade.query.filter_by(user_id=user_id).delete()
                    current_app.logger.info(f"Deleted {trades_count} trades for user {user_id}")
                    print(f"   ✅ Deleted {trades_count} trades")
                    
                sessions_count = TradingSession.query.filter_by(user_id=user_id).count()
                if sessions_count > 0:
                    TradingSession.query.filter_by(user_id=user_id).delete()
                    current_app.logger.info(f"Deleted {sessions_count} trading sessions for user {user_id}")
                    print(f"   ✅ Deleted {sessions_count} trading sessions")

                # 5. Delete fee calculations
                fees_count = FeeCalculation.query.filter_by(user_id=user_id).count()
                if fees_count > 0:
                    FeeCalculation.query.filter_by(user_id=user_id).delete()
                    current_app.logger.info(f"Deleted {fees_count} fee calculations for user {user_id}")
                    print(f"   ✅ Deleted {fees_count} fee calculations")

                # 6. Delete coupon usage
                coupon_usage_count = CouponUsage.query.filter_by(user_id=user_id).count()
                if coupon_usage_count > 0:
                    CouponUsage.query.filter_by(user_id=user_id).delete()
                    current_app.logger.info(f"Deleted {coupon_usage_count} coupon usages for user {user_id}")
                    print(f"   ✅ Deleted {coupon_usage_count} coupon usages")

                # 7. Delete referral data (handle complex relationships)
                user_referrals = Referral.query.filter_by(referrer_id=user_id).all()
                earnings_count = 0
                for referral in user_referrals:
                    earnings = ReferralEarning.query.filter_by(referral_id=referral.id).all()
                    for earning in earnings:
                        db.session.delete(earning)
                        earnings_count += 1
                if earnings_count > 0:
                    current_app.logger.info(f"Deleted {earnings_count} referral earnings for user {user_id}")
                    print(f"   ✅ Deleted {earnings_count} referral earnings")
                
                referrals_as_referrer = Referral.query.filter_by(referrer_id=user_id).count()
                if referrals_as_referrer > 0:
                    Referral.query.filter_by(referrer_id=user_id).delete()
                    current_app.logger.info(f"Deleted {referrals_as_referrer} referrals where user is referrer")
                    print(f"   ✅ Deleted {referrals_as_referrer} referrals as referrer")
                    
                referrals_as_referee = Referral.query.filter_by(referee_id=user_id).count()
                if referrals_as_referee > 0:
                    Referral.query.filter_by(referee_id=user_id).delete()
                    current_app.logger.info(f"Deleted {referrals_as_referee} referrals where user is referee")
                    print(f"   ✅ Deleted {referrals_as_referee} referrals as referee")
                    
                profile_count = ReferrerProfile.query.filter_by(user_id=user_id).count()
                if profile_count > 0:
                    ReferrerProfile.query.filter_by(user_id=user_id).delete()
                    current_app.logger.info(f"Deleted {profile_count} referrer profiles for user {user_id}")
                    print(f"   ✅ Deleted {profile_count} referrer profiles")

                # 8. Delete Solana payment data
                solana_payments_count = SolanaPayment.query.filter_by(user_id=user_id).count()
                if solana_payments_count > 0:
                    SolanaPayment.query.filter_by(user_id=user_id).delete()
                    current_app.logger.info(f"Deleted {solana_payments_count} Solana payments for user {user_id}")
                    print(f"   ✅ Deleted {solana_payments_count} Solana payments")
                    
                billing_count = MembershipBilling.query.filter_by(user_id=user_id).count()
                if billing_count > 0:
                    MembershipBilling.query.filter_by(user_id=user_id).delete()
                    current_app.logger.info(f"Deleted {billing_count} membership billings for user {user_id}")
                    print(f"   ✅ Deleted {billing_count} membership billings")

                # 9. Delete payments (must be after Solana payments due to potential references)
                payments_count = Payment.query.filter_by(user_id=user_id).count()
                if payments_count > 0:
                    Payment.query.filter_by(user_id=user_id).delete()
                    current_app.logger.info(f"Deleted {payments_count} payments for user {user_id}")
                    print(f"   ✅ Deleted {payments_count} payments")

                # 10. Delete subscriptions (must be after payments due to foreign key)
                print("   🔄 About to delete subscriptions...")
                subscriptions_count = Subscription.query.filter_by(user_id=user_id).count()
                print(f"   Found {subscriptions_count} subscriptions to delete")
                
                if subscriptions_count > 0:
                    # Try different deletion approaches
                    print("   🔄 Attempting SQLAlchemy deletion...")
                    try:
                        Subscription.query.filter_by(user_id=user_id).delete()
                        current_app.logger.info(f"Deleted {subscriptions_count} subscriptions for user {user_id}")
                        print(f"   ✅ Deleted {subscriptions_count} subscriptions")
                    except Exception as sub_error:
                        print(f"   ❌ SQLAlchemy deletion failed: {sub_error}")
                        
                        # Try raw SQL deletion
                        print("   🔄 Attempting raw SQL deletion...")
                        from sqlalchemy import text
                        result = db.session.execute(text(
                            "DELETE FROM subscriptions WHERE user_id = :user_id"
                        ), {"user_id": user_id})
                        print(f"   ✅ Raw SQL deleted {result.rowcount} subscriptions")

                # Handle remaining models
                from app.models.paper_trading import PaperTradingAccount, PaperTrade, PaperTradingSession, PaperBalanceSnapshot
                from app.models.user_balance_tracker import UserBalanceTracker, BalanceSnapshot
                from app.models.user_tier_status import UserTierStatus
                
                # Paper trading cleanup
                paper_accounts = PaperTradingAccount.query.filter_by(user_id=user_id).all()
                for account in paper_accounts:
                    PaperBalanceSnapshot.query.filter_by(paper_account_id=account.id).delete()
                PaperTrade.query.filter_by(user_id=user_id).delete()
                PaperTradingSession.query.filter_by(user_id=user_id).delete()
                PaperTradingAccount.query.filter_by(user_id=user_id).delete()
                
                # Balance tracking cleanup
                balance_trackers = UserBalanceTracker.query.filter_by(user_id=user_id).all()
                for tracker in balance_trackers:
                    BalanceSnapshot.query.filter_by(tracker_id=tracker.id).delete()
                UserBalanceTracker.query.filter_by(user_id=user_id).delete()
                
                # Tier status cleanup
                tier_status_count = UserTierStatus.query.filter_by(user_id=user_id).count()
                if tier_status_count > 0:
                    UserTierStatus.query.filter_by(user_id=user_id).delete()
                    current_app.logger.info(f"Deleted {tier_status_count} tier status records for user {user_id}")
                    print(f"   ✅ Deleted {tier_status_count} tier status records")
                
                # Finally delete the user using raw SQL to avoid SQLAlchemy relationship issues
                print("   🔄 About to delete user...")
                from sqlalchemy import text
                result = db.session.execute(text(
                    "DELETE FROM users WHERE id = :user_id"
                ), {"user_id": user_id})
                current_app.logger.info(f"Deleted user {user_id}")
                print(f"   ✅ Deleted user: {result.rowcount}")
                
                # Commit all changes
                db.session.commit()
                current_app.logger.info(f"Successfully completed comprehensive deletion for user {user_id}")
                print("   ✅ All changes committed successfully")
                
                # Verify deletion
                remaining_user = User.query.filter_by(id=user_id).first()
                if remaining_user:
                    print("   ❌ User still exists after deletion!")
                    return False
                else:
                    print("   ✅ User completely deleted!")
                    return True
                    
            except Exception as deletion_error:
                print(f"❌ Deletion error: {deletion_error}")
                import traceback
                print(f"Traceback: {traceback.format_exc()}")
                db.session.rollback()
                return False
                
    except Exception as e:
        print(f"\n❌ Exact admin route test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 Starting EXACT admin route deletion test...")
    print("=" * 70)
    
    success = test_exact_admin_route_call()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 EXACT ADMIN ROUTE TEST PASSED!")
        print("   The admin route should work on the web now.")
    else:
        print("💥 EXACT ADMIN ROUTE TEST FAILED!")
        print("   The specific issue has been identified.")
        
    print("=" * 70)
