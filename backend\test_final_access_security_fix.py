#!/usr/bin/env python3
"""
Final test to verify all .ts files have correct accessSecurity sections
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_access_security_section(lang):
    """Test that accessSecurity section exists and has all required keys in .ts file"""
    print(f"  Testing {lang.upper()}:")
    
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if accessSecurity section exists
        access_security_match = re.search(r'"accessSecurity":\s*{([^}]+)}', content, re.DOTALL)
        if not access_security_match:
            print(f"     [FAIL] accessSecurity section not found")
            return False
        
        access_security_content = access_security_match.group(1)
        
        # Required keys for accessSecurity
        required_keys = [
            'title', 'subtitle', 'totalLogins', 'successRate', 'uniqueIPs',
            'twoFactorStatus', 'enabled', 'disabled', 'currentSession',
            'currentSessionDescription', 'recentLoginActivity', 'recentLoginDescription',
            'refresh', 'ipAddress', 'timestamp', 'status', 'userAgent',
            'successful', 'failed', 'noLoginActivity', 'currentDevice',
            'location', 'sessionStart'
        ]
        
        missing_keys = []
        for key in required_keys:
            if f'"{key}"' not in access_security_content:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"     [FAIL] Missing keys: {', '.join(missing_keys)}")
            return False
        
        print(f"     [PASS] All {len(required_keys)} accessSecurity keys present")
        return True
        
    except Exception as e:
        print(f"     [ERROR] Error reading {lang} file: {str(e)}")
        return False

def test_navigation_access_security(lang):
    """Test that navigation.accessSecurity key exists in .ts file"""
    try:
        with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if navigation section exists and contains accessSecurity
        navigation_match = re.search(r'"navigation":\s*{([^}]+)}', content, re.DOTALL)
        if navigation_match:
            navigation_content = navigation_match.group(1)
            if '"accessSecurity"' in navigation_content:
                return True
        
        return False
        
    except Exception as e:
        return False

def run_final_test():
    """Run final comprehensive test"""
    print("FINAL ACCESSSECURITY TRANSLATION TEST")
    print("=" * 60)
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    navigation_passed = 0
    section_passed = 0
    total_failed = 0
    
    print("\n1. Testing Navigation AccessSecurity Keys:")
    for lang in languages:
        if test_navigation_access_security(lang):
            print(f"  {lang.upper()}: [PASS] navigation.accessSecurity exists")
            navigation_passed += 1
        else:
            print(f"  {lang.upper()}: [FAIL] navigation.accessSecurity missing")
            total_failed += 1
    
    print("\n2. Testing AccessSecurity Section Content:")
    for lang in languages:
        if test_access_security_section(lang):
            section_passed += 1
        else:
            total_failed += 1
    
    print("\n" + "=" * 60)
    print("FINAL TEST SUMMARY")
    print("=" * 60)
    print(f"Total Languages: {len(languages)}")
    print(f"Navigation Keys Passed: {navigation_passed}/{len(languages)}")
    print(f"Section Content Passed: {section_passed}/{len(languages)}")
    print(f"Total Failed Tests: {total_failed}")
    
    if total_failed == 0:
        print("\n🎉 ALL TESTS PASSED! COMPLETE SUCCESS!")
        print("\n✅ SIDEBAR FIX: All navigation.accessSecurity keys present")
        print("✅ PAGE CONTENT FIX: All accessSecurity sections complete")
        print("✅ ALL 8 LANGUAGES: English, Spanish, Portuguese, Korean, Japanese, German, French, Chinese")
        
        print("\n🎯 FINAL STEPS TO SEE THE FIX:")
        print("1. 🔄 Restart the frontend development server")
        print("2. 🧹 Clear browser cache (Ctrl+Shift+R or Cmd+Shift+R)")
        print("3. 🔄 Refresh the Access & Security page")
        
        print("\n🚀 EXPECTED RESULTS:")
        print("• Sidebar should show 'Access & Security' (not navigation.accessSecurity)")
        print("• Page content should show proper translations (not accessSecurity.title)")
        print("• All text should be in the selected language")
        
        print("\n✨ TRANSLATION ISSUE COMPLETELY RESOLVED! ✨")
        return True
    else:
        print(f"\n❌ {total_failed} test(s) failed. Please review implementation.")
        return False

if __name__ == "__main__":
    success = run_final_test()
    sys.exit(0 if success else 1)
