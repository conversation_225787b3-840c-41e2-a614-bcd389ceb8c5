#!/usr/bin/env python3
"""
Final test script to verify admin deletion works completely
This simulates the exact admin route after removing duplicate code
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_final_admin_deletion():
    """Test the final admin deletion after removing duplicate code"""
    print("🧪 Testing FINAL admin deletion (after removing duplicates)...")
    
    try:
        from app import create_app, db
        from app.models.user import User
        from app.models.subscription import Subscription, SubscriptionTier
        from app.models.user_tier_status import UserTierStatus
        from app.models.security_log import LoginAttempt, SecurityLog
        
        app = create_app()
        
        with app.app_context():
            # Create a comprehensive test user with various data
            test_user = User(
                email="<EMAIL>",
                full_name="Final Test Deletion User",
                password="testpassword123"
            )
            
            db.session.add(test_user)
            db.session.flush()  # Get the user ID
            
            user_id = test_user.id
            print(f"✅ Created comprehensive test user: {user_id}")
            
            # Add subscription
            subscription = Subscription(user_id=user_id, tier=SubscriptionTier.TIER_1)
            db.session.add(subscription)
            
            # Add tier status
            tier_status = UserTierStatus(user_id=user_id, tier_1=True, tier_2=False, tier_3=False)
            db.session.add(tier_status)
            
            # Add login attempt
            login_attempt = LoginAttempt(ip_address="127.0.0.1", user_agent="Test Browser", user_id=user_id)
            login_attempt.mark_success()
            db.session.add(login_attempt)
            
            # Add security log
            security_log = SecurityLog(user_id=user_id, event_type="test_event", ip_address="127.0.0.1")
            db.session.add(security_log)
            
            db.session.commit()
            print("✅ Added comprehensive test data")
            
            # Now test the EXACT comprehensive deletion logic from admin route
            print("\n🔄 Testing comprehensive deletion logic...")
            
            # Import all models that have foreign keys to users (with error handling)
            try:
                from app.models.security_log import LoginAttempt, SecurityLog, APICredential
                from app.models.subscription import Subscription
                from app.models.payment import Payment
                from app.models.trade import Trade, TradingSession
                from app.models.fee_calculation import FeeCalculation
                from app.models.user import User2FABackupCode, User2FAEmailCode
                from app.models.admin import CouponUsage
                from app.models.referral import Referral, ReferralEarning, ReferrerProfile
                from app.models.solana_payment import SolanaPayment, MembershipBilling
                print("✅ All comprehensive imports successful")
            except ImportError as e:
                print(f"❌ Import error in comprehensive deletion: {e}")
                return False
            
            # Delete in order to respect foreign key dependencies
            
            # 1. Delete 2FA related data
            backup_codes_count = User2FABackupCode.query.filter_by(user_id=user_id).count()
            if backup_codes_count > 0:
                User2FABackupCode.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {backup_codes_count} 2FA backup codes")
                
            email_codes_count = User2FAEmailCode.query.filter_by(user_id=user_id).count()
            if email_codes_count > 0:
                User2FAEmailCode.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {email_codes_count} 2FA email codes")

            # 2. Delete security and login data
            login_attempts_count = LoginAttempt.query.filter_by(user_id=user_id).count()
            if login_attempts_count > 0:
                LoginAttempt.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {login_attempts_count} login attempts")

            security_logs_count = SecurityLog.query.filter_by(user_id=user_id).count()
            if security_logs_count > 0:
                SecurityLog.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {security_logs_count} security logs")

            # 3. Delete API credentials
            api_creds_count = APICredential.query.filter_by(user_id=user_id).count()
            if api_creds_count > 0:
                APICredential.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {api_creds_count} API credentials")

            # 4. Delete trading data
            trades_count = Trade.query.filter_by(user_id=user_id).count()
            if trades_count > 0:
                Trade.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {trades_count} trades")
                
            sessions_count = TradingSession.query.filter_by(user_id=user_id).count()
            if sessions_count > 0:
                TradingSession.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {sessions_count} trading sessions")

            # 5. Delete fee calculations
            fees_count = FeeCalculation.query.filter_by(user_id=user_id).count()
            if fees_count > 0:
                FeeCalculation.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {fees_count} fee calculations")

            # 6. Delete coupon usage
            coupon_usage_count = CouponUsage.query.filter_by(user_id=user_id).count()
            if coupon_usage_count > 0:
                CouponUsage.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {coupon_usage_count} coupon usages")

            # 7. Delete referral data (handle complex relationships)
            user_referrals = Referral.query.filter_by(referrer_id=user_id).all()
            earnings_count = 0
            for referral in user_referrals:
                earnings = ReferralEarning.query.filter_by(referral_id=referral.id).all()
                for earning in earnings:
                    db.session.delete(earning)
                    earnings_count += 1
            if earnings_count > 0:
                print(f"   ✅ Deleted {earnings_count} referral earnings")
            
            referrals_as_referrer = Referral.query.filter_by(referrer_id=user_id).count()
            if referrals_as_referrer > 0:
                Referral.query.filter_by(referrer_id=user_id).delete()
                print(f"   ✅ Deleted {referrals_as_referrer} referrals as referrer")
                
            referrals_as_referee = Referral.query.filter_by(referee_id=user_id).count()
            if referrals_as_referee > 0:
                Referral.query.filter_by(referee_id=user_id).delete()
                print(f"   ✅ Deleted {referrals_as_referee} referrals as referee")
                
            profile_count = ReferrerProfile.query.filter_by(user_id=user_id).count()
            if profile_count > 0:
                ReferrerProfile.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {profile_count} referrer profiles")

            # 8. Delete Solana payment data
            solana_payments_count = SolanaPayment.query.filter_by(user_id=user_id).count()
            if solana_payments_count > 0:
                SolanaPayment.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {solana_payments_count} Solana payments")
                
            billing_count = MembershipBilling.query.filter_by(user_id=user_id).count()
            if billing_count > 0:
                MembershipBilling.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {billing_count} membership billings")

            # 9. Delete payments (must be after Solana payments due to potential references)
            payments_count = Payment.query.filter_by(user_id=user_id).count()
            if payments_count > 0:
                Payment.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {payments_count} payments")

            # 10. Delete subscriptions (must be after payments due to foreign key)
            subscriptions_count = Subscription.query.filter_by(user_id=user_id).count()
            if subscriptions_count > 0:
                Subscription.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {subscriptions_count} subscriptions")
            
            # Handle remaining models (paper trading, balance tracking, tier status)
            from app.models.paper_trading import PaperTradingAccount, PaperTrade, PaperTradingSession, PaperBalanceSnapshot
            from app.models.user_balance_tracker import UserBalanceTracker, BalanceSnapshot
            from app.models.user_tier_status import UserTierStatus
            
            # Paper trading cleanup
            paper_accounts = PaperTradingAccount.query.filter_by(user_id=user_id).all()
            for account in paper_accounts:
                PaperBalanceSnapshot.query.filter_by(paper_account_id=account.id).delete()
            PaperTrade.query.filter_by(user_id=user_id).delete()
            PaperTradingSession.query.filter_by(user_id=user_id).delete()
            PaperTradingAccount.query.filter_by(user_id=user_id).delete()
            
            # Balance tracking cleanup
            balance_trackers = UserBalanceTracker.query.filter_by(user_id=user_id).all()
            for tracker in balance_trackers:
                BalanceSnapshot.query.filter_by(tracker_id=tracker.id).delete()
            UserBalanceTracker.query.filter_by(user_id=user_id).delete()
            
            # Tier status cleanup
            tier_status_count = UserTierStatus.query.filter_by(user_id=user_id).count()
            if tier_status_count > 0:
                UserTierStatus.query.filter_by(user_id=user_id).delete()
                print(f"   ✅ Deleted {tier_status_count} tier status records")
            
            # Finally delete the user
            user_count = User.query.filter_by(id=user_id).count()
            if user_count > 0:
                User.query.filter_by(id=user_id).delete()
                print(f"   ✅ Deleted user")
            
            db.session.commit()
            
            # Verify complete deletion
            remaining_user = User.query.filter_by(id=user_id).first()
            if remaining_user:
                print("   ❌ User still exists after deletion!")
                return False
            else:
                print("   ✅ User completely deleted!")
                return True
                
    except Exception as e:
        print(f"\n❌ Final admin deletion test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 Starting FINAL admin deletion test...")
    print("=" * 60)
    
    success = test_final_admin_deletion()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 FINAL ADMIN DELETION TEST PASSED!")
        print("   ✅ No duplicate code conflicts")
        print("   ✅ All imports work correctly")
        print("   ✅ Comprehensive deletion successful")
        print("   ✅ Ready for web testing!")
    else:
        print("💥 FINAL ADMIN DELETION TEST FAILED!")
        print("   ❌ Issues still need to be fixed")
        
    print("=" * 60)
