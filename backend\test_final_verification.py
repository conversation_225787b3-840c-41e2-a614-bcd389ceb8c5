#!/usr/bin/env python3
"""
Final verification test for all DeepTrade admin panel enhancements
"""

import sys
import os
import re
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_coupon_delete_implementation():
    """Test coupon delete endpoint implementation"""
    print("Testing Coupon Delete Endpoint...")
    
    try:
        with open('app/api/admin_routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for DELETE route
        if "@admin_bp.route('/coupons/<string:coupon_code>', methods=['DELETE'])" in content:
            print("  [PASS] DELETE route for coupons exists")
        else:
            print("  [FAIL] DELETE route for coupons missing")
            return False
        
        # Check for delete function
        if "def delete_coupon(coupon_code):" in content:
            print("  [PASS] delete_coupon function exists")
        else:
            print("  [FAIL] delete_coupon function missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_container_spacing():
    """Test container spacing implementation"""
    print("Testing Container Spacing...")
    
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for mb-8 classes in containers
        mb8_count = len(re.findall(r'mb-8', content))
        if mb8_count >= 3:  # Should have at least 3 containers with mb-8
            print(f"  [PASS] Found {mb8_count} containers with mb-8 spacing")
        else:
            print(f"  [FAIL] Only found {mb8_count} containers with mb-8 spacing")
            return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_pagination_implementation():
    """Test pagination implementation"""
    print("Testing Pagination Implementation...")
    
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for pagination variables
        pagination_vars = ['currentUsersPage', 'currentCouponsPage', 'currentIPLogsPage']
        for var in pagination_vars:
            if var in content:
                print(f"  [PASS] {var} variable found")
            else:
                print(f"  [FAIL] {var} variable missing")
                return False
        
        # Check for pagination controls
        pagination_controls = ['users-per-page', 'coupons-per-page', 'users-prev-btn', 'users-next-btn']
        for control in pagination_controls:
            if f'id="{control}"' in content:
                print(f"  [PASS] {control} control found")
            else:
                print(f"  [FAIL] {control} control missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_container_width_optimization():
    """Test container width optimization"""
    print("Testing Container Width Optimization...")
    
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for optimal width containers
        if 'id="admin-dashboard" class="max-w-7xl mx-auto' in content:
            print("  [PASS] Main dashboard uses optimal max-width constraint")
        else:
            print("  [FAIL] Main dashboard not using max-w-7xl constraint")
            return False
        
        # Check for responsive table headers
        responsive_headers = len(re.findall(r'px-2 sm:px-4 py-3', content))
        if responsive_headers >= 5:
            print(f"  [PASS] Found {responsive_headers} responsive table headers")
        else:
            print(f"  [FAIL] Only found {responsive_headers} responsive table headers")
            return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_modal_theme_fixes():
    """Test modal theme fixes"""
    print("Testing Modal Theme Fixes...")
    
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for dark theme text colors
        dark_theme_patterns = [
            'text-gray-900 dark:text-gray-100',
            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
            'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
        ]
        
        for pattern in dark_theme_patterns:
            if pattern in content:
                print(f"  [PASS] Dark theme pattern found: {pattern[:30]}...")
            else:
                print(f"  [FAIL] Dark theme pattern missing: {pattern[:30]}...")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_access_logs_integration():
    """Test access logs integration"""
    print("Testing Access Logs Integration...")
    
    try:
        # Check backend endpoint
        with open('app/api/user_routes.py', 'r', encoding='utf-8') as f:
            backend_content = f.read()
        
        if "/security/access-logs" in backend_content and "get_user_access_logs" in backend_content:
            print("  [PASS] Backend access logs endpoint exists")
        else:
            print("  [FAIL] Backend access logs endpoint missing")
            return False
        
        # Check frontend integration
        try:
            with open('../frontend/src/pages/AccessSecurity.tsx', 'r', encoding='utf-8') as f:
                frontend_content = f.read()
            
            if "/api/users/security/access-logs?limit=5" in frontend_content:
                print("  [PASS] Frontend calls endpoint with limit=5")
            else:
                print("  [FAIL] Frontend not calling endpoint with limit=5")
                return False
                
        except FileNotFoundError:
            print("  [WARN] Frontend file not found, skipping frontend test")
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def run_all_tests():
    """Run all verification tests"""
    print("DEEPTRADE ADMIN PANEL ENHANCEMENTS - FINAL VERIFICATION")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Coupon Delete Endpoint", test_coupon_delete_implementation),
        ("Container Spacing", test_container_spacing),
        ("Pagination Implementation", test_pagination_implementation),
        ("Container Width Optimization", test_container_width_optimization),
        ("Modal Theme Fixes", test_modal_theme_fixes),
        ("Access Logs Integration", test_access_logs_integration)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"  [PASS] {test_name} completed successfully")
            else:
                failed += 1
                print(f"  [FAIL] {test_name} failed")
        except Exception as e:
            failed += 1
            print(f"  [ERROR] {test_name} error: {str(e)}")
        print()
    
    print("=" * 60)
    print("FINAL VERIFICATION SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {len(tests)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed/len(tests)*100):.1f}%")
    print()
    
    if failed == 0:
        print("ALL TESTS PASSED! Implementation is ready for deployment.")
        print()
        print("Implemented Features:")
        print("- Fixed coupon delete endpoint (404 error resolved)")
        print("- Added symmetric container spacing with mb-8 margins")
        print("- Implemented pagination controls (25/50/100 items per page)")
        print("- Optimized container widths for full screen utilization")
        print("- Fixed modal themes for proper dark/light mode support")
        print("- Updated access logs to show last 5 connections")
        return True
    else:
        print(f"{failed} test(s) failed. Please review implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
