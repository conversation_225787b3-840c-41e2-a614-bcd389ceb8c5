#!/usr/bin/env python3
"""
Test script to verify frontend-backend integration for coupon redemption.
This script tests the exact same flow that the frontend uses.
"""
import requests
import json
import sys
import os
from datetime import datetime, <PERSON><PERSON><PERSON>

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.admin import Admin<PERSON>ser, CouponCode, CouponUsage
from app.models.user_tier_status import UserTierStatus
from werkzeug.security import generate_password_hash

# Configuration
BACKEND_URL = "http://127.0.0.1:5000"
FRONTEND_URL = "http://localhost:5173"

class FrontendIntegrationTest:
    def __init__(self):
        self.admin_token = None
        self.user_token = None
        self.test_user_id = None
        self.test_coupon_code = 'FRONTEND2024'
        
    def print_separator(self, title):
        """Print a separator with title."""
        print(f"\n{'='*70}")
        print(f"  {title}")
        print(f"{'='*70}")
        
    def print_subsection(self, title):
        """Print a subsection separator."""
        print(f"\n{'-'*50}")
        print(f"  {title}")
        print(f"{'-'*50}")

    def setup_test_data(self):
        """Set up test admin, user, and coupon"""
        self.print_subsection("Setting Up Test Data")
        
        app = create_app()
        with app.app_context():
            try:
                # Create super admin
                super_admin = AdminUser.query.filter_by(username='frontend_admin').first()
                if not super_admin:
                    super_admin = AdminUser(
                        username='frontend_admin',
                        password='frontend123',
                        is_super_admin=True
                    )
                    super_admin.is_active = True
                    db.session.add(super_admin)
                    db.session.flush()
                else:
                    super_admin.is_super_admin = True
                    super_admin.is_active = True
                    super_admin.password_hash = generate_password_hash('frontend123')
                
                # Create test user
                test_user = User.query.filter_by(email='<EMAIL>').first()
                if test_user:
                    # Clean up existing data
                    UserTierStatus.query.filter_by(user_id=test_user.id).delete()
                    CouponUsage.query.filter_by(user_id=test_user.id).delete()
                    db.session.delete(test_user)
                    db.session.flush()
                
                test_user = User(
                    email='<EMAIL>',
                    full_name='Frontend Test User'
                )
                test_user.set_password('frontend123')
                test_user.email_verified = True
                db.session.add(test_user)
                db.session.flush()
                
                self.test_user_id = test_user.id
                
                # Create user tier status (Tier 1 by default)
                user_tier_status = UserTierStatus(user_id=test_user.id)
                db.session.add(user_tier_status)
                
                # Delete existing test coupon
                existing_coupon = CouponCode.query.filter_by(code=self.test_coupon_code).first()
                if existing_coupon:
                    CouponUsage.query.filter_by(coupon_id=existing_coupon.id).delete()
                    db.session.delete(existing_coupon)
                    db.session.flush()
                
                # Create test coupon
                coupon = CouponCode(
                    code=self.test_coupon_code,
                    tier_level=2,
                    expiration_date=datetime.utcnow() + timedelta(days=30),
                    created_by=super_admin.id,
                    max_uses=5,
                    description='Frontend integration test coupon'
                )
                db.session.add(coupon)
                db.session.commit()
                
                print(f"✅ Created super admin: {super_admin.username}")
                print(f"✅ Created test user: {test_user.email} (ID: {test_user.id})")
                print(f"✅ Created test coupon: {coupon.code}")
                
                return True
                
            except Exception as e:
                print(f"❌ Error setting up test data: {e}")
                db.session.rollback()
                return False

    def test_user_login(self):
        """Test user authentication using frontend endpoint"""
        self.print_subsection("Testing User Login (Frontend Flow)")
        
        try:
            login_data = {
                "email": "<EMAIL>",
                "password": "frontend123"
            }
            
            # Use the same endpoint as frontend
            response = requests.post(f"{BACKEND_URL}/api/auth/login", json=login_data)
            
            if response.status_code == 200:
                data = response.json()
                self.user_token = data.get('access_token')
                print(f"✅ User login successful")
                print(f"📝 User data: {data.get('user', {}).get('full_name')}")
                print(f"📝 Token length: {len(self.user_token) if self.user_token else 0}")
                return True
            else:
                print(f"❌ User login failed: {response.status_code}")
                print(f"📝 Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ User login error: {e}")
            return False

    def test_coupon_redemption_frontend_flow(self):
        """Test coupon redemption using exact frontend flow"""
        self.print_subsection("Testing Coupon Redemption (Frontend Flow)")
        
        if not self.user_token:
            print("❌ No user token available")
            return False
        
        try:
            # Use exact same headers and data as frontend
            headers = {
                'Authorization': f'Bearer {self.user_token}',
                'Content-Type': 'application/json'
            }
            
            redemption_data = {
                'coupon_code': self.test_coupon_code
            }
            
            print(f"📡 Making request to: {BACKEND_URL}/api/trading/coupon/redeem")
            print(f"📝 Headers: {headers}")
            print(f"📝 Data: {redemption_data}")
            
            response = requests.post(
                f"{BACKEND_URL}/api/trading/coupon/redeem",
                headers=headers,
                json=redemption_data
            )
            
            print(f"📡 Response Status: {response.status_code}")
            print(f"📝 Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Coupon redemption successful!")
                print(f"📝 Response data:")
                print(json.dumps(data, indent=2))
                
                # Verify response structure matches frontend expectations
                required_fields = ['message', 'success', 'coupon', 'user_tier', 'access_token']
                missing_fields = [field for field in required_fields if field not in data]
                
                if missing_fields:
                    print(f"⚠️  Missing fields in response: {missing_fields}")
                    return False
                
                # Verify coupon structure
                coupon_data = data.get('coupon', {})
                required_coupon_fields = ['code', 'tier_level', 'description']
                missing_coupon_fields = [field for field in required_coupon_fields if field not in coupon_data]
                
                if missing_coupon_fields:
                    print(f"⚠️  Missing coupon fields: {missing_coupon_fields}")
                    return False
                
                # Verify user_tier structure
                user_tier_data = data.get('user_tier', {})
                required_tier_fields = ['previous_tier', 'new_tier', 'expires_in_days']
                missing_tier_fields = [field for field in required_tier_fields if field not in user_tier_data]
                
                if missing_tier_fields:
                    print(f"⚠️  Missing user_tier fields: {missing_tier_fields}")
                    return False
                
                print(f"✅ Response structure is correct for frontend")
                
                # Update token for verification
                self.user_token = data['access_token']
                
                return True
            else:
                print(f"❌ Coupon redemption failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"📝 Error response:")
                    print(json.dumps(error_data, indent=2))
                except:
                    print(f"📝 Raw response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Coupon redemption error: {e}")
            return False

    def test_admin_panel_display(self):
        """Test admin panel coupon display"""
        self.print_subsection("Testing Admin Panel Display")
        
        try:
            # Login as admin
            admin_login_data = {
                "username": "frontend_admin",
                "password": "frontend123"
            }
            
            response = requests.post(f"{BACKEND_URL}/api/admin/login", json=admin_login_data)
            
            if response.status_code == 200:
                admin_data = response.json()
                admin_token = admin_data.get('access_token')
                print(f"✅ Admin login successful")
                
                # Get coupons list
                headers = {
                    'Authorization': f'Bearer {admin_token}',
                    'Content-Type': 'application/json'
                }
                
                response = requests.get(f"{BACKEND_URL}/api/admin/coupons", headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    coupons = data.get('coupons', [])
                    
                    print(f"✅ Admin coupon list successful")
                    print(f"📊 Found {len(coupons)} coupons")
                    
                    # Find our test coupon
                    test_coupon = None
                    for coupon in coupons:
                        if coupon.get('code') == self.test_coupon_code:
                            test_coupon = coupon
                            break
                    
                    if test_coupon:
                        print(f"✅ Test coupon found in admin panel")
                        print(f"📝 Admin panel will display:")
                        print(f"   Code: {test_coupon.get('code')}")
                        print(f"   Tier: {test_coupon.get('tier_level')}")
                        print(f"   Used: {test_coupon.get('usage_count')}")
                        print(f"   Max Uses: {test_coupon.get('max_uses')}")
                        print(f"   Expires: {test_coupon.get('expiration_date')}")
                        print(f"   Status: {'Active' if test_coupon.get('is_active') else 'Inactive'}")
                        
                        # Check if all required fields are present
                        required_fields = ['code', 'tier_level', 'usage_count', 'max_uses', 'expiration_date']
                        missing_fields = [field for field in required_fields if field not in test_coupon]
                        
                        if missing_fields:
                            print(f"❌ Missing fields for admin display: {missing_fields}")
                            return False
                        else:
                            print(f"✅ All required fields present for admin display")
                            return True
                    else:
                        print(f"❌ Test coupon not found in admin panel")
                        return False
                else:
                    print(f"❌ Admin coupon list failed: {response.status_code}")
                    return False
            else:
                print(f"❌ Admin login failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Admin panel test error: {e}")
            return False

    def cleanup_test_data(self):
        """Clean up test data"""
        self.print_subsection("Cleaning Up Test Data")
        
        app = create_app()
        with app.app_context():
            try:
                # Delete test coupon and usage
                coupon = CouponCode.query.filter_by(code=self.test_coupon_code).first()
                if coupon:
                    CouponUsage.query.filter_by(coupon_id=coupon.id).delete()
                    db.session.delete(coupon)
                
                # Delete test user and related data
                if self.test_user_id:
                    CouponUsage.query.filter_by(user_id=self.test_user_id).delete()
                    UserTierStatus.query.filter_by(user_id=self.test_user_id).delete()
                    
                    test_user = User.query.get(self.test_user_id)
                    if test_user:
                        db.session.delete(test_user)
                
                db.session.commit()
                print(f"✅ Cleaned up test data")
                return True
                
            except Exception as e:
                print(f"❌ Error cleaning up test data: {e}")
                db.session.rollback()
                return False

    def run_integration_test(self):
        """Run comprehensive frontend integration test"""
        self.print_separator("FRONTEND-BACKEND INTEGRATION TEST")
        
        print("🚀 Testing frontend-backend integration for coupon redemption")
        print(f"📡 Backend URL: {BACKEND_URL}")
        print(f"🌐 Frontend URL: {FRONTEND_URL}")
        print("\n📝 Testing:")
        print("   • Test data setup")
        print("   • User authentication (frontend flow)")
        print("   • Coupon redemption (frontend flow)")
        print("   • Admin panel display")
        print("   • Data cleanup")
        
        # Step 1: Set up test data
        self.print_separator("STEP 1: Setup Test Data")
        setup_ok = self.setup_test_data()
        
        # Step 2: Test user login
        self.print_separator("STEP 2: User Authentication")
        user_login_ok = self.test_user_login()
        
        # Step 3: Test coupon redemption
        self.print_separator("STEP 3: Coupon Redemption")
        redemption_ok = self.test_coupon_redemption_frontend_flow()
        
        # Step 4: Test admin panel display
        self.print_separator("STEP 4: Admin Panel Display")
        admin_panel_ok = self.test_admin_panel_display()
        
        # Step 5: Cleanup
        self.print_separator("STEP 5: Cleanup")
        cleanup_ok = self.cleanup_test_data()
        
        # Results
        self.print_separator("INTEGRATION TEST RESULTS")
        
        results = {
            "Test Data Setup": setup_ok,
            "User Authentication": user_login_ok,
            "Coupon Redemption": redemption_ok,
            "Admin Panel Display": admin_panel_ok,
            "Cleanup": cleanup_ok
        }
        
        all_passed = True
        for test_name, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{status} {test_name}")
            if not passed:
                all_passed = False
        
        print(f"\n🎯 OVERALL RESULT:")
        if all_passed:
            print("🎉 ALL INTEGRATION TESTS PASSED!")
            print("✅ Frontend-backend integration is working")
            print("✅ Coupon redemption flow is functional")
            print("✅ Admin panel display is correct")
            print("✅ Ready for user testing")
            print(f"\n🌐 You can now test at: {FRONTEND_URL}/redeem-coupon")
            print(f"📝 Test coupon code: {self.test_coupon_code}")
            print(f"👤 Test user: <EMAIL> / frontend123")
        else:
            print("💥 SOME INTEGRATION TESTS FAILED")
            print("🔧 Check the error messages above for details")
        
        return all_passed

def main():
    """Main function"""
    tester = FrontendIntegrationTest()
    success = tester.run_integration_test()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
