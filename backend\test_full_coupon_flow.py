#!/usr/bin/env python3
"""
Comprehensive test script to verify the complete coupon redemption flow.
Tests backend API, database operations, and identifies frontend integration issues.
"""
import requests
import json
import sys
import os
from datetime import datetime, timed<PERSON><PERSON>

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.admin import AdminUser, CouponCode, CouponUsage
from app.models.user_tier_status import UserTierStatus
from werkzeug.security import generate_password_hash

# Configuration
BASE_URL = "http://127.0.0.1:5000"

class FullCouponFlowTest:
    def __init__(self):
        self.admin_token = None
        self.user_token = None
        self.test_user_id = None
        self.test_coupon_code = 'TESTFLOW2024'
        
    def print_separator(self, title):
        """Print a separator with title."""
        print(f"\n{'='*70}")
        print(f"  {title}")
        print(f"{'='*70}")
        
    def print_subsection(self, title):
        """Print a subsection separator."""
        print(f"\n{'-'*50}")
        print(f"  {title}")
        print(f"{'-'*50}")

    def setup_test_data(self):
        """Set up test admin, user, and coupon"""
        self.print_subsection("Setting Up Test Data")
        
        app = create_app()
        with app.app_context():
            try:
                # Create super admin
                super_admin = AdminUser.query.filter_by(username='test_admin').first()
                if not super_admin:
                    super_admin = AdminUser(
                        username='test_admin',
                        password='test123',
                        is_super_admin=True
                    )
                    super_admin.is_active = True
                    db.session.add(super_admin)
                    db.session.flush()
                else:
                    super_admin.is_super_admin = True
                    super_admin.is_active = True
                    super_admin.password_hash = generate_password_hash('test123')
                
                # Create test user
                test_user = User.query.filter_by(email='<EMAIL>').first()
                if test_user:
                    db.session.delete(test_user)
                    db.session.flush()
                
                test_user = User(
                    email='<EMAIL>',
                    full_name='Flow Test User'
                )
                test_user.set_password('testpass123')
                test_user.email_verified = True
                db.session.add(test_user)
                db.session.flush()
                
                self.test_user_id = test_user.id
                
                # Ensure user has tier status (Tier 1 by default)
                user_tier_status = UserTierStatus.query.filter_by(user_id=test_user.id).first()
                if user_tier_status:
                    db.session.delete(user_tier_status)
                    db.session.flush()
                
                user_tier_status = UserTierStatus(user_id=test_user.id)
                db.session.add(user_tier_status)
                
                # Delete existing test coupon
                existing_coupon = CouponCode.query.filter_by(code=self.test_coupon_code).first()
                if existing_coupon:
                    CouponUsage.query.filter_by(coupon_id=existing_coupon.id).delete()
                    db.session.delete(existing_coupon)
                    db.session.flush()
                
                # Create test coupon
                coupon = CouponCode(
                    code=self.test_coupon_code,
                    tier_level=2,
                    expiration_date=datetime.utcnow() + timedelta(days=30),
                    created_by=super_admin.id,
                    max_uses=5,
                    description='Test coupon for flow verification'
                )
                db.session.add(coupon)
                db.session.commit()
                
                print(f"✅ Created super admin: {super_admin.username}")
                print(f"✅ Created test user: {test_user.email} (ID: {test_user.id})")
                print(f"✅ Created test coupon: {coupon.code}")
                
                return True
                
            except Exception as e:
                print(f"❌ Error setting up test data: {e}")
                db.session.rollback()
                return False

    def test_admin_login(self):
        """Test admin authentication"""
        self.print_subsection("Testing Admin Login")
        
        try:
            login_data = {
                "username": "test_admin",
                "password": "test123"
            }
            
            response = requests.post(f"{BASE_URL}/api/admin/login", json=login_data)
            
            if response.status_code == 200:
                data = response.json()
                self.admin_token = data.get('access_token')
                print(f"✅ Admin login successful")
                return True
            else:
                print(f"❌ Admin login failed: {response.status_code}")
                print(f"📝 Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Admin login error: {e}")
            return False

    def test_user_login(self):
        """Test user authentication"""
        self.print_subsection("Testing User Login")
        
        try:
            login_data = {
                "email": "<EMAIL>",
                "password": "testpass123"
            }
            
            response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
            
            if response.status_code == 200:
                data = response.json()
                self.user_token = data.get('access_token')
                print(f"✅ User login successful")
                return True
            else:
                print(f"❌ User login failed: {response.status_code}")
                print(f"📝 Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ User login error: {e}")
            return False

    def test_coupon_list_api(self):
        """Test admin coupon list API"""
        self.print_subsection("Testing Coupon List API")
        
        if not self.admin_token:
            print("❌ No admin token available")
            return False
        
        try:
            headers = {
                'Authorization': f'Bearer {self.admin_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(f"{BASE_URL}/api/admin/coupons", headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                coupons = data.get('coupons', [])
                
                print(f"✅ Coupon list API successful")
                print(f"📊 Found {len(coupons)} coupons")
                
                # Find our test coupon
                test_coupon = None
                for coupon in coupons:
                    if coupon.get('code') == self.test_coupon_code:
                        test_coupon = coupon
                        break
                
                if test_coupon:
                    print(f"✅ Test coupon found in list")
                    print(f"📝 Coupon data structure:")
                    for key, value in test_coupon.items():
                        print(f"   {key}: {value}")
                    
                    # Check for the problematic fields
                    issues = []
                    if 'tier_level' not in test_coupon:
                        issues.append("Missing 'tier_level' field")
                    if 'usage_count' not in test_coupon:
                        issues.append("Missing 'usage_count' field")
                    if 'expiration_date' not in test_coupon:
                        issues.append("Missing 'expiration_date' field")
                    
                    if issues:
                        print(f"⚠️  Data structure issues found:")
                        for issue in issues:
                            print(f"   - {issue}")
                    else:
                        print(f"✅ Coupon data structure looks correct")
                    
                    return True
                else:
                    print(f"❌ Test coupon not found in list")
                    return False
            else:
                print(f"❌ Coupon list API failed: {response.status_code}")
                print(f"📝 Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Coupon list API error: {e}")
            return False

    def test_coupon_redemption_api(self):
        """Test coupon redemption API"""
        self.print_subsection("Testing Coupon Redemption API")
        
        if not self.user_token:
            print("❌ No user token available")
            return False
        
        try:
            headers = {
                'Authorization': f'Bearer {self.user_token}',
                'Content-Type': 'application/json'
            }
            
            redemption_data = {
                'coupon_code': self.test_coupon_code
            }
            
            response = requests.post(
                f"{BASE_URL}/api/trading/coupon/redeem",
                headers=headers,
                json=redemption_data
            )
            
            print(f"📡 Response Status: {response.status_code}")
            print(f"📝 Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Coupon redemption successful!")
                print(f"📝 Response data:")
                print(json.dumps(data, indent=2))
                
                # Update user token for subsequent tests
                if 'access_token' in data:
                    self.user_token = data['access_token']
                
                return True
            else:
                print(f"❌ Coupon redemption failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"📝 Error response:")
                    print(json.dumps(error_data, indent=2))
                except:
                    print(f"📝 Raw response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Coupon redemption API error: {e}")
            return False

    def verify_database_changes(self):
        """Verify database changes after redemption"""
        self.print_subsection("Verifying Database Changes")
        
        app = create_app()
        with app.app_context():
            try:
                # Check user tier status
                user_tier_status = UserTierStatus.query.filter_by(user_id=self.test_user_id).first()
                
                if user_tier_status:
                    current_tier = user_tier_status.get_current_tier()
                    print(f"📊 User tier status:")
                    print(f"   🏆 Current tier: {current_tier}")
                    print(f"   📅 Tier 1: {user_tier_status.tier_1}")
                    print(f"   📅 Tier 2: {user_tier_status.tier_2}")
                    print(f"   📅 Tier 3: {user_tier_status.tier_3}")
                    
                    if user_tier_status.tier_2:
                        print(f"   📅 Last payment: {user_tier_status.last_payment_date}")
                        print(f"   📅 Next payment: {user_tier_status.next_payment_date}")
                    
                    # Check coupon usage
                    coupon_usage = CouponUsage.query.filter_by(user_id=self.test_user_id).first()
                    if coupon_usage:
                        print(f"✅ Coupon usage recorded:")
                        print(f"   🎫 Coupon ID: {coupon_usage.coupon_id}")
                        print(f"   📅 Used at: {coupon_usage.used_at}")
                        
                        # Check coupon usage count
                        coupon = CouponCode.query.get(coupon_usage.coupon_id)
                        if coupon:
                            print(f"✅ Coupon usage count updated:")
                            print(f"   📊 Usage count: {coupon.usage_count}")
                            print(f"   📊 Max uses: {coupon.max_uses}")
                    else:
                        print(f"❌ No coupon usage found")
                        return False
                    
                    return current_tier == 2  # Should be tier 2 after redemption
                else:
                    print(f"❌ User tier status not found")
                    return False
                
            except Exception as e:
                print(f"❌ Error verifying database changes: {e}")
                return False

    def cleanup_test_data(self):
        """Clean up test data"""
        self.print_subsection("Cleaning Up Test Data")
        
        app = create_app()
        with app.app_context():
            try:
                # Delete test coupon and usage
                coupon = CouponCode.query.filter_by(code=self.test_coupon_code).first()
                if coupon:
                    CouponUsage.query.filter_by(coupon_id=coupon.id).delete()
                    db.session.delete(coupon)
                
                # Delete test user and related data
                if self.test_user_id:
                    CouponUsage.query.filter_by(user_id=self.test_user_id).delete()
                    UserTierStatus.query.filter_by(user_id=self.test_user_id).delete()
                    
                    test_user = User.query.get(self.test_user_id)
                    if test_user:
                        db.session.delete(test_user)
                
                db.session.commit()
                print(f"✅ Cleaned up test data")
                return True
                
            except Exception as e:
                print(f"❌ Error cleaning up test data: {e}")
                db.session.rollback()
                return False

    def run_full_test(self):
        """Run comprehensive coupon flow test"""
        self.print_separator("FULL COUPON REDEMPTION FLOW TEST")
        
        print("🚀 Testing complete coupon redemption flow")
        print(f"📡 Backend URL: {BASE_URL}")
        print("\n📝 Testing:")
        print("   • Test data setup")
        print("   • Admin authentication")
        print("   • User authentication")
        print("   • Coupon list API (admin panel data)")
        print("   • Coupon redemption API")
        print("   • Database verification")
        print("   • Data cleanup")
        
        # Step 1: Set up test data
        self.print_separator("STEP 1: Setup Test Data")
        setup_ok = self.setup_test_data()
        
        # Step 2: Test admin login
        self.print_separator("STEP 2: Admin Authentication")
        admin_login_ok = self.test_admin_login()
        
        # Step 3: Test user login
        self.print_separator("STEP 3: User Authentication")
        user_login_ok = self.test_user_login()
        
        # Step 4: Test coupon list API
        self.print_separator("STEP 4: Coupon List API")
        coupon_list_ok = self.test_coupon_list_api()
        
        # Step 5: Test coupon redemption
        self.print_separator("STEP 5: Coupon Redemption API")
        redemption_ok = self.test_coupon_redemption_api()
        
        # Step 6: Verify database changes
        self.print_separator("STEP 6: Database Verification")
        db_verification_ok = self.verify_database_changes()
        
        # Step 7: Cleanup
        self.print_separator("STEP 7: Cleanup")
        cleanup_ok = self.cleanup_test_data()
        
        # Results
        self.print_separator("TEST RESULTS SUMMARY")
        
        results = {
            "Test Data Setup": setup_ok,
            "Admin Authentication": admin_login_ok,
            "User Authentication": user_login_ok,
            "Coupon List API": coupon_list_ok,
            "Coupon Redemption API": redemption_ok,
            "Database Verification": db_verification_ok,
            "Cleanup": cleanup_ok
        }
        
        all_passed = True
        for test_name, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{status} {test_name}")
            if not passed:
                all_passed = False
        
        print(f"\n🎯 OVERALL RESULT:")
        if all_passed:
            print("🎉 ALL TESTS PASSED!")
            print("✅ Complete coupon redemption flow is working")
            print("✅ Backend APIs are functional")
            print("✅ Database operations are correct")
            print("✅ Ready for frontend integration")
        else:
            print("💥 SOME TESTS FAILED")
            print("🔧 Check the error messages above for details")
            print("🔍 Issues identified that need fixing")
        
        return all_passed

def main():
    """Main function"""
    tester = FullCouponFlowTest()
    success = tester.run_full_test()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
