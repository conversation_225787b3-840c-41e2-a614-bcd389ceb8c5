#!/usr/bin/env python3
"""
Test script for Hybrid Deep Learning + Traditional ML Integration
Demonstrates how deep learning enhances existing ML predictions
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime
import logging
from flask import Flask

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_app():
    """Create Flask app for database context"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///deeptrade.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    from app.models import db
    db.init_app(app)
    
    return app

def create_mock_market_data():
    """Create realistic mock market data for testing"""
    np.random.seed(42)
    
    # Generate 200 hours of realistic BTC price data
    dates = pd.date_range(start='2024-01-01', periods=200, freq='H')
    
    # Start with base price
    base_price = 45000.0
    prices = [base_price]
    
    # Generate realistic price movements
    for i in range(1, 200):
        # Random walk with some trend
        change_pct = np.random.normal(0, 0.02)  # 2% volatility
        new_price = prices[-1] * (1 + change_pct)
        prices.append(max(new_price, 1000))  # Minimum price floor
    
    # Create OHLCV data
    data = []
    for i, price in enumerate(prices):
        high = price * (1 + abs(np.random.normal(0, 0.01)))
        low = price * (1 - abs(np.random.normal(0, 0.01)))
        open_price = prices[i-1] if i > 0 else price
        close_price = price
        volume = np.random.uniform(1000, 10000)
        
        data.append({
            'timestamp': dates[i],
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    logger.info(f"📊 Created mock market data: {len(df)} records, price range ${df['close'].min():.0f}-${df['close'].max():.0f}")
    return df

def test_traditional_ml_only():
    """Test traditional ML system without deep learning"""
    logger.info("\n" + "="*60)
    logger.info("🔍 TESTING TRADITIONAL ML ONLY")
    logger.info("="*60)
    
    try:
        from app.services.trading_signals import TradingSignalGenerator
        
        # Create mock user ID
        user_id = 1
        
        # Initialize signal generator (deep learning disabled)
        os.environ['HYBRID_DEEP_ML_ENABLED'] = 'false'
        signal_generator = TradingSignalGenerator(user_id, admin_monitoring_mode=True)
        
        # Generate mock market data
        market_data = create_mock_market_data()
        
        # Generate signal
        signal_result = signal_generator.generate_signal(market_data)
        
        if signal_result and 'signal' in signal_result:
            logger.info(f"✅ Traditional ML Signal: {signal_result['signal']}")
            logger.info(f"📊 Confidence: {signal_result.get('confidence', 0):.1f}%")
            logger.info(f"🎯 Method: {signal_result.get('method', 'Unknown')}")
            logger.info(f"💰 Entry: ${signal_result.get('entry_price', 0):.2f}")
            logger.info(f"🛑 Stop Loss: ${signal_result.get('stop_loss', 0):.2f}")
            logger.info(f"🎯 Take Profit: ${signal_result.get('take_profit', 0):.2f}")
            
            return signal_result
        else:
            logger.warning("⚠️ No signal generated by traditional ML")
            return None
            
    except Exception as e:
        logger.error(f"❌ Traditional ML test failed: {e}")
        return None

def test_hybrid_ml_system():
    """Test hybrid ML system with deep learning enhancement"""
    logger.info("\n" + "="*60)
    logger.info("🚀 TESTING HYBRID ML + DEEP LEARNING")
    logger.info("="*60)
    
    try:
        from app.services.trading_signals import TradingSignalGenerator
        from app.services.hybrid_deep_ml import HybridDeepMLEnhancer
        
        # Enable hybrid deep learning
        os.environ['HYBRID_DEEP_ML_ENABLED'] = 'true'
        
        # Create mock user ID
        user_id = 1
        
        # Initialize hybrid enhancer and train it
        logger.info("🤖 Initializing and training hybrid deep learning system...")
        hybrid_enhancer = HybridDeepMLEnhancer()
        
        # Generate training data
        training_data = create_mock_market_data()
        
        # Train the models
        training_result = hybrid_enhancer.train_deep_models(training_data)
        
        if not training_result.get('success', False):
            logger.error("❌ Deep learning training failed")
            return None
        
        logger.info(f"✅ Deep learning models trained successfully!")
        logger.info(f"📊 LSTM Accuracy: {training_result['lstm_accuracy']:.3f}")
        logger.info(f"📊 CNN Accuracy: {training_result['cnn_accuracy']:.3f}")
        logger.info(f"📊 Ensemble Accuracy: {training_result['ensemble_accuracy']:.3f}")
        
        # Initialize signal generator with hybrid system
        signal_generator = TradingSignalGenerator(user_id, admin_monitoring_mode=True)
        
        # Manually set the hybrid enhancer (since it was trained outside)
        signal_generator.hybrid_deep_ml = hybrid_enhancer
        
        # Generate test market data
        test_data = create_mock_market_data()
        
        # Generate enhanced signal
        signal_result = signal_generator.generate_signal(test_data)
        
        if signal_result and 'signal' in signal_result:
            logger.info(f"✅ Hybrid ML Signal: {signal_result['signal']}")
            logger.info(f"📊 Confidence: {signal_result.get('confidence', 0):.1f}%")
            logger.info(f"🎯 Method: {signal_result.get('method', 'Unknown')}")
            logger.info(f"💰 Entry: ${signal_result.get('entry_price', 0):.2f}")
            logger.info(f"🛑 Stop Loss: ${signal_result.get('stop_loss', 0):.2f}")
            logger.info(f"🎯 Take Profit: ${signal_result.get('take_profit', 0):.2f}")
            
            # Check for deep learning enhancement
            if 'deep_learning_enhanced' in signal_result:
                logger.info("🚀 Deep Learning Enhancement Applied!")
            
            return signal_result
        else:
            logger.warning("⚠️ No signal generated by hybrid ML")
            return None
            
    except Exception as e:
        logger.error(f"❌ Hybrid ML test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_systems():
    """Compare traditional ML vs hybrid ML performance"""
    logger.info("\n" + "="*60)
    logger.info("⚖️ COMPARING TRADITIONAL ML vs HYBRID ML")
    logger.info("="*60)
    
    # Create Flask app context
    app = create_app()
    
    with app.app_context():
        # Test traditional ML
        traditional_result = test_traditional_ml_only()
        
        # Test hybrid ML
        hybrid_result = test_hybrid_ml_system()
        
        # Compare results
        if traditional_result and hybrid_result:
            logger.info("\n📊 COMPARISON RESULTS:")
            logger.info("-" * 40)
            
            trad_conf = traditional_result.get('confidence', 0)
            hybrid_conf = hybrid_result.get('confidence', 0)
            
            logger.info(f"Traditional ML Confidence: {trad_conf:.1f}%")
            logger.info(f"Hybrid ML Confidence: {hybrid_conf:.1f}%")
            
            if hybrid_conf > trad_conf:
                improvement = hybrid_conf - trad_conf
                logger.info(f"🚀 Improvement: +{improvement:.1f}% confidence boost!")
            elif hybrid_conf < trad_conf:
                reduction = trad_conf - hybrid_conf
                logger.info(f"⚠️ Reduction: -{reduction:.1f}% confidence (safety mechanism)")
            else:
                logger.info("📊 Same confidence (no enhancement applied)")
            
            # Check methods
            trad_method = traditional_result.get('method', 'Unknown')
            hybrid_method = hybrid_result.get('method', 'Unknown')
            
            logger.info(f"Traditional Method: {trad_method}")
            logger.info(f"Hybrid Method: {hybrid_method}")
            
            if 'DEEP_LEARNING' in hybrid_method:
                logger.info("✅ Deep learning enhancement confirmed!")
            
            return True
        else:
            logger.error("❌ Could not compare systems - one or both failed")
            return False

def main():
    """Main test function"""
    logger.info("=" * 80)
    logger.info("🧪 HYBRID DEEP LEARNING + TRADITIONAL ML INTEGRATION TEST")
    logger.info("=" * 80)
    
    try:
        # Run comparison
        success = compare_systems()
        
        if success:
            logger.info("\n" + "=" * 80)
            logger.info("🎉 HYBRID SYSTEM INTEGRATION TEST SUCCESSFUL!")
            logger.info("=" * 80)
            logger.info("✅ Traditional ML system working")
            logger.info("✅ Deep learning models trained")
            logger.info("✅ Hybrid enhancement applied")
            logger.info("✅ Integration with trading signals complete")
            
            logger.info("\n🚀 SYSTEM ARCHITECTURE:")
            logger.info("┌─ Traditional ML (96% Elite + 80% SL/TP)")
            logger.info("├─ Deep Learning Enhancement Layer")
            logger.info("│  ├─ LSTM (Time Series Patterns)")
            logger.info("│  ├─ CNN (Candlestick Patterns)")
            logger.info("│  └─ Hybrid Ensemble (Combined)")
            logger.info("└─ Enhanced Predictions (Better Accuracy)")
            
            logger.info("\n💡 BENEFITS:")
            logger.info("• Keeps existing 96% Elite ML accuracy")
            logger.info("• Adds deep learning pattern recognition")
            logger.info("• Weighted combination (70% traditional + 30% deep)")
            logger.info("• Fallback to traditional ML if deep learning fails")
            logger.info("• Enhanced SL/TP predictions")
            
            logger.info("\n🎯 NEXT STEPS:")
            logger.info("1. Set HYBRID_DEEP_ML_ENABLED=true in .env")
            logger.info("2. Run: python train_hybrid_deep_ml.py")
            logger.info("3. Monitor '[HYBRID_ML]' logs in trading signals")
            logger.info("4. Adjust weights based on performance")
            
        else:
            logger.error("\n❌ INTEGRATION TEST FAILED")
            logger.error("Check logs above for error details")
            
    except Exception as e:
        logger.error(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
