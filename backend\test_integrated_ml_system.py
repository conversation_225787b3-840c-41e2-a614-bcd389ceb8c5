#!/usr/bin/env python3
"""
Test the integrated ML system with Elite ML + SL/TP ML
Tests the complete trading signal generation with independent ML predictors
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedel<PERSON>

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_integrated_ml_system():
    """Test the complete integrated ML system"""
    print("🚀 Testing Integrated ML System")
    print("=" * 60)
    
    try:
        # Import Flask app and create application context
        from app import create_app
        app = create_app()
        
        with app.app_context():
            # Import required services
            from app.services.market_data import BinanceMarketData
            from app.services.trading_signals import TradingSignalGenerator
            from app.models.user import User
            from app.models.user_tier_status import UserTierStatus
            from app import db
            
            # Initialize services
            market_service = BinanceMarketData()
            
            print("✅ Services initialized")
            
            # Create or get test user
            test_user = User.query.filter_by(email='<EMAIL>').first()
            if not test_user:
                test_user = User(
                    email='<EMAIL>',
                    full_name='Test User',
                    password='test_password'
                )
                test_user.is_active = True
                db.session.add(test_user)
                db.session.commit()
                print("✅ Test user created")
            else:
                print("✅ Test user found")
            
            # Ensure user has tier status
            tier_status = UserTierStatus.query.filter_by(user_id=test_user.id).first()
            if not tier_status:
                tier_status = UserTierStatus(
                    user_id=test_user.id,
                    tier_1=False,
                    tier_2=True,  # Set to tier 2 for testing
                    tier_3=False
                )
                db.session.add(tier_status)
                db.session.commit()
                print("✅ Test user tier status created")
            else:
                print("✅ Test user tier status found")
            
            # Initialize trading signal generator
            signal_generator = TradingSignalGenerator(
                user_id=str(test_user.id),
                exchange_service=None,  # Mock exchange service
                admin_monitoring_mode=True
            )
            
            print("✅ Trading signal generator initialized")
            print(f"   🤖 Elite ML Available: {signal_generator.elite_predictor is not None}")
            print(f"   🎯 SL/TP ML Available: {signal_generator.sl_tp_manager is not None}")
            
            # Test scenarios
            test_scenarios = [
                {
                    "name": "Strong Bull Market",
                    "description": "Strong uptrend with low volatility - should trigger Elite ML BUY",
                    "trend": 1.2,
                    "volatility": 0.3,
                    "expected_signal": "BUY"
                },
                {
                    "name": "Strong Bear Market", 
                    "description": "Strong downtrend with medium volatility - should trigger Elite ML SELL",
                    "trend": -1.0,
                    "volatility": 0.4,
                    "expected_signal": "SELL"
                },
                {
                    "name": "Moderate Bull Market",
                    "description": "Moderate uptrend - may trigger Legacy BUY with ML SL/TP",
                    "trend": 0.6,
                    "volatility": 0.5,
                    "expected_signal": "BUY"
                },
                {
                    "name": "Sideways High Vol",
                    "description": "Choppy market - should trigger HOLD or low confidence signals",
                    "trend": 0.1,
                    "volatility": 0.9,
                    "expected_signal": "HOLD"
                },
                {
                    "name": "Volatile Bear Market",
                    "description": "Strong downtrend with high volatility - Elite ML SELL",
                    "trend": -0.8,
                    "volatility": 0.7,
                    "expected_signal": "SELL"
                }
            ]
            
            test_results = []
            
            for scenario in test_scenarios:
                print(f"\n🎯 Testing: {scenario['name']}")
                print(f"   📋 {scenario['description']}")
                print("-" * 50)
                
                # Generate test market data
                market_data = generate_realistic_market_data(
                    scenario['trend'], 
                    scenario['volatility'], 
                    300  # More data for better ML predictions
                )
                
                if market_data is None:
                    print("   ❌ Failed to generate market data")
                    continue
                
                # Generate trading signals
                try:
                    signals = signal_generator.generate_signals('BTCUSDT', '1h')
                    
                    if signals and signals.get('signal') != 'HOLD':
                        signal = signals['signal']
                        confidence = signals['confidence']
                        entry_price = signals['entry_price']
                        stop_loss = signals.get('stop_loss')
                        take_profit = signals.get('take_profit')
                        signal_type = signals.get('signal_type', 'UNKNOWN')
                        
                        print(f"   🎯 SIGNAL: {signal}")
                        print(f"   📊 Confidence: {confidence:.1f}%")
                        print(f"   💰 Entry: ${entry_price:,.2f}")
                        print(f"   🛡️ Stop Loss: ${stop_loss:,.2f}")
                        print(f"   🎯 Take Profit: ${take_profit:,.2f}")
                        print(f"   🤖 Signal Type: {signal_type}")
                        
                        # Calculate metrics
                        if stop_loss and take_profit:
                            risk = abs(entry_price - stop_loss)
                            reward = abs(take_profit - entry_price)
                            risk_reward = reward / risk if risk > 0 else 0
                            
                            sl_distance_pct = (risk / entry_price) * 100
                            tp_distance_pct = (reward / entry_price) * 100
                            
                            print(f"   📈 Risk-Reward: 1:{risk_reward:.2f}")
                            print(f"   📏 SL Distance: {sl_distance_pct:.2f}%")
                            print(f"   📏 TP Distance: {tp_distance_pct:.2f}%")
                            
                            # Assess signal quality
                            signal_quality = assess_signal_quality(
                                signal, confidence, risk_reward, sl_distance_pct, tp_distance_pct, signal_type
                            )
                            print(f"   🏅 Quality: {signal_quality}")
                            
                            test_results.append({
                                'scenario': scenario['name'],
                                'signal': signal,
                                'confidence': confidence,
                                'entry_price': entry_price,
                                'stop_loss': stop_loss,
                                'take_profit': take_profit,
                                'risk_reward': risk_reward,
                                'sl_distance_pct': sl_distance_pct,
                                'tp_distance_pct': tp_distance_pct,
                                'signal_type': signal_type,
                                'quality': signal_quality,
                                'expected_signal': scenario['expected_signal']
                            })
                        else:
                            print("   ⚠️ Missing SL/TP data")
                            test_results.append({
                                'scenario': scenario['name'],
                                'signal': signal,
                                'confidence': confidence,
                                'signal_type': signal_type,
                                'quality': 'INCOMPLETE',
                                'expected_signal': scenario['expected_signal']
                            })
                    else:
                        print("   🔄 HOLD Signal - No trade generated")
                        test_results.append({
                            'scenario': scenario['name'],
                            'signal': 'HOLD',
                            'confidence': 0,
                            'signal_type': 'HOLD',
                            'quality': 'HOLD',
                            'expected_signal': scenario['expected_signal']
                        })
                        
                except Exception as e:
                    print(f"   ❌ Signal generation failed: {e}")
                    import traceback
                    traceback.print_exc()
            
            # Overall system assessment
            print(f"\n🏆 INTEGRATED SYSTEM ASSESSMENT:")
            print("=" * 60)
            
            if test_results:
                # Calculate metrics
                valid_signals = [r for r in test_results if r['signal'] != 'HOLD' and r.get('quality') != 'INCOMPLETE']
                elite_signals = [r for r in valid_signals if r.get('signal_type') == 'ELITE_ML']
                legacy_signals = [r for r in valid_signals if r.get('signal_type') == 'LEGACY']
                
                print(f"📊 Total Tests: {len(test_results)}")
                print(f"📊 Valid Signals: {len(valid_signals)}")
                print(f"📊 Elite ML Signals: {len(elite_signals)}")
                print(f"📊 Legacy Signals: {len(legacy_signals)}")
                print(f"📊 HOLD Signals: {len([r for r in test_results if r['signal'] == 'HOLD'])}")
                
                if valid_signals:
                    avg_confidence = np.mean([r['confidence'] for r in valid_signals])
                    avg_risk_reward = np.mean([r.get('risk_reward', 0) for r in valid_signals if r.get('risk_reward')])
                    
                    print(f"📊 Average Confidence: {avg_confidence:.1f}%")
                    print(f"📊 Average Risk-Reward: 1:{avg_risk_reward:.2f}")
                    
                    # Quality distribution
                    quality_counts = {}
                    for result in test_results:
                        quality = result.get('quality', 'UNKNOWN')
                        quality_counts[quality] = quality_counts.get(quality, 0) + 1
                    
                    print(f"\n📊 Quality Distribution:")
                    for quality, count in quality_counts.items():
                        print(f"   {quality}: {count}")
                    
                    # Best performing signals
                    excellent_signals = [r for r in valid_signals if r.get('quality') == 'EXCELLENT']
                    if excellent_signals:
                        print(f"\n🏆 Excellent Signals: {len(excellent_signals)}")
                        for signal in excellent_signals:
                            print(f"   🥇 {signal['scenario']}: {signal['signal']} ({signal['confidence']:.1f}%, RR=1:{signal.get('risk_reward', 0):.2f})")
                    
                    # System readiness
                    if len(excellent_signals) >= 2 and avg_confidence >= 75:
                        system_status = "🏆 EXCELLENT - Production Ready"
                    elif len(valid_signals) >= 3 and avg_confidence >= 65:
                        system_status = "✅ GOOD - Ready with monitoring"
                    elif len(valid_signals) >= 2:
                        system_status = "⚖️ MODERATE - Needs optimization"
                    else:
                        system_status = "❌ POOR - Requires improvement"
                    
                    print(f"\n🎯 SYSTEM STATUS: {system_status}")
                    
                    return {
                        'success': True,
                        'total_tests': len(test_results),
                        'valid_signals': len(valid_signals),
                        'elite_signals': len(elite_signals),
                        'legacy_signals': len(legacy_signals),
                        'avg_confidence': avg_confidence,
                        'avg_risk_reward': avg_risk_reward,
                        'excellent_signals': len(excellent_signals),
                        'system_status': system_status,
                        'test_results': test_results
                    }
                else:
                    print("❌ No valid signals generated")
                    return {'success': False, 'error': 'No valid signals'}
            else:
                print("❌ No test results available")
                return {'success': False, 'error': 'No test results'}
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

def generate_realistic_market_data(trend, volatility, candles):
    """Generate realistic market data for testing"""
    try:
        base_price = 65000
        prices = []
        volumes = []
        
        for i in range(candles):
            # Trend component
            trend_component = (trend * i * 15)
            
            # Volatility component with realistic patterns
            noise = np.random.normal(0, volatility * 250)
            
            # Add cyclical patterns and momentum
            cycle = np.sin(i * 0.12) * volatility * 80
            momentum = np.random.normal(0, 40) if i > 0 else 0
            
            # Price calculation
            price = base_price + trend_component + noise + cycle + momentum
            prices.append(max(price, 45000))  # Floor at $45k
            
            # Volume with realistic patterns
            base_volume = 5000
            volume_noise = np.random.uniform(0.7, 1.3)
            volume_trend = 1.2 if abs(trend_component) > 500 else 1.0  # Higher volume in trending markets
            volumes.append(base_volume * volume_noise * volume_trend)
        
        # Create OHLC data
        market_data = pd.DataFrame({
            'timestamp': [1640995200000 + (i * 3600000) for i in range(candles)],
            'open': prices,
            'high': [p * (1 + np.random.uniform(0.002, 0.010)) for p in prices],
            'low': [p * (1 - np.random.uniform(0.002, 0.010)) for p in prices],
            'close': [p * (1 + np.random.uniform(-0.004, 0.004)) for p in prices],
            'volume': volumes
        })
        
        # Ensure OHLC relationships
        for i in range(len(market_data)):
            open_price = market_data.loc[i, 'open']
            close_price = market_data.loc[i, 'close']
            market_data.loc[i, 'high'] = max(market_data.loc[i, 'high'], open_price, close_price)
            market_data.loc[i, 'low'] = min(market_data.loc[i, 'low'], open_price, close_price)
        
        return market_data
        
    except Exception as e:
        return None

def assess_signal_quality(signal, confidence, risk_reward, sl_distance_pct, tp_distance_pct, signal_type):
    """Assess the quality of a trading signal"""
    try:
        score = 0
        
        # Confidence scoring
        if confidence >= 90:
            score += 30
        elif confidence >= 80:
            score += 25
        elif confidence >= 70:
            score += 20
        elif confidence >= 60:
            score += 15
        else:
            score += 10
        
        # Risk-reward scoring
        if risk_reward >= 2.5:
            score += 25
        elif risk_reward >= 2.0:
            score += 20
        elif risk_reward >= 1.8:
            score += 15
        elif risk_reward >= 1.5:
            score += 10
        else:
            score += 5
        
        # SL distance scoring (optimal range 0.5% - 2.0%)
        if 0.5 <= sl_distance_pct <= 2.0:
            score += 20
        elif 0.3 <= sl_distance_pct <= 3.0:
            score += 15
        else:
            score += 5
        
        # TP distance scoring (optimal range 1.0% - 4.0%)
        if 1.0 <= tp_distance_pct <= 4.0:
            score += 15
        elif 0.8 <= tp_distance_pct <= 6.0:
            score += 10
        else:
            score += 5
        
        # Signal type bonus
        if signal_type == 'ELITE_ML':
            score += 10
        elif signal_type == 'LEGACY':
            score += 5
        
        # Quality assessment
        if score >= 85:
            return "🏆 EXCELLENT"
        elif score >= 70:
            return "✅ GOOD"
        elif score >= 55:
            return "⚖️ MODERATE"
        else:
            return "❌ POOR"
            
    except Exception as e:
        return "❓ UNKNOWN"

if __name__ == "__main__":
    print("🚀 DeepTrade Integrated ML System Test")
    print("=" * 60)
    
    results = test_integrated_ml_system()
    
    if results and results['success']:
        print(f"\n🎉 INTEGRATION TEST COMPLETE!")
        print(f"   🎯 System Status: {results['system_status']}")
        print(f"   📊 Valid Signals: {results['valid_signals']}/{results['total_tests']}")
        print(f"   🤖 Elite ML: {results['elite_signals']}, Legacy: {results['legacy_signals']}")
        print(f"   📈 Avg Confidence: {results['avg_confidence']:.1f}%")
        print(f"   📈 Avg Risk-Reward: 1:{results['avg_risk_reward']:.2f}")
        print(f"   🏆 Excellent Signals: {results['excellent_signals']}")
    else:
        error = results.get('error', 'Unknown error') if results else 'Test failed'
        print(f"\n❌ Integration test failed: {error}")
