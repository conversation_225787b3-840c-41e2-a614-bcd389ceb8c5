#!/usr/bin/env python3
"""
Test script to verify IP summary enhancement with user information
"""

import requests
import json
from datetime import datetime

def test_ip_summary_api():
    """Test the IP summary API endpoint to verify user information is included"""
    
    # Test IP address (using one with actual user data)
    test_ip = "*************"
    
    # Admin login credentials (using working credentials from database)
    admin_credentials = {
        "username": "<EMAIL>",  # Working admin username
        "password": "admin123"  # Working admin password
    }
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # Step 1: Login as admin to get token
        print("🔐 Logging in as admin...")
        login_response = requests.post(
            f"{base_url}/api/admin/login",
            json=admin_credentials,
            headers={"Content-Type": "application/json"}
        )
        
        if login_response.status_code != 200:
            print(f"❌ Admin login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return
        
        login_data = login_response.json()
        admin_token = login_data.get('access_token')
        
        if not admin_token:
            print("❌ No access token received")
            return
        
        print("✅ Admin login successful")
        
        # Step 2: Test IP summary endpoint
        print(f"📊 Testing IP summary for {test_ip}...")
        summary_response = requests.get(
            f"{base_url}/api/admin/ip/summary/{test_ip}",
            headers={
                "Authorization": f"Bearer {admin_token}",
                "Content-Type": "application/json"
            }
        )
        
        if summary_response.status_code != 200:
            print(f"❌ IP summary request failed: {summary_response.status_code}")
            print(f"Response: {summary_response.text}")
            return
        
        summary_data = summary_response.json()
        
        print("✅ IP summary retrieved successfully")
        print(f"📍 IP Address: {summary_data.get('ip_address')}")
        print(f"📊 Total Logins: {summary_data.get('total_logins', 0)}")
        print(f"❌ Failed Attempts: {summary_data.get('failed_attempts', 0)}")
        
        # Check access logs for user information
        access_logs = summary_data.get('access_logs', [])
        print(f"📝 Access Logs Count: {len(access_logs)}")
        
        if access_logs:
            print("\n🔍 Sample Access Log Entry:")
            sample_log = access_logs[0]
            
            # Check if enhanced user information is present
            user_email = sample_log.get('user_email')
            complete_user_id = sample_log.get('complete_user_id')
            user_full_name = sample_log.get('user_full_name')
            
            print(f"   User ID: {sample_log.get('user_id', 'N/A')}")
            print(f"   Complete User ID: {complete_user_id or 'N/A'}")
            print(f"   User Email: {user_email or 'N/A'}")
            print(f"   User Full Name: {user_full_name or 'N/A'}")
            print(f"   Login Successful: {sample_log.get('login_successful', 'N/A')}")
            print(f"   Timestamp: {sample_log.get('login_timestamp', 'N/A')}")
            
            # Verify enhancement worked
            if user_email or complete_user_id:
                print("\n✅ Enhancement successful! User information is included.")
            else:
                print("\n⚠️  Enhancement may not be working - no user information found.")
        else:
            print("\n⚠️  No access logs found for this IP address.")
        
        print(f"\n📄 Full response saved to ip_summary_test_result.json")
        
        # Save full response for inspection
        with open('ip_summary_test_result.json', 'w') as f:
            json.dump(summary_data, f, indent=2, default=str)
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - make sure the backend server is running on http://127.0.0.1:5000")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🧪 Testing IP Summary Enhancement")
    print("=" * 50)
    test_ip_summary_api()
