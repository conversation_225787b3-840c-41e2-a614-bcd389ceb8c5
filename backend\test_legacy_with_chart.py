#!/usr/bin/env python3
"""
Test original legacy system combined with chart prediction
(Not in isolation - test the full CHART_ONLY scenario)
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_legacy_with_chart():
    """Test original legacy system when combined with chart prediction"""
    print("📊 Legacy + Chart Prediction Combined Test")
    print("=" * 60)
    
    try:
        # Import Flask app and create application context
        from app import create_app
        app = create_app()
        
        with app.app_context():
            # Import required services
            from app.services.market_data import BinanceMarketData
            from app.services.trading_signals import TradingSignalGenerator
            
            # Initialize services
            market_service = BinanceMarketData()
            signal_generator = TradingSignalGenerator("test_user", market_service)
            
            print("✅ Services initialized successfully")
            
            # Test different market scenarios with chart predictions
            scenarios = [
                ("Bull Trend + Bullish Chart", 0.8, 0.3, "bullish"),
                ("Bear Trend + Bearish Chart", -0.6, 0.4, "bearish"),
                ("Sideways + Neutral Chart", 0.1, 0.2, "neutral"),
                ("Bull Trend + Bearish Chart", 0.8, 0.3, "bearish"),  # Conflicting
                ("Bear Trend + Bullish Chart", -0.6, 0.4, "bullish")   # Conflicting
            ]
            
            total_signals = 0
            total_days = len(scenarios)
            
            for scenario_name, trend_per_hour, volatility, chart_direction in scenarios:
                print(f"\n🎯 Testing {scenario_name}...")
                print("-" * 50)
                
                # Generate 24 hours of data (1 day)
                hours = 48  # Use 48 hours for better indicator calculation
                base_price = 65000
                
                # Create market data with trend and volatility
                prices = []
                for i in range(hours):
                    trend_component = trend_per_hour * i
                    noise = np.random.normal(0, volatility * 100)
                    price = base_price + trend_component + noise
                    prices.append(max(price, 50000))  # Floor at $50k
                
                # Create OHLC data
                mock_data = pd.DataFrame({
                    'timestamp': [1640995200000 + (i * 3600000) for i in range(hours)],
                    'open': prices,
                    'high': [p * (1 + np.random.uniform(0.002, 0.008)) for p in prices],
                    'low': [p * (1 - np.random.uniform(0.002, 0.008)) for p in prices],
                    'close': [p * (1 + np.random.uniform(-0.003, 0.003)) for p in prices],
                    'volume': [np.random.uniform(4000, 8000) for _ in range(hours)]
                })
                
                # Ensure OHLC relationships
                for i in range(len(mock_data)):
                    open_price = mock_data.loc[i, 'open']
                    close_price = mock_data.loc[i, 'close']
                    mock_data.loc[i, 'high'] = max(mock_data.loc[i, 'high'], open_price, close_price)
                    mock_data.loc[i, 'low'] = min(mock_data.loc[i, 'low'], open_price, close_price)
                
                current_price = float(mock_data['close'].iloc[-1])
                print(f"   💰 Final Price: ${current_price:,.0f}")
                print(f"   📈 Total Change: {((current_price / base_price) - 1) * 100:+.1f}%")
                print(f"   📊 Chart Direction: {chart_direction}")
                
                # Test the FULL system (Elite ML neutral + Chart prediction + Legacy)
                signals_today = 0
                test_hours = [24, 30, 36, 42, 47]  # Test last day
                
                for test_hour in test_hours:
                    # Use data up to test hour
                    current_data = mock_data.iloc[:test_hour+1].copy()
                    
                    try:
                        # Test the FULL confirmation system (not just legacy)
                        # This will simulate Elite ML being neutral and chart having direction
                        
                        # Mock Elite ML as neutral (confidence < 90%)
                        elite_signal = {
                            'signal': 'HOLD',
                            'confidence': 75,  # Below 90% threshold
                            'reason': 'Neutral market conditions'
                        }

                        # Mock Chart prediction based on scenario
                        if chart_direction == "bullish":
                            chart_dir = "UP"
                            chart_conf = 0.82
                            forecast_data = (2.5, 82)  # (forecast_change, confidence)
                        elif chart_direction == "bearish":
                            chart_dir = "DOWN"
                            chart_conf = 0.78
                            forecast_data = (-2.2, 78)
                        else:  # neutral
                            chart_dir = "NEUTRAL"
                            chart_conf = 0.65
                            forecast_data = (0.3, 65)

                        # Test the confirmation logic with correct parameters
                        result = signal_generator._apply_confirmation_logic(
                            elite_dir="NEUTRAL",
                            elite_conf=0.75,
                            elite_signal=elite_signal,
                            chart_dir=chart_dir,
                            chart_conf=chart_conf,
                            market_data=current_data,
                            symbol="BTCUSDT",
                            forecast_data=forecast_data
                        )
                        
                        signal = result.get('signal', 'HOLD')
                        confidence = result.get('confidence', 0)
                        confirmation = result.get('confirmation', 'UNKNOWN')
                        
                        if signal != 'HOLD':
                            signals_today += 1
                            print(f"   ⚡ Hour {test_hour}: {signal} ({confidence:.1f}%) - {confirmation}")
                        
                    except Exception as e:
                        print(f"   ❌ Error at hour {test_hour}: {e}")
                
                print(f"   🎯 Signals Today: {signals_today}")
                total_signals += signals_today
            
            # Calculate results
            avg_signals_per_day = total_signals / total_days
            
            print(f"\n📈 LEGACY + CHART COMBINED RESULTS:")
            print("=" * 50)
            print(f"   📊 Total Signals: {total_signals}")
            print(f"   📅 Total Days Tested: {total_days}")
            print(f"   🎯 Average Signals/Day: {avg_signals_per_day:.1f}")
            
            # Frequency assessment
            if avg_signals_per_day == 0:
                assessment = "❌ TOO CONSERVATIVE - No signals"
            elif avg_signals_per_day < 1:
                assessment = "⚠️ VERY CONSERVATIVE - Less than 1/day"
            elif avg_signals_per_day <= 3:
                assessment = "✅ BALANCED - Good frequency"
            elif avg_signals_per_day <= 6:
                assessment = "⚡ ACTIVE - High frequency"
            else:
                assessment = "🚨 TOO AGGRESSIVE - Very high frequency"
            
            print(f"   📋 Assessment: {assessment}")
            
            # Compare with previous tests
            print(f"\n🔄 Comparison:")
            print(f"   📊 Legacy Only: 0.4 signals/day")
            print(f"   📊 Legacy + Chart: {avg_signals_per_day:.1f} signals/day")
            
            if avg_signals_per_day > 0.4:
                improvement = avg_signals_per_day / 0.4
                print(f"   📈 Improvement: {improvement:.1f}x better with chart prediction")
            elif avg_signals_per_day == 0.4:
                print(f"   📈 Same frequency as legacy only")
            else:
                print(f"   📉 Lower frequency than legacy only")
            
            return avg_signals_per_day
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 0

if __name__ == "__main__":
    print("🚀 Legacy + Chart Prediction Test")
    print("=" * 50)
    
    frequency = test_legacy_with_chart()
    
    if frequency > 0:
        print(f"\n🎉 LEGACY + CHART GENERATES {frequency:.1f} SIGNALS/DAY!")
        if frequency > 1:
            print(f"   ✅ Good frequency for practical trading")
        else:
            print(f"   ⚠️ Conservative but working")
    else:
        print(f"\n❌ LEGACY + CHART NOT GENERATING SIGNALS")
