#!/usr/bin/env python3
"""Test ML forecast generation to debug zero values in admin terminal."""

import logging
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.market_data import ml_service

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_ml_forecast():
    """Test the ML forecast generation."""
    print("🔍 Testing ML forecast generation...")
    print("=" * 50)
    
    try:
        # Test the ML forecast generation
        forecast = ml_service.generate_ensemble_forecast('BTCUSDT', '1h', 72)
        
        if not forecast:
            print("❌ No forecast returned!")
            return
            
        print(f"✅ Forecast generated successfully!")
        print(f"📊 Forecast result keys: {list(forecast.keys())}")
        print()
        
        # Check key values
        is_mock = forecast.get('is_mock', False)
        current_price = forecast.get('current_price', 0)
        highest_price = forecast.get('highest_price', 0)
        lowest_price = forecast.get('lowest_price', 0)
        swing_high = forecast.get('swing_high', None)
        swing_low = forecast.get('swing_low', None)
        
        print(f"🎭 Is mock forecast: {is_mock}")
        print(f"💰 Current price: ${current_price:,.2f}")
        print(f"📈 Highest price: ${highest_price:,.2f}")
        print(f"📉 Lowest price: ${lowest_price:,.2f}")
        print(f"🔺 Swing high: {swing_high}")
        print(f"🔻 Swing low: {swing_low}")
        print()
        
        # Calculate potential moves
        if current_price > 0:
            potential_up_move = (highest_price - current_price) / current_price * 100
            potential_down_move = (current_price - lowest_price) / current_price * 100
            print(f"📊 Potential up move: {potential_up_move:.2f}%")
            print(f"📊 Potential down move: {potential_down_move:.2f}%")
        else:
            print("❌ Current price is 0 - this is the problem!")
            
        # Check if we're using mock data
        if is_mock:
            print()
            print("⚠️  USING MOCK DATA - This explains the zero values!")
            print("🔧 The system is falling back to mock forecast")
            print("🔍 Check Binance API connection and data availability")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ml_forecast()
