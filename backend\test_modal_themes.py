#!/usr/bin/env python3
"""
Test script for admin panel modal theme fixes
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modal_theme_fixes():
    """Test that modal themes are properly implemented"""
    print("🧪 TESTING ADMIN PANEL MODAL THEME FIXES...")
    print("="*50)
    
    try:
        # Read the admin panel HTML
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check IP Details modal theme
        print("1. Testing IP Details modal theme:")
        
        # Check for proper dark theme text colors in table cells
        ip_details_patterns = [
            r'text-gray-900 dark:text-gray-100.*toLocaleString',  # Table cell text
            r'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',  # Success badge
            r'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',  # Failed badge
            r'py-2 px-4 text-gray-900 dark:text-gray-100'  # Consistent cell styling
        ]
        
        for pattern in ip_details_patterns:
            if re.search(pattern, content):
                print(f"   ✅ IP Details modal pattern found: {pattern[:50]}...")
            else:
                print(f"   ❌ IP Details modal pattern missing: {pattern[:50]}...")
        
        # Test 2: Check Admin IP History modal theme
        print("\n2. Testing Admin IP History modal theme:")
        
        admin_history_patterns = [
            r'font-medium text-gray-900 dark:text-white.*IP Address',  # Table headers
            r'py-2 px-4 font-mono text-gray-900 dark:text-gray-100',  # IP address cell
            r'py-2 px-4 text-gray-900 dark:text-gray-100.*geographic_location',  # Location cell
            r'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200.*Success',  # Success badge
            r'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200.*Failed'  # Failed badge
        ]
        
        for pattern in admin_history_patterns:
            if re.search(pattern, content):
                print(f"   ✅ Admin History modal pattern found: {pattern[:50]}...")
            else:
                print(f"   ❌ Admin History modal pattern missing: {pattern[:50]}...")
        
        # Test 3: Check modal structure consistency
        print("\n3. Testing modal structure consistency:")
        
        modal_structure_patterns = [
            r'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50',  # Modal overlay
            r'bg-white dark:bg-gray-800 rounded-lg shadow-xl',  # Modal content
            r'flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700',  # Modal header
            r'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300',  # Close button
            r'flex-1 overflow-y-auto p-6'  # Modal body
        ]
        
        for pattern in modal_structure_patterns:
            matches = len(re.findall(pattern, content))
            if matches >= 2:  # Should appear in both modals
                print(f"   ✅ Modal structure pattern found {matches} times: {pattern[:50]}...")
            else:
                print(f"   ❌ Modal structure pattern found only {matches} times: {pattern[:50]}...")
        
        # Test 4: Check badge theme consistency
        print("\n4. Testing badge theme consistency:")
        
        # Count all badge patterns to ensure consistency
        success_badges = len(re.findall(r'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200', content))
        failed_badges = len(re.findall(r'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200', content))
        
        print(f"   ✅ Success badges with dark theme: {success_badges}")
        print(f"   ✅ Failed badges with dark theme: {failed_badges}")
        
        if success_badges >= 2 and failed_badges >= 2:
            print("   ✅ Badge themes are consistent across modals")
        else:
            print("   ❌ Badge themes may be inconsistent")
        
        # Test 5: Check table styling consistency
        print("\n5. Testing table styling consistency:")
        
        table_patterns = [
            r'min-w-full text-sm',  # Table base classes
            r'border-b border-gray-100 dark:border-gray-700',  # Row borders
            r'py-2 px-4.*text-gray-900 dark:text-gray-100',  # Cell text colors
            r'font-medium text-gray-900 dark:text-white'  # Header text colors
        ]
        
        for pattern in table_patterns:
            matches = len(re.findall(pattern, content))
            if matches >= 2:
                print(f"   ✅ Table pattern found {matches} times: {pattern[:40]}...")
            else:
                print(f"   ❌ Table pattern found only {matches} times: {pattern[:40]}...")
        
        print("\n🎉 MODAL THEME FIXES TEST COMPLETED!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_theme_improvements_summary():
    """Provide summary of theme improvements"""
    print("\n🎨 THEME IMPROVEMENTS SUMMARY...")
    print("="*50)
    
    improvements = [
        "🔧 Fixed table cell text colors (text-gray-900 dark:text-gray-100)",
        "🔧 Added dark theme variants to status badges",
        "🔧 Improved table header styling with proper dark theme",
        "🔧 Added consistent padding and spacing (py-2 px-4)",
        "🔧 Fixed modal header structure and padding",
        "🔧 Ensured consistent border colors (dark:border-gray-700)",
        "🔧 Added hover effects for close buttons",
        "🔧 Maintained responsive design in modals"
    ]
    
    for improvement in improvements:
        print(f"   {improvement}")
    
    return True

if __name__ == "__main__":
    success1 = test_modal_theme_fixes()
    success2 = test_theme_improvements_summary()
    
    if success1 and success2:
        print("\n✅ All modal theme tests completed successfully!")
        print("📋 Benefits:")
        print("   - Consistent dark/light theme support in all modals")
        print("   - Proper text contrast for accessibility")
        print("   - Professional appearance across all admin interfaces")
        print("   - Consistent badge and table styling")
    else:
        print("\n❌ Some modal theme tests failed!")
        sys.exit(1)
