#!/usr/bin/env python3
"""
Test script for new admin panel features: IP pagination and user pruning
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ip_pagination_implementation():
    """Test IP Address Management pagination implementation"""
    print("Testing IP Address Management Pagination...")
    
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for IP logs pagination controls
        print("  1. Testing pagination controls:")
        
        pagination_controls = [
            'ip-logs-per-page',
            'ip-logs-pagination-info',
            'ip-logs-prev-btn',
            'ip-logs-next-btn',
            'ip-logs-page-info'
        ]
        
        for control in pagination_controls:
            if f'id="{control}"' in content:
                print(f"     [PASS] {control} control found")
            else:
                print(f"     [FAIL] {control} control missing")
                return False
        
        # Test 2: Check for pagination JavaScript functions
        print("  2. Testing JavaScript functions:")
        
        js_functions = [
            'loadIPAccessLogs(page = 1, perPage = 25)',
            'updateIPLogsPagination',
            'currentIPLogsPage',
            'currentIPLogsPerPage'
        ]
        
        for func in js_functions:
            if func in content:
                print(f"     [PASS] {func} found")
            else:
                print(f"     [FAIL] {func} missing")
                return False
        
        # Test 3: Check for event listeners
        print("  3. Testing event listeners:")
        
        event_listeners = [
            'ip-logs-per-page.*addEventListener',
            'ip-logs-prev-btn.*addEventListener',
            'ip-logs-next-btn.*addEventListener'
        ]
        
        for listener in event_listeners:
            if re.search(listener, content):
                print(f"     [PASS] {listener} event listener found")
            else:
                print(f"     [FAIL] {listener} event listener missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_prune_button_implementation():
    """Test User Management prune button implementation"""
    print("Testing User Management Prune Button...")
    
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for prune button
        print("  1. Testing prune button:")
        
        if 'id="prune-users-btn"' in content and 'Prune Unverified' in content:
            print("     [PASS] Prune button found")
        else:
            print("     [FAIL] Prune button missing")
            return False
        
        # Test 2: Check for prune modal
        print("  2. Testing prune modal:")
        
        modal_elements = [
            'showPruneUsersModal',
            'prune-inactivity-period',
            'prune-confirm-checkbox',
            'executePruneUsers'
        ]
        
        for element in modal_elements:
            if element in content:
                print(f"     [PASS] {element} found")
            else:
                print(f"     [FAIL] {element} missing")
                return False
        
        # Test 3: Check for inactivity options
        print("  3. Testing inactivity options:")
        
        inactivity_options = [
            '1 day (Test)',
            '30 days',
            '90 days',
            '1 year'
        ]
        
        for option in inactivity_options:
            if option in content:
                print(f"     [PASS] {option} found")
            else:
                print(f"     [FAIL] {option} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_prune_backend_endpoint():
    """Test prune users backend endpoint"""
    print("Testing Prune Users Backend Endpoint...")
    
    try:
        with open('app/api/admin_routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for prune endpoint
        print("  1. Testing endpoint definition:")
        
        endpoint_patterns = [
            "@admin_bp.route('/users/prune', methods=['POST'])",
            "@super_admin_required",
            "def prune_unverified_users():",
            "Prune (delete) unverified users"
        ]
        
        for pattern in endpoint_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        # Test 2: Check for functionality
        print("  2. Testing functionality:")
        
        functionality_patterns = [
            'inactivity_days = data.get',
            'cutoff_date = datetime.utcnow() - timedelta',
            'User.is_verified == False',
            'User.created_at < cutoff_date',
            'deleted_count',
            'AdminAction.log_action'
        ]
        
        for pattern in functionality_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        # Test 3: Check for proper cleanup
        print("  3. Testing data cleanup:")
        
        cleanup_patterns = [
            'IPAccessLog.query.filter_by(user_id=user_id).delete()',
            'UserTierStatus.query.filter_by(user_id=user_id).delete()',
            'APICredential.query.filter_by(user_id=user_id).delete()',
            'CouponUsage.query.filter_by(user_id=user_id).delete()',
            'db.session.delete(user)'
        ]
        
        for pattern in cleanup_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def test_security_and_validation():
    """Test security and validation aspects"""
    print("Testing Security and Validation...")
    
    try:
        with open('app/api/admin_routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for super admin requirement
        print("  1. Testing security:")
        
        if '@super_admin_required' in content and 'prune_unverified_users' in content:
            print("     [PASS] Super admin requirement found")
        else:
            print("     [FAIL] Super admin requirement missing")
            return False
        
        # Test 2: Check for input validation
        print("  2. Testing input validation:")
        
        validation_patterns = [
            'isinstance(inactivity_days, int)',
            'inactivity_days < 1',
            'Invalid inactivity_days parameter'
        ]
        
        for pattern in validation_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        # Test 3: Check for error handling
        print("  3. Testing error handling:")
        
        error_patterns = [
            'try:',
            'except Exception as e:',
            'db.session.rollback()',
            'current_app.logger.error'
        ]
        
        for pattern in error_patterns:
            if pattern in content:
                print(f"     [PASS] {pattern} found")
            else:
                print(f"     [FAIL] {pattern} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] {str(e)}")
        return False

def run_all_tests():
    """Run all new feature tests"""
    print("NEW ADMIN PANEL FEATURES TEST")
    print("=" * 60)
    
    tests = [
        ("IP Address Management Pagination", test_ip_pagination_implementation),
        ("User Management Prune Button", test_prune_button_implementation),
        ("Prune Users Backend Endpoint", test_prune_backend_endpoint),
        ("Security and Validation", test_security_and_validation)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"  [PASS] {test_name} completed successfully")
            else:
                failed += 1
                print(f"  [FAIL] {test_name} failed")
        except Exception as e:
            failed += 1
            print(f"  [ERROR] {test_name} error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("NEW FEATURES TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {len(tests)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed/len(tests)*100):.1f}%")
    
    if failed == 0:
        print("\nALL TESTS PASSED! New features implemented successfully.")
        print("\nImplemented Features:")
        print("- IP Address Management pagination (25/50/100 items per page)")
        print("- User Management prune button with inactivity options")
        print("- Backend API endpoint for pruning unverified users")
        print("- Proper security, validation, and error handling")
        print("- Admin action logging for audit trail")
        return True
    else:
        print(f"\n{failed} test(s) failed. Please review implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
