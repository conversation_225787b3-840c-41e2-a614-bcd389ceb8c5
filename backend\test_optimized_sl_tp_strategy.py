#!/usr/bin/env python3
"""
Test optimized Stop Loss and Take Profit strategies
Goal: Maximize win rate and profit while minimizing drawdown
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_sl_tp_strategies():
    """Test different SL/TP strategies to find optimal configuration"""
    print("🎯 Stop Loss & Take Profit Strategy Optimization")
    print("=" * 70)
    
    try:
        # Import Flask app and create application context
        from app import create_app
        app = create_app()
        
        with app.app_context():
            # Import required services
            from app.services.market_data import BinanceMarketData
            from app.services.trading_signals import TradingSignalGenerator
            
            # Initialize services
            market_service = BinanceMarketData()
            signal_generator = TradingSignalGenerator("test_user", market_service)
            
            print("✅ Services initialized successfully")
            
            # Define SL/TP strategies to test
            strategies = [
                {
                    "name": "Current System (ATR-based)",
                    "description": "2 ATR SL, 4 ATR TP (1:2 ratio)",
                    "sl_method": "atr",
                    "sl_multiplier": 2.0,
                    "tp_method": "atr", 
                    "tp_multiplier": 4.0,
                    "min_sl_pct": 0.5,
                    "min_tp_pct": 1.0
                },
                {
                    "name": "Swing Point Optimized",
                    "description": "Swing-based SL with 1:2.5 ratio",
                    "sl_method": "swing",
                    "sl_buffer": 0.3,  # 0.3% buffer beyond swing
                    "tp_method": "ratio",
                    "tp_ratio": 2.5,
                    "min_sl_pct": 0.4,
                    "min_tp_pct": 1.0
                },
                {
                    "name": "Volatility Adaptive",
                    "description": "Volatility-based dynamic SL/TP",
                    "sl_method": "volatility",
                    "sl_multiplier": 1.5,
                    "tp_method": "volatility",
                    "tp_multiplier": 3.5,
                    "min_sl_pct": 0.3,
                    "min_tp_pct": 0.8
                },
                {
                    "name": "Tight Scalping",
                    "description": "Tight SL/TP for high frequency",
                    "sl_method": "fixed",
                    "sl_pct": 0.4,
                    "tp_method": "fixed",
                    "tp_pct": 0.8,
                    "min_sl_pct": 0.3,
                    "min_tp_pct": 0.6
                },
                {
                    "name": "Conservative Wide",
                    "description": "Wide SL/TP for higher win rate",
                    "sl_method": "fixed",
                    "sl_pct": 1.2,
                    "tp_method": "fixed", 
                    "tp_pct": 2.0,
                    "min_sl_pct": 1.0,
                    "min_tp_pct": 1.5
                }
            ]
            
            # Test market scenarios
            market_scenarios = [
                ("Trending Bull", 0.8, 0.3, 100),   # Strong uptrend, low volatility
                ("Trending Bear", -0.6, 0.4, 100),  # Strong downtrend, medium volatility
                ("Choppy Sideways", 0.1, 0.8, 200), # Sideways, high volatility
                ("Volatile Bull", 1.2, 0.9, 150),   # Strong uptrend, high volatility
                ("Weak Bear", -0.3, 0.5, 120)       # Weak downtrend, medium volatility
            ]
            
            strategy_results = []
            
            for strategy in strategies:
                print(f"\n🔧 Testing {strategy['name']}...")
                print(f"   📋 {strategy['description']}")
                print("-" * 50)
                
                total_trades = 0
                total_wins = 0
                total_pnl = 0
                max_drawdown = 0
                current_drawdown = 0
                peak_balance = 10000
                current_balance = 10000
                
                scenario_results = []
                
                for scenario_name, trend, volatility, candles in market_scenarios:
                    print(f"   🎯 {scenario_name}...")
                    
                    # Generate realistic market data
                    base_price = 65000
                    prices = []
                    
                    for i in range(candles):
                        # Trend component
                        trend_component = (trend * i * 10)
                        
                        # Volatility component
                        noise = np.random.normal(0, volatility * 200)
                        
                        # Price calculation
                        price = base_price + trend_component + noise
                        prices.append(max(price, 50000))  # Floor at $50k
                    
                    # Create OHLC data
                    mock_data = pd.DataFrame({
                        'timestamp': [1640995200000 + (i * 3600000) for i in range(candles)],
                        'open': prices,
                        'high': [p * (1 + np.random.uniform(0.002, 0.008)) for p in prices],
                        'low': [p * (1 - np.random.uniform(0.002, 0.008)) for p in prices],
                        'close': [p * (1 + np.random.uniform(-0.003, 0.003)) for p in prices],
                        'volume': [np.random.uniform(4000, 8000) for _ in range(candles)]
                    })
                    
                    # Ensure OHLC relationships
                    for i in range(len(mock_data)):
                        open_price = mock_data.loc[i, 'open']
                        close_price = mock_data.loc[i, 'close']
                        mock_data.loc[i, 'high'] = max(mock_data.loc[i, 'high'], open_price, close_price)
                        mock_data.loc[i, 'low'] = min(mock_data.loc[i, 'low'], open_price, close_price)
                    
                    # Simulate trades with this strategy
                    scenario_trades = 0
                    scenario_wins = 0
                    scenario_pnl = 0
                    
                    # Generate 5-10 random trade entries for this scenario
                    num_trades = np.random.randint(5, 11)
                    
                    for trade_idx in range(num_trades):
                        # Random entry point
                        entry_idx = np.random.randint(20, len(mock_data) - 20)
                        entry_price = float(mock_data.iloc[entry_idx]['close'])
                        
                        # Random signal direction (based on trend)
                        if trend > 0.3:
                            signal = 'BUY' if np.random.random() > 0.3 else 'SELL'
                        elif trend < -0.3:
                            signal = 'SELL' if np.random.random() > 0.3 else 'BUY'
                        else:
                            signal = np.random.choice(['BUY', 'SELL'])
                        
                        # Calculate SL/TP based on strategy
                        sl_price, tp_price = calculate_sl_tp(
                            strategy, entry_price, mock_data.iloc[:entry_idx+1], signal
                        )
                        
                        if not sl_price or not tp_price:
                            continue
                        
                        # Simulate trade outcome
                        trade_result = simulate_trade_outcome(
                            entry_price, sl_price, tp_price, signal,
                            mock_data.iloc[entry_idx:], entry_idx
                        )
                        
                        if trade_result:
                            scenario_trades += 1
                            total_trades += 1
                            
                            pnl_pct = trade_result['pnl_pct']
                            scenario_pnl += pnl_pct
                            total_pnl += pnl_pct
                            
                            if pnl_pct > 0:
                                scenario_wins += 1
                                total_wins += 1
                            
                            # Update balance and drawdown
                            current_balance += (current_balance * pnl_pct / 100)
                            
                            if current_balance > peak_balance:
                                peak_balance = current_balance
                                current_drawdown = 0
                            else:
                                current_drawdown = (peak_balance - current_balance) / peak_balance * 100
                                max_drawdown = max(max_drawdown, current_drawdown)
                    
                    scenario_win_rate = (scenario_wins / scenario_trades * 100) if scenario_trades > 0 else 0
                    print(f"      📊 {scenario_trades} trades, {scenario_win_rate:.1f}% win rate, {scenario_pnl:.2f}% PnL")
                    
                    scenario_results.append({
                        'scenario': scenario_name,
                        'trades': scenario_trades,
                        'win_rate': scenario_win_rate,
                        'pnl': scenario_pnl
                    })
                
                # Calculate overall strategy performance
                overall_win_rate = (total_wins / total_trades * 100) if total_trades > 0 else 0
                avg_pnl_per_trade = total_pnl / total_trades if total_trades > 0 else 0
                final_balance = current_balance
                total_return = (final_balance - 10000) / 10000 * 100
                
                print(f"\n   📈 STRATEGY RESULTS:")
                print(f"      🎯 Total Trades: {total_trades}")
                print(f"      ✅ Win Rate: {overall_win_rate:.1f}%")
                print(f"      💰 Total PnL: {total_pnl:.2f}%")
                print(f"      📊 Avg PnL/Trade: {avg_pnl_per_trade:.2f}%")
                print(f"      💵 Final Balance: ${final_balance:,.0f}")
                print(f"      📈 Total Return: {total_return:.1f}%")
                print(f"      📉 Max Drawdown: {max_drawdown:.1f}%")
                
                # Strategy assessment
                if overall_win_rate >= 70 and total_return >= 15 and max_drawdown <= 10:
                    assessment = "🏆 EXCELLENT"
                elif overall_win_rate >= 60 and total_return >= 10 and max_drawdown <= 15:
                    assessment = "✅ GOOD"
                elif overall_win_rate >= 50 and total_return >= 5:
                    assessment = "⚖️ MODERATE"
                else:
                    assessment = "❌ POOR"
                
                print(f"      🏅 Assessment: {assessment}")
                
                strategy_results.append({
                    'name': strategy['name'],
                    'description': strategy['description'],
                    'total_trades': total_trades,
                    'win_rate': overall_win_rate,
                    'total_pnl': total_pnl,
                    'avg_pnl_per_trade': avg_pnl_per_trade,
                    'total_return': total_return,
                    'max_drawdown': max_drawdown,
                    'final_balance': final_balance,
                    'assessment': assessment,
                    'scenario_results': scenario_results
                })
            
            # Find best strategy
            print(f"\n🏆 STRATEGY COMPARISON:")
            print("=" * 70)
            
            # Sort by total return
            strategy_results.sort(key=lambda x: x['total_return'], reverse=True)
            
            for i, result in enumerate(strategy_results, 1):
                print(f"{i}. {result['assessment']} {result['name']}")
                print(f"   📊 {result['win_rate']:.1f}% win rate | {result['total_return']:.1f}% return | {result['max_drawdown']:.1f}% drawdown")
            
            # Best strategy details
            best_strategy = strategy_results[0]
            print(f"\n🥇 BEST STRATEGY: {best_strategy['name']}")
            print(f"   📋 {best_strategy['description']}")
            print(f"   🎯 Win Rate: {best_strategy['win_rate']:.1f}%")
            print(f"   💰 Total Return: {best_strategy['total_return']:.1f}%")
            print(f"   📉 Max Drawdown: {best_strategy['max_drawdown']:.1f}%")
            print(f"   📊 Avg PnL/Trade: {best_strategy['avg_pnl_per_trade']:.2f}%")
            
            return best_strategy
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def calculate_sl_tp(strategy, entry_price, historical_data, signal):
    """Calculate stop loss and take profit based on strategy"""
    try:
        if len(historical_data) < 20:
            return None, None
        
        # Calculate ATR for ATR-based strategies
        if strategy['sl_method'] == 'atr' or strategy['tp_method'] == 'atr':
            high_low = historical_data['high'] - historical_data['low']
            high_close = abs(historical_data['high'] - historical_data['close'].shift(1))
            low_close = abs(historical_data['low'] - historical_data['close'].shift(1))
            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            atr = true_range.rolling(14).mean().iloc[-1]
        
        # Calculate volatility for volatility-based strategies
        if strategy['sl_method'] == 'volatility' or strategy['tp_method'] == 'volatility':
            returns = historical_data['close'].pct_change()
            volatility = returns.rolling(20).std().iloc[-1] * entry_price
        
        # Calculate swing points for swing-based strategies
        if strategy['sl_method'] == 'swing':
            swing_high = historical_data['high'].rolling(10).max().iloc[-1]
            swing_low = historical_data['low'].rolling(10).min().iloc[-1]
        
        # Calculate Stop Loss
        if strategy['sl_method'] == 'atr':
            if signal == 'BUY':
                sl_price = entry_price - (atr * strategy['sl_multiplier'])
            else:
                sl_price = entry_price + (atr * strategy['sl_multiplier'])
        elif strategy['sl_method'] == 'swing':
            if signal == 'BUY':
                sl_price = swing_low * (1 - strategy['sl_buffer'] / 100)
            else:
                sl_price = swing_high * (1 + strategy['sl_buffer'] / 100)
        elif strategy['sl_method'] == 'volatility':
            if signal == 'BUY':
                sl_price = entry_price - (volatility * strategy['sl_multiplier'])
            else:
                sl_price = entry_price + (volatility * strategy['sl_multiplier'])
        elif strategy['sl_method'] == 'fixed':
            if signal == 'BUY':
                sl_price = entry_price * (1 - strategy['sl_pct'] / 100)
            else:
                sl_price = entry_price * (1 + strategy['sl_pct'] / 100)
        
        # Calculate Take Profit
        if strategy['tp_method'] == 'atr':
            if signal == 'BUY':
                tp_price = entry_price + (atr * strategy['tp_multiplier'])
            else:
                tp_price = entry_price - (atr * strategy['tp_multiplier'])
        elif strategy['tp_method'] == 'ratio':
            sl_distance = abs(entry_price - sl_price)
            if signal == 'BUY':
                tp_price = entry_price + (sl_distance * strategy['tp_ratio'])
            else:
                tp_price = entry_price - (sl_distance * strategy['tp_ratio'])
        elif strategy['tp_method'] == 'volatility':
            if signal == 'BUY':
                tp_price = entry_price + (volatility * strategy['tp_multiplier'])
            else:
                tp_price = entry_price - (volatility * strategy['tp_multiplier'])
        elif strategy['tp_method'] == 'fixed':
            if signal == 'BUY':
                tp_price = entry_price * (1 + strategy['tp_pct'] / 100)
            else:
                tp_price = entry_price * (1 - strategy['tp_pct'] / 100)
        
        # Apply minimum thresholds
        min_sl_distance = entry_price * strategy['min_sl_pct'] / 100
        min_tp_distance = entry_price * strategy['min_tp_pct'] / 100
        
        if signal == 'BUY':
            if (entry_price - sl_price) < min_sl_distance:
                sl_price = entry_price - min_sl_distance
            if (tp_price - entry_price) < min_tp_distance:
                tp_price = entry_price + min_tp_distance
        else:
            if (sl_price - entry_price) < min_sl_distance:
                sl_price = entry_price + min_sl_distance
            if (entry_price - tp_price) < min_tp_distance:
                tp_price = entry_price - min_tp_distance
        
        return sl_price, tp_price
        
    except Exception as e:
        return None, None

def simulate_trade_outcome(entry_price, sl_price, tp_price, signal, future_data, entry_idx):
    """Simulate trade outcome based on future price action"""
    try:
        if len(future_data) < 2:
            return None
        
        # Check each candle after entry
        for i in range(1, min(len(future_data), 50)):  # Max 50 candles
            candle = future_data.iloc[i]
            high = candle['high']
            low = candle['low']
            
            if signal == 'BUY':
                # Check stop loss first (more likely to hit)
                if low <= sl_price:
                    pnl_pct = ((sl_price - entry_price) / entry_price) * 100
                    return {
                        'exit_price': sl_price,
                        'exit_reason': 'stop_loss',
                        'pnl_pct': pnl_pct,
                        'candles_held': i
                    }
                # Check take profit
                elif high >= tp_price:
                    pnl_pct = ((tp_price - entry_price) / entry_price) * 100
                    return {
                        'exit_price': tp_price,
                        'exit_reason': 'take_profit',
                        'pnl_pct': pnl_pct,
                        'candles_held': i
                    }
            else:  # SELL
                # Check stop loss first
                if high >= sl_price:
                    pnl_pct = ((entry_price - sl_price) / entry_price) * 100
                    return {
                        'exit_price': sl_price,
                        'exit_reason': 'stop_loss',
                        'pnl_pct': pnl_pct,
                        'candles_held': i
                    }
                # Check take profit
                elif low <= tp_price:
                    pnl_pct = ((entry_price - tp_price) / entry_price) * 100
                    return {
                        'exit_price': tp_price,
                        'exit_reason': 'take_profit',
                        'pnl_pct': pnl_pct,
                        'candles_held': i
                    }
        
        # If no SL/TP hit, exit at market after 50 candles
        final_price = future_data.iloc[-1]['close']
        if signal == 'BUY':
            pnl_pct = ((final_price - entry_price) / entry_price) * 100
        else:
            pnl_pct = ((entry_price - final_price) / entry_price) * 100
        
        return {
            'exit_price': final_price,
            'exit_reason': 'timeout',
            'pnl_pct': pnl_pct,
            'candles_held': len(future_data) - 1
        }
        
    except Exception as e:
        return None

if __name__ == "__main__":
    print("🚀 DeepTrade SL/TP Strategy Optimization")
    print("=" * 50)
    
    best_strategy = test_sl_tp_strategies()
    
    if best_strategy:
        print(f"\n🎉 OPTIMIZATION COMPLETE!")
        print(f"   🏆 Best Strategy: {best_strategy['name']}")
        print(f"   📊 Performance: {best_strategy['win_rate']:.1f}% win rate, {best_strategy['total_return']:.1f}% return")
        print(f"   💡 Ready for implementation!")
    else:
        print(f"\n❌ Optimization failed - check logs for details")
