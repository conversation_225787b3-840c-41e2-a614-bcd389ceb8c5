#!/usr/bin/env python3
"""
Test the restored original legacy conditions
"""

import sys
import os
import pandas as pd
import numpy as np

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_original_legacy():
    """Test the restored original legacy conditions"""
    print("🔄 Testing Restored Original Legacy Conditions")
    print("=" * 60)
    
    try:
        # Import Flask app and create application context
        from app import create_app
        app = create_app()
        
        with app.app_context():
            # Import required services
            from app.services.market_data import BinanceMarketData
            from app.services.trading_signals import TradingSignalGenerator
            
            # Initialize services
            market_service = BinanceMarketData()
            signal_generator = TradingSignalGenerator("test_user", market_service)
            
            print("✅ Services initialized successfully")
            
            # Create simple uptrend data that should match original conditions
            print(f"\n🎯 Creating Simple Uptrend (Original Legacy Style)...")
            
            # Generate 50 hours of simple uptrend
            hours = 50
            base_price = 65000
            
            # Simple steady uptrend
            prices = [base_price + (i * 20) for i in range(hours)]  # $20/hour
            
            # Create simple OHLC
            mock_data = pd.DataFrame({
                'timestamp': [1640995200000 + (i * 3600000) for i in range(hours)],
                'open': prices,
                'high': [p * 1.005 for p in prices],  # 0.5% higher
                'low': [p * 0.998 for p in prices],   # 0.2% lower  
                'close': [p * 1.002 for p in prices], # 0.2% higher than open
                'volume': [5000 for _ in range(hours)]  # Constant volume
            })
            
            current_price = float(mock_data['close'].iloc[-1])
            print(f"   💰 Current Price: ${current_price:,.2f}")
            print(f"   📈 Total Gain: +${current_price - base_price:,.2f}")
            
            # Create simple forecast with 2% upside
            forecast = np.array([current_price * (1 + 0.02 + i*0.0005) for i in range(24)])
            highest_price = np.max(forecast)
            lowest_price = np.min(forecast)
            
            print(f"   📈 Forecast High: ${highest_price:,.2f} (+{((highest_price/current_price)-1)*100:.1f}%)")
            
            # Simple swing points
            swing_points = {
                'swing_high': current_price * 1.025,  # 2.5% above
                'swing_low': current_price * 0.98     # 2% below
            }
            
            print(f"   🔺 Swing High: ${swing_points['swing_high']:,.2f}")
            print(f"   🔻 Swing Low: ${swing_points['swing_low']:,.2f}")
            
            # Calculate Heikin-Ashi
            heikin_ashi = signal_generator._calculate_heikin_ashi(mock_data)
            print(f"   🕯️ HA Color: {heikin_ashi.get('ha_color', 'N/A')}")
            print(f"   🕯️ Prev HA Color: {heikin_ashi.get('prev_ha_color', 'N/A')}")
            print(f"   🕯️ Previous HA Close: {heikin_ashi.get('previous_ha_close', 'N/A')}")
            
            # Test the system
            result = signal_generator._analyze_trading_conditions(
                market_data=mock_data,
                forecast=forecast,
                swing_points=swing_points,
                heikin_ashi=heikin_ashi,
                highest_price=highest_price,
                lowest_price=lowest_price
            )
            
            signal = result.get('signal', 'UNKNOWN')
            confidence = result.get('confidence', 0)
            
            print(f"\n🎯 ORIGINAL LEGACY RESULT:")
            print(f"   Signal: {signal}")
            print(f"   Confidence: {confidence:.1f}%")
            
            if signal == 'BUY':
                print(f"   ✅ SUCCESS! Original legacy BUY signal generated!")
                return True
            else:
                print(f"   ❌ FAILED! Still getting {signal}")
                
                # Debug the original conditions manually
                print(f"\n🔍 Debugging Original Legacy Conditions:")
                
                # Get the calculated values
                if 'optimized_indicators' in result:
                    opt_ind = result['optimized_indicators']
                    ma = opt_ind.get('moving_averages', {})
                    
                    current_sma12 = ma.get('sma12', 0)
                    avg_upper_bound = opt_ind.get('price_levels', {}).get('avg_upper_bound', 0)
                    avg_lower_bound = opt_ind.get('price_levels', {}).get('avg_lower_bound', 0)
                    
                    print(f"   💰 Current Price: ${current_price:,.2f}")
                    print(f"   📊 SMA12: ${current_sma12:,.2f}")
                    print(f"   📈 Avg Upper Bound: ${avg_upper_bound:,.2f}")
                    print(f"   📉 Avg Lower Bound: ${avg_lower_bound:,.2f}")
                    
                    # Calculate potential moves
                    potential_up_move = (highest_price - current_price) / current_price
                    potential_down_move = (current_price - lowest_price) / current_price
                    
                    print(f"   📈 Potential Up: {potential_up_move*100:.2f}%")
                    print(f"   📉 Potential Down: {potential_down_move*100:.2f}%")
                    
                    # Check each original BUY condition
                    print(f"\n📋 Original BUY Conditions Check:")
                    
                    has_open_position = False  # Assume no position
                    last_swing_low = swing_points.get('swing_low')
                    ha_color = heikin_ashi.get('ha_color')
                    prev_ha_color = heikin_ashi.get('prev_ha_color')
                    previous_ha_close = heikin_ashi.get('previous_ha_close')
                    
                    cond1 = not has_open_position
                    cond2 = last_swing_low is not None and last_swing_low < current_price
                    cond3 = potential_up_move > 0.01
                    cond4 = ha_color == "green"
                    cond5 = prev_ha_color == "green"
                    cond6 = previous_ha_close > current_sma12
                    cond7 = current_price < avg_upper_bound
                    
                    print(f"   1. No Position: {cond1}")
                    print(f"   2. Swing Low < Price: {cond2} ({last_swing_low} < {current_price})")
                    print(f"   3. Up Move > 1%: {cond3} ({potential_up_move*100:.2f}%)")
                    print(f"   4. HA Green: {cond4} ('{ha_color}')")
                    print(f"   5. Prev HA Green: {cond5} ('{prev_ha_color}')")
                    print(f"   6. Prev HA > SMA12: {cond6} ({previous_ha_close} > {current_sma12})")
                    print(f"   7. Price < Upper Bound: {cond7} ({current_price} < {avg_upper_bound})")
                    
                    all_conditions = [cond1, cond2, cond3, cond4, cond5, cond6, cond7]
                    print(f"\n   🎯 All Conditions Met: {all(all_conditions)}")
                    print(f"   📊 Conditions Passed: {sum(all_conditions)}/7")
                    
                    if not all(all_conditions):
                        failed = [i+1 for i, cond in enumerate(all_conditions) if not cond]
                        print(f"   ❌ Failed Conditions: {failed}")
                
                return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Original Legacy Test")
    print("=" * 40)
    
    success = test_original_legacy()
    
    if success:
        print(f"\n🎉 ORIGINAL LEGACY WORKING!")
    else:
        print(f"\n❌ ORIGINAL LEGACY STILL NOT WORKING")
