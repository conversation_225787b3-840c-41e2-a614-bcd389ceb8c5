#!/usr/bin/env python3
"""
Test script for admin panel pagination functionality
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pagination_implementation():
    """Test that pagination is properly implemented in admin panel"""
    print("🧪 TESTING ADMIN PANEL PAGINATION IMPLEMENTATION...")
    print("="*50)
    
    try:
        # Read the admin panel HTML
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test 1: Check for pagination state variables
        pagination_vars = [
            'currentUsersPage',
            'currentUsersPerPage', 
            'currentCouponsPage',
            'currentCouponsPerPage',
            'currentIPLogsPage',
            'currentIPLogsPerPage'
        ]
        
        print("1. Testing pagination state variables:")
        for var in pagination_vars:
            if var in content:
                print(f"   ✅ {var} found")
            else:
                print(f"   ❌ {var} missing")
        
        # Test 2: Check for pagination controls in HTML
        pagination_controls = [
            'users-per-page',
            'users-pagination-info',
            'users-prev-btn',
            'users-next-btn',
            'coupons-per-page',
            'coupons-pagination-info',
            'coupons-prev-btn',
            'coupons-next-btn'
        ]
        
        print("\n2. Testing pagination controls in HTML:")
        for control in pagination_controls:
            if f'id="{control}"' in content:
                print(f"   ✅ {control} control found")
            else:
                print(f"   ❌ {control} control missing")
        
        # Test 3: Check for pagination functions
        pagination_functions = [
            'updateUsersPagination',
            'updateCouponsPagination',
            'loadUsers.*page.*perPage',
            'loadCoupons.*page.*perPage'
        ]
        
        print("\n3. Testing pagination functions:")
        for func in pagination_functions:
            if re.search(func, content):
                print(f"   ✅ {func} function found")
            else:
                print(f"   ❌ {func} function missing")
        
        # Test 4: Check for event listeners
        event_listeners = [
            'users-per-page.*addEventListener',
            'users-prev-btn.*addEventListener',
            'users-next-btn.*addEventListener',
            'coupons-per-page.*addEventListener',
            'coupons-prev-btn.*addEventListener',
            'coupons-next-btn.*addEventListener'
        ]
        
        print("\n4. Testing pagination event listeners:")
        for listener in event_listeners:
            if re.search(listener, content):
                print(f"   ✅ {listener} event listener found")
            else:
                print(f"   ❌ {listener} event listener missing")
        
        # Test 5: Check for per-page options (25, 50, 100)
        per_page_options = ['<option value="25">25</option>', '<option value="50">50</option>', '<option value="100">100</option>']
        
        print("\n5. Testing per-page options:")
        for option in per_page_options:
            if option in content:
                print(f"   ✅ {option} found")
            else:
                print(f"   ❌ {option} missing")
        
        # Test 6: Check API calls use pagination parameters
        api_calls = [
            r'fetch\(`/api/admin/users\?page=\$\{page\}&per_page=\$\{perPage\}`',
            r'fetch\(`/api/admin/coupons\?page=\$\{page\}&per_page=\$\{perPage\}`'
        ]
        
        print("\n6. Testing API calls with pagination:")
        for call in api_calls:
            if re.search(call, content):
                print(f"   ✅ Pagination API call found")
            else:
                print(f"   ❌ Pagination API call missing")
        
        print("\n🎉 PAGINATION IMPLEMENTATION TEST COMPLETED!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_backend_pagination():
    """Test backend pagination support"""
    print("\n🔧 TESTING BACKEND PAGINATION SUPPORT...")
    print("="*50)
    
    try:
        # Check admin_routes.py for pagination
        with open('app/api/admin_routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for pagination in existing endpoints
        endpoints_with_pagination = [
            'list_users.*pagination',
            'list_coupons.*pagination',
            'list_admin_actions.*pagination'
        ]
        
        print("Backend endpoints with pagination:")
        for endpoint in endpoints_with_pagination:
            if re.search(endpoint, content, re.DOTALL):
                print(f"   ✅ {endpoint} has pagination")
            else:
                print(f"   ❌ {endpoint} missing pagination")
        
        # Check for pagination response format
        pagination_response = [
            'pagination.*page',
            'pagination.*pages',
            'pagination.*per_page',
            'pagination.*total',
            'pagination.*has_next',
            'pagination.*has_prev'
        ]
        
        print("\nPagination response format:")
        for field in pagination_response:
            if re.search(field, content):
                print(f"   ✅ {field} in response")
            else:
                print(f"   ❌ {field} missing from response")
        
        return True
        
    except Exception as e:
        print(f"❌ Backend test failed: {str(e)}")
        return False

if __name__ == "__main__":
    success1 = test_pagination_implementation()
    success2 = test_backend_pagination()
    
    if success1 and success2:
        print("\n✅ All pagination tests completed successfully!")
        print("📋 Summary:")
        print("   - Pagination state variables implemented")
        print("   - HTML controls added for users and coupons")
        print("   - JavaScript functions for pagination updates")
        print("   - Event listeners for pagination interactions")
        print("   - Per-page options: 25, 50, 100 items")
        print("   - Backend API endpoints support pagination")
    else:
        print("\n❌ Some pagination tests failed!")
        sys.exit(1)
