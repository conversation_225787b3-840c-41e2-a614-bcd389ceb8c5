#!/usr/bin/env python3
"""
Direct test of payment flow without requiring frontend token.
This will test the database operations directly.
"""

import sys
sys.path.append('.')

from app import create_app
from app.models import User, UserTierStatus, SolanaPayment
from app.models.solana_payment import SolanaPaymentStatus, SolanaPaymentType
from app.services.membership_service import MembershipService
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
import uuid
from datetime import datetime, timezone

def print_separator(title):
    """Print a separator with title.""" 
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def test_payment_flow():
    """Test the payment flow directly."""
    
    app = create_app()
    
    with app.app_context():
        print_separator("DeepTrade Payment Flow Direct Test")
        
        # Find the user we've been testing with
        user = User.query.filter_by(email='<EMAIL>').first()
        if not user:
            print("❌ User not found!")
            return False
            
        print(f"✅ Found user: {user.email} (ID: {user.id})")
        
        # Get or create tier status
        tier_status = UserTierStatus.query.filter_by(user_id=user.id).first()
        if not tier_status:
            print("❌ No tier status found!")
            return False
            
        print(f"📊 Current tier status:")
        print(f"   - Profit Share Owed: ${tier_status.profit_share_owed}")
        print(f"   - Payment Status: {tier_status.payment_status}")
        print(f"   - Account Disabled: {tier_status.account_disabled}")
        
        # Step 1: Clear any existing debt
        print_separator("STEP 1: Clear Existing Debt")
        original_debt = tier_status.profit_share_owed
        tier_status.profit_share_owed = 0.0
        tier_status.payment_status = 'paid'
        tier_status.account_disabled = False
        tier_status.updated_at = datetime.now(timezone.utc)
        
        from app import db
        db.session.commit()
        print(f"✅ Cleared existing debt of ${original_debt}")
        
        # Step 2: Add test debt
        print_separator("STEP 2: Add Test Debt")
        test_debt = 0.01
        tier_status.profit_share_owed = test_debt
        tier_status.payment_status = 'overdue'
        tier_status.account_disabled = True
        tier_status.updated_at = datetime.now(timezone.utc)
        db.session.commit()
        
        print(f"✅ Added test debt: ${test_debt}")
        print(f"   - Payment Status: {tier_status.payment_status}")
        print(f"   - Account Disabled: {tier_status.account_disabled}")
        
        # Step 3: Test the clear_debt method
        print_separator("STEP 3: Test clear_debt Method")
        print(f"🔍 Before clear_debt:")
        print(f"   - Debt: ${tier_status.profit_share_owed}")
        print(f"   - Status: {tier_status.payment_status}")
        print(f"   - Disabled: {tier_status.account_disabled}")
        
        # Call the clear_debt method
        tier_status.clear_debt(test_debt)
        db.session.commit()
        
        print(f"🔍 After clear_debt:")
        print(f"   - Debt: ${tier_status.profit_share_owed}")
        print(f"   - Status: {tier_status.payment_status}")
        print(f"   - Disabled: {tier_status.account_disabled}")
        
        # Step 4: Test the membership service
        print_separator("STEP 4: Test Membership Service")
        
        # Add debt again for membership service test
        tier_status.profit_share_owed = test_debt
        tier_status.payment_status = 'overdue'
        tier_status.account_disabled = True
        db.session.commit()
        
        print(f"🔍 Before membership service:")
        print(f"   - Debt: ${tier_status.profit_share_owed}")
        print(f"   - Status: {tier_status.payment_status}")
        print(f"   - Disabled: {tier_status.account_disabled}")
        
        # Create a simulated payment
        simulated_payment = SolanaPayment(
            user_id=user.id,
            payment_type=SolanaPaymentType.PROFIT_SHARE,
            amount=test_debt,
            to_address='TEST_TREASURY_ADDRESS',
            transaction_signature=f'TEST_TX_{uuid.uuid4().hex[:16]}',
            status=SolanaPaymentStatus.CONFIRMED,
            processed_at=datetime.now(timezone.utc)
        )
        db.session.add(simulated_payment)
        db.session.commit()
        
        # Process the payment using membership service
        try:
            result = MembershipService.process_tier_2_payment(user.id, simulated_payment.id)
            print(f"✅ Membership service result: {result}")
        except Exception as e:
            print(f"❌ Membership service error: {e}")
            return False
        
        # Refresh tier status from database
        db.session.refresh(tier_status)
        
        print(f"🔍 After membership service:")
        print(f"   - Debt: ${tier_status.profit_share_owed}")
        print(f"   - Status: {tier_status.payment_status}")
        print(f"   - Disabled: {tier_status.account_disabled}")
        
        # Step 5: Verification
        print_separator("VERIFICATION RESULTS")
        
        success = (
            tier_status.profit_share_owed == 0.0 and
            tier_status.payment_status == 'paid' and
            not tier_status.account_disabled
        )
        
        print(f"💰 DEBT STATUS:")
        if tier_status.profit_share_owed == 0.0:
            print(f"   ✅ SUCCESS: Debt cleared to $0.00")
        else:
            print(f"   ❌ FAILURE: Debt not cleared! Still owe: ${tier_status.profit_share_owed}")
        
        print(f"📋 PAYMENT STATUS:")
        if tier_status.payment_status == 'paid':
            print(f"   ✅ SUCCESS: Payment status is 'paid'")
        else:
            print(f"   ❌ FAILURE: Payment status is '{tier_status.payment_status}' (expected 'paid')")
        
        print(f"🔓 ACCOUNT STATUS:")
        if not tier_status.account_disabled:
            print(f"   ✅ SUCCESS: Account is enabled")
        else:
            print(f"   ❌ FAILURE: Account is still disabled")
        
        print(f"\n🎯 OVERALL RESULT:")
        if success:
            print(f"   🎉 PAYMENT FLOW WORKING CORRECTLY!")
        else:
            print(f"   💥 PAYMENT FLOW HAS ISSUES!")
        
        return success

if __name__ == "__main__":
    success = test_payment_flow()
    exit(0 if success else 1)
