#!/usr/bin/env python3
"""
Test script to verify the payment flow is working correctly.
This script will:
1. Add test debt
2. Make a simulated payment
3. Check if the debt is properly cleared
4. Verify the payment status is updated
"""

import requests
import json
import sys
import os

# Add the backend directory to the path so we can import models
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configuration
BASE_URL = "http://localhost:5000"
TEST_USER_EMAIL = "<EMAIL>"  # Replace with your test user email
TEST_USER_PASSWORD = "your_password"  # Replace with your test user password

def login_user(email, password):
    """Login and get access token."""
    print(f"🔐 Logging in user: {email}")
    
    login_data = {
        "email": email,
        "password": password
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    
    if response.status_code == 200:
        data = response.json()
        token = data.get('access_token')
        print(f"✅ Login successful, token: {token[:20]}...")
        return token
    else:
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return None

def get_headers(token):
    """Get headers with authorization token."""
    return {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

def check_debt_status(token):
    """Check current debt status."""
    print("\n📊 Checking current debt status...")
    
    response = requests.get(f"{BASE_URL}/api/trading/test/debug-debt", headers=get_headers(token))
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Current debt status:")
        print(f"   - Profit Share Owed: ${data['profit_share_owed']}")
        print(f"   - Payment Status: {data['payment_status']}")
        print(f"   - Account Disabled: {data['account_disabled']}")
        print(f"   - Raw Debt Value: {data['raw_debt_value']} ({data['debt_type']})")
        return data
    else:
        print(f"❌ Failed to get debt status: {response.status_code} - {response.text}")
        return None

def add_test_debt(token, amount=0.01):
    """Add test debt."""
    print(f"\n💰 Adding test debt: ${amount}")
    
    response = requests.post(
        f"{BASE_URL}/api/trading/test/add-profit-share-debt",
        headers=get_headers(token),
        json={"amount": amount}
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Test debt added: {data['message']}")
        print(f"   - Total owed: ${data['total_owed']}")
        return True
    else:
        print(f"❌ Failed to add test debt: {response.status_code} - {response.text}")
        return False

def make_test_payment(token, amount):
    """Make a test payment."""
    print(f"\n💳 Making test payment: ${amount}")
    
    response = requests.post(
        f"{BASE_URL}/api/trading/test/simulate-profit-share-payment",
        headers=get_headers(token),
        json={"amount": amount}
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Test payment successful:")
        print(f"   - Message: {data['message']}")
        print(f"   - Payment Amount: ${data['payment']['amount']}")
        print(f"   - Original Debt: ${data['debt_info']['original_debt']}")
        print(f"   - Remaining Debt: ${data['debt_info']['remaining_debt']}")
        print(f"   - Payment Status: {data['debt_info']['payment_status']}")
        print(f"   - Account Enabled: {data['debt_info']['account_enabled']}")
        return data
    else:
        print(f"❌ Test payment failed: {response.status_code} - {response.text}")
        return None

def get_tier_status(token):
    """Get tier status to verify payment was processed."""
    print("\n🎯 Getting tier status...")
    
    response = requests.get(f"{BASE_URL}/api/trading/tier/status", headers=get_headers(token))
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Tier status:")
        print(f"   - Profit Share Owed: ${data['profit_share_owed']}")
        print(f"   - Tier: {data['tier']}")
        return data
    else:
        print(f"❌ Failed to get tier status: {response.status_code} - {response.text}")
        return None

def clear_all_debt(token):
    """Clear all debt for clean testing."""
    print("\n🧹 Clearing all debt...")
    
    response = requests.post(
        f"{BASE_URL}/api/trading/test/clear-profit-share-debt",
        headers=get_headers(token)
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ All debt cleared: {data['message']}")
        return True
    else:
        print(f"❌ Failed to clear debt: {response.status_code} - {response.text}")
        return False

def main():
    """Main test function."""
    print("🧪 DeepTrade Payment Flow Test")
    print("=" * 50)
    
    # Note: You'll need to provide valid credentials
    print("⚠️  Please update the script with valid user credentials before running!")
    print("   - Update TEST_USER_EMAIL and TEST_USER_PASSWORD variables")
    print("   - Make sure the user exists in the database")
    print("   - Make sure the backend is running on http://localhost:5000")
    
    # Uncomment and update these lines with valid credentials
    # token = login_user(TEST_USER_EMAIL, TEST_USER_PASSWORD)
    # if not token:
    #     return
    
    # For now, let's test with a manual token if you have one
    print("\n🔧 Manual Testing Mode")
    print("To test manually:")
    print("1. Login to the frontend and get your access token from localStorage")
    print("2. Replace 'YOUR_TOKEN_HERE' below with your actual token")
    print("3. Uncomment the test code")
    
    # Manual token testing (uncomment and replace with actual token)
    # token = "YOUR_TOKEN_HERE"
    # 
    # print("\n🧪 Starting payment flow test...")
    # 
    # # Step 1: Clear any existing debt
    # clear_all_debt(token)
    # 
    # # Step 2: Check initial status
    # initial_status = check_debt_status(token)
    # 
    # # Step 3: Add test debt
    # if add_test_debt(token, 0.01):
    #     # Step 4: Check debt after adding
    #     debt_status = check_debt_status(token)
    #     
    #     if debt_status and debt_status['profit_share_owed'] > 0:
    #         # Step 5: Make payment
    #         payment_result = make_test_payment(token, debt_status['profit_share_owed'])
    #         
    #         if payment_result:
    #             # Step 6: Check final status
    #             final_debt_status = check_debt_status(token)
    #             final_tier_status = get_tier_status(token)
    #             
    #             # Step 7: Verify payment was successful
    #             print("\n🔍 VERIFICATION RESULTS:")
    #             print("=" * 30)
    #             
    #             if final_debt_status and final_debt_status['profit_share_owed'] == 0:
    #                 print("✅ SUCCESS: Debt properly cleared!")
    #             else:
    #                 print("❌ FAILURE: Debt not properly cleared!")
    #                 print(f"   Expected: $0.00")
    #                 print(f"   Actual: ${final_debt_status['profit_share_owed'] if final_debt_status else 'Unknown'}")
    #             
    #             if final_debt_status and final_debt_status['payment_status'] == 'paid':
    #                 print("✅ SUCCESS: Payment status updated to 'paid'!")
    #             else:
    #                 print("❌ FAILURE: Payment status not updated!")
    #                 print(f"   Expected: 'paid'")
    #                 print(f"   Actual: '{final_debt_status['payment_status'] if final_debt_status else 'Unknown'}'")

if __name__ == "__main__":
    main()
