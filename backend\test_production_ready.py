#!/usr/bin/env python3
"""
Production Readiness Test for Hybrid Deep Learning System
Tests the complete integration with real Binance data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_binance_data_fetch():
    """Test fetching real data from Binance API"""
    logger.info("=" * 80)
    logger.info("📡 TESTING BINANCE API DATA FETCH")
    logger.info("=" * 80)
    
    try:
        import requests
        
        # Your Binance API credentials
        API_KEY = "73UnYTIA60rZV2c92ULMSUnEVHHjdaG5z1YAqlPz1oHT1ux1eQV5DZzLxFj21DNL"
        
        # Test API connection
        url = 'https://fapi.binance.com/fapi/v1/klines'
        params = {
            'symbol': 'BTCUSDT',
            'interval': '1h',
            'limit': 100
        }
        
        headers = {
            'X-MBX-APIKEY': API_KEY
        }
        
        logger.info("📡 Fetching 100 hours of BTC/USDT data...")
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()
        
        data = response.json()
        
        if data and len(data) > 0:
            # Convert to DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert to proper types
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col])
            
            df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
            
            logger.info(f"✅ Successfully fetched {len(df)} records")
            logger.info(f"📅 Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
            logger.info(f"📊 Price range: ${df['close'].min():.2f} - ${df['close'].max():.2f}")
            logger.info(f"📈 Current price: ${df['close'].iloc[-1]:.2f}")
            
            return df
        else:
            logger.error("❌ No data received from Binance API")
            return None
            
    except Exception as e:
        logger.error(f"❌ Binance API test failed: {e}")
        return None

def test_hybrid_system_with_real_data():
    """Test hybrid system with real Binance data"""
    logger.info("\n" + "=" * 80)
    logger.info("🚀 TESTING HYBRID SYSTEM WITH REAL BINANCE DATA")
    logger.info("=" * 80)
    
    try:
        # Fetch real market data
        market_data = test_binance_data_fetch()
        
        if market_data is None:
            logger.error("❌ Cannot test without market data")
            return False
        
        # Import hybrid system
        from app.services.hybrid_deep_ml import HybridDeepMLEnhancer
        
        # Initialize and load trained models
        logger.info("🤖 Loading trained hybrid deep learning models...")
        hybrid_enhancer = HybridDeepMLEnhancer()
        
        if not hybrid_enhancer.load_models():
            logger.error("❌ Could not load trained models")
            return False
        
        logger.info("✅ Hybrid models loaded successfully!")
        
        # Test Elite ML Enhancement
        logger.info("\n🧪 Testing Elite ML Enhancement with real data...")
        mock_traditional_prediction = {
            'signal': 'BUY',
            'confidence': 85.0,
            'method': 'ELITE_ML'
        }
        
        enhanced_prediction = hybrid_enhancer.enhance_elite_ml_prediction(
            market_data, mock_traditional_prediction
        )
        
        if enhanced_prediction:
            logger.info(f"✅ Enhanced Signal: {enhanced_prediction['signal']}")
            logger.info(f"📊 Enhanced Confidence: {enhanced_prediction.get('confidence', 0):.1f}%")
            logger.info(f"🎯 Method: {enhanced_prediction.get('method', 'Unknown')}")
            
            if 'deep_learning_enhancement' in enhanced_prediction:
                enhancement = enhanced_prediction['deep_learning_enhancement']
                logger.info(f"🚀 Deep Learning Applied: {enhancement.get('agreement', 'Unknown')}")
                logger.info(f"🤖 Deep Signal: {enhancement.get('deep_signal', 'Unknown')}")
                logger.info(f"📈 Deep Confidence: {enhancement.get('deep_confidence', 0):.1f}%")
        
        # Test SL/TP Enhancement
        logger.info("\n🧪 Testing SL/TP Enhancement with real data...")
        current_price = float(market_data['close'].iloc[-1])
        
        mock_sl_tp_prediction = {
            'sl_result': {'sl_price': current_price * 0.98, 'confidence': 75.0},
            'tp_result': {'tp_price': current_price * 1.04, 'confidence': 80.0},
            'final_risk_reward': 2.0,
            'system_status': 'OPTIMAL'
        }
        
        enhanced_sl_tp = hybrid_enhancer.enhance_sl_tp_prediction(
            market_data, current_price, 'BUY', mock_sl_tp_prediction
        )
        
        if enhanced_sl_tp:
            logger.info(f"✅ Enhanced SL: ${enhanced_sl_tp['sl_result']['sl_price']:.2f}")
            logger.info(f"✅ Enhanced TP: ${enhanced_sl_tp['tp_result']['tp_price']:.2f}")
            
            if 'deep_learning_enhancement' in enhanced_sl_tp:
                enhancement = enhanced_sl_tp['deep_learning_enhancement']
                logger.info(f"🚀 Deep SL: ${enhancement.get('deep_sl', 0):.2f}")
                logger.info(f"🚀 Deep TP: ${enhancement.get('deep_tp', 0):.2f}")
        
        logger.info("\n🎯 HYBRID SYSTEM TEST SUCCESSFUL!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Hybrid system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_status():
    """Test the integration status of all systems"""
    logger.info("\n" + "=" * 80)
    logger.info("🔍 CHECKING SYSTEM INTEGRATION STATUS")
    logger.info("=" * 80)
    
    try:
        # Check environment configuration
        logger.info("📋 Environment Configuration:")
        hybrid_enabled = os.getenv('HYBRID_DEEP_ML_ENABLED', 'false').lower() == 'true'
        traditional_weight = float(os.getenv('HYBRID_DEEP_ML_TRADITIONAL_WEIGHT', '0.7'))
        deep_weight = float(os.getenv('HYBRID_DEEP_ML_DEEP_WEIGHT', '0.3'))
        min_confidence = float(os.getenv('HYBRID_DEEP_ML_MIN_CONFIDENCE', '70.0'))
        
        logger.info(f"  • Hybrid Deep ML Enabled: {'✅' if hybrid_enabled else '❌'}")
        logger.info(f"  • Traditional ML Weight: {traditional_weight:.1f}")
        logger.info(f"  • Deep Learning Weight: {deep_weight:.1f}")
        logger.info(f"  • Minimum Confidence: {min_confidence:.1f}%")
        
        # Check model files
        logger.info("\n📁 Model Files:")
        model_files = [
            'models/hybrid_lstm_model.h5',
            'models/hybrid_cnn_model.h5', 
            'models/hybrid_ensemble_model.h5'
        ]
        
        models_exist = 0
        for model_file in model_files:
            if os.path.exists(model_file):
                logger.info(f"  • {model_file}: ✅")
                models_exist += 1
            else:
                logger.info(f"  • {model_file}: ❌")
        
        # Check system imports
        logger.info("\n🔧 System Components:")
        try:
            from app.services.hybrid_deep_ml import HybridDeepMLEnhancer
            logger.info("  • Hybrid Deep ML Service: ✅")
        except ImportError as e:
            logger.info(f"  • Hybrid Deep ML Service: ❌ ({e})")
        
        try:
            from app.services.trading_signals import TradingSignalGenerator
            logger.info("  • Trading Signals Integration: ✅")
        except ImportError as e:
            logger.info(f"  • Trading Signals Integration: ❌ ({e})")
        
        # Overall status
        logger.info("\n📊 INTEGRATION STATUS:")
        if hybrid_enabled and models_exist >= 2:
            logger.info("🎉 SYSTEM READY FOR PRODUCTION!")
            logger.info("✅ Hybrid deep learning is enabled and models are trained")
            logger.info("✅ System will automatically enhance ML predictions")
            return True
        else:
            logger.warning("⚠️ SYSTEM NOT FULLY READY")
            if not hybrid_enabled:
                logger.warning("• Enable hybrid system: HYBRID_DEEP_ML_ENABLED=true")
            if models_exist < 2:
                logger.warning("• Train models: python train_hybrid_deep_ml.py")
            return False
            
    except Exception as e:
        logger.error(f"❌ Integration status check failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("=" * 100)
    logger.info("🧪 DEEPTRADE HYBRID DEEP LEARNING PRODUCTION READINESS TEST")
    logger.info("=" * 100)
    
    try:
        # Test 1: Binance API connection
        binance_success = test_binance_data_fetch() is not None
        
        # Test 2: Hybrid system with real data
        hybrid_success = test_hybrid_system_with_real_data()
        
        # Test 3: Integration status
        integration_success = test_integration_status()
        
        # Final results
        logger.info("\n" + "=" * 100)
        logger.info("🏆 PRODUCTION READINESS TEST RESULTS")
        logger.info("=" * 100)
        
        logger.info(f"📡 Binance API Connection: {'✅ PASS' if binance_success else '❌ FAIL'}")
        logger.info(f"🚀 Hybrid System Test: {'✅ PASS' if hybrid_success else '❌ FAIL'}")
        logger.info(f"🔧 Integration Status: {'✅ READY' if integration_success else '⚠️ NOT READY'}")
        
        total_tests = 3
        passed_tests = sum([binance_success, hybrid_success, integration_success])
        
        if passed_tests == total_tests:
            logger.info(f"\n🎉 ALL TESTS PASSED ({passed_tests}/{total_tests})")
            logger.info("🚀 HYBRID DEEP LEARNING SYSTEM IS PRODUCTION READY!")
            logger.info("\n💡 The system will now:")
            logger.info("  • Enhance Elite ML predictions with deep learning")
            logger.info("  • Optimize SL/TP levels using pattern recognition")
            logger.info("  • Maintain 96% Elite ML accuracy as baseline")
            logger.info("  • Automatically fallback to traditional ML if needed")
            logger.info("\n🔍 Monitor logs for '[HYBRID_ML]' enhancement messages")
            return True
        else:
            logger.error(f"\n❌ TESTS FAILED ({passed_tests}/{total_tests})")
            logger.error("System needs attention before production use")
            return False
            
    except Exception as e:
        logger.error(f"\n❌ Test suite failed: {e}")
        return False

if __name__ == "__main__":
    main()
