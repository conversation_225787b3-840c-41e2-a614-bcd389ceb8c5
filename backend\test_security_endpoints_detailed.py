#!/usr/bin/env python3
"""
Detailed test of security endpoints to verify data structure
"""

import sys
import os
import requests
import json

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_security_endpoints_detailed():
    """Test security endpoints with detailed output"""
    print("🔍 DETAILED SECURITY ENDPOINTS TEST...")
    print("="*50)
    
    base_url = "http://localhost:5000"
    
    # First login to get token
    print("1. Logging in...")
    login_response = requests.post(f"{base_url}/api/auth/login", 
                                 json={
                                     "email": "<EMAIL>",
                                     "password": "testpassword123"
                                 },
                                 timeout=10)
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        return
    
    access_token = login_response.json().get('access_token')
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    print("✅ Login successful")
    
    # Test access logs endpoint
    print("\n2. Testing access logs endpoint...")
    logs_response = requests.get(f"{base_url}/api/users/security/access-logs?limit=5", 
                               headers=headers, timeout=10)
    
    if logs_response.status_code == 200:
        logs_data = logs_response.json()
        print("✅ Access logs endpoint working")
        print(f"   Total logs: {logs_data.get('total', 0)}")
        print(f"   Returned logs: {len(logs_data.get('access_logs', []))}")
        
        if logs_data.get('access_logs'):
            sample_log = logs_data['access_logs'][0]
            print("   Sample log structure:")
            for key, value in sample_log.items():
                print(f"     {key}: {type(value).__name__} = {value}")
    else:
        print(f"❌ Access logs failed: {logs_response.status_code}")
        print(f"   Error: {logs_response.text}")
    
    # Test security stats endpoint
    print("\n3. Testing security stats endpoint...")
    stats_response = requests.get(f"{base_url}/api/users/security/stats", 
                                headers=headers, timeout=10)
    
    if stats_response.status_code == 200:
        stats_data = stats_response.json()
        print("✅ Security stats endpoint working")
        print("   Stats structure:")
        for key, value in stats_data.items():
            if isinstance(value, dict):
                print(f"     {key}:")
                for subkey, subvalue in value.items():
                    print(f"       {subkey}: {type(subvalue).__name__} = {subvalue}")
            else:
                print(f"     {key}: {type(value).__name__} = {value}")
    else:
        print(f"❌ Security stats failed: {stats_response.status_code}")
        print(f"   Error: {stats_response.text}")
    
    # Test current session endpoint
    print("\n4. Testing current session endpoint...")
    session_response = requests.get(f"{base_url}/api/users/security/current-session", 
                                  headers=headers, timeout=10)
    
    if session_response.status_code == 200:
        session_data = session_response.json()
        print("✅ Current session endpoint working")
        print("   Session structure:")
        for key, value in session_data.items():
            if isinstance(value, dict):
                print(f"     {key}:")
                for subkey, subvalue in value.items():
                    print(f"       {subkey}: {type(subvalue).__name__} = {subvalue}")
            else:
                print(f"     {key}: {type(value).__name__} = {value}")
    else:
        print(f"❌ Current session failed: {session_response.status_code}")
        print(f"   Error: {session_response.text}")

if __name__ == "__main__":
    test_security_endpoints_detailed()
