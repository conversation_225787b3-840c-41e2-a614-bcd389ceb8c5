#!/usr/bin/env python3
"""
Test the current signal endpoint to verify the fix is working
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def login_admin():
    """Login as admin and get JWT token"""
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json={
            "email": ADMIN_EMAIL,
            "password": ADMIN_PASSWORD
        })
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            print(f"✅ Admin login successful")
            return token
        else:
            print(f"❌ Admin login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_current_signal_endpoint(token):
    """Test the current signal endpoint"""
    try:
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        print("🔍 Testing /api/admin/trading-bot/current-signal endpoint...")
        
        response = requests.get(f"{BASE_URL}/api/admin/trading-bot/current-signal", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Endpoint responded successfully!")
            print(f"   Signal: {data.get('signal', 'N/A')}")
            print(f"   Confidence: {data.get('confidence', 'N/A')}%")
            print(f"   Current Price: ${data.get('current_price', 'N/A'):,.2f}" if data.get('current_price') else "   Current Price: N/A")
            print(f"   Timestamp: {data.get('timestamp', 'N/A')}")
            
            if data.get('error'):
                print(f"   ⚠️  Error in response: {data.get('error')}")
            
            # Check if signal is not HOLD
            signal = data.get('signal', 'HOLD')
            if signal != 'HOLD':
                print(f"🎉 SUCCESS: Signal is {signal} (not HOLD)!")
                print("   This means the fix is working and conditions are being met!")
            else:
                print("ℹ️  Signal is HOLD - this could be correct if market conditions don't meet criteria")
                print("   Check the backend logs for detailed condition analysis")
            
            return True
            
        else:
            print(f"❌ Endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    print("🔍 TESTING CURRENT SIGNAL ENDPOINT AFTER FIX")
    print(f"Testing against: {BASE_URL}")
    print(f"Timestamp: {datetime.now()}")
    print()
    
    # Login first
    token = login_admin()
    if not token:
        print("❌ Cannot proceed without admin token")
        return
    
    # Test the current signal endpoint
    success = test_current_signal_endpoint(token)
    
    print()
    if success:
        print("🎉 ENDPOINT TEST COMPLETED!")
        print("The refresh button should now work properly.")
        print()
        print("💡 NEXT STEPS:")
        print("1. Check the backend logs for detailed signal generation info")
        print("2. Use the refresh button in the admin dashboard")
        print("3. Compare with terminal bot output to verify consistency")
    else:
        print("❌ ENDPOINT TEST FAILED!")
        print("Check the backend server and try again.")

if __name__ == "__main__":
    main()
