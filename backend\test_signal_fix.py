#!/usr/bin/env python3
"""
Quick test to verify the signal generation fix is working
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_signal_generation():
    """Test signal generation without Flask context"""
    
    print("🔍 TESTING SIGNAL GENERATION FIX")
    print("=" * 50)
    
    try:
        # Test import
        from app.services.trading_signals import TradingSignalGenerator
        print("✅ Successfully imported TradingSignalGenerator")
        
        # Test market data import
        from app.services.market_data import BinanceMarketData
        print("✅ Successfully imported BinanceMarketData")
        
        # Create market service
        market_service = BinanceMarketData()
        print("✅ Successfully created market service")
        
        # Test futures klines method
        if hasattr(market_service, 'get_futures_klines'):
            print("✅ Market service has get_futures_klines method")
        else:
            print("❌ Market service missing get_futures_klines method")
            
        # Test basic data fetch
        try:
            klines = market_service.get_futures_klines('BTCUSDT', '1h', 10)
            if klines and len(klines) > 0:
                print(f"✅ Successfully fetched {len(klines)} klines from futures API")
                print(f"   Latest price: ${float(klines[-1]['close']):,.2f}")
            else:
                print("❌ Failed to fetch klines or empty result")
        except Exception as e:
            print(f"❌ Error fetching klines: {e}")
        
        print()
        print("🎯 SUMMARY:")
        print("- All imports successful")
        print("- Market service created")
        print("- Futures API accessible")
        print("- Signal generator should now work with consistent data")
        print()
        print("💡 NEXT STEPS:")
        print("1. Restart the backend server")
        print("2. Use the refresh button in the admin dashboard")
        print("3. Check the backend logs for detailed signal generation info")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_signal_generation()
