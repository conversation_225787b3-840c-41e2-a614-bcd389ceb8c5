#!/usr/bin/env python3
"""
Simple test for ML integration using real market data
Tests the basic functionality of the integrated ML system
"""

import sys
import os
import pandas as pd
import numpy as np

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_ml_integration():
    """Simple test using real market data"""
    print("🧪 Simple ML Integration Test")
    print("=" * 40)
    
    try:
        # Import Flask app and create application context
        from app import create_app
        app = create_app()
        
        with app.app_context():
            # Import required services
            from app.services.market_data import BinanceMarketData
            from app.services.sl_tp_ml_predictors import SLTPMLManager
            
            # Initialize services
            market_service = BinanceMarketData()
            sl_tp_manager = SLTPMLManager()
            
            print("✅ Services initialized")
            
            # Test 1: SL/TP ML System
            print("\n🎯 Test 1: SL/TP ML System")
            print("-" * 30)
            
            # Create simple test data
            test_data = pd.DataFrame({
                'timestamp': [1640995200000 + (i * 3600000) for i in range(100)],
                'open': [65000 + i * 10 + np.random.normal(0, 50) for i in range(100)],
                'high': [65100 + i * 10 + np.random.normal(0, 60) for i in range(100)],
                'low': [64900 + i * 10 + np.random.normal(0, 40) for i in range(100)],
                'close': [65000 + i * 10 + np.random.normal(0, 45) for i in range(100)],
                'volume': [np.random.uniform(4000, 8000) for _ in range(100)]
            })
            
            # Ensure OHLC relationships
            for i in range(len(test_data)):
                open_price = test_data.loc[i, 'open']
                close_price = test_data.loc[i, 'close']
                test_data.loc[i, 'high'] = max(test_data.loc[i, 'high'], open_price, close_price)
                test_data.loc[i, 'low'] = min(test_data.loc[i, 'low'], open_price, close_price)
            
            # Test SL/TP predictions
            entry_price = 65500
            
            print(f"   📊 Testing BUY signal at ${entry_price:,.0f}")
            sl_tp_result = sl_tp_manager.get_optimal_sl_tp(test_data, entry_price, 'BUY')
            
            if sl_tp_result:
                print(f"   ✅ SL/TP Result:")
                print(f"      🛡️ Stop Loss: ${sl_tp_result['sl_result']['sl_price']:,.2f}")
                print(f"      🎯 Take Profit: ${sl_tp_result['tp_result']['tp_price']:,.2f}")
                print(f"      📈 Risk-Reward: 1:{sl_tp_result['final_risk_reward']:.2f}")
                print(f"      🤖 System Status: {sl_tp_result['system_status']}")
                print(f"      📊 Combined Confidence: {sl_tp_result['combined_confidence']:.1f}%")
            else:
                print("   ❌ SL/TP prediction failed")
            
            print(f"\n   📊 Testing SELL signal at ${entry_price:,.0f}")
            sl_tp_result_sell = sl_tp_manager.get_optimal_sl_tp(test_data, entry_price, 'SELL')
            
            if sl_tp_result_sell:
                print(f"   ✅ SL/TP Result:")
                print(f"      🛡️ Stop Loss: ${sl_tp_result_sell['sl_result']['sl_price']:,.2f}")
                print(f"      🎯 Take Profit: ${sl_tp_result_sell['tp_result']['tp_price']:,.2f}")
                print(f"      📈 Risk-Reward: 1:{sl_tp_result_sell['final_risk_reward']:.2f}")
                print(f"      🤖 System Status: {sl_tp_result_sell['system_status']}")
                print(f"      📊 Combined Confidence: {sl_tp_result_sell['combined_confidence']:.1f}%")
            else:
                print("   ❌ SL/TP prediction failed")
            
            # Test 2: Individual ML Predictors
            print("\n🎯 Test 2: Individual ML Predictors")
            print("-" * 35)
            
            from app.services.sl_tp_ml_predictors import StopLossMLPredictor, TakeProfitMLPredictor
            
            sl_predictor = StopLossMLPredictor()
            tp_predictor = TakeProfitMLPredictor()
            
            # Test SL predictor
            sl_result = sl_predictor.predict_optimal_sl(test_data, entry_price, 'BUY')
            print(f"   🛡️ SL Predictor: ${sl_result['sl_price']:,.2f} ({sl_result['confidence']:.1f}% confidence)")
            
            # Test TP predictor
            tp_result = tp_predictor.predict_optimal_tp(test_data, entry_price, 'BUY', sl_result['sl_price'])
            print(f"   🎯 TP Predictor: ${tp_result['tp_price']:,.2f} ({tp_result['confidence']:.1f}% confidence)")
            
            # Test 3: Real Market Data (if available)
            print("\n🎯 Test 3: Real Market Data")
            print("-" * 28)
            
            try:
                # Try to get real market data
                real_data = market_service.get_historical_data('BTCUSDT', '1h', 100)
                
                if real_data is not None and len(real_data) > 50:
                    print("   ✅ Real market data retrieved")
                    
                    current_price = float(real_data['close'].iloc[-1])
                    print(f"   📊 Current BTC Price: ${current_price:,.2f}")
                    
                    # Test with real data
                    real_sl_tp = sl_tp_manager.get_optimal_sl_tp(real_data, current_price, 'BUY')
                    
                    if real_sl_tp:
                        print(f"   ✅ Real Data SL/TP:")
                        print(f"      🛡️ Stop Loss: ${real_sl_tp['sl_result']['sl_price']:,.2f}")
                        print(f"      🎯 Take Profit: ${real_sl_tp['tp_result']['tp_price']:,.2f}")
                        print(f"      📈 Risk-Reward: 1:{real_sl_tp['final_risk_reward']:.2f}")
                        print(f"      🤖 System Status: {real_sl_tp['system_status']}")
                    else:
                        print("   ❌ Real data SL/TP prediction failed")
                else:
                    print("   ⚠️ Could not retrieve real market data")
                    
            except Exception as e:
                print(f"   ⚠️ Real market data test failed: {e}")
            
            # Test 4: Trading Signal Generator (Basic)
            print("\n🎯 Test 4: Trading Signal Generator")
            print("-" * 35)
            
            try:
                from app.services.trading_signals import TradingSignalGenerator
                from app.models.user import User
                from app import db
                
                # Get or create test user
                test_user = User.query.filter_by(email='<EMAIL>').first()
                if not test_user:
                    test_user = User(
                        email='<EMAIL>',
                        full_name='Test User',
                        password='test_password'
                    )
                    test_user.is_active = True
                    db.session.add(test_user)
                    db.session.commit()
                
                # Initialize signal generator
                signal_generator = TradingSignalGenerator(
                    user_id=str(test_user.id),
                    exchange_service=None,
                    admin_monitoring_mode=True
                )
                
                print(f"   ✅ Signal generator initialized")
                print(f"   🤖 Elite ML: {'Available' if signal_generator.elite_predictor else 'Not Available'}")
                print(f"   🎯 SL/TP ML: {'Available' if signal_generator.sl_tp_manager else 'Not Available'}")
                
                # Test ML SL/TP calculation directly
                if hasattr(signal_generator, '_calculate_ml_sl_tp'):
                    ml_sl_tp = signal_generator._calculate_ml_sl_tp(test_data, entry_price, 'BUY')
                    print(f"   ✅ ML SL/TP Calculation:")
                    print(f"      🛡️ SL: ${ml_sl_tp['stop_loss']:,.2f}")
                    print(f"      🎯 TP: ${ml_sl_tp['take_profit']:,.2f}")
                    print(f"      📈 RR: 1:{ml_sl_tp['risk_reward_ratio']:.2f}")
                    print(f"      🔧 Method: {ml_sl_tp['method']}")
                else:
                    print("   ❌ ML SL/TP method not found")
                
            except Exception as e:
                print(f"   ❌ Signal generator test failed: {e}")
                import traceback
                traceback.print_exc()
            
            # Summary
            print(f"\n🏆 TEST SUMMARY:")
            print("=" * 40)
            
            success_count = 0
            total_tests = 4
            
            if sl_tp_result and sl_tp_result_sell:
                success_count += 1
                print("✅ SL/TP ML System: Working")
            else:
                print("❌ SL/TP ML System: Failed")
            
            if sl_result and tp_result:
                success_count += 1
                print("✅ Individual Predictors: Working")
            else:
                print("❌ Individual Predictors: Failed")
            
            try:
                if real_sl_tp:
                    success_count += 1
                    print("✅ Real Market Data: Working")
                else:
                    print("⚠️ Real Market Data: Limited")
            except:
                print("⚠️ Real Market Data: Not tested")
            
            try:
                if signal_generator and signal_generator.sl_tp_manager:
                    success_count += 1
                    print("✅ Signal Generator Integration: Working")
                else:
                    print("❌ Signal Generator Integration: Failed")
            except:
                print("❌ Signal Generator Integration: Failed")
            
            success_rate = (success_count / total_tests) * 100
            print(f"\n📊 Success Rate: {success_count}/{total_tests} ({success_rate:.0f}%)")
            
            if success_rate >= 75:
                status = "🏆 EXCELLENT"
            elif success_rate >= 50:
                status = "✅ GOOD"
            elif success_rate >= 25:
                status = "⚖️ MODERATE"
            else:
                status = "❌ POOR"
            
            print(f"🎯 Overall Status: {status}")
            
            return {
                'success': success_rate >= 50,
                'success_rate': success_rate,
                'status': status,
                'tests_passed': success_count,
                'total_tests': total_tests
            }
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    print("🚀 DeepTrade Simple ML Integration Test")
    print("=" * 45)
    
    results = test_simple_ml_integration()
    
    if results and results['success']:
        print(f"\n🎉 SIMPLE TEST COMPLETE!")
        print(f"   🎯 Status: {results['status']}")
        print(f"   📊 Success Rate: {results['success_rate']:.0f}%")
        print(f"   ✅ Tests Passed: {results['tests_passed']}/{results['total_tests']}")
    else:
        error = results.get('error', 'Unknown error') if results else 'Test failed'
        print(f"\n❌ Simple test failed: {error}")
