#!/usr/bin/env python3
"""
Comprehensive test system for SL/TP ML models
Tests the new independent ML predictors for Stop Loss and Take Profit
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_sl_tp_ml_system():
    """Test the complete SL/TP ML system"""
    print("🧪 Testing SL/TP ML System")
    print("=" * 50)
    
    try:
        # Import Flask app and create application context
        from app import create_app
        app = create_app()
        
        with app.app_context():
            # Import required services
            from app.services.market_data import BinanceMarketData
            from app.services.sl_tp_ml_predictors import SLTPMLManager
            from train_sl_tp_models import train_sl_tp_models, load_trained_models
            
            # Initialize services
            market_service = BinanceMarketData()
            
            print("✅ Services initialized")
            
            # Check if models exist, if not train them
            print("\n🔍 Checking for trained models...")
            
            sl_predictor, tp_predictor = load_trained_models()
            
            if not sl_predictor or not tp_predictor or not sl_predictor.is_trained or not tp_predictor.is_trained:
                print("   ⚠️ Models not found or not trained. Training now...")
                training_success = train_sl_tp_models()
                
                if not training_success:
                    print("   ❌ Training failed. Using fallback methods.")
                    return False
                
                # Load the newly trained models
                sl_predictor, tp_predictor = load_trained_models()
            
            # Initialize the manager
            sl_tp_manager = SLTPMLManager()
            sl_tp_manager.sl_predictor = sl_predictor
            sl_tp_manager.tp_predictor = tp_predictor
            
            print(f"✅ Models loaded - SL: {sl_predictor.accuracy*100:.1f}%, TP: {tp_predictor.accuracy*100:.1f}%")
            
            # Test scenarios
            test_scenarios = [
                {
                    "name": "Bull Trending Market",
                    "trend": 0.8,
                    "volatility": 0.3,
                    "expected_signal": "BUY",
                    "description": "Strong uptrend with low volatility"
                },
                {
                    "name": "Bear Trending Market", 
                    "trend": -0.6,
                    "volatility": 0.4,
                    "expected_signal": "SELL",
                    "description": "Strong downtrend with medium volatility"
                },
                {
                    "name": "High Volatility Sideways",
                    "trend": 0.1,
                    "volatility": 0.9,
                    "expected_signal": "BUY",
                    "description": "Choppy market with high volatility"
                },
                {
                    "name": "Low Volatility Bull",
                    "trend": 0.4,
                    "volatility": 0.2,
                    "expected_signal": "BUY", 
                    "description": "Steady uptrend with low volatility"
                },
                {
                    "name": "Volatile Bear Market",
                    "trend": -0.8,
                    "volatility": 0.7,
                    "expected_signal": "SELL",
                    "description": "Strong downtrend with high volatility"
                }
            ]
            
            test_results = []
            
            for scenario in test_scenarios:
                print(f"\n🎯 Testing: {scenario['name']}")
                print(f"   📋 {scenario['description']}")
                print("-" * 40)
                
                # Generate test market data
                market_data = generate_test_market_data(
                    scenario['trend'], 
                    scenario['volatility'], 
                    200
                )
                
                if market_data is None:
                    print("   ❌ Failed to generate market data")
                    continue
                
                # Test multiple entry points
                entry_tests = []
                
                for test_idx in range(5):  # 5 tests per scenario
                    # Random entry point
                    entry_idx = np.random.randint(50, len(market_data) - 20)
                    entry_price = float(market_data.iloc[entry_idx]['close'])
                    signal = scenario['expected_signal']
                    
                    # Get historical data for prediction
                    historical_data = market_data.iloc[:entry_idx+1].copy()
                    
                    # Test individual predictors
                    print(f"   🔬 Test {test_idx + 1}: Entry at ${entry_price:,.0f}")
                    
                    # SL Prediction
                    sl_result = sl_predictor.predict_optimal_sl(historical_data, entry_price, signal)
                    print(f"      🛡️ SL: ${sl_result['sl_price']:,.0f} ({sl_result['sl_distance_pct']:.2f}%) - {sl_result['confidence']:.1f}% conf")
                    
                    # TP Prediction
                    tp_result = tp_predictor.predict_optimal_tp(historical_data, entry_price, signal, sl_result['sl_price'])
                    print(f"      🎯 TP: ${tp_result['tp_price']:,.0f} ({tp_result['tp_distance_pct']:.2f}%) - {tp_result['confidence']:.1f}% conf")
                    
                    # Combined prediction using manager
                    combined_result = sl_tp_manager.get_optimal_sl_tp(historical_data, entry_price, signal)
                    
                    if combined_result:
                        print(f"      🤖 Combined: RR={combined_result['final_risk_reward']:.2f}, Conf={combined_result['combined_confidence']:.1f}%")
                        print(f"      📊 Status: {combined_result['system_status']}")
                        
                        # Validate results
                        validation = validate_sl_tp_results(
                            entry_price, signal, sl_result, tp_result, combined_result
                        )
                        
                        entry_tests.append({
                            'entry_price': entry_price,
                            'sl_result': sl_result,
                            'tp_result': tp_result,
                            'combined_result': combined_result,
                            'validation': validation
                        })
                        
                        print(f"      ✅ Validation: {validation['status']}")
                        if validation['issues']:
                            for issue in validation['issues']:
                                print(f"         ⚠️ {issue}")
                    else:
                        print("      ❌ Combined prediction failed")
                
                # Scenario summary
                if entry_tests:
                    avg_sl_conf = np.mean([test['sl_result']['confidence'] for test in entry_tests])
                    avg_tp_conf = np.mean([test['tp_result']['confidence'] for test in entry_tests])
                    avg_rr = np.mean([test['combined_result']['final_risk_reward'] for test in entry_tests])
                    valid_tests = sum(1 for test in entry_tests if test['validation']['status'] == 'VALID')
                    
                    print(f"\n   📊 SCENARIO SUMMARY:")
                    print(f"      🎯 Avg SL Confidence: {avg_sl_conf:.1f}%")
                    print(f"      🎯 Avg TP Confidence: {avg_tp_conf:.1f}%")
                    print(f"      📈 Avg Risk-Reward: 1:{avg_rr:.2f}")
                    print(f"      ✅ Valid Tests: {valid_tests}/{len(entry_tests)}")
                    
                    scenario_assessment = assess_scenario_performance(avg_sl_conf, avg_tp_conf, avg_rr, valid_tests, len(entry_tests))
                    print(f"      🏅 Assessment: {scenario_assessment}")
                    
                    test_results.append({
                        'scenario': scenario['name'],
                        'avg_sl_conf': avg_sl_conf,
                        'avg_tp_conf': avg_tp_conf,
                        'avg_rr': avg_rr,
                        'valid_rate': valid_tests / len(entry_tests) * 100,
                        'assessment': scenario_assessment,
                        'entry_tests': entry_tests
                    })
            
            # Overall system assessment
            print(f"\n🏆 OVERALL SYSTEM ASSESSMENT:")
            print("=" * 50)
            
            if test_results:
                overall_sl_conf = np.mean([result['avg_sl_conf'] for result in test_results])
                overall_tp_conf = np.mean([result['avg_tp_conf'] for result in test_results])
                overall_rr = np.mean([result['avg_rr'] for result in test_results])
                overall_valid_rate = np.mean([result['valid_rate'] for result in test_results])
                
                print(f"📊 Average SL Confidence: {overall_sl_conf:.1f}%")
                print(f"📊 Average TP Confidence: {overall_tp_conf:.1f}%")
                print(f"📊 Average Risk-Reward: 1:{overall_rr:.2f}")
                print(f"📊 Overall Valid Rate: {overall_valid_rate:.1f}%")
                
                # System readiness assessment
                if (overall_sl_conf >= 70 and overall_tp_conf >= 70 and 
                    overall_rr >= 2.0 and overall_valid_rate >= 80):
                    system_status = "🏆 EXCELLENT - Ready for production"
                elif (overall_sl_conf >= 60 and overall_tp_conf >= 60 and 
                      overall_rr >= 1.8 and overall_valid_rate >= 70):
                    system_status = "✅ GOOD - Ready with monitoring"
                elif (overall_sl_conf >= 50 and overall_tp_conf >= 50 and 
                      overall_rr >= 1.5 and overall_valid_rate >= 60):
                    system_status = "⚖️ MODERATE - Needs improvement"
                else:
                    system_status = "❌ POOR - Requires retraining"
                
                print(f"\n🎯 SYSTEM STATUS: {system_status}")
                
                # Best performing scenario
                best_scenario = max(test_results, key=lambda x: x['avg_sl_conf'] + x['avg_tp_conf'] + x['avg_rr'] * 10)
                print(f"🥇 Best Scenario: {best_scenario['scenario']}")
                print(f"   📊 SL: {best_scenario['avg_sl_conf']:.1f}%, TP: {best_scenario['avg_tp_conf']:.1f}%, RR: 1:{best_scenario['avg_rr']:.2f}")
                
                return {
                    'success': True,
                    'overall_sl_conf': overall_sl_conf,
                    'overall_tp_conf': overall_tp_conf,
                    'overall_rr': overall_rr,
                    'overall_valid_rate': overall_valid_rate,
                    'system_status': system_status,
                    'best_scenario': best_scenario['scenario'],
                    'test_results': test_results
                }
            else:
                print("❌ No test results available")
                return {'success': False}
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False}

def generate_test_market_data(trend, volatility, candles):
    """Generate realistic test market data"""
    try:
        base_price = 65000
        prices = []
        
        for i in range(candles):
            # Trend component
            trend_component = (trend * i * 12)
            
            # Volatility component
            noise = np.random.normal(0, volatility * 200)
            
            # Add some realistic patterns
            cycle = np.sin(i * 0.15) * volatility * 60
            momentum = np.random.normal(0, 30) if i > 0 else 0
            
            # Price calculation
            price = base_price + trend_component + noise + cycle + momentum
            prices.append(max(price, 45000))  # Floor at $45k
        
        # Create OHLC data
        market_data = pd.DataFrame({
            'timestamp': [1640995200000 + (i * 3600000) for i in range(candles)],
            'open': prices,
            'high': [p * (1 + np.random.uniform(0.001, 0.008)) for p in prices],
            'low': [p * (1 - np.random.uniform(0.001, 0.008)) for p in prices],
            'close': [p * (1 + np.random.uniform(-0.003, 0.003)) for p in prices],
            'volume': [np.random.uniform(3500, 8500) for _ in range(candles)]
        })
        
        # Ensure OHLC relationships
        for i in range(len(market_data)):
            open_price = market_data.loc[i, 'open']
            close_price = market_data.loc[i, 'close']
            market_data.loc[i, 'high'] = max(market_data.loc[i, 'high'], open_price, close_price)
            market_data.loc[i, 'low'] = min(market_data.loc[i, 'low'], open_price, close_price)
        
        return market_data
        
    except Exception as e:
        return None

def validate_sl_tp_results(entry_price, signal, sl_result, tp_result, combined_result):
    """Validate SL/TP results for correctness"""
    issues = []
    
    try:
        sl_price = sl_result['sl_price']
        tp_price = tp_result['tp_price']
        
        # Check SL direction
        if signal == 'BUY' and sl_price >= entry_price:
            issues.append("SL should be below entry for BUY signal")
        elif signal == 'SELL' and sl_price <= entry_price:
            issues.append("SL should be above entry for SELL signal")
        
        # Check TP direction
        if signal == 'BUY' and tp_price <= entry_price:
            issues.append("TP should be above entry for BUY signal")
        elif signal == 'SELL' and tp_price >= entry_price:
            issues.append("TP should be below entry for SELL signal")
        
        # Check distances
        sl_distance_pct = abs(entry_price - sl_price) / entry_price * 100
        tp_distance_pct = abs(tp_price - entry_price) / entry_price * 100
        
        if sl_distance_pct < 0.2:
            issues.append("SL too tight (< 0.2%)")
        elif sl_distance_pct > 3.0:
            issues.append("SL too wide (> 3.0%)")
        
        if tp_distance_pct < 0.5:
            issues.append("TP too tight (< 0.5%)")
        elif tp_distance_pct > 8.0:
            issues.append("TP too wide (> 8.0%)")
        
        # Check risk-reward ratio
        risk = abs(entry_price - sl_price)
        reward = abs(tp_price - entry_price)
        rr_ratio = reward / risk if risk > 0 else 0
        
        if rr_ratio < 1.2:
            issues.append(f"Poor risk-reward ratio: 1:{rr_ratio:.2f}")
        
        # Check confidence levels
        if sl_result['confidence'] < 40:
            issues.append("Low SL confidence")
        if tp_result['confidence'] < 40:
            issues.append("Low TP confidence")
        
        status = "VALID" if len(issues) == 0 else "ISSUES"
        
        return {
            'status': status,
            'issues': issues,
            'sl_distance_pct': sl_distance_pct,
            'tp_distance_pct': tp_distance_pct,
            'risk_reward_ratio': rr_ratio
        }
        
    except Exception as e:
        return {
            'status': 'ERROR',
            'issues': [f"Validation error: {str(e)}"],
            'sl_distance_pct': 0,
            'tp_distance_pct': 0,
            'risk_reward_ratio': 0
        }

def assess_scenario_performance(sl_conf, tp_conf, rr, valid_tests, total_tests):
    """Assess scenario performance"""
    valid_rate = valid_tests / total_tests * 100
    
    if sl_conf >= 75 and tp_conf >= 75 and rr >= 2.5 and valid_rate >= 90:
        return "🏆 EXCELLENT"
    elif sl_conf >= 65 and tp_conf >= 65 and rr >= 2.0 and valid_rate >= 80:
        return "✅ GOOD"
    elif sl_conf >= 55 and tp_conf >= 55 and rr >= 1.8 and valid_rate >= 70:
        return "⚖️ MODERATE"
    else:
        return "❌ POOR"

if __name__ == "__main__":
    print("🚀 DeepTrade SL/TP ML System Test")
    print("=" * 50)
    
    results = test_sl_tp_ml_system()
    
    if results and results['success']:
        print(f"\n🎉 TESTING COMPLETE!")
        print(f"   🎯 System Status: {results['system_status']}")
        print(f"   📊 Performance: SL={results['overall_sl_conf']:.1f}%, TP={results['overall_tp_conf']:.1f}%")
        print(f"   📈 Risk-Reward: 1:{results['overall_rr']:.2f}")
        print(f"   ✅ Valid Rate: {results['overall_valid_rate']:.1f}%")
    else:
        print(f"\n❌ Testing failed - check logs for details")
