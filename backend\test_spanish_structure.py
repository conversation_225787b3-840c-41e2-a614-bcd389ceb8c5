#!/usr/bin/env python3
"""
Test the Spanish auth structure
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_spanish_structure():
    """Test the Spanish auth structure"""
    try:
        with open('../frontend/src/i18n/locales/es/common.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test structure
        has_auth_errors = bool(re.search(r'"auth":\s*{[^}]*"errors":\s*{[^}]*"emailInvalid"', content, re.DOTALL))
        has_auth_pwd_req = bool(re.search(r'"auth":\s*{[^}]*"passwordRequirements":\s*{[^}]*"title"', content, re.DOTALL))
        has_auth_login = bool(re.search(r'"auth":\s*{[^}]*"login":\s*{[^}]*"title"', content, re.DOTALL))
        has_auth_register = bool(re.search(r'"auth":\s*{[^}]*"register":\s*{[^}]*"title"', content, re.DOTALL))
        has_nested_errors = bool(re.search(r'"login":\s*{[^}]*"errors":', content, re.DOTALL))
        
        print('Spanish Auth Structure Test:')
        print(f'auth.errors: {"✅" if has_auth_errors else "❌"}')
        print(f'auth.passwordRequirements: {"✅" if has_auth_pwd_req else "❌"}')
        print(f'auth.login: {"✅" if has_auth_login else "❌"}')
        print(f'auth.register: {"✅" if has_auth_register else "❌"}')
        print(f'No nested errors: {"✅" if not has_nested_errors else "❌"}')
        
        all_correct = all([has_auth_errors, has_auth_pwd_req, has_auth_login, has_auth_register, not has_nested_errors])
        print(f'Overall: {"✅ PASS" if all_correct else "❌ FAIL"}')
        
        return all_correct
        
    except Exception as e:
        print(f'Error: {str(e)}')
        return False

if __name__ == "__main__":
    success = test_spanish_structure()
    sys.exit(0 if success else 1)
