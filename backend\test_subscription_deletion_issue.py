#!/usr/bin/env python3
"""
Test script to identify the specific subscription deletion issue
Focus on the UPDATE vs DELETE problem
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_subscription_deletion_issue():
    """Test the specific subscription deletion issue"""
    print("🧪 Testing subscription deletion issue...")
    
    try:
        from app import create_app, db
        from app.models.user import User
        from app.models.subscription import Subscription, SubscriptionTier
        from sqlalchemy import text
        
        app = create_app()
        
        with app.app_context():
            # Create test user
            import uuid
            unique_id = str(uuid.uuid4())[:8]
            test_user = User(
                email=f"sub.test.{unique_id}@example.com",
                full_name="Subscription Test User",
                password="testpassword123"
            )
            
            db.session.add(test_user)
            db.session.flush()
            
            user_id = test_user.id
            print(f"✅ Created test user: {user_id}")
            
            # Create subscription
            subscription = Subscription(user_id=user_id, tier=SubscriptionTier.TIER_1)
            db.session.add(subscription)
            db.session.commit()
            
            subscription_id = subscription.id
            print(f"✅ Created subscription: {subscription_id}")
            
            # Test different deletion approaches
            print("\n🔄 Testing different subscription deletion approaches...")
            
            # Approach 1: Direct SQLAlchemy deletion
            print("1️⃣ Testing direct SQLAlchemy deletion...")
            try:
                subs_to_delete = Subscription.query.filter_by(user_id=user_id).all()
                print(f"   Found {len(subs_to_delete)} subscriptions")
                
                for sub in subs_to_delete:
                    print(f"   Subscription details: ID={sub.id}, user_id={sub.user_id}, tier={sub.tier}")
                    db.session.delete(sub)
                
                db.session.commit()
                print("   ✅ Direct SQLAlchemy deletion successful")
                
                # Verify deletion
                remaining = Subscription.query.filter_by(user_id=user_id).count()
                print(f"   Remaining subscriptions: {remaining}")
                
                if remaining > 0:
                    print("   ❌ Subscriptions still exist!")
                    return False
                
            except Exception as e:
                print(f"   ❌ Direct SQLAlchemy deletion failed: {e}")
                db.session.rollback()
                
                # Try approach 2: Query.delete()
                print("\n2️⃣ Testing Query.delete() approach...")
                try:
                    count = Subscription.query.filter_by(user_id=user_id).count()
                    print(f"   Found {count} subscriptions to delete")
                    
                    deleted_count = Subscription.query.filter_by(user_id=user_id).delete()
                    print(f"   Query.delete() returned: {deleted_count}")
                    
                    db.session.commit()
                    print("   ✅ Query.delete() successful")
                    
                except Exception as e2:
                    print(f"   ❌ Query.delete() failed: {e2}")
                    db.session.rollback()
                    
                    # Try approach 3: Raw SQL
                    print("\n3️⃣ Testing raw SQL deletion...")
                    try:
                        result = db.session.execute(text(
                            "DELETE FROM subscriptions WHERE user_id = :user_id"
                        ), {"user_id": user_id})
                        print(f"   Raw SQL deleted: {result.rowcount}")
                        
                        db.session.commit()
                        print("   ✅ Raw SQL deletion successful")
                        
                    except Exception as e3:
                        print(f"   ❌ Raw SQL deletion failed: {e3}")
                        db.session.rollback()
                        return False
            
            # Now try to delete the user
            print("\n🔄 Testing user deletion after subscription cleanup...")
            try:
                User.query.filter_by(id=user_id).delete()
                db.session.commit()
                print("   ✅ User deletion successful")
                return True
                
            except Exception as user_error:
                print(f"   ❌ User deletion failed: {user_error}")
                db.session.rollback()
                return False
                
    except Exception as e:
        print(f"\n❌ Subscription deletion test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_subscription_model_relationships():
    """Test subscription model relationships that might cause UPDATE issues"""
    print("\n🧪 Testing subscription model relationships...")
    
    try:
        from app import create_app, db
        from app.models.subscription import Subscription
        from app.models.user import User
        
        app = create_app()
        
        with app.app_context():
            print("🔍 Checking Subscription model relationships...")
            
            # Check if there are any relationships that might cause cascading updates
            subscription_mapper = Subscription.__mapper__
            print(f"   Subscription table: {subscription_mapper.local_table.name}")
            
            print("   Relationships:")
            for rel_name, relationship in subscription_mapper.relationships.items():
                print(f"     {rel_name}: {relationship}")
                print(f"       Back populates: {getattr(relationship, 'back_populates', 'None')}")
                print(f"       Cascade: {getattr(relationship, 'cascade', 'None')}")
            
            print("   Foreign keys:")
            for fk in subscription_mapper.local_table.foreign_keys:
                print(f"     {fk}")
                print(f"       On delete: {fk.ondelete}")
                print(f"       On update: {fk.onupdate}")
            
            # Check User model relationships to Subscription
            print("\n   User model relationships to Subscription:")
            user_mapper = User.__mapper__
            for rel_name, relationship in user_mapper.relationships.items():
                if 'subscription' in rel_name.lower():
                    print(f"     {rel_name}: {relationship}")
                    print(f"       Back populates: {getattr(relationship, 'back_populates', 'None')}")
                    print(f"       Cascade: {getattr(relationship, 'cascade', 'None')}")
            
            return True
            
    except Exception as e:
        print(f"\n❌ Relationship test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_database_constraints():
    """Test database-level constraints that might cause issues"""
    print("\n🧪 Testing database constraints...")
    
    try:
        from app import create_app, db
        from sqlalchemy import text
        
        app = create_app()
        
        with app.app_context():
            print("🔍 Checking database constraints...")
            
            # Check foreign key constraints
            result = db.session.execute(text("""
                SELECT 
                    CONSTRAINT_NAME,
                    TABLE_NAME,
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME,
                    UPDATE_RULE,
                    DELETE_RULE
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE REFERENCED_TABLE_NAME = 'users' 
                AND TABLE_SCHEMA = DATABASE()
            """))
            
            constraints = result.fetchall()
            print(f"   Found {len(constraints)} foreign key constraints to users table:")
            
            for constraint in constraints:
                print(f"     {constraint[1]}.{constraint[2]} -> {constraint[3]}.{constraint[4]}")
                print(f"       Constraint: {constraint[0]}")
                print(f"       Update rule: {constraint[5]}")
                print(f"       Delete rule: {constraint[6]}")
                print()
            
            return True
            
    except Exception as e:
        print(f"\n❌ Database constraints test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 Starting subscription deletion issue investigation...")
    print("=" * 70)
    
    # Test 1: Subscription deletion approaches
    deletion_success = test_subscription_deletion_issue()
    
    # Test 2: Model relationships
    relationships_success = test_subscription_model_relationships()
    
    # Test 3: Database constraints
    constraints_success = test_database_constraints()
    
    print("\n" + "=" * 70)
    if deletion_success and relationships_success and constraints_success:
        print("🎉 SUBSCRIPTION DELETION INVESTIGATION COMPLETE!")
        print("   All tests passed - the issue should be resolved.")
    else:
        print("💥 SUBSCRIPTION DELETION ISSUES IDENTIFIED!")
        print("   Check the test results above for specific problems.")
        
    print("=" * 70)
