#!/usr/bin/env python3
"""
Test script to identify subscription integrity issues
The error shows user_id cannot be null in subscriptions UPDATE
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_subscription_integrity():
    """Test subscription table integrity and foreign key constraints"""
    print("🧪 Testing subscription table integrity...")
    
    try:
        from app import create_app, db
        from app.models.subscription import Subscription
        from app.models.user import User
        from sqlalchemy import text
        
        app = create_app()
        
        with app.app_context():
            print("🔍 Checking subscription table structure...")
            
            # Check for orphaned subscriptions (subscriptions without valid users)
            result = db.session.execute(text("""
                SELECT s.id, s.user_id, u.id as actual_user_id
                FROM subscriptions s
                LEFT JOIN users u ON s.user_id = u.id
                WHERE u.id IS NULL
            """))
            
            orphaned_subscriptions = result.fetchall()
            if orphaned_subscriptions:
                print(f"❌ Found {len(orphaned_subscriptions)} orphaned subscriptions:")
                for sub in orphaned_subscriptions:
                    print(f"   Subscription ID: {sub[0]}, User ID: {sub[1]}")
            else:
                print("✅ No orphaned subscriptions found")
            
            # Check for subscriptions with NULL user_id
            result = db.session.execute(text("""
                SELECT id, user_id FROM subscriptions WHERE user_id IS NULL
            """))
            
            null_user_subscriptions = result.fetchall()
            if null_user_subscriptions:
                print(f"❌ Found {len(null_user_subscriptions)} subscriptions with NULL user_id:")
                for sub in null_user_subscriptions:
                    print(f"   Subscription ID: {sub[0]}")
            else:
                print("✅ No subscriptions with NULL user_id found")
            
            # Check subscription table constraints
            result = db.session.execute(text("""
                SHOW CREATE TABLE subscriptions
            """))
            
            table_structure = result.fetchone()
            print(f"\n📋 Subscription table structure:")
            print(table_structure[1])
            
            return len(orphaned_subscriptions) == 0 and len(null_user_subscriptions) == 0
            
    except Exception as e:
        print(f"\n❌ Subscription integrity test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_admin_deletion_with_debug():
    """Test admin deletion with detailed debugging"""
    print("\n🧪 Testing admin deletion with detailed debugging...")
    
    try:
        from app import create_app, db
        from app.models.user import User
        from app.models.subscription import Subscription, SubscriptionTier
        from app.models.user_tier_status import UserTierStatus
        from sqlalchemy import text
        
        app = create_app()
        
        with app.app_context():
            # Create test user
            import uuid
            unique_id = str(uuid.uuid4())[:8]
            test_user = User(
                email=f"debug.test.{unique_id}@example.com",
                full_name="Debug Test User",
                password="testpassword123"
            )
            
            db.session.add(test_user)
            db.session.flush()
            
            user_id = test_user.id
            print(f"✅ Created debug test user: {user_id}")
            
            # Create subscription
            subscription = Subscription(user_id=user_id, tier=SubscriptionTier.TIER_1)
            db.session.add(subscription)
            db.session.flush()
            
            subscription_id = subscription.id
            print(f"✅ Created subscription: {subscription_id}")
            
            # Create tier status
            tier_status = UserTierStatus(user_id=user_id, tier_1=True, tier_2=False, tier_3=False)
            db.session.add(tier_status)
            db.session.commit()
            
            print(f"✅ Created tier status")
            
            # Now test deletion step by step with debugging
            print(f"\n🔍 Starting deletion process for user: {user_id}")
            
            # Check what exists before deletion
            print("📊 Before deletion:")
            user_count = User.query.filter_by(id=user_id).count()
            sub_count = Subscription.query.filter_by(user_id=user_id).count()
            tier_count = UserTierStatus.query.filter_by(user_id=user_id).count()
            print(f"   Users: {user_count}")
            print(f"   Subscriptions: {sub_count}")
            print(f"   Tier status: {tier_count}")
            
            # Step 1: Delete subscriptions first
            print("\n🔄 Step 1: Deleting subscriptions...")
            try:
                subscriptions_to_delete = Subscription.query.filter_by(user_id=user_id).all()
                print(f"   Found {len(subscriptions_to_delete)} subscriptions to delete")
                
                for sub in subscriptions_to_delete:
                    print(f"   Deleting subscription: {sub.id} for user: {sub.user_id}")
                    db.session.delete(sub)
                
                db.session.commit()
                print("   ✅ Subscriptions deleted successfully")
                
            except Exception as e:
                print(f"   ❌ Error deleting subscriptions: {e}")
                db.session.rollback()
                return False
            
            # Step 2: Delete tier status
            print("\n🔄 Step 2: Deleting tier status...")
            try:
                tier_statuses_to_delete = UserTierStatus.query.filter_by(user_id=user_id).all()
                print(f"   Found {len(tier_statuses_to_delete)} tier statuses to delete")
                
                for tier in tier_statuses_to_delete:
                    print(f"   Deleting tier status: {tier.id} for user: {tier.user_id}")
                    db.session.delete(tier)
                
                db.session.commit()
                print("   ✅ Tier status deleted successfully")
                
            except Exception as e:
                print(f"   ❌ Error deleting tier status: {e}")
                db.session.rollback()
                return False
            
            # Step 3: Delete user
            print("\n🔄 Step 3: Deleting user...")
            try:
                users_to_delete = User.query.filter_by(id=user_id).all()
                print(f"   Found {len(users_to_delete)} users to delete")
                
                for user in users_to_delete:
                    print(f"   Deleting user: {user.id} ({user.email})")
                    db.session.delete(user)
                
                db.session.commit()
                print("   ✅ User deleted successfully")
                
            except Exception as e:
                print(f"   ❌ Error deleting user: {e}")
                db.session.rollback()
                return False
            
            # Verify deletion
            print("\n🔍 Verifying deletion:")
            user_count = User.query.filter_by(id=user_id).count()
            sub_count = Subscription.query.filter_by(user_id=user_id).count()
            tier_count = UserTierStatus.query.filter_by(user_id=user_id).count()
            print(f"   Users remaining: {user_count}")
            print(f"   Subscriptions remaining: {sub_count}")
            print(f"   Tier status remaining: {tier_count}")
            
            if user_count == 0 and sub_count == 0 and tier_count == 0:
                print("   ✅ Complete deletion successful!")
                return True
            else:
                print("   ❌ Deletion incomplete!")
                return False
                
    except Exception as e:
        print(f"\n❌ Debug deletion test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_raw_sql_deletion():
    """Test deletion using raw SQL to avoid ORM issues"""
    print("\n🧪 Testing raw SQL deletion...")
    
    try:
        from app import create_app, db
        from app.models.user import User
        from app.models.subscription import Subscription, SubscriptionTier
        from app.models.user_tier_status import UserTierStatus
        from sqlalchemy import text
        
        app = create_app()
        
        with app.app_context():
            # Create test user
            import uuid
            unique_id = str(uuid.uuid4())[:8]
            test_user = User(
                email=f"rawsql.test.{unique_id}@example.com",
                full_name="Raw SQL Test User",
                password="testpassword123"
            )
            
            db.session.add(test_user)
            db.session.flush()
            
            user_id = test_user.id
            print(f"✅ Created raw SQL test user: {user_id}")
            
            # Create subscription
            subscription = Subscription(user_id=user_id, tier=SubscriptionTier.TIER_1)
            db.session.add(subscription)
            
            # Create tier status
            tier_status = UserTierStatus(user_id=user_id, tier_1=True, tier_2=False, tier_3=False)
            db.session.add(tier_status)
            db.session.commit()
            
            print(f"✅ Created test data")
            
            # Now test raw SQL deletion
            print(f"\n🔄 Testing raw SQL deletion for user: {user_id}")
            
            # Step 1: Delete subscriptions using raw SQL
            print("   🔄 Deleting subscriptions with raw SQL...")
            result = db.session.execute(text(
                "DELETE FROM subscriptions WHERE user_id = :user_id"
            ), {"user_id": user_id})
            print(f"   ✅ Deleted {result.rowcount} subscriptions")
            
            # Step 2: Delete tier status using raw SQL
            print("   🔄 Deleting tier status with raw SQL...")
            result = db.session.execute(text(
                "DELETE FROM user_tier_status WHERE user_id = :user_id"
            ), {"user_id": user_id})
            print(f"   ✅ Deleted {result.rowcount} tier status records")
            
            # Step 3: Delete user using raw SQL
            print("   🔄 Deleting user with raw SQL...")
            result = db.session.execute(text(
                "DELETE FROM users WHERE id = :user_id"
            ), {"user_id": user_id})
            print(f"   ✅ Deleted {result.rowcount} users")
            
            db.session.commit()
            
            # Verify deletion
            print("\n🔍 Verifying raw SQL deletion:")
            result = db.session.execute(text("SELECT COUNT(*) FROM users WHERE id = :user_id"), {"user_id": user_id})
            user_count = result.scalar()
            
            result = db.session.execute(text("SELECT COUNT(*) FROM subscriptions WHERE user_id = :user_id"), {"user_id": user_id})
            sub_count = result.scalar()
            
            result = db.session.execute(text("SELECT COUNT(*) FROM user_tier_status WHERE user_id = :user_id"), {"user_id": user_id})
            tier_count = result.scalar()
            
            print(f"   Users remaining: {user_count}")
            print(f"   Subscriptions remaining: {sub_count}")
            print(f"   Tier status remaining: {tier_count}")
            
            if user_count == 0 and sub_count == 0 and tier_count == 0:
                print("   ✅ Raw SQL deletion successful!")
                return True
            else:
                print("   ❌ Raw SQL deletion incomplete!")
                return False
                
    except Exception as e:
        print(f"\n❌ Raw SQL deletion test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 Starting comprehensive subscription integrity tests...")
    print("=" * 70)
    
    # Test 1: Check subscription integrity
    integrity_success = test_subscription_integrity()
    
    # Test 2: Debug admin deletion
    if integrity_success:
        debug_success = test_admin_deletion_with_debug()
    else:
        debug_success = False
    
    # Test 3: Raw SQL deletion
    if debug_success:
        raw_sql_success = test_raw_sql_deletion()
    else:
        raw_sql_success = False
    
    print("\n" + "=" * 70)
    if integrity_success and debug_success and raw_sql_success:
        print("🎉 ALL SUBSCRIPTION INTEGRITY TESTS PASSED!")
        print("   The admin deletion should work correctly now.")
    else:
        print("💥 SUBSCRIPTION INTEGRITY TESTS FAILED!")
        print("   Issues need to be fixed before web testing.")
        
    print("=" * 70)
