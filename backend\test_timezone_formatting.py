#!/usr/bin/env python3
"""
Test script to verify GMT-3 timezone formatting in JavaScript
"""

import json
from datetime import datetime

# Create a test HTML file to verify the timezone formatting
html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>GMT-3 Timezone Test</title>
</head>
<body>
    <h1>GMT-3 Timezone Formatting Test</h1>
    <div id="results"></div>
    
    <script>
        // Test timestamp (current UTC time)
        const testTimestamp = new Date().toISOString();
        console.log('UTC Timestamp:', testTimestamp);
        
        // Format with GMT-3 (America/Sao_Paulo)
        const gmt3Formatted = new Date(testTimestamp).toLocaleString('en-US', {
            timeZone: 'America/Sao_Paulo', // GMT-3
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
        
        // Format with UTC for comparison
        const utcFormatted = new Date(testTimestamp).toLocaleString('en-US', {
            timeZone: 'UTC',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
        
        const results = document.getElementById('results');
        results.innerHTML = `
            <p><strong>UTC Time:</strong> ${utcFormatted}</p>
            <p><strong>GMT-3 Time:</strong> ${gmt3Formatted}</p>
            <p><strong>Difference:</strong> GMT-3 should be 3 hours behind UTC</p>
        `;
        
        console.log('UTC Formatted:', utcFormatted);
        console.log('GMT-3 Formatted:', gmt3Formatted);
    </script>
</body>
</html>
"""

# Save the test file
with open('timezone_test.html', 'w') as f:
    f.write(html_content)

print("✅ Created timezone_test.html")
print("📝 Open this file in a browser to verify GMT-3 formatting")
print("🕐 The GMT-3 time should be 3 hours behind UTC time")
