#!/usr/bin/env python3
"""
Test script for Trading Bot Monitoring endpoints
Tests all the endpoints that the admin dashboard uses for trading bot data
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"

def login_admin():
    """Login as admin and get token"""
    print("🔐 LOGGING IN AS ADMIN")
    print("="*50)
    
    login_data = {
        "username": ADMIN_USERNAME,
        "password": ADMIN_PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/api/admin/login", json=login_data)
    
    if response.status_code == 200:
        data = response.json()
        token = data.get('access_token')
        print(f"✅ Login successful")
        print(f"Token preview: {token[:50]}...")
        return token
    else:
        print(f"❌ Login failed: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def test_endpoint(endpoint, token, description):
    """Test a specific endpoint"""
    print(f"\n🧪 TESTING: {description}")
    print(f"URL: {BASE_URL}{endpoint}")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SUCCESS")
            print(f"Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            
            # Show some sample data
            if isinstance(data, dict):
                for key, value in list(data.items())[:3]:  # Show first 3 items
                    if isinstance(value, (str, int, float, bool)):
                        print(f"  {key}: {value}")
                    elif isinstance(value, dict):
                        print(f"  {key}: {dict(list(value.items())[:2])}")  # Show first 2 sub-items
                    else:
                        print(f"  {key}: {type(value).__name__}")
            
            return True
        else:
            print(f"❌ FAILED")
            print(f"Response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

def main():
    print("🔍 TRADING BOT MONITORING ENDPOINTS TEST")
    print(f"Testing against: {BASE_URL}")
    print(f"Timestamp: {datetime.now()}")
    print()
    
    # Login first
    token = login_admin()
    if not token:
        print("❌ Cannot proceed without admin token")
        return
    
    # List of endpoints to test (matching the frontend calls)
    endpoints_to_test = [
        ("/api/admin/trading-bot/health", "System Health"),
        ("/api/admin/trading-bot/performance", "Performance Data"),
        ("/api/admin/trading-bot/signals", "Trading Signals"),
        ("/api/admin/trading-bot/alerts", "Trading Alerts"),
        ("/api/admin/trading-bot/status", "Trading Bot Status"),
        ("/api/admin/trading-bot/containers", "Active Containers"),
        ("/api/admin/trading-bot/conditions", "Trading Conditions"),
    ]
    
    print("\n" + "="*70)
    print("TESTING ALL TRADING BOT ENDPOINTS")
    print("="*70)
    
    results = {}
    for endpoint, description in endpoints_to_test:
        success = test_endpoint(endpoint, token, description)
        results[endpoint] = success
    
    # Summary
    print("\n" + "="*70)
    print("📊 TEST SUMMARY")
    print("="*70)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    failed_tests = total_tests - passed_tests
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests} ✅")
    print(f"Failed: {failed_tests} ❌")
    
    if failed_tests > 0:
        print(f"\n❌ FAILED ENDPOINTS:")
        for endpoint, success in results.items():
            if not success:
                print(f"  - {endpoint}")
    
    print(f"\n🎯 SUCCESS RATE: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 ALL ENDPOINTS WORKING!")
    else:
        print("⚠️  Some endpoints need attention")

if __name__ == "__main__":
    main()
