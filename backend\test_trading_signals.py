#!/usr/bin/env python3
"""Test trading signals generation to debug zero values in admin terminal."""

import logging
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.trading_signals import TradingSignalGenerator

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_trading_signals():
    """Test the trading signals generation."""
    print("🔍 Testing Trading Signals Generation...")
    print("=" * 50)
    
    try:
        # Create signal generator
        signal_gen = TradingSignalGenerator()
        
        # Generate signals for BTCUSDT
        print("📊 Generating signals for BTCUSDT...")
        signals = signal_gen.generate_signals('BTCUSDT')
        
        if not signals:
            print("❌ No signals returned!")
            return
            
        if 'error' in signals:
            print(f"❌ Error in signals: {signals['error']}")
            return
            
        print(f"✅ Signals generated successfully!")
        print()
        
        # Display key signal information
        signal_type = signals.get('signal', 'UNKNOWN')
        confidence = signals.get('confidence', 0)
        current_price = signals.get('current_price', 0)
        swing_high = signals.get('swing_high', None)
        swing_low = signals.get('swing_low', None)
        potential_up_move = signals.get('potential_up_move', 0)
        potential_down_move = signals.get('potential_down_move', 0)
        ha_color = signals.get('ha_color', 'unknown')
        prev_ha_color = signals.get('prev_ha_color', 'unknown')
        
        print(f"🎯 Signal: {signal_type}")
        print(f"📊 Confidence: {confidence}%")
        print(f"💰 Current price: ${current_price:,.2f}")
        print(f"🔺 Swing high: {swing_high}")
        print(f"🔻 Swing low: {swing_low}")
        print(f"📈 Potential up move: {potential_up_move}%")
        print(f"📉 Potential down move: {potential_down_move}%")
        print(f"🟢 HA Color: {ha_color}")
        print(f"🔄 Prev HA Color: {prev_ha_color}")
        print()
        
        # Check BUY conditions
        print("🔍 BUY Signal Conditions:")
        buy_swing_condition = swing_low is not None and swing_low < current_price
        buy_move_condition = potential_up_move > 1.0
        buy_trend_condition = ha_color == "green" and prev_ha_color == "green"
        
        print(f"   Swing Low < Price: {buy_swing_condition} (swing_low: {swing_low}, price: {current_price})")
        print(f"   Up Move > 1%: {buy_move_condition} ({potential_up_move}%)")
        print(f"   Green Trend: {buy_trend_condition} (current: {ha_color}, prev: {prev_ha_color})")
        print()
        
        # Check SELL conditions  
        print("🔍 SELL Signal Conditions:")
        sell_swing_condition = swing_high is not None and swing_high > current_price
        sell_move_condition = potential_down_move > 1.0
        sell_trend_condition = ha_color == "red" and prev_ha_color == "red"
        
        print(f"   Swing High > Price: {sell_swing_condition} (swing_high: {swing_high}, price: {current_price})")
        print(f"   Down Move > 1%: {sell_move_condition} ({potential_down_move}%)")
        print(f"   Red Trend: {sell_trend_condition} (current: {ha_color}, prev: {prev_ha_color})")
        print()
        
        # Analyze why conditions are failing
        print("🔧 Analysis:")
        if swing_high is None and swing_low is None:
            print("❌ No swing points found - this is a major issue!")
        if potential_up_move <= 1.0 and potential_down_move <= 1.0:
            print("❌ Potential moves too small (both < 1%)")
        if ha_color not in ['green', 'red']:
            print(f"❌ Invalid HA color: {ha_color}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_trading_signals()
