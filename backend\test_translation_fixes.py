#!/usr/bin/env python3
"""
Test script to verify AccessSecurity translation fixes
"""

import sys
import os
import json

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_navigation_access_security():
    """Test that navigation.accessSecurity key exists in all language files"""
    print("Testing Navigation AccessSecurity Key...")
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    for lang in languages:
        print(f"  Testing {lang.upper()}:")
        
        try:
            with open(f'../frontend/src/i18n/locales/{lang}/common.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Check if navigation.accessSecurity exists
            if 'navigation' in data and 'accessSecurity' in data['navigation']:
                value = data['navigation']['accessSecurity']
                print(f"     [PASS] navigation.accessSecurity = '{value}'")
            else:
                print(f"     [FAIL] navigation.accessSecurity missing")
                return False
                
        except Exception as e:
            print(f"     [ERROR] Error reading {lang} file: {str(e)}")
            return False
    
    return True

def test_missing_common_keys():
    """Test that previously missing common keys are now present"""
    print("Testing Missing Common Keys...")
    
    required_keys = ['and', 'apply', 'clear', 'copied', 'copy', 'disable', 'download', 
                    'enable', 'export', 'filter', 'import', 'info', 'ok', 'previous', 
                    'print', 'refresh', 'reset', 'search', 'select', 'share', 'sort', 
                    'submit', 'toggleOff', 'toggleOn', 'upload']
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    for lang in languages:
        print(f"  Testing {lang.upper()}:")
        
        try:
            with open(f'../frontend/src/i18n/locales/{lang}/common.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            missing_keys = []
            if 'common' in data:
                for key in required_keys:
                    if key not in data['common']:
                        missing_keys.append(key)
            else:
                missing_keys = required_keys
            
            if missing_keys:
                print(f"     [FAIL] Missing keys: {', '.join(missing_keys)}")
                return False
            else:
                print(f"     [PASS] All {len(required_keys)} common keys present")
                
        except Exception as e:
            print(f"     [ERROR] Error reading {lang} file: {str(e)}")
            return False
    
    return True

def test_auth_error_keys():
    """Test that auth error keys are present"""
    print("Testing Auth Error Keys...")
    
    required_auth_keys = ['emailInvalid', 'emailRequired', 'invalidCredentials', 
                         'passwordRequired', 'passwordTooShort', 'passwordsNotMatch', 
                         'termsRequired']
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    for lang in languages:
        print(f"  Testing {lang.upper()}:")
        
        try:
            with open(f'../frontend/src/i18n/locales/{lang}/common.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            missing_keys = []
            if 'auth' in data and 'errors' in data['auth']:
                for key in required_auth_keys:
                    if key not in data['auth']['errors']:
                        missing_keys.append(key)
            else:
                missing_keys = required_auth_keys
            
            if missing_keys:
                print(f"     [FAIL] Missing auth error keys: {', '.join(missing_keys)}")
                return False
            else:
                print(f"     [PASS] All {len(required_auth_keys)} auth error keys present")
                
        except Exception as e:
            print(f"     [ERROR] Error reading {lang} file: {str(e)}")
            return False
    
    return True

def test_trading_keys():
    """Test that trading keys are present"""
    print("Testing Trading Keys...")
    
    required_trading_keys = ['autoMoveStopLoss', 'autoTradingActiveDesc', 'autoTradingDisabledDesc',
                           'enableAutoTrading', 'executeTradeButton', 'firstTp', 'historyDesc',
                           'noActiveSignals', 'noOpenPositions', 'noTradingHistory', 'positionsDesc',
                           'refreshSignals', 'secondTp', 'strategy', 'viewDetailsButton', 'waitingForSignals']
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    for lang in languages:
        print(f"  Testing {lang.upper()}:")
        
        try:
            with open(f'../frontend/src/i18n/locales/{lang}/common.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            missing_keys = []
            if 'trading' in data:
                for key in required_trading_keys:
                    if key not in data['trading']:
                        missing_keys.append(key)
            else:
                missing_keys = required_trading_keys
            
            if missing_keys:
                print(f"     [FAIL] Missing trading keys: {', '.join(missing_keys)}")
                return False
            else:
                print(f"     [PASS] All {len(required_trading_keys)} trading keys present")
                
        except Exception as e:
            print(f"     [ERROR] Error reading {lang} file: {str(e)}")
            return False
    
    return True

def test_password_requirements_keys():
    """Test that password requirements keys are present"""
    print("Testing Password Requirements Keys...")
    
    required_password_keys = ['length', 'lowercase', 'number', 'special', 'title', 'uppercase']
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    for lang in languages:
        print(f"  Testing {lang.upper()}:")
        
        try:
            with open(f'../frontend/src/i18n/locales/{lang}/common.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            missing_keys = []
            if 'auth' in data and 'passwordRequirements' in data['auth']:
                for key in required_password_keys:
                    if key not in data['auth']['passwordRequirements']:
                        missing_keys.append(key)
            else:
                missing_keys = required_password_keys
            
            if missing_keys:
                print(f"     [FAIL] Missing password requirement keys: {', '.join(missing_keys)}")
                return False
            else:
                print(f"     [PASS] All {len(required_password_keys)} password requirement keys present")
                
        except Exception as e:
            print(f"     [ERROR] Error reading {lang} file: {str(e)}")
            return False
    
    return True

def test_json_validity():
    """Test that all JSON files are valid"""
    print("Testing JSON File Validity...")
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    for lang in languages:
        print(f"  Testing {lang.upper()}:")
        
        try:
            with open(f'../frontend/src/i18n/locales/{lang}/common.json', 'r', encoding='utf-8') as f:
                json.load(f)
            print(f"     [PASS] Valid JSON structure")
                
        except json.JSONDecodeError as e:
            print(f"     [FAIL] Invalid JSON: {str(e)}")
            return False
        except Exception as e:
            print(f"     [ERROR] Error reading {lang} file: {str(e)}")
            return False
    
    return True

def run_all_tests():
    """Run all translation fix tests"""
    print("ACCESSSECURITY TRANSLATION FIX TEST")
    print("=" * 60)
    
    tests = [
        ("Navigation AccessSecurity Key", test_navigation_access_security),
        ("Missing Common Keys", test_missing_common_keys),
        ("Auth Error Keys", test_auth_error_keys),
        ("Trading Keys", test_trading_keys),
        ("Password Requirements Keys", test_password_requirements_keys),
        ("JSON File Validity", test_json_validity)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"  [PASS] {test_name} completed successfully")
            else:
                failed += 1
                print(f"  [FAIL] {test_name} failed")
        except Exception as e:
            failed += 1
            print(f"  [ERROR] {test_name} error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("TRANSLATION FIX TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {len(tests)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed/len(tests)*100):.1f}%")
    
    if failed == 0:
        print("\nALL TESTS PASSED! Translation fixes implemented successfully.")
        print("\nFix Details:")
        print("✅ Added navigation.accessSecurity key to all 8 language files")
        print("✅ Added missing common keys (and, toggleOff, toggleOn, etc.) to all languages")
        print("✅ Verified auth error keys are present in all languages")
        print("✅ Verified trading keys are present in all languages")
        print("✅ Verified password requirement keys are present in all languages")
        print("✅ All JSON files have valid structure")
        print("\n🎯 SIDEBAR TRANSLATION ISSUE RESOLVED!")
        print("🎯 CONSOLE WARNING ISSUES RESOLVED!")
        return True
    else:
        print(f"\n{failed} test(s) failed. Please review implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
