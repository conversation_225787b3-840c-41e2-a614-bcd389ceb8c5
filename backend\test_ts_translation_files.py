#!/usr/bin/env python3
"""
Test script to verify .ts translation files have the correct navigation.accessSecurity key
"""

import sys
import os
import re

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ts_navigation_access_security():
    """Test that navigation.accessSecurity key exists in all .ts language files"""
    print("Testing Navigation AccessSecurity Key in .ts Files...")
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    for lang in languages:
        print(f"  Testing {lang.upper()}:")
        
        try:
            with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if navigation section exists and contains accessSecurity
            navigation_match = re.search(r'"navigation":\s*{([^}]+)}', content, re.DOTALL)
            if navigation_match:
                navigation_content = navigation_match.group(1)
                if '"accessSecurity"' in navigation_content:
                    # Extract the value
                    access_security_match = re.search(r'"accessSecurity":\s*"([^"]+)"', navigation_content)
                    if access_security_match:
                        value = access_security_match.group(1)
                        print(f"     [PASS] navigation.accessSecurity = '{value}'")
                    else:
                        print(f"     [FAIL] navigation.accessSecurity found but value not extracted")
                        return False
                else:
                    print(f"     [FAIL] navigation.accessSecurity missing from navigation section")
                    return False
            else:
                print(f"     [FAIL] navigation section not found")
                return False
                
        except Exception as e:
            print(f"     [ERROR] Error reading {lang} file: {str(e)}")
            return False
    
    return True

def test_ts_file_syntax():
    """Test that all .ts files have valid syntax"""
    print("Testing .ts File Syntax...")
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    for lang in languages:
        print(f"  Testing {lang.upper()}:")
        
        try:
            with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Basic syntax checks
            if not content.startswith('export default {'):
                print(f"     [FAIL] File should start with 'export default {{'")
                return False

            if not content.rstrip().endswith('};'):
                print("     [FAIL] File should end with '};'")
                return False
            
            # Check for balanced braces
            open_braces = content.count('{')
            close_braces = content.count('}')
            if open_braces != close_braces:
                print(f"     [FAIL] Unbalanced braces: {open_braces} open, {close_braces} close")
                return False
            
            print(f"     [PASS] Valid TypeScript syntax")
                
        except Exception as e:
            print(f"     [ERROR] Error reading {lang} file: {str(e)}")
            return False
    
    return True

def test_navigation_section_completeness():
    """Test that navigation section has all expected keys"""
    print("Testing Navigation Section Completeness...")
    
    expected_keys = ['dashboard', 'trading', 'signals', 'apiCredentials', 'autoTrading', 
                    'tierManagement', 'referrals', 'accessSecurity', 'settings', 'help', 
                    'login', 'register', 'logout', 'home']
    
    languages = ['en', 'es', 'pt', 'ko', 'ja', 'de', 'fr', 'zh']
    
    for lang in languages:
        print(f"  Testing {lang.upper()}:")
        
        try:
            with open(f'../frontend/src/i18n/locales/{lang}/common.ts', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract navigation section
            navigation_match = re.search(r'"navigation":\s*{([^}]+)}', content, re.DOTALL)
            if navigation_match:
                navigation_content = navigation_match.group(1)
                
                missing_keys = []
                for key in expected_keys:
                    if f'"{key}"' not in navigation_content:
                        missing_keys.append(key)
                
                if missing_keys:
                    print(f"     [FAIL] Missing navigation keys: {', '.join(missing_keys)}")
                    return False
                else:
                    print(f"     [PASS] All {len(expected_keys)} navigation keys present")
            else:
                print(f"     [FAIL] navigation section not found")
                return False
                
        except Exception as e:
            print(f"     [ERROR] Error reading {lang} file: {str(e)}")
            return False
    
    return True

def run_all_tests():
    """Run all .ts translation file tests"""
    print("TYPESCRIPT TRANSLATION FILES TEST")
    print("=" * 60)
    
    tests = [
        ("Navigation AccessSecurity Key", test_ts_navigation_access_security),
        ("TypeScript File Syntax", test_ts_file_syntax),
        ("Navigation Section Completeness", test_navigation_section_completeness)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"  [PASS] {test_name} completed successfully")
            else:
                failed += 1
                print(f"  [FAIL] {test_name} failed")
        except Exception as e:
            failed += 1
            print(f"  [ERROR] {test_name} error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("TYPESCRIPT TRANSLATION TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {len(tests)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed/len(tests)*100):.1f}%")
    
    if failed == 0:
        print("\nALL TESTS PASSED! TypeScript translation files are correct.")
        print("\nFix Details:")
        print("✅ Added navigation.accessSecurity key to all 8 .ts language files")
        print("✅ All TypeScript files have valid syntax")
        print("✅ All navigation sections are complete")
        print("\n🎯 SIDEBAR SHOULD NOW DISPLAY CORRECTLY!")
        print("🎯 FRONTEND RESTART RECOMMENDED TO PICK UP CHANGES!")
        return True
    else:
        print(f"\n{failed} test(s) failed. Please review implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
