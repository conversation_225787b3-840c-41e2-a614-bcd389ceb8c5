#!/usr/bin/env python3
"""
Test script to check user deletion functionality directly
This bypasses the web interface to test the SQL deletion logic
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User, User2FABackupCode, User2FAEmailCode
from app.models.paper_trading import PaperTradingAccount, PaperTrade, PaperTradingSession, PaperBalanceSnapshot
from app.models.user_balance_tracker import UserBalanceTracker, BalanceSnapshot
from app.models.security_log import LoginAttempt, SecurityLog, APICredential
from app.models.subscription import Subscription
from app.models.payment import Payment
from app.models.trade import Trade, TradingSession
from app.models.fee_calculation import FeeCalculation
from app.models.admin import CouponUsage
from app.models.referral import Referral, ReferralEarning, ReferrerProfile
from app.models.solana_payment import SolanaPayment, MembershipBilling
from app.models.user_tier_status import UserTierStatus
from sqlalchemy import text

def test_user_deletion():
    """Test user deletion with the same logic as the admin route"""
    app = create_app()
    
    with app.app_context():
        # Target user ID that's been failing
        user_id = "399a1279-b2f0-4612-820f-ac9ad9a0834e"
        
        print(f"🔍 Testing deletion for user: {user_id}")
        print("=" * 60)
        
        # Check if user exists
        user = User.query.filter_by(id=user_id).first()
        if not user:
            print(f"❌ User {user_id} not found!")
            return False
        
        print(f"✅ User found: {user.email}")
        
        # Check what data exists for this user
        print("\n📊 Checking existing data:")

        # Paper trading data
        paper_accounts = PaperTradingAccount.query.filter_by(user_id=user_id).all()
        paper_trades = PaperTrade.query.filter_by(user_id=user_id).count()
        paper_sessions = PaperTradingSession.query.filter_by(user_id=user_id).count()

        print(f"   📈 Paper trading accounts: {len(paper_accounts)}")
        print(f"   📈 Paper trades: {paper_trades}")
        print(f"   📈 Paper sessions: {paper_sessions}")

        # Balance tracking data
        balance_trackers = UserBalanceTracker.query.filter_by(user_id=user_id).all()
        print(f"   💰 Balance trackers: {len(balance_trackers)}")

        # Security data
        login_attempts = LoginAttempt.query.filter_by(user_id=user_id).count()
        security_logs = SecurityLog.query.filter_by(user_id=user_id).count()
        api_credentials = APICredential.query.filter_by(user_id=user_id).count()
        print(f"   🔐 Login attempts: {login_attempts}")
        print(f"   🛡️ Security logs: {security_logs}")
        print(f"   🔑 API credentials: {api_credentials}")

        # 2FA data
        backup_codes = User2FABackupCode.query.filter_by(user_id=user_id).count()
        email_codes = User2FAEmailCode.query.filter_by(user_id=user_id).count()
        print(f"   🔒 2FA backup codes: {backup_codes}")
        print(f"   📧 2FA email codes: {email_codes}")

        # Trading data
        trades = Trade.query.filter_by(user_id=user_id).count()
        trading_sessions = TradingSession.query.filter_by(user_id=user_id).count()
        fee_calculations = FeeCalculation.query.filter_by(user_id=user_id).count()
        print(f"   📊 Trades: {trades}")
        print(f"   🎯 Trading sessions: {trading_sessions}")
        print(f"   💸 Fee calculations: {fee_calculations}")

        # Subscription and payment data
        subscriptions = Subscription.query.filter_by(user_id=user_id).count()
        payments = Payment.query.filter_by(user_id=user_id).count()
        print(f"   📋 Subscriptions: {subscriptions}")
        print(f"   💳 Payments: {payments}")

        # Admin and referral data
        coupon_usage = CouponUsage.query.filter_by(user_id=user_id).count()
        referrals_as_referrer = Referral.query.filter_by(referrer_id=user_id).count()
        referrals_as_referee = Referral.query.filter_by(referee_id=user_id).count()
        referrer_profiles = ReferrerProfile.query.filter_by(user_id=user_id).count()
        print(f"   🎫 Coupon usage: {coupon_usage}")
        print(f"   👥 Referrals as referrer: {referrals_as_referrer}")
        print(f"   👥 Referrals as referee: {referrals_as_referee}")
        print(f"   👤 Referrer profiles: {referrer_profiles}")

        # Get referral earnings through referral relationships
        referral_earnings = 0
        user_referrals = Referral.query.filter_by(referrer_id=user_id).all()
        for referral in user_referrals:
            earnings = ReferralEarning.query.filter_by(referral_id=referral.id).count()
            referral_earnings += earnings
        print(f"   💰 Referral earnings: {referral_earnings}")

        # Solana payment data
        solana_payments = SolanaPayment.query.filter_by(user_id=user_id).count()
        membership_billing = MembershipBilling.query.filter_by(user_id=user_id).count()
        print(f"   ⚡ Solana payments: {solana_payments}")
        print(f"   📄 Membership billing: {membership_billing}")

        # Tier status
        tier_status = UserTierStatus.query.filter_by(user_id=user_id).count()
        print(f"   🏆 Tier status: {tier_status}")
        
        # Count snapshots
        paper_snapshots = 0
        balance_snapshots = 0
        
        for account in paper_accounts:
            snapshots = PaperBalanceSnapshot.query.filter_by(paper_account_id=account.id).count()
            paper_snapshots += snapshots
            print(f"      📸 Account {account.id}: {snapshots} snapshots")
        
        for tracker in balance_trackers:
            snapshots = BalanceSnapshot.query.filter_by(tracker_id=tracker.id).count()
            balance_snapshots += snapshots
            print(f"      📸 Tracker {tracker.id}: {snapshots} snapshots")
        
        print(f"   📸 Total paper snapshots: {paper_snapshots}")
        print(f"   📸 Total balance snapshots: {balance_snapshots}")
        
        # Test the comprehensive deletion logic step by step
        print("\n🧪 Testing COMPREHENSIVE deletion steps:")

        try:
            # Step 1: Delete 2FA data
            print("\n1️⃣ Testing 2FA data deletion...")
            if backup_codes > 0:
                result = db.session.execute(text("DELETE FROM user_2fa_backup_codes WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted 2FA backup codes: {result.rowcount}")
            if email_codes > 0:
                result = db.session.execute(text("DELETE FROM user_2fa_email_codes WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted 2FA email codes: {result.rowcount}")
            db.session.commit()

            # Step 2: Delete security data
            print("\n2️⃣ Testing security data deletion...")
            if login_attempts > 0:
                result = db.session.execute(text("DELETE FROM login_attempts WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted login attempts: {result.rowcount}")
            if security_logs > 0:
                result = db.session.execute(text("DELETE FROM security_logs WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted security logs: {result.rowcount}")
            if api_credentials > 0:
                result = db.session.execute(text("DELETE FROM api_credentials WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted API credentials: {result.rowcount}")
            db.session.commit()

            # Step 3: Delete trading data
            print("\n3️⃣ Testing trading data deletion...")
            if trades > 0:
                result = db.session.execute(text("DELETE FROM trades WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted trades: {result.rowcount}")
            if trading_sessions > 0:
                result = db.session.execute(text("DELETE FROM trading_sessions WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted trading sessions: {result.rowcount}")
            if fee_calculations > 0:
                result = db.session.execute(text("DELETE FROM fee_calculations WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted fee calculations: {result.rowcount}")
            db.session.commit()

            # Step 4: Delete admin and referral data
            print("\n4️⃣ Testing admin and referral data deletion...")
            if coupon_usage > 0:
                result = db.session.execute(text("DELETE FROM coupon_usage WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted coupon usage: {result.rowcount}")

            # Delete referral earnings first (they reference referrals)
            if referral_earnings > 0:
                # Delete earnings for referrals where user is the referrer
                result = db.session.execute(text(
                    "DELETE re FROM referral_earnings re "
                    "JOIN referrals r ON re.referral_id = r.id "
                    "WHERE r.referrer_id = :user_id"
                ), {"user_id": user_id})
                print(f"   ✅ Deleted referral earnings: {result.rowcount}")

            # Delete referrals where user is referrer or referee
            if referrals_as_referrer > 0:
                result = db.session.execute(text("DELETE FROM referrals WHERE referrer_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted referrals as referrer: {result.rowcount}")
            if referrals_as_referee > 0:
                result = db.session.execute(text("DELETE FROM referrals WHERE referee_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted referrals as referee: {result.rowcount}")

            if referrer_profiles > 0:
                result = db.session.execute(text("DELETE FROM referrer_profiles WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted referrer profiles: {result.rowcount}")
            db.session.commit()

            # Step 5: Delete Solana payment data
            print("\n5️⃣ Testing Solana payment data deletion...")
            if solana_payments > 0:
                result = db.session.execute(text("DELETE FROM solana_payments WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted Solana payments: {result.rowcount}")
            if membership_billing > 0:
                result = db.session.execute(text("DELETE FROM membership_billing WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted membership billing: {result.rowcount}")
            db.session.commit()

            # Step 6: Delete payments (after Solana payments)
            print("\n6️⃣ Testing payments deletion...")
            if payments > 0:
                result = db.session.execute(text("DELETE FROM payments WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted payments: {result.rowcount}")
            db.session.commit()

            # Step 7: Delete subscriptions (after payments)
            print("\n7️⃣ Testing subscriptions deletion...")
            if subscriptions > 0:
                result = db.session.execute(text("DELETE FROM subscriptions WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted subscriptions: {result.rowcount}")
            db.session.commit()

            # Step 8: Delete paper trading data
            print("\n8️⃣ Testing paper trading deletion...")
            if len(paper_accounts) > 0:
                # Delete balance snapshots first
                result = db.session.execute(text(
                    "DELETE pbs FROM paper_balance_snapshots pbs "
                    "JOIN paper_trading_accounts pta ON pbs.paper_account_id = pta.id "
                    "WHERE pta.user_id = :user_id"
                ), {"user_id": user_id})
                print(f"   ✅ Deleted paper balance snapshots: {result.rowcount}")

                result = db.session.execute(text("DELETE FROM paper_trades WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted paper trades: {result.rowcount}")

                result = db.session.execute(text("DELETE FROM paper_trading_sessions WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted paper sessions: {result.rowcount}")

                result = db.session.execute(text("DELETE FROM paper_trading_accounts WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted paper accounts: {result.rowcount}")
            db.session.commit()

            # Step 9: Delete balance tracking data
            print("\n9️⃣ Testing balance tracking deletion...")
            if len(balance_trackers) > 0:
                result = db.session.execute(text(
                    "DELETE bs FROM balance_snapshots bs "
                    "JOIN user_balance_trackers ubt ON bs.tracker_id = ubt.id "
                    "WHERE ubt.user_id = :user_id"
                ), {"user_id": user_id})
                print(f"   ✅ Deleted balance snapshots: {result.rowcount}")

                result = db.session.execute(text("DELETE FROM user_balance_trackers WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted balance trackers: {result.rowcount}")
            db.session.commit()

            # Step 10: Delete tier status
            print("\n🔟 Testing tier status deletion...")
            if tier_status > 0:
                result = db.session.execute(text("DELETE FROM user_tier_status WHERE user_id = :user_id"), {"user_id": user_id})
                print(f"   ✅ Deleted tier status: {result.rowcount}")
            db.session.commit()

            # Step 11: Finally delete the user
            print("\n1️⃣1️⃣ Testing user deletion...")
            result = db.session.execute(text("DELETE FROM users WHERE id = :user_id"), {"user_id": user_id})
            print(f"   ✅ Deleted user: {result.rowcount}")
            db.session.commit()
            print("   ✅ User deletion committed successfully!")
            
            # Verify deletion
            print("\n🔍 Verifying deletion...")
            remaining_user = User.query.filter_by(id=user_id).first()
            if remaining_user:
                print("   ❌ User still exists after deletion!")
                return False
            else:
                print("   ✅ User successfully deleted!")
                return True
                
        except Exception as e:
            print(f"\n❌ Error during deletion: {e}")
            print(f"   Error type: {type(e).__name__}")
            import traceback
            print(f"   Traceback: {traceback.format_exc()}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    print("🚀 Starting user deletion test...")
    success = test_user_deletion()
    
    if success:
        print("\n🎉 SUCCESS: User deletion test passed!")
        print("   The deletion logic works correctly.")
    else:
        print("\n💥 FAILURE: User deletion test failed!")
        print("   There are still issues with the deletion logic.")
    
    print("\n" + "=" * 60)
