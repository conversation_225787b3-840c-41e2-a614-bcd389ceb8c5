#!/usr/bin/env python3
"""
Test script to verify the user toggle status fix
"""
import requests
import json
import sys

# Configuration
BASE_URL = "http://127.0.0.1:5000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "12345"

def get_admin_token():
    """Get admin authentication token"""
    try:
        response = requests.post(f"{BASE_URL}/api/admin/login", json={
            "username": ADMIN_USERNAME,
            "password": ADMIN_PASSWORD
        })
        
        if response.status_code == 200:
            data = response.json()
            return data.get('access_token')
        else:
            print(f"❌ Admin login failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error during admin login: {e}")
        return None

def test_user_toggle_endpoint(token):
    """Test the new user toggle endpoint"""
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # First, get list of users to find a test user
    print("📋 Getting user list...")
    response = requests.get(f"{BASE_URL}/api/admin/users", headers=headers)
    
    if response.status_code != 200:
        print(f"❌ Failed to get users: {response.status_code}")
        print(f"Response: {response.text}")
        return False
    
    users_data = response.json()
    users = users_data.get('users', [])
    
    if not users:
        print("❌ No users found to test with")
        return False
    
    # Find a user to test with (not the admin)
    test_user = None
    for user in users:
        if user.get('email') != f"{ADMIN_USERNAME}@deeptrade.com":
            test_user = user
            break
    
    if not test_user:
        print("❌ No suitable test user found")
        return False
    
    user_id = test_user['id']
    current_status = test_user['is_active']
    
    print(f"🧪 Testing toggle for user: {test_user['email']}")
    print(f"   Current status: {'Active' if current_status else 'Inactive'}")
    
    # Test the toggle endpoint
    print("🔄 Testing toggle endpoint...")
    response = requests.post(
        f"{BASE_URL}/api/admin/users/{user_id}/toggle-status",
        headers=headers,
        json={"reason": "Testing toggle functionality"}
    )
    
    if response.status_code == 200:
        result = response.json()
        new_status = result.get('new_status')
        print(f"✅ Toggle successful!")
        print(f"   New status: {'Active' if new_status else 'Inactive'}")
        print(f"   Message: {result.get('message')}")
        
        # Verify the change by getting user profile
        print("🔍 Verifying change in user profile...")
        profile_response = requests.get(
            f"{BASE_URL}/api/admin/users/{user_id}/profile",
            headers=headers
        )
        
        if profile_response.status_code == 200:
            profile_data = profile_response.json()
            profile_status = profile_data['user_info']['status']
            expected_status = 'active' if new_status else 'inactive'
            
            if profile_status == expected_status:
                print(f"✅ Profile status matches: {profile_status}")
            else:
                print(f"❌ Profile status mismatch: expected {expected_status}, got {profile_status}")
                return False
        else:
            print(f"❌ Failed to get user profile: {profile_response.status_code}")
            return False
        
        # Toggle back to original state
        print("🔄 Toggling back to original state...")
        response = requests.post(
            f"{BASE_URL}/api/admin/users/{user_id}/toggle-status",
            headers=headers,
            json={"reason": "Restoring original state after test"}
        )
        
        if response.status_code == 200:
            result = response.json()
            restored_status = result.get('new_status')
            if restored_status == current_status:
                print(f"✅ Successfully restored original status: {'Active' if restored_status else 'Inactive'}")
            else:
                print(f"❌ Failed to restore original status")
                return False
        else:
            print(f"❌ Failed to toggle back: {response.status_code}")
            return False
        
        return True
    else:
        print(f"❌ Toggle failed: {response.status_code}")
        print(f"Response: {response.text}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing User Toggle Status Fix")
    print("=" * 50)
    
    # Get admin token
    print("🔐 Getting admin authentication token...")
    token = get_admin_token()
    
    if not token:
        print("❌ Failed to get admin token")
        sys.exit(1)
    
    print("✅ Admin token obtained")
    
    # Test the toggle endpoint
    success = test_user_toggle_endpoint(token)
    
    if success:
        print("\n🎉 All tests passed!")
        print("✅ User toggle endpoint is working correctly")
        print("✅ Status consistency between list and profile is maintained")
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
