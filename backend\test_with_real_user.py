#!/usr/bin/env python3
"""
Test the comprehensive trading framework with a real user or create a test user
"""

import sys
import os
from datetime import datetime

# Add backend to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_with_real_user():
    """Test the framework with a real user or create one"""
    print("🔍 TESTING WITH REAL USER DATA")
    print("=" * 50)
    
    try:
        from app import create_app
        from app.models.user import User
        from app import db
        
        app = create_app()
        
        with app.app_context():
            # Check for existing users
            users = User.query.limit(5).all()
            print(f"📊 Found {len(users)} users in database")
            
            if users:
                # Use first available user
                test_user = users[0]
                print(f"✅ Using existing user: ID={test_user.id}, Email={test_user.email}")
            else:
                # Create a test user
                print("🏗️  Creating test user for testing...")
                test_user = User(
                    email='<EMAIL>',
                    password_hash='test_hash',
                    is_verified=True,
                    is_active=True,
                    auto_trading_enabled=False,
                    risk_settings_configured=True,
                    investment_percentage=10,
                    leverage_multiplier=3,
                    selected_exchange='binance',
                    account_type='FUTURES'
                )
                
                try:
                    db.session.add(test_user)
                    db.session.commit()
                    print(f"✅ Created test user: ID={test_user.id}, Email={test_user.email}")
                except Exception as e:
                    print(f"❌ Failed to create test user: {e}")
                    db.session.rollback()
                    return False
            
            # Now test the framework with this user
            print(f"\n🧪 Testing framework with user ID: {test_user.id}")
            
            from comprehensive_trading_test import TradingWorkflowTester
            
            # Create custom tester with real user ID
            class RealUserTester(TradingWorkflowTester):
                def __init__(self, user_id):
                    super().__init__()
                    self.real_user_id = user_id
                
                def test_signal_generation_with_real_user(self):
                    """Test signal generation with real user"""
                    print("🎯 Testing signal generation with real user...")
                    
                    try:
                        from app.services.trading_signals import TradingSignalGenerator
                        from app.services.market_data import BinanceMarketData
                        
                        market_service = BinanceMarketData()
                        signal_generator = TradingSignalGenerator(
                            user_id=self.real_user_id,
                            exchange_service=market_service,
                            admin_monitoring_mode=True
                        )
                        
                        # Generate signal
                        signal_result = signal_generator.generate_signals('BTCUSDT', '1h')
                        
                        if signal_result and not signal_result.get('error'):
                            signal = signal_result.get('signal', 'UNKNOWN')
                            confidence = signal_result.get('confidence', 0)
                            
                            print(f"✅ Signal generated successfully!")
                            print(f"   Signal: {signal}")
                            print(f"   Confidence: {confidence}%")
                            print(f"   Current Price: ${signal_result.get('current_price', 'N/A'):,.2f}")
                            
                            # Print detailed conditions
                            if 'swing_high' in signal_result:
                                print(f"   Swing High: {signal_result.get('swing_high')}")
                            if 'swing_low' in signal_result:
                                print(f"   Swing Low: {signal_result.get('swing_low')}")
                            if 'ha_color' in signal_result:
                                print(f"   HA Color: {signal_result.get('ha_color')}")
                            if 'prev_ha_color' in signal_result:
                                print(f"   Previous HA Color: {signal_result.get('prev_ha_color')}")
                            
                            return True
                        else:
                            error_msg = signal_result.get('error', 'Unknown error')
                            print(f"❌ Signal generation failed: {error_msg}")
                            return False
                            
                    except Exception as e:
                        print(f"❌ Signal generation test failed: {e}")
                        return False
                
                def test_ml_forecast_with_real_user(self):
                    """Test ML forecast with real user"""
                    print("🤖 Testing ML forecast...")
                    
                    try:
                        from app.services.market_data import ml_service
                        
                        forecast_result = ml_service.generate_ensemble_forecast('BTCUSDT', '1h', 24)
                        
                        if forecast_result and forecast_result.get('forecast'):
                            forecast_data = forecast_result['forecast']
                            confidence = forecast_result.get('confidence', 0)
                            
                            print(f"✅ ML forecast generated successfully!")
                            print(f"   Forecast Length: {len(forecast_data)} predictions")
                            print(f"   Confidence: {confidence}%")
                            
                            if 'direction' in forecast_result:
                                print(f"   Direction: {forecast_result.get('direction')}")
                            if 'trend' in forecast_result:
                                print(f"   Trend: {forecast_result.get('trend')}")
                            
                            return True
                        else:
                            print(f"❌ ML forecast generation failed")
                            return False
                            
                    except Exception as e:
                        print(f"❌ ML forecast test failed: {e}")
                        return False
            
            # Run tests with real user
            tester = RealUserTester(test_user.id)
            
            print("\n" + "=" * 50)
            print("🚀 RUNNING TESTS WITH REAL USER")
            print("=" * 50)
            
            # Test 1: Signal Generation
            signal_success = tester.test_signal_generation_with_real_user()
            
            # Test 2: ML Forecast
            forecast_success = tester.test_ml_forecast_with_real_user()
            
            # Summary
            print(f"\n📊 REAL USER TEST SUMMARY:")
            print(f"   Signal Generation: {'✅ PASSED' if signal_success else '❌ FAILED'}")
            print(f"   ML Forecast: {'✅ PASSED' if forecast_success else '❌ FAILED'}")
            
            success_count = sum([signal_success, forecast_success])
            total_tests = 2
            success_rate = (success_count / total_tests) * 100
            
            print(f"   Overall Success Rate: {success_rate:.1f}%")
            
            if success_rate >= 50:
                print(f"🎉 REAL USER TESTING SUCCESSFUL!")
                return True
            else:
                print(f"❌ REAL USER TESTING FAILED")
                return False
                
    except Exception as e:
        print(f"❌ Real user testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print(f"🚀 DEEPTRADE REAL USER TESTING")
    print(f"Timestamp: {datetime.now()}")
    print("=" * 60)
    
    success = test_with_real_user()
    
    if success:
        print(f"\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
    else:
        print(f"\n❌ SOME TESTS FAILED")
        sys.exit(1)
