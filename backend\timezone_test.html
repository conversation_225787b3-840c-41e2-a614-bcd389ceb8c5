
<!DOCTYPE html>
<html>
<head>
    <title>GMT-3 Timezone Test</title>
</head>
<body>
    <h1>GMT-3 Timezone Formatting Test</h1>
    <div id="results"></div>
    
    <script>
        // Test timestamp (current UTC time)
        const testTimestamp = new Date().toISOString();
        console.log('UTC Timestamp:', testTimestamp);
        
        // Format with GMT-3 (America/Sao_Paulo)
        const gmt3Formatted = new Date(testTimestamp).toLocaleString('en-US', {
            timeZone: 'America/Sao_Paulo', // GMT-3
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
        
        // Format with UTC for comparison
        const utcFormatted = new Date(testTimestamp).toLocaleString('en-US', {
            timeZone: 'UTC',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
        
        const results = document.getElementById('results');
        results.innerHTML = `
            <p><strong>UTC Time:</strong> ${utcFormatted}</p>
            <p><strong>GMT-3 Time:</strong> ${gmt3Formatted}</p>
            <p><strong>Difference:</strong> GMT-3 should be 3 hours behind UTC</p>
        `;
        
        console.log('UTC Formatted:', utcFormatted);
        console.log('GMT-3 Formatted:', gmt3Formatted);
    </script>
</body>
</html>
