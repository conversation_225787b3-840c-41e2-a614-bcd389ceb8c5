import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.metrics import mean_squared_error
import requests
from datetime import datetime, timedelta, timezone
import time
import plotly.graph_objs as go
import base64
from io import BytesIO
from flask import Flask, render_template, jsonify, request
import os
import threading
import logging
logging.basicConfig(level=logging.INFO)
import hmac
import hashlib
import json
import time
import traceback

# Initialize the Binance client with API key and secret
API_KEY = '73UnYTIA60rZV2c92ULMSUnEVHHjdaG5z1YAqlPz1oHT1ux1eQV5DZzLxFj21DNL'
API_SECRET = 'wKPsGu54IFCtsGnEC5RooQy34lOmGRVJjRmivFtBP3tmPzWueee9PXzaVwm6ffYB'

# Base API endpoint
BASE_URL = 'https://fapi.binance.com'

# Initialize Flask app
app = Flask(__name__)

call_lock = threading.Lock()

# Set balance and trading conditions
symbol = 'BTCUSDT'
current_status = ""
trades = []
leverage = 1  # Default to 1x - will be updated from user settings
starting_balance = 134.62

# Add near other global variables
trade_history_cache = []

def create_signed_params(params):
    query_string = '&'.join([f'{k}={v}' for k, v in params.items()])
    signature = hmac.new(API_SECRET.encode('utf-8'), query_string.encode('utf-8'), hashlib.sha256).hexdigest()
    params['signature'] = signature
    return params

def get_usdt_balance():
    global balance2
    endpoint = '/fapi/v2/account'
    timestamp = int(time.time() * 1000)

    params = {
        'timestamp': timestamp,
        'recvWindow': 5000
    }

    params = create_signed_params(params)

    response = requests.get(BASE_URL + endpoint, params=params, headers={'X-MBX-APIKEY': API_KEY})
    data = response.json()

    try:
        for asset in data.get('assets', []):
            if asset['asset'] == 'USDT':
                balance2 = float(asset['walletBalance'])
                return balance2
    except Exception as e:
        print(f"Error fetching USDT balance: {e}")
    return balance2

# Track balances
balance = get_usdt_balance()
cont = 0
invest_zero = 0.00
invest_percentage = 0  # Default to 0% - users must configure their risk settings
user_leverage = 1  # Default to 1x - users must configure their risk settings
position_open = False
side = None  # To track entry signal
take_profit = None
stop_loss = None
pnl = None

investment_amount = balance * (invest_percentage / 100)
margin_amount = investment_amount / 10
last_candle_time = None

def load_user_risk_settings(user_id=None):
    """Load user's risk settings from database if available."""
    global invest_percentage, user_leverage, leverage

    try:
        # This would need to be integrated with the Flask app context
        # For now, we'll keep the default values
        # In a full integration, this would query the database for user settings

        # Example of how this would work:
        # from app.models.user import User
        # user = User.query.get(user_id)
        # if user and user.risk_settings_configured:
        #     invest_percentage = float(user.investment_percentage)
        #     user_leverage = float(user.leverage_multiplier)
        #     leverage = user_leverage  # Update global leverage variable
        #     print(f"Loaded user settings: {invest_percentage}% investment, {user_leverage}x leverage")
        # else:
        #     print("User has not configured risk settings - using safe defaults")

        # Update global leverage to match user_leverage
        leverage = user_leverage
        print(f"Current settings: {invest_percentage}% investment, {user_leverage}x leverage")

    except Exception as e:
        print(f"Error loading user risk settings: {e}")
        # Keep safe defaults
        invest_percentage = 0
        user_leverage = 1
        leverage = 1

# Make forecast, symbol_data, and current_price global variables
forecast = None
symbol_data = None
current_price = None

# Price level variables for enhanced trading signals
upper_price_levels = None
lower_price_levels = None
highest_upper_price = None
lowest_lower_price = None

# Load user risk settings on startup
load_user_risk_settings()

def get_open_positions():
    """Fetch only current open positions from Binance"""
    global trades, take_profit, stop_loss, position_amt, pnl, position_open
    current_positions = []  # List to hold current open positions
    position_found = False  # Flag to track if a position is found for the symbol

    endpoint = '/fapi/v2/positionRisk'
    timestamp = int(time.time() * 1000)
    params = {
        'timestamp': timestamp,
        'recvWindow': 5000
    }
    params = create_signed_params(params)

    try:
        # Fetch positions from Binance
        response = requests.get(BASE_URL + endpoint, params=params, headers={'X-MBX-APIKEY': API_KEY})
        positions = response.json()
        for position in positions:
            if float(position['positionAmt']) != 0 and position['symbol'] == symbol:  # Only process if position exists for our trading symbol
                position_found = True  # Mark that a position exists
                entry_price = float(position['entryPrice'])
                position_amt = float(position['positionAmt'])
                side = 'LONG' if position_amt > 0 else 'SHORT'

                # Fetch unrealized PnL
                unrealized_pnl = float(position['unRealizedProfit'])
                pnl = round(unrealized_pnl, 4)
                roi = round((unrealized_pnl * 100 / investment_amount), 2)

                # Fetch open orders to find stop loss and take profit
                open_orders_endpoint = '/fapi/v1/openOrders'
                open_orders_params = {
                    'timestamp': timestamp,
                    'symbol': symbol,
                    'recvWindow': 5000
                }
                open_orders_params = create_signed_params(open_orders_params)
                open_orders_response = requests.get(BASE_URL + open_orders_endpoint, params=open_orders_params, headers={'X-MBX-APIKEY': API_KEY})
                open_orders = open_orders_response.json()

                stop_loss = None
                take_profit = None

                # Process orders to identify stop loss and take profit
                for order in open_orders:
                    if order['type'] in ['STOP', 'STOP_MARKET']:
                        stop_loss = float(order['stopPrice'])
                    elif order['type'] in ['TAKE_PROFIT', 'TAKE_PROFIT_MARKET']:
                        take_profit = float(order['stopPrice'])

                # Add the position to current_positions
                current_positions.append({
                    'symbol': symbol,
                    'entry_date': datetime.now().strftime('%Y-%m-%d %H:%M'),
                    'entry_price': entry_price,  # Binance's entryPrice for open positions
                    'quantity': abs(position_amt),  # Use absolute value
                    'side': side,
                    'pnl': pnl,
                    'roi': roi,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'status': 'Active'
                })

        # Update position_open based on whether a position was found
        position_open = position_found

    except Exception as e:
        print(f"Error fetching open positions: {e}")
        position_open = False  # Ensure position_open is set safely in case of an error

    return position_open, current_positions




def load_trade_history():
    """Load trades from history file"""
    history_file = 'trade_history.json'
    try:
        if os.path.exists(history_file):
            with open(history_file, 'r') as f:
                data = json.load(f)
                return data.get('trades', [])
        return []
    except Exception as e:
        print(f"Error loading trade history: {e}")
        return []

def update_trade_history_cache():
    """Update the trade history cache with latest trades"""
    global trade_history_cache
    try:
        new_trades = get_position_history()
        if new_trades:
            trade_history_cache = new_trades
            trade_history_cache.sort(key=lambda x: x['exit_time'], reverse=True)  # Sort trades by exit_time in descending order (latest first)
            print(f"Trade history cache updated with {len(new_trades)} trades")
    except Exception as e:
        print(f"Error updating trade history cache: {e}")

def get_cached_trade_history():
    """Get trade history from cache, update if empty"""
    global trade_history_cache
    if not trade_history_cache:
        update_trade_history_cache()
    return trade_history_cache



# Fetch data on startup
get_usdt_balance()
#get_trade_history(symbol)
get_open_positions()


def check_candle():
    global forecast, symbol_data, current_price, chart_image, last_candle_time
    global side, take_profit, stop_loss, current_status
    global balance, pnl, current_candle_time, investment_amount
    global symbol, highest_price, lowest_price, trades, position_open
    global upper_price_levels, lower_price_levels, highest_upper_price, lowest_lower_price

    # Create a class-level lock
    if not hasattr(check_candle, 'position_lock'):
        check_candle.position_lock = threading.Lock()
    
    while True:
        try:
            with check_candle.position_lock:
                # Fetch historical data for the symbol
                data = fetch_binance_futures_data(symbol, interval='1h', lookback=1000)
                get_usdt_balance()
                position_status, _ = get_open_positions()
                
                # Strictly update position_open based on exchange status
                if position_open != position_status:
                    print(f"Position status changed from {position_open} to {position_status}")
                    position_open = position_status
                    
                    # If position is now closed (either manually or by bot), clean up any pending orders
                    if not position_open:
                        print("Position is closed, cleaning up pending orders...")
                        cancel_all_orders(symbol)
                        trades = [trade for trade in trades if trade['status'] != 'Open']
                        print("Pending orders cleaned up and trades list updated")
                
                # Current candle information
                current_candle_time = data.index[-1]
                current_price = data['close'].iloc[-1]
                date_time = datetime.now().strftime("%H:%M:%S")

                print(f"\nCurrent Time: {date_time}")
                print(f"Position Open: {position_open}")
                
                # New candle check
                if last_candle_time is None or current_candle_time > last_candle_time:
                    last_candle_time = current_candle_time
                    # Fetch forecast and update the global variables
                    forecast, _, _, symbol_data = get_forecast(symbol)
                    chart_image_path = 'forecast.html'
                    plot_html = plot_price_chart(symbol_data, forecast, chart_image_path)
                    chart_image = plot_html if plot_html else None
                    
                    # Check trailing stop conditions only if position is open
                    '''if position_open:
                        for trade in trades:
                            # For BUY positions: trail when forecast turns bearish
                            if trade['side'] == 'BUY' and highest_price < trade['entry']:
                                trailing_stop(trade, highest_price, trailing_offset_pct=0.005)
                            # For SELL positions: trail when forecast turns bullish
                            elif trade['side'] == 'SELL' and lowest_price > trade['entry']:
                                trailing_stop(trade, lowest_price, trailing_offset_pct=0.005)'''
                
                # Only check for new trades if NO position is open
                if not position_open:
                    check_trading(symbol)
                else:
                    check_exit()
                
                time.sleep(5)  # Prevent excessive CPU usage
                
        except Exception as e:
            print(f"Error in check_candle: {e}")
            traceback.print_exc()
            time.sleep(5)

def trailing_stop(trade, current_price, trailing_offset_pct):
    """
    Updates stop-loss dynamically based on a percentage-based trailing stop.

    Args:
        trade (dict): Trade details (e.g., 'side', 'stop_loss', 'entry').
        current_price (float): The current market price.
        trailing_offset_pct (float): Trailing stop offset as a percentage (e.g., 0.01 for 1%).
    """
    # Calculate the trailing offset in absolute value based on entry price
    trailing_offset = trade['entry'] * trailing_offset_pct

    if trade['side'] == 'BUY':
        # Calculate new stop-loss for Long positions
        new_stop_loss = current_price - trailing_offset
        if new_stop_loss > trade['stop_loss']:  # Stop-loss only moves up
            trade['stop_loss'] = new_stop_loss
            print(f"Trailing Stop for BUY updated. Stop-Loss: {trade['stop_loss']:.2f}")

    elif trade['side'] == 'SELL':
        # Calculate new stop-loss for Short positions
        new_stop_loss = current_price + trailing_offset
        if new_stop_loss < trade['stop_loss']:  # Stop-loss only moves down
            trade['stop_loss'] = new_stop_loss
            print(f"Trailing Stop for SELL updated. Stop-Loss: {trade['stop_loss']:.2f}")
                

# Function to fetch historical data from Coinbase API
def fetch_binance_futures_data(symbol, interval, lookback=1000):
        # Construct the URL for the futures klines endpoint
        url = f"{BASE_URL}/fapi/v1/klines"
        
        # Define the query parameters
        params = {
            'symbol': symbol,  # e.g., 'BTCUSDT'
            'interval': interval,  # e.g., '1h'
            'limit': lookback,  # Limit the number of records
        }
        
        # Send the request to the Binance API
        response = requests.get(url, params=params)
        
        # Check if the request was successful
        if response.status_code != 200:
            logging.error(f"Error fetching data from Binance: {response.text}")
            return pd.DataFrame()

        # Parse the response JSON data
        klines = response.json()

        # Create a DataFrame from the response data
        data = pd.DataFrame(klines, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 
                                             'close_time', 'quote_asset_volume', 'number_of_trades', 
                                             'taker_buy_base', 'taker_buy_quote', 'ignore'])
        
        # Convert 'timestamp' to datetime
        data['timestamp'] = pd.to_datetime(data['timestamp'], unit='ms')
        data.set_index('timestamp', inplace=True)

        # Convert 'close' column to float
        data['close'] = data['close'].astype(float)

        return data
    
def get_forecast(symbol):
        global timeframe, highest_price, lowest_price, last_swing_high, last_swing_low, current_price, symbol_data
        try:
            timeframe = '1h'  # This is a string, which is expected by fetch_binance_futures_data
            # Fetch historical data from Binance Futures
            symbol_data = fetch_binance_futures_data(symbol=symbol, interval=timeframe, lookback=1000)
            # Get the current price (last close price)
            current_price = symbol_data['close'].iloc[-1]
            
        except Exception as e:
            print(f"Error fetching data: {e}")
            return None
            
        try:
            # Prediction logic with ensemble approach
            scaler = RobustScaler()
            scaled_data = scaler.fit_transform(symbol_data['close'].values.reshape(-1, 1))
            
            def prepare_data(data, time_steps):
                X, y = [], []
                for i in range(len(data) - time_steps):
                    X.append(data[i:(i + time_steps)].flatten())
                    y.append(data[i + time_steps][0])
                return np.array(X), np.array(y)
                
            time_steps = 288  # Historical candles to compute
            X, y = prepare_data(scaled_data, time_steps)
            
            split_ratio = 0.8
            split_index = int(split_ratio * len(X))
            X_train, X_test = X[:split_index], X[split_index:]
            y_train, y_test = y[:split_index], y[split_index:]
            
            # Initialize and train models
            rf_model = RandomForestRegressor(n_estimators=200, random_state=42)
            gb_model = GradientBoostingRegressor(n_estimators=100, learning_rate=0.1, random_state=42)
            lr_model = LinearRegression()
            
            rf_model.fit(X_train, y_train)
            gb_model.fit(X_train, y_train)
            lr_model.fit(X_train, y_train)
            
            # Calculate model weights based on validation performance
            rf_pred_val = rf_model.predict(X_test)
            gb_pred_val = gb_model.predict(X_test)
            lr_pred_val = lr_model.predict(X_test)
            
            rf_score = 1 / (mean_squared_error(y_test, rf_pred_val) + 1e-6)
            gb_score = 1 / (mean_squared_error(y_test, gb_pred_val) + 1e-6)
            lr_score = 1 / (mean_squared_error(y_test, lr_pred_val) + 1e-6)
            
            total_score = rf_score + gb_score + lr_score
            rf_weight = rf_score / total_score
            gb_weight = gb_score / total_score
            lr_weight = lr_score / total_score
            
            print(f"Model weights - RF: {rf_weight:.2f}, GB: {gb_weight:.2f}, LR: {lr_weight:.2f}")
            
            future_hours = 72
            X_future = scaled_data[-time_steps:].reshape(1, -1)
            predicted_prices_scaled = []
            
            # Generate ensemble predictions with exponential smoothing
            alpha = 0.7  # Smoothing factor
            last_pred = scaled_data[-1][0]  # Initialize with last known value
            
            for _ in range(future_hours):
                # Get predictions from all models
                rf_pred = rf_model.predict(X_future)
                gb_pred = gb_model.predict(X_future)
                lr_pred = lr_model.predict(X_future)
                
                # Weighted ensemble prediction
                ensemble_pred = (rf_weight * rf_pred +
                               gb_weight * gb_pred +
                               lr_weight * lr_pred)
                
                # Apply exponential smoothing
                smoothed_pred = alpha * ensemble_pred + (1 - alpha) * last_pred
                last_pred = smoothed_pred[0]
                
                predicted_prices_scaled.append(last_pred)
                
                # Update future window
                X_future = np.append(X_future[:, 1:], smoothed_pred.reshape(1, 1), axis=1)
                
            predicted_prices = scaler.inverse_transform(np.array(predicted_prices_scaled).reshape(-1, 1))
            highest_price = np.max(predicted_prices)
            lowest_price = np.min(predicted_prices)
            
            # Add price levels for more granular trading signals
            upper_price_levels = predicted_prices * 1.01
            lower_price_levels = predicted_prices * 0.99
            highest_upper_price = np.max(upper_price_levels)
            lowest_lower_price = np.min(lower_price_levels)
            
            # Determine swing high and low for stop loss
            last_swing_high, last_swing_low = find_swing_points(symbol_data)
            
            return predicted_prices, highest_price, lowest_price, symbol_data
            
        except Exception as e:
            print(f"Error during prediction: {e}")
            traceback.print_exc()
            return None
        
        

def find_swing_points(data, max_look_back=24):
        last_swing_high = None
        last_swing_low = None
        for i in range(1, len(data) - 1):
            # Dynamic look-back for the current candle high
            look_back_range = min(i, max_look_back)
            prev_highs = data['high'].iloc[i - look_back_range:i]
            next_high = data['high'].iloc[i + 1]
            # Check if current high is greater than the max high in the look-back range and next candle
            if data['high'].iloc[i] > np.max(prev_highs) and data['high'].iloc[i] > next_high:
                last_swing_high = float(data['high'].iloc[i])
            # Dynamic look-back for the current candle low
            prev_lows = data['low'].iloc[i - look_back_range:i]
            next_low = data['low'].iloc[i + 1]
            # Check if current low is lower than the min low in the look-back range and next candle
            if data['low'].iloc[i] < np.min(prev_lows) and data['low'].iloc[i] < next_low:
                last_swing_low = float(data['low'].iloc[i])
        return last_swing_high, last_swing_low

def check_trading(symbol):
    global current_status, take_profit, stop_loss, side, trades, investment_amount, quantity, position_open, upper_price_levels, lower_price_levels, highest_upper_price, lowest_lower_price

    # Double-check no position is open
    position_check, _ = get_open_positions()
    if position_check:
        print("Position already exists, skipping trade check")
        return

    try:
        # Fetch data and ensure numeric types
        data = fetch_binance_futures_data(symbol=symbol, interval=timeframe, lookback=1000)
        data['open'] = pd.to_numeric(data['open'], errors='coerce')
        data['high'] = pd.to_numeric(data['high'], errors='coerce')
        data['low'] = pd.to_numeric(data['low'], errors='coerce')
        data['close'] = pd.to_numeric(data['close'], errors='coerce')
        
        current_price = float(data['close'].iloc[-1])
        previous_candle = float(data['close'].iloc[-2])
        
        # Calculate Heikin-Ashi with proper numeric handling
        ha_data = data.copy()
        ha_data['HA_Close'] = (ha_data['open'].astype(float) + 
                              ha_data['high'].astype(float) + 
                              ha_data['low'].astype(float) + 
                              ha_data['close'].astype(float)) / 4
        
        ha_data['HA_Open'] = ha_data['open'].astype(float)
        
        # Calculate HA_Open with proper type handling
        for i in range(len(ha_data)):
            if i > 0:
                ha_data.iloc[i, ha_data.columns.get_loc('HA_Open')] = float(
                    (float(ha_data['HA_Open'].iloc[i-1]) + float(ha_data['HA_Close'].iloc[i-1])) / 2
                )
        
        # Calculate HA High and Low
        ha_data['HA_High'] = ha_data[['high', 'HA_Open', 'HA_Close']].astype(float).max(axis=1)
        ha_data['HA_Low'] = ha_data[['low', 'HA_Open', 'HA_Close']].astype(float).min(axis=1)
        
        # Get Heikin-Ashi values for conditions
        current_ha = float(ha_data['HA_Close'].iloc[-1])
        previous_ha = float(ha_data['HA_Close'].iloc[-2])
        ha_color = "green" if float(ha_data['HA_Close'].iloc[-1]) > float(ha_data['HA_Open'].iloc[-1]) else "red"
        prev_ha_color = "green" if float(ha_data['HA_Close'].iloc[-2]) > float(ha_data['HA_Open'].iloc[-2]) else "red"

        # Calculate indicators
        last_swing_high, last_swing_low = find_swing_points(data)
        data['SMA12'] = data['close'].astype(float).rolling(window=12).mean()
        current_sma50 = float(data['SMA12'].iloc[-1])
        previous_sma = float(data['SMA12'].iloc[-2])

        # Debug prints
        print(f"Previous HA Close: {previous_ha}")
        print(f"SMA12: {current_sma50}")
        print(f"HA Colors - Current: {ha_color}, Previous: {prev_ha_color}")
        
        # Final position check before placing order
        final_check, _ = get_open_positions()
        if final_check:
            print("Position opened by another process, skipping trade")
            return

        # Calculate quantity ensuring minimum notional value of 0.002 BTC
        min_notional = 0.002  # Fixed BTC amount
        quantity = max(round((investment_amount / current_price), 4), min_notional)
        
        # Calculate potential moves in both directions
        potential_up_move = (highest_price - current_price) / current_price
        potential_down_move = (current_price - lowest_price) / current_price
        
        # Debug prints
        print(f"Swing High: {last_swing_high}")
        print(f"Swing Low: {last_swing_low}")
        print(f"Highest Price: {highest_price}")
        print(f"Lowest Price: {lowest_price}")
        print(f"Current Price: {current_price}")
        print(f"Current SMA50: {current_sma50}")
        print(f"Potential Up Move: {potential_up_move*100:.2f}%")
        print(f"Potential Down Move: {potential_down_move*100:.2f}%")
        print(f"HA Colors - Current: {ha_color}, Previous: {prev_ha_color}")
        print(f"Final Check: {final_check}")
        print(f"Quantity: {quantity}")
        print(f"Order Value: ${quantity * current_price:.2f}")
        print(f"\n")

        # Enhanced trading conditions with ensemble predictions
        if (not final_check and
            last_swing_high > current_price and
            potential_down_move > 0.01 and  # At least 1% potential down move
            ha_color == "red" and
            prev_ha_color == "red" and
            previous_ha < current_sma50 and  # Previous HA close below SMA12
            current_price > np.mean(lower_price_levels)):  # Price above average lower bound
            
            side = "SELL"
            stop_loss = round(last_swing_high * 1.003, 4)
            take_profit = round(lowest_lower_price, 4)  # Using the lowest predicted level
            print(f"SELL - Potential Down Move: {potential_down_move*100:.2f}%")
            
            '''if place_order(symbol, side, quantity, current_price, stop_loss, take_profit):
                trades.append({
                    "symbol": symbol,
                    "side": side,
                    "quantity": quantity,
                    "entry_price": current_price,
                    "stop_loss": stop_loss,
                    "take_profit": take_profit,
                    "status": "Open",
                    "exit": None
                })'''

        elif (not final_check and
              last_swing_low < current_price and
              potential_up_move > 0.01 and  # At least 1% potential up move
              ha_color == "green" and
              prev_ha_color == "green" and
              previous_ha > current_sma50 and  # Previous HA close above SMA12
              current_price < np.mean(upper_price_levels)):  # Price below average upper bound
            
            side = "BUY"
            stop_loss = round(last_swing_low / 1.003, 4)
            take_profit = round(highest_upper_price, 4)  # Using the highest predicted level
            print(f"BUY - Potential Up Move: {potential_up_move*100:.2f}%")
            
            '''if place_order(symbol, side, quantity, current_price, stop_loss, take_profit):
                trades.append({
                    "symbol": symbol,
                    "side": side,
                    "quantity": quantity,
                    "entry_price": current_price,
                    "stop_loss": stop_loss,
                    "take_profit": take_profit,
                    "status": "Open",
                    "exit": None
                })'''
        
    except Exception as e:
        print(f"Error in check_trading: {e}")
        traceback.print_exc()

def get_symbol_precision(symbol):
    try:
        response = requests.get(f"{BASE_URL}/fapi/v1/exchangeInfo")
        data = response.json()
        
        for symbol_info in data['symbols']:
            if symbol_info['symbol'] == symbol:
                quantity_precision = int(symbol_info['quantityPrecision'])
                price_precision = int(symbol_info['pricePrecision'])
                return quantity_precision, price_precision
                
        print(f"Symbol {symbol} not found in exchange info")
        return None, None
        
    except Exception as e:
        print(f"Error getting symbol precision: {e}")
        return None, None

def round_to_precision(value, precision):
    """Round a value to the specified precision."""
    if precision is None:
        return value
    return float(f"{value:.{precision}f}")

def place_order(symbol, side, quantity, current_price, stop_loss, take_profit):
    try:
        # Final verification before placing order
        position_check, _ = get_open_positions()
        if position_check:  # Access the boolean from the tuple
            print("Position already exists, cancelling order placement")
            return False

        # Get symbol precision
        quantity_precision, price_precision = get_symbol_precision(symbol)
        if quantity_precision is None or price_precision is None:
            print("Unable to fetch symbol precision")
            return False
        
        # Round values according to symbol precision
        quantity = round_to_precision(quantity, quantity_precision)
        stop_loss = round_to_precision(stop_loss, price_precision)
        take_profit = round_to_precision(take_profit, price_precision)
        
        # Place the main order
        params = {
            'symbol': symbol,
            'side': side,
            'type': 'MARKET',
            'quantity': quantity,
            'timestamp': int(time.time() * 1000)
        }
        
        # Sign the request
        query_string = '&'.join([f"{key}={value}" for key, value in params.items()])
        signature = hmac.new(API_SECRET.encode(), query_string.encode(), hashlib.sha256).hexdigest()
        params['signature'] = signature
        
        # Send the main order
        headers = {'X-MBX-APIKEY': API_KEY}
        response = requests.post(f"{BASE_URL}/fapi/v1/order", headers=headers, params=params)
        
        if response.status_code == 200:
            # Place stop loss order
            sl_params = {
                'symbol': symbol,
                'side': 'SELL' if side == 'BUY' else 'BUY',
                'type': 'STOP_MARKET',
                'stopPrice': stop_loss,
                'closePosition': 'true',
                'timestamp': int(time.time() * 1000)
            }
            query_string = '&'.join([f"{key}={value}" for key, value in sl_params.items()])
            signature = hmac.new(API_SECRET.encode(), query_string.encode(), hashlib.sha256).hexdigest()
            sl_params['signature'] = signature
            
            stop_response = requests.post(f"{BASE_URL}/fapi/v1/order", headers=headers, params=sl_params)
            
            # Place take profit order
            tp_params = {
                'symbol': symbol,
                'side': 'SELL' if side == 'BUY' else 'BUY',
                'type': 'TAKE_PROFIT_MARKET',
                'stopPrice': take_profit,
                'closePosition': 'true',
                'timestamp': int(time.time() * 1000)
            }
            query_string = '&'.join([f"{key}={value}" for key, value in tp_params.items()])
            signature = hmac.new(API_SECRET.encode(), query_string.encode(), hashlib.sha256).hexdigest()
            tp_params['signature'] = signature
            
            tp_response = requests.post(f"{BASE_URL}/fapi/v1/order", headers=headers, params=tp_params)
            
            if stop_response.status_code == 200 and tp_response.status_code == 200:
                print(f"Successfully placed {side} order with stop loss at {stop_loss} and take profit at {take_profit}")
                return True
            else:
                print(f"Error placing SL/TP orders: SL={stop_response.text}, TP={tp_response.text}")
                return False
        else:
            print(f"Error placing main order: {response.text}")
            return False
            
    except Exception as e:
        print(f"Error placing order: {e}")
        traceback.print_exc()
        return False

def update_tp_order(trade):
    try:
        # Cancel existing TP order
        cancel_all_orders(trade['symbol'])

        # Place a new TP order
        tp_order_params = {
            'symbol': trade['symbol'],
            'side': 'SELL' if trade['side'] == 'BUY' else 'BUY',
            'type': 'TAKE_PROFIT_MARKET',
            'quantity': trade['quantity'],  # Use the quantity from the trade dictionary
            'stopPrice': round_to_precision(trade['take_profit'], get_symbol_precision(trade['symbol'])[1]),
            'reduceOnly': True,
            'recvWindow': 5000,
            'timestamp': int(time.time() * 1000),
        }

        tp_order_query = '&'.join([f"{key}={value}" for key, value in tp_order_params.items()])
        tp_order_signature = hmac.new(API_SECRET.encode(), tp_order_query.encode(), hashlib.sha256).hexdigest()
        tp_order_params['signature'] = tp_order_signature

        headers = {'X-MBX-APIKEY': API_KEY}
        response = requests.post(f"{BASE_URL}/fapi/v1/order", headers=headers, params=tp_order_params)
        if response.status_code == 200:
            print(f"Take-profit order updated successfully: {trade['take_profit']}")
        else:
            print(f"Failed to update take-profit order: {response.json()}")
    except Exception as e:
        print(f"Error updating take-profit order: {e}")

def check_exit():
    global position_open, trades, side, current_price, take_profit, stop_loss

    # Fetch open positions and orders from Binance
    position_open, open_trades = get_open_positions()

    # If no open positions are found, it means the position has been closed
    if not position_open:
        # Do not cancel pending orders, let them remain active
        position_open = False
        return

    # If position is still open, check if the current price hits the take-profit or stop-loss levels
    if position_open:
        for trade in open_trades:
            # Check conditions for stop-loss or take-profit hit
            hit_tp = ((trade['side'] == "BUY" and current_price >= trade['take_profit']) or
                     (trade['side'] == "SELL" and current_price <= trade['take_profit']))
            hit_sl = ((trade['side'] == "BUY" and current_price <= trade['stop_loss']) or
                     (trade['side'] == "SELL" and current_price >= trade['stop_loss']))
            
            if hit_tp or hit_sl:
                # Calculate PnL
                if trade['side'] == "BUY":
                    pnl = (current_price - trade['entry_price']) * trade['quantity']
                else:
                    pnl = (trade['entry_price'] - current_price) * trade['quantity']
                
                # Create trade history entry
                trade_data = {
                    'symbol': symbol,
                    'side': trade['side'],
                    'quantity': trade['quantity'],
                    'entry_price': trade['entry_price'],
                    'exit_price': current_price,
                    'stop_loss': trade['stop_loss'],
                    'take_profit': trade['take_profit'],
                    'status': 'Closed',
                    'pnl': pnl,
                    'exit_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'exit_reason': 'Take Profit' if hit_tp else 'Stop Loss'
                }
                
                # Close the position but keep pending orders
                close_all_positions(symbol)
                
                # Update state
                position_open = False
                
                # Save to trade history
                save_trade_history(trade_data)
                update_trade_history_cache()
                
                exit_reason = "Take Profit" if hit_tp else "Stop Loss"
                print(f"Position closed ({exit_reason}) - Side: {trade['side']}, Entry: {trade['entry_price']}, Exit: {current_price}, PnL: {pnl}")
                break
        

def cancel_all_orders(symbol):
    params = {
        'symbol': symbol,
        'recvWindow': 5000,
        'timestamp': int(time.time() * 1000),
    }
    query_string = '&'.join([f"{key}={value}" for key, value in params.items()])
    signature = hmac.new(API_SECRET.encode(), query_string.encode(), hashlib.sha256).hexdigest()
    params['signature'] = signature

    headers = {'X-MBX-APIKEY': API_KEY}
    response = requests.delete(f"{BASE_URL}/fapi/v1/allOpenOrders", headers=headers, params=params)
    response_data = response.json()

    if response.status_code != 200:
        print(f"Failed to cancel all orders: {response_data}")
    else:
        print(f"All orders canceled successfully: {response_data}")

    return response_data

def close_all_positions(symbol):
    # Fetch current position information
    position_info_params = {
        'symbol': symbol,
        'recvWindow': 5000,
        'timestamp': int(time.time() * 1000),
    }
    position_info_query = '&'.join([f"{key}={value}" for key, value in position_info_params.items()])
    position_info_signature = hmac.new(API_SECRET.encode(), position_info_query.encode(), hashlib.sha256).hexdigest()
    position_info_params['signature'] = position_info_signature

    headers = {'X-MBX-APIKEY': API_KEY}
    position_info_response = requests.get(f"{BASE_URL}/fapi/v2/positionRisk", headers=headers, params=position_info_params)
    position_info_data = position_info_response.json()

    if position_info_response.status_code != 200:
        print(f"Failed to fetch position information: {position_info_data}")
        return

    # Check and close positions
    for position in position_info_data:
        position_amt = float(position['positionAmt'])
        if position['symbol'] == symbol and position_amt != 0:
            side = 'SELL' if position_amt > 0 else 'BUY'  # Opposite side to close the position
            quantity = abs(position_amt)

            close_params = {
                'symbol': symbol,
                'side': side,
                'type': 'MARKET',
                'quantity': quantity,
                'reduceOnly': True,
                'recvWindow': 5000,
                'timestamp': int(time.time() * 1000),
            }
            close_query = '&'.join([f"{key}={value}" for key, value in close_params.items()])
            close_signature = hmac.new(API_SECRET.encode(), close_query.encode(), hashlib.sha256).hexdigest()
            close_params['signature'] = close_signature

            close_response = requests.post(f"{BASE_URL}/fapi/v1/order", headers=headers, params=close_params)
            close_data = close_response.json()

            if close_response.status_code == 200:
                print(f"Closed position for {symbol}. Quantity: {quantity}, Side: {side}.")
            else:
                print(f"Failed to close position: {close_data}")

def save_trade_history(trade_data):
    """Save trade to trade history file"""
    history_file = 'trade_history.json'
    try:
        # Load existing trades
        if os.path.exists(history_file):
            with open(history_file, 'r') as f:
                data = json.load(f)
                trades_history = data.get('trades', [])
        else:
            trades_history = []
        
        # Add new trade with timestamp
        trade_data['saved_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        trades_history.append(trade_data)
        
        # Save updated history
        with open(history_file, 'w') as f:
            json.dump({'trades': trades_history}, f, indent=4)
            
    except Exception as e:
        print(f"Error saving trade history: {e}")

def plot_price_chart(symbol_data, forecast, filename='forecast.html'):
    """Generate and save price chart with forecast using Plotly"""
    try:
        current_timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        num_periods = len(forecast)
        
        # Get the last 5 days of data for the candlestick chart
        symbol_data_last_5_days = symbol_data[-120:]  # Last 120 hours (5 days)
        
        # Create figure with transparent background
        fig = go.Figure()
        
        # Add candlestick chart
        fig.add_trace(go.Candlestick(
            x=symbol_data_last_5_days.index,
            open=symbol_data_last_5_days['open'],
            high=symbol_data_last_5_days['high'],
            low=symbol_data_last_5_days['low'],
            close=symbol_data_last_5_days['close'],
            name='Current Price',
            increasing_line_color='#22C55E',
            decreasing_line_color='#EF4444',
            increasing_fillcolor='rgba(34, 197, 94, 0.6)',
            decreasing_fillcolor='rgba(239, 68, 68, 0.6)'
        ))
        
        # Create future dates for forecast
        future_index = pd.date_range(start=symbol_data.index[-1], periods=num_periods, freq='h')
        
        # Add prediction traces
        fig.add_trace(go.Scatter(
            x=future_index,
            y=forecast.flatten(),
            mode='lines',
            name='Predicted Price',
            line=dict(
                color='rgba(14, 165, 233, 0.9)',
                width=2.5,
                dash='dot'
            ),
            hovertemplate='Price: $%{y:.4f}<extra></extra>'
        ))
        
        # Add price bounds
        fig.add_trace(go.Scatter(
            x=future_index,
            y=lower_price_levels.flatten(),
            mode='lines',
            showlegend=False,
            line=dict(color='rgba(34, 197, 94, 0.4)', width=0),
            hovertemplate='Lower: $%{y:.4f}<extra></extra>'
        ))
        
        fig.add_trace(go.Scatter(
            x=future_index,
            y=upper_price_levels.flatten(),
            mode='lines',
            name='Price Range',
            fill='tonexty',
            fillcolor='rgba(34, 197, 94, 0.15)',
            line=dict(color='rgba(34, 197, 94, 0.4)', width=0),
            hovertemplate='Upper: $%{y:.4f}<extra></extra>'
        ))
        
        # Update layout
        fig.update_layout(
            title={
                'text': f'{symbol} Price Forecast',
                'y': 0.95,
                'x': 0.5,
                'xanchor': 'center',
                'yanchor': 'top',
                'font': dict(size=24)
            },
            xaxis_title='Time (UTC)',
            yaxis_title='Price (USD)',
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            legend=dict(
                yanchor='top',
                y=0.99,
                xanchor='left',
                x=0.01,
                bgcolor='rgba(255, 255, 255, 0.85)',
                bordercolor='rgba(0, 0, 0, 0.15)',
                borderwidth=1,
                font=dict(size=12, color='#1E293B')
            ),
            margin=dict(l=50, r=50, t=80, b=50),
            autosize=True,
            height=600,
            xaxis_rangeslider_visible=False,
            font=dict(family='Arial, sans-serif', size=12, color='#2E4053'),
            hovermode='x unified',
            modebar_orientation='h'
        )
        
        # Save the plot as HTML
        plot_html = fig.to_html(full_html=False, include_plotlyjs='cdn')
        
        # Save to file
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(plot_html)
        
        return plot_html
        
    except Exception as e:
        print(f"Error generating price chart: {e}")
        return None

def plot_to_base64(filename='forecast.html'):
    """Get the plot HTML content from file"""
    try:
        with open(filename, "r", encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Error reading plot HTML: {e}")
        return None

def get_position_history():
    """Fetch trade history from Binance and format it"""
    endpoint = '/fapi/v1/userTrades'
    timestamp = int(time.time() * 1000)
    
    params = {
        'symbol': symbol,
        'timestamp': timestamp,
        'limit': 1000,  # Maximum allowed by Binance
        'recvWindow': 5000
    }
    
    params = create_signed_params(params)
    
    try:
        response = requests.get(BASE_URL + endpoint, params=params, headers={'X-MBX-APIKEY': API_KEY})
        trades_data = response.json()
        
        if isinstance(trades_data, list):
            formatted_trades = []
            for trade in trades_data:
                close_price = float(trade['price'])
                qty = abs(float(trade['qty']))
                pnl = float(trade['realizedPnl'])
                is_short = pnl < 0
                
                # Calculate entry price
                if pnl != 0:  # If there's PnL, this is a closing trade
                    if is_short:
                        # For shorts: entry_price = close_price - (abs(PnL) / quantity)
                        entry_price = close_price - (abs(pnl) / qty)
                    else:
                        # For longs: entry_price = close_price - (Pnl / quantity)
                        entry_price = close_price - (pnl / qty)
                else:
                    # If no PnL, this is an opening trade, so entry = close
                    entry_price = close_price
                
                formatted_trade = {
                    'symbol': trade['symbol'],
                    'side': 'SHORT' if is_short else 'LONG',
                    'quantity': qty,
                    'entry_price': round(entry_price, 5),
                    'close_price': round(close_price, 5),
                    'pnl': pnl,
                    'status': 'Closed',
                    'time': datetime.fromtimestamp(trade['time']/1000).strftime('%Y-%m-%d %H:%M:%S'),
                    'exit_time': datetime.fromtimestamp(trade['time']/1000).strftime('%Y-%m-%d %H:%M:%S')
                }
                #print(f"Trade: side={formatted_trade['side']}, qty={qty}, entry={formatted_trade['entry_price']}, close={formatted_trade['close_price']}, pnl={pnl}")
                formatted_trades.append(formatted_trade)
            return formatted_trades
    except Exception as e:
        print(f"Error fetching trade history from Binance: {e}")
        return []








trading_thread = threading.Thread(target=check_candle, daemon=True)
trading_thread.start()

@app.route('/update_trades')
def update_trades():
    """Update trades display with current positions and history"""
    global trades, balance2, current_price, investment_amount, margin_amount
    
    try:
        # Get current open positions from Binance
        position_open, current_positions = get_open_positions()
        
        # Get historical trades from cache but filter out active positions
        historical_trades = [trade for trade in get_cached_trade_history() if trade.get('status') != 'Active']
        
        # Update balance
        balance2 = get_usdt_balance()
        investment_amount = balance2 * (invest_percentage / 100) if balance2 else 0
        margin_amount = investment_amount / 10
        
        # Format current positions to match the expected structure
        formatted_positions = []
        for pos in current_positions:
            formatted_pos = {
                'symbol': pos['symbol'],
                'side': pos['side'],
                'quantity': pos['quantity'],
                'entry_price': pos['entry_price'],
                'take_profit': pos['take_profit'],
                'stop_loss': pos['stop_loss'],
                'unrealized_pnl': pos['pnl'],
                'roi': pos['roi'],
                'status': 'Active',
                'entry_date': pos['entry_date']
            }
            formatted_positions.append(formatted_pos)
        
        return jsonify({
            'starting_balance': starting_balance,
            'symbol': symbol,
            'current_price': current_price,
            'trades': historical_trades,
            'open_positions': formatted_positions,
            'balance': balance2,
            'investment_amount': margin_amount
        })
    except Exception as e:
        print(f"Error in update_trades: {e}")
        return jsonify({
            'starting_balance': starting_balance,
            'symbol': symbol,
            'current_price': current_price,
            'trades': [],
            'open_positions': [],
            'balance': balance2,
            'investment_amount': margin_amount
        })

# Legacy frontend route removed - now handled by separate frontend application

if __name__ == '__main__':
    # Initialize trade history cache
    update_trade_history_cache()
    
    # Start the trading logic in a separate thread
    trading_thread = threading.Thread(target=check_candle, daemon=True)
    trading_thread.start()
    
    app.run(host='0.0.0.0', port=8080, debug=True)

#comandos para gunicorn:  gunicorn --timeout 120 --bind 0.0.0.0:8080 trade34:app
