{"start_time": "2025-07-28T10:18:05.905068", "test_suites": {"Signal Generation Testing": {"status": "COMPLETED", "tests": {"signal_generator_import": {"status": "PASSED", "message": "Signal generator imported and initialized successfully"}, "market_data_fetch": {"status": "PASSED", "message": "Fetched 100 klines, current price: $118,630.00", "data": {"klines_count": 100, "current_price": 118630.0, "data_source": "futures_api"}}, "signal_generation_real_data": {"status": "FAILED", "message": "Signal generation failed: Signal generation failed: Signal generation exception: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}}, "total_tests": 3, "passed_tests": 2, "failed_tests": 1, "skipped_tests": 0}, "ML Forecast Integration": {"status": "COMPLETED", "tests": {"ml_service_import": {"status": "PASSED", "message": "ML service imported and has required methods"}, "ensemble_forecast_generation": {"status": "PASSED", "message": "Forecast generated with 24 predictions, confidence: 0%", "data": {"forecast_length": 24, "confidence": 0, "has_direction": false, "has_trend": false}}}, "total_tests": 2, "passed_tests": 2, "failed_tests": 0, "skipped_tests": 0}, "Trade Execution Pipeline": {"status": "COMPLETED", "tests": {"trading_container_management": {"status": "FAILED", "message": "Trading container management failed: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}, "exchange_service_integration": {"status": "PASSED", "message": "3/4 exchanges passed integration test", "data": {"binance": {"service_created": true, "has_required_methods": true, "status": "PASSED"}, "binance_us": {"service_created": true, "has_required_methods": true, "status": "PASSED"}, "kraken": {"service_created": true, "has_required_methods": true, "status": "PASSED"}, "bitso": {"service_created": false, "error": "Unsupported exchange: bitso. Supported exchanges: binance, binance_us, kraken, bybit, hyperliquid", "status": "FAILED"}}}, "paper_trading_service": {"status": "FAILED", "message": "Paper trading service failed: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}}, "total_tests": 3, "passed_tests": 1, "failed_tests": 2, "skipped_tests": 0}, "Integration Testing": {"status": "COMPLETED", "tests": {"end_to_end_signal_flow": {"status": "FAILED", "message": "End-to-end workflow failed: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}, "user_tier_validation": {"status": "PASSED", "message": "12/12 tier validation tests passed", "data": {"tier_1_binance": {"max_leverage": 3.0, "status": "PASSED"}, "tier_1_binance_us": {"max_leverage": 1, "status": "PASSED"}, "tier_1_kraken": {"max_leverage": 3.0, "status": "PASSED"}, "tier_1_bitso": {"max_leverage": 1, "status": "PASSED"}, "tier_2_binance": {"max_leverage": 5.0, "status": "PASSED"}, "tier_2_binance_us": {"max_leverage": 1, "status": "PASSED"}, "tier_2_kraken": {"max_leverage": 5.0, "status": "PASSED"}, "tier_2_bitso": {"max_leverage": 1, "status": "PASSED"}, "tier_3_binance": {"max_leverage": 10.0, "status": "PASSED"}, "tier_3_binance_us": {"max_leverage": 1, "status": "PASSED"}, "tier_3_kraken": {"max_leverage": 10.0, "status": "PASSED"}, "tier_3_bitso": {"max_leverage": 1, "status": "PASSED"}}}}, "total_tests": 2, "passed_tests": 1, "failed_tests": 1, "skipped_tests": 0}, "Error Handling & Edge Cases": {"status": "COMPLETED", "tests": {"insufficient_balance_handling": {"status": "FAILED", "message": "Insufficient balance test failed: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}, "api_connection_failure_handling": {"status": "PASSED", "message": "API connection failures handled gracefully", "data": {"graceful_handling": true, "fallback_available": true}}, "position_conflict_resolution": {"status": "PASSED", "message": "Position conflict resolution logic validated", "data": {"detect_existing_position": true, "clear_pending_orders": true, "validate_new_signal": true, "prevent_conflicting_trades": true}}}, "total_tests": 3, "passed_tests": 2, "failed_tests": 1, "skipped_tests": 0}, "Performance & Load Testing": {"status": "COMPLETED", "tests": {"signal_generation_performance": {"status": "PASSED", "message": "Signal generation took 0.00 seconds", "data": {"generation_time_seconds": 0.0, "performance_acceptable": true, "signal_generated": false}}}, "total_tests": 1, "passed_tests": 1, "failed_tests": 0, "skipped_tests": 0}}, "summary": {"total_tests": 14, "passed_tests": 9, "failed_tests": 5, "skipped_tests": 0}, "end_time": "2025-07-28T10:18:28.496099", "duration": "0:00:22.591031"}