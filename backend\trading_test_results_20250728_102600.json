{"start_time": "2025-07-28T10:25:38.242682", "test_suites": {"Signal Generation Testing": {"status": "COMPLETED", "tests": {"signal_generator_import": {"status": "PASSED", "message": "Signal generator imported and initialized successfully"}, "market_data_fetch": {"status": "PASSED", "message": "Fetched 100 klines, current price: $118,551.40", "data": {"klines_count": 100, "current_price": 118551.4, "data_source": "futures_api"}}, "signal_generation_real_data": {"status": "FAILED", "message": "Signal generation failed: Signal generation failed: User not found"}}, "total_tests": 3, "passed_tests": 2, "failed_tests": 1, "skipped_tests": 0}, "ML Forecast Integration": {"status": "COMPLETED", "tests": {"ml_service_import": {"status": "PASSED", "message": "ML service imported and has required methods"}, "ensemble_forecast_generation": {"status": "PASSED", "message": "Forecast generated with 24 predictions, confidence: 0%", "data": {"forecast_length": 24, "confidence": 0, "has_direction": false, "has_trend": false}}}, "total_tests": 2, "passed_tests": 2, "failed_tests": 0, "skipped_tests": 0}, "Trade Execution Pipeline": {"status": "COMPLETED", "tests": {"trading_container_management": {"status": "PASSED", "message": "Trading container created and configured successfully", "data": {"container_created": true, "risk_params_set": true, "user_id": "1"}}, "exchange_service_integration": {"status": "PASSED", "message": "3/4 exchanges passed integration test", "data": {"binance": {"service_created": true, "has_required_methods": true, "status": "PASSED"}, "binance_us": {"service_created": true, "has_required_methods": true, "status": "PASSED"}, "kraken": {"service_created": true, "has_required_methods": true, "status": "PASSED"}, "bitso": {"service_created": false, "error": "Unsupported exchange: bitso. Supported exchanges: binance, binance_us, kraken, bybit, hyperliquid", "status": "FAILED"}}}, "paper_trading_service": {"status": "FAILED", "message": "Paper trading service failed: (MySQLdb.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`deeptrade`.`paper_trading_accounts`, CONSTRAINT `paper_trading_accounts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`))')\n[SQL: INSERT INTO paper_trading_accounts (id, user_id, virtual_balance, initial_balance, total_pnl, total_trades_count, winning_trades_count, losing_trades_count, win_rate, reset_count, last_reset_at, created_at, updated_at) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]\n[parameters: ('0764dbec-1b8d-4783-ad10-6dfc0a9cd24a', '1', Decimal('10000'), Decimal('10000'), 0.0, 0, 0, 0, 0.0, 0, None, datetime.datetime(2025, 7, 28, 13, 25, 59, 175677), datetime.datetime(2025, 7, 28, 13, 25, 59, 175677))]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)"}}, "total_tests": 3, "passed_tests": 2, "failed_tests": 1, "skipped_tests": 0}, "Integration Testing": {"status": "COMPLETED", "tests": {"end_to_end_signal_flow": {"status": "FAILED", "message": "End-to-end workflow failed", "data": {"signal_generated": false, "container_configured": true, "forecast_processed": true, "workflow_complete": true}}, "user_tier_validation": {"status": "PASSED", "message": "12/12 tier validation tests passed", "data": {"tier_1_binance": {"max_leverage": 3.0, "status": "PASSED"}, "tier_1_binance_us": {"max_leverage": 1, "status": "PASSED"}, "tier_1_kraken": {"max_leverage": 3.0, "status": "PASSED"}, "tier_1_bitso": {"max_leverage": 1, "status": "PASSED"}, "tier_2_binance": {"max_leverage": 5.0, "status": "PASSED"}, "tier_2_binance_us": {"max_leverage": 1, "status": "PASSED"}, "tier_2_kraken": {"max_leverage": 5.0, "status": "PASSED"}, "tier_2_bitso": {"max_leverage": 1, "status": "PASSED"}, "tier_3_binance": {"max_leverage": 10.0, "status": "PASSED"}, "tier_3_binance_us": {"max_leverage": 1, "status": "PASSED"}, "tier_3_kraken": {"max_leverage": 10.0, "status": "PASSED"}, "tier_3_bitso": {"max_leverage": 1, "status": "PASSED"}}}}, "total_tests": 2, "passed_tests": 1, "failed_tests": 1, "skipped_tests": 0}, "Error Handling & Edge Cases": {"status": "COMPLETED", "tests": {"insufficient_balance_handling": {"status": "PASSED", "message": "Insufficient balance correctly detected: 50.0 < 100.0", "data": {"mock_balance": 50.0, "minimum_required": 100.0, "balance_sufficient": false, "should_disable_trading": true}}, "api_connection_failure_handling": {"status": "PASSED", "message": "API connection failures handled gracefully", "data": {"graceful_handling": true, "fallback_available": true}}, "position_conflict_resolution": {"status": "PASSED", "message": "Position conflict resolution logic validated", "data": {"detect_existing_position": true, "clear_pending_orders": true, "validate_new_signal": true, "prevent_conflicting_trades": true}}}, "total_tests": 3, "passed_tests": 3, "failed_tests": 0, "skipped_tests": 0}, "Performance & Load Testing": {"status": "COMPLETED", "tests": {"signal_generation_performance": {"status": "PASSED", "message": "Signal generation took 0.00 seconds", "data": {"generation_time_seconds": 0.0, "performance_acceptable": true, "signal_generated": false}}}, "total_tests": 1, "passed_tests": 1, "failed_tests": 0, "skipped_tests": 0}}, "summary": {"total_tests": 14, "passed_tests": 11, "failed_tests": 3, "skipped_tests": 0}, "end_time": "2025-07-28T10:26:00.213661", "duration": "0:00:21.970979"}