#!/usr/bin/env python3
"""
Training system for SL and TP ML models
Generates training data and trains specialized models
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import joblib

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def train_sl_tp_models():
    """Train both SL and TP ML models with comprehensive data"""
    print("🤖 Training SL/TP ML Models")
    print("=" * 50)
    
    try:
        # Import Flask app and create application context
        from app import create_app
        app = create_app()
        
        with app.app_context():
            # Import required services
            from app.services.market_data import BinanceMarketData
            from app.services.sl_tp_ml_predictors import StopLossMLPredictor, TakeProfitMLPredictor
            
            # Initialize services
            market_service = BinanceMarketData()
            sl_predictor = StopLossMLPredictor()
            tp_predictor = TakeProfitMLPredictor()
            
            print("✅ Services initialized")
            
            # Generate comprehensive training data
            print("\n📊 Generating training data...")
            
            # Create diverse market scenarios for training
            training_scenarios = [
                # (trend, volatility, candles, scenario_name)
                (0.8, 0.3, 500, "Strong Bull Low Vol"),
                (-0.6, 0.4, 500, "Strong Bear Med Vol"),
                (0.1, 0.8, 500, "Sideways High Vol"),
                (1.2, 0.9, 500, "Volatile Bull"),
                (-0.9, 0.7, 500, "Volatile Bear"),
                (0.3, 0.5, 500, "Weak Bull Med Vol"),
                (-0.2, 0.6, 500, "Weak Bear Med Vol"),
                (0.0, 0.4, 500, "Pure Sideways"),
                (0.5, 0.2, 500, "Steady Bull Low Vol"),
                (-0.4, 0.3, 500, "Steady Bear Low Vol")
            ]
            
            sl_training_data = []
            tp_training_data = []
            
            for trend, volatility, candles, scenario_name in training_scenarios:
                print(f"   🎯 Processing {scenario_name}...")
                
                # Generate realistic market data
                base_price = 65000
                prices = []
                
                for i in range(candles):
                    # Trend component
                    trend_component = (trend * i * 8)
                    
                    # Volatility component with realistic patterns
                    noise = np.random.normal(0, volatility * 180)
                    
                    # Add some cyclical patterns
                    cycle = np.sin(i * 0.1) * volatility * 50
                    
                    # Price calculation
                    price = base_price + trend_component + noise + cycle
                    prices.append(max(price, 45000))  # Floor at $45k
                
                # Create OHLC data with realistic relationships
                mock_data = pd.DataFrame({
                    'timestamp': [1640995200000 + (i * 3600000) for i in range(candles)],
                    'open': prices,
                    'high': [p * (1 + np.random.uniform(0.001, 0.006)) for p in prices],
                    'low': [p * (1 - np.random.uniform(0.001, 0.006)) for p in prices],
                    'close': [p * (1 + np.random.uniform(-0.002, 0.002)) for p in prices],
                    'volume': [np.random.uniform(3500, 7500) for _ in range(candles)]
                })
                
                # Ensure OHLC relationships
                for i in range(len(mock_data)):
                    open_price = mock_data.loc[i, 'open']
                    close_price = mock_data.loc[i, 'close']
                    mock_data.loc[i, 'high'] = max(mock_data.loc[i, 'high'], open_price, close_price)
                    mock_data.loc[i, 'low'] = min(mock_data.loc[i, 'low'], open_price, close_price)
                
                # Generate training samples from this scenario
                num_samples = 100  # 100 samples per scenario
                
                for sample_idx in range(num_samples):
                    # Random entry point (leave room for history and future)
                    entry_idx = np.random.randint(50, len(mock_data) - 100)
                    entry_price = float(mock_data.iloc[entry_idx]['close'])
                    
                    # Random signal direction (biased by trend)
                    if trend > 0.4:
                        signal = 'BUY' if np.random.random() > 0.25 else 'SELL'
                    elif trend < -0.4:
                        signal = 'SELL' if np.random.random() > 0.25 else 'BUY'
                    else:
                        signal = np.random.choice(['BUY', 'SELL'])
                    
                    # Historical data for feature extraction
                    historical_data = mock_data.iloc[:entry_idx+1].copy()
                    
                    # Future data for optimal SL/TP calculation
                    future_data = mock_data.iloc[entry_idx:entry_idx+50].copy()
                    
                    if len(future_data) < 10:
                        continue
                    
                    # Calculate optimal SL and TP based on future price action
                    optimal_sl, optimal_tp = calculate_optimal_sl_tp_from_future(
                        entry_price, signal, future_data, volatility
                    )
                    
                    if optimal_sl is None or optimal_tp is None:
                        continue
                    
                    # Extract features for SL model
                    sl_features = sl_predictor.extract_sl_features(historical_data, entry_price, signal)
                    if sl_features is not None:
                        sl_distance_pct = abs(entry_price - optimal_sl) / entry_price * 100
                        sl_training_data.append({
                            'features': sl_features.flatten(),
                            'target': sl_distance_pct,
                            'signal': signal,
                            'scenario': scenario_name
                        })
                    
                    # Extract features for TP model
                    tp_features = tp_predictor.extract_tp_features(historical_data, entry_price, signal, optimal_sl)
                    if tp_features is not None:
                        tp_distance_pct = abs(optimal_tp - entry_price) / entry_price * 100
                        tp_training_data.append({
                            'features': tp_features.flatten(),
                            'target': tp_distance_pct,
                            'signal': signal,
                            'scenario': scenario_name
                        })
            
            print(f"✅ Generated {len(sl_training_data)} SL samples and {len(tp_training_data)} TP samples")
            
            # Train SL Model
            print("\n🎯 Training Stop Loss ML Model...")
            if len(sl_training_data) > 100:
                sl_success = train_sl_model(sl_predictor, sl_training_data)
                print(f"   {'✅' if sl_success else '❌'} SL Model Training: {'Success' if sl_success else 'Failed'}")
            else:
                print("   ❌ Insufficient SL training data")
                sl_success = False
            
            # Train TP Model
            print("\n🎯 Training Take Profit ML Model...")
            if len(tp_training_data) > 100:
                tp_success = train_tp_model(tp_predictor, tp_training_data)
                print(f"   {'✅' if tp_success else '❌'} TP Model Training: {'Success' if tp_success else 'Failed'}")
            else:
                print("   ❌ Insufficient TP training data")
                tp_success = False
            
            # Save trained models
            if sl_success:
                save_sl_model(sl_predictor)
                print("   💾 SL Model saved")
            
            if tp_success:
                save_tp_model(tp_predictor)
                print("   💾 TP Model saved")
            
            # Test the trained models
            print("\n🧪 Testing trained models...")
            test_results = test_trained_models(sl_predictor, tp_predictor)
            
            print(f"\n🎉 Training Complete!")
            print(f"   🎯 SL Model: {'✅ Ready' if sl_success else '❌ Failed'}")
            print(f"   🎯 TP Model: {'✅ Ready' if tp_success else '❌ Failed'}")
            
            if test_results:
                print(f"   📊 Test Accuracy: SL={test_results['sl_accuracy']:.1f}%, TP={test_results['tp_accuracy']:.1f}%")
            
            return sl_success and tp_success
            
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def calculate_optimal_sl_tp_from_future(entry_price, signal, future_data, volatility):
    """
    Calculate optimal SL and TP based on actual future price movement
    This creates the ground truth for training
    """
    try:
        if len(future_data) < 5:
            return None, None
        
        # Analyze future price action
        future_highs = future_data['high'].values
        future_lows = future_data['low'].values
        future_closes = future_data['close'].values
        
        max_favorable = 0
        max_adverse = 0
        
        if signal == 'BUY':
            # For BUY signals
            max_favorable = max(future_highs) - entry_price  # Max profit potential
            max_adverse = entry_price - min(future_lows)     # Max loss potential
            
            # Optimal SL: Protect against major adverse moves (use 70% of max adverse)
            optimal_sl_distance = min(max_adverse * 0.7, entry_price * 0.015)  # Cap at 1.5%
            optimal_sl = entry_price - optimal_sl_distance
            
            # Optimal TP: Capture reasonable profit (use 60% of max favorable)
            optimal_tp_distance = max(max_favorable * 0.6, entry_price * 0.008)  # Min 0.8%
            optimal_tp = entry_price + optimal_tp_distance
            
        else:  # SELL
            # For SELL signals
            max_favorable = entry_price - min(future_lows)   # Max profit potential
            max_adverse = max(future_highs) - entry_price    # Max loss potential
            
            # Optimal SL: Protect against major adverse moves
            optimal_sl_distance = min(max_adverse * 0.7, entry_price * 0.015)  # Cap at 1.5%
            optimal_sl = entry_price + optimal_sl_distance
            
            # Optimal TP: Capture reasonable profit
            optimal_tp_distance = max(max_favorable * 0.6, entry_price * 0.008)  # Min 0.8%
            optimal_tp = entry_price - optimal_tp_distance
        
        # Apply volatility adjustments
        volatility_factor = min(volatility, 1.0)  # Cap at 1.0
        
        # Wider SL in high volatility
        if volatility_factor > 0.6:
            sl_adjustment = entry_price * 0.003 * volatility_factor
            if signal == 'BUY':
                optimal_sl -= sl_adjustment
            else:
                optimal_sl += sl_adjustment
        
        # Ensure minimum distances
        min_sl_distance = entry_price * 0.003  # 0.3%
        min_tp_distance = entry_price * 0.006  # 0.6%
        
        if signal == 'BUY':
            optimal_sl = min(optimal_sl, entry_price - min_sl_distance)
            optimal_tp = max(optimal_tp, entry_price + min_tp_distance)
        else:
            optimal_sl = max(optimal_sl, entry_price + min_sl_distance)
            optimal_tp = min(optimal_tp, entry_price - min_tp_distance)
        
        # Ensure reasonable risk-reward (minimum 1:1.5)
        risk = abs(entry_price - optimal_sl)
        reward = abs(optimal_tp - entry_price)
        
        if reward < (risk * 1.5):
            if signal == 'BUY':
                optimal_tp = entry_price + (risk * 1.8)  # 1:1.8 ratio
            else:
                optimal_tp = entry_price - (risk * 1.8)
        
        return optimal_sl, optimal_tp
        
    except Exception as e:
        return None, None

def train_sl_model(sl_predictor, training_data):
    """Train the Stop Loss ML model"""
    try:
        # Prepare training data
        X = np.array([sample['features'] for sample in training_data])
        y = np.array([sample['target'] for sample in training_data])
        
        # Remove any invalid samples
        valid_mask = ~(np.isnan(X).any(axis=1) | np.isnan(y))
        X = X[valid_mask]
        y = y[valid_mask]
        
        if len(X) < 50:
            return False
        
        # Scale features
        X_scaled = sl_predictor.scaler.fit_transform(X)
        
        # Train each model in ensemble
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42)
        
        accuracies = []
        
        for model_name, model in sl_predictor.models.items():
            model.fit(X_train, y_train)
            
            # Calculate accuracy (within 20% of actual)
            y_pred = model.predict(X_test)
            accuracy = np.mean(np.abs(y_pred - y_test) / y_test < 0.2)
            accuracies.append(accuracy)
            
            print(f"     {model_name.upper()}: {accuracy*100:.1f}% accuracy")
        
        sl_predictor.accuracy = np.mean(accuracies)
        sl_predictor.is_trained = True
        
        return True
        
    except Exception as e:
        print(f"   ❌ SL training error: {e}")
        return False

def train_tp_model(tp_predictor, training_data):
    """Train the Take Profit ML model"""
    try:
        # Prepare training data
        X = np.array([sample['features'] for sample in training_data])
        y = np.array([sample['target'] for sample in training_data])
        
        # Remove any invalid samples
        valid_mask = ~(np.isnan(X).any(axis=1) | np.isnan(y))
        X = X[valid_mask]
        y = y[valid_mask]
        
        if len(X) < 50:
            return False
        
        # Scale features
        X_scaled = tp_predictor.scaler.fit_transform(X)
        
        # Train each model in ensemble
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42)
        
        accuracies = []
        
        for model_name, model in tp_predictor.models.items():
            model.fit(X_train, y_train)
            
            # Calculate accuracy (within 25% of actual)
            y_pred = model.predict(X_test)
            accuracy = np.mean(np.abs(y_pred - y_test) / y_test < 0.25)
            accuracies.append(accuracy)
            
            print(f"     {model_name.upper()}: {accuracy*100:.1f}% accuracy")
        
        tp_predictor.accuracy = np.mean(accuracies)
        tp_predictor.is_trained = True
        
        return True
        
    except Exception as e:
        print(f"   ❌ TP training error: {e}")
        return False

def save_sl_model(sl_predictor):
    """Save the trained SL model"""
    try:
        model_dir = "models"
        os.makedirs(model_dir, exist_ok=True)
        
        # Save each model and scaler
        for model_name, model in sl_predictor.models.items():
            joblib.dump(model, f"{model_dir}/sl_model_{model_name}.pkl")
        
        joblib.dump(sl_predictor.scaler, f"{model_dir}/sl_scaler.pkl")
        
        # Save metadata
        metadata = {
            'accuracy': sl_predictor.accuracy,
            'is_trained': sl_predictor.is_trained,
            'model_weights': sl_predictor.model_weights,
            'trained_at': datetime.now().isoformat()
        }
        
        import json
        with open(f"{model_dir}/sl_metadata.json", 'w') as f:
            json.dump(metadata, f)
        
    except Exception as e:
        print(f"   ❌ Error saving SL model: {e}")

def save_tp_model(tp_predictor):
    """Save the trained TP model"""
    try:
        model_dir = "models"
        os.makedirs(model_dir, exist_ok=True)
        
        # Save each model and scaler
        for model_name, model in tp_predictor.models.items():
            joblib.dump(model, f"{model_dir}/tp_model_{model_name}.pkl")
        
        joblib.dump(tp_predictor.scaler, f"{model_dir}/tp_scaler.pkl")
        
        # Save metadata
        metadata = {
            'accuracy': tp_predictor.accuracy,
            'is_trained': tp_predictor.is_trained,
            'model_weights': tp_predictor.model_weights,
            'trained_at': datetime.now().isoformat()
        }
        
        import json
        with open(f"{model_dir}/tp_metadata.json", 'w') as f:
            json.dump(metadata, f)
        
    except Exception as e:
        print(f"   ❌ Error saving TP model: {e}")

def test_trained_models(sl_predictor, tp_predictor):
    """Test the trained models with sample data"""
    try:
        # Create sample market data for testing
        test_data = pd.DataFrame({
            'timestamp': [1640995200000 + (i * 3600000) for i in range(100)],
            'open': [65000 + i * 10 + np.random.normal(0, 50) for i in range(100)],
            'high': [65100 + i * 10 + np.random.normal(0, 60) for i in range(100)],
            'low': [64900 + i * 10 + np.random.normal(0, 40) for i in range(100)],
            'close': [65000 + i * 10 + np.random.normal(0, 45) for i in range(100)],
            'volume': [np.random.uniform(4000, 8000) for _ in range(100)]
        })
        
        # Ensure OHLC relationships
        for i in range(len(test_data)):
            open_price = test_data.loc[i, 'open']
            close_price = test_data.loc[i, 'close']
            test_data.loc[i, 'high'] = max(test_data.loc[i, 'high'], open_price, close_price)
            test_data.loc[i, 'low'] = min(test_data.loc[i, 'low'], open_price, close_price)
        
        # Test predictions
        entry_price = 65500
        signal = 'BUY'
        
        sl_result = sl_predictor.predict_optimal_sl(test_data, entry_price, signal)
        tp_result = tp_predictor.predict_optimal_tp(test_data, entry_price, signal, sl_result['sl_price'])
        
        print(f"   🧪 Test SL: ${sl_result['sl_price']:.2f} ({sl_result['confidence']:.1f}% confidence)")
        print(f"   🧪 Test TP: ${tp_result['tp_price']:.2f} ({tp_result['confidence']:.1f}% confidence)")
        print(f"   🧪 Risk-Reward: 1:{tp_result['risk_reward_ratio']:.2f}")
        
        return {
            'sl_accuracy': sl_predictor.accuracy * 100,
            'tp_accuracy': tp_predictor.accuracy * 100,
            'test_successful': True
        }
        
    except Exception as e:
        print(f"   ❌ Testing error: {e}")
        return None

def load_trained_models():
    """Load previously trained SL/TP models"""
    try:
        from app.services.sl_tp_ml_predictors import StopLossMLPredictor, TakeProfitMLPredictor

        sl_predictor = StopLossMLPredictor()
        tp_predictor = TakeProfitMLPredictor()

        model_dir = "models"

        # Load SL model
        if os.path.exists(f"{model_dir}/sl_metadata.json"):
            import json
            with open(f"{model_dir}/sl_metadata.json", 'r') as f:
                sl_metadata = json.load(f)

            # Load models
            for model_name in sl_predictor.models.keys():
                if os.path.exists(f"{model_dir}/sl_model_{model_name}.pkl"):
                    sl_predictor.models[model_name] = joblib.load(f"{model_dir}/sl_model_{model_name}.pkl")

            # Load scaler
            if os.path.exists(f"{model_dir}/sl_scaler.pkl"):
                sl_predictor.scaler = joblib.load(f"{model_dir}/sl_scaler.pkl")

            sl_predictor.accuracy = sl_metadata.get('accuracy', 0.0)
            sl_predictor.is_trained = sl_metadata.get('is_trained', False)

            print(f"✅ SL Model loaded (accuracy: {sl_predictor.accuracy*100:.1f}%)")

        # Load TP model
        if os.path.exists(f"{model_dir}/tp_metadata.json"):
            import json
            with open(f"{model_dir}/tp_metadata.json", 'r') as f:
                tp_metadata = json.load(f)

            # Load models
            for model_name in tp_predictor.models.keys():
                if os.path.exists(f"{model_dir}/tp_model_{model_name}.pkl"):
                    tp_predictor.models[model_name] = joblib.load(f"{model_dir}/tp_model_{model_name}.pkl")

            # Load scaler
            if os.path.exists(f"{model_dir}/tp_scaler.pkl"):
                tp_predictor.scaler = joblib.load(f"{model_dir}/tp_scaler.pkl")

            tp_predictor.accuracy = tp_metadata.get('accuracy', 0.0)
            tp_predictor.is_trained = tp_metadata.get('is_trained', False)

            print(f"✅ TP Model loaded (accuracy: {tp_predictor.accuracy*100:.1f}%)")

        return sl_predictor, tp_predictor

    except Exception as e:
        print(f"❌ Error loading models: {e}")
        return None, None

if __name__ == "__main__":
    print("🚀 DeepTrade SL/TP ML Training System")
    print("=" * 50)

    success = train_sl_tp_models()

    if success:
        print(f"\n🎉 TRAINING SUCCESSFUL!")
        print(f"   🤖 Independent SL/TP ML models ready")
        print(f"   🎯 Ready for integration with Elite ML system")
    else:
        print(f"\n❌ Training failed - check logs for details")
