#!/usr/bin/env python3
"""
Update existing users to have correct account types for their selected exchange
"""

import mysql.connector
from mysql.connector import Error

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'deeptrade',
    'user': 'deeptrade_user',
    'password': '123456'
}

def update_account_types():
    """Update account types based on exchange"""
    connection = None
    cursor = None
    
    try:
        print("🔄 Connecting to MySQL database...")
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("✅ Connected successfully!")
        
        # Update account types based on exchange
        updates = [
            ("UPDATE users SET account_type = 'FUTURES' WHERE selected_exchange = 'binance'", "Binance -> FUTURES"),
            ("UPDATE users SET account_type = 'SPOT_MARGIN' WHERE selected_exchange = 'binance_us'", "Binance US -> SPOT_MARGIN"),
            ("UPDATE users SET account_type = 'FUTURES' WHERE selected_exchange = 'kraken'", "Kraken -> FUTURES"),
            ("UPDATE users SET account_type = 'SPOT' WHERE selected_exchange = 'bitso'", "Bitso -> SPOT")
        ]
        
        print("📝 Updating account types...")
        
        for update_sql, description in updates:
            print(f"  {description}")
            cursor.execute(update_sql)
            affected_rows = cursor.rowcount
            print(f"    Updated {affected_rows} users")
        
        # Show current distribution
        print("\n📊 Current account type distribution:")
        cursor.execute("""
            SELECT selected_exchange, account_type, COUNT(*) as count
            FROM users 
            GROUP BY selected_exchange, account_type
            ORDER BY selected_exchange, account_type
        """)
        
        results = cursor.fetchall()
        for exchange, account_type, count in results:
            print(f"  {exchange} ({account_type}): {count} users")
        
        connection.commit()
        print("\n✅ Account types updated successfully!")
        
    except Error as e:
        print(f"❌ Update failed: {e}")
        if connection:
            connection.rollback()
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
            print("🔌 Database connection closed.")

if __name__ == "__main__":
    print("🔧 Updating Account Types for Correct Exchange Market Types")
    print("=" * 60)
    update_account_types()
