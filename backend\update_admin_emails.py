#!/usr/bin/env python3
"""
Update Admin Usernames to Email Format Script
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.admin import AdminUser
from werkzeug.security import generate_password_hash

def update_admin_usernames():
    """Update admin usernames to email format"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔍 CHECKING CURRENT ADMIN USERS...")
            print("="*50)
            
            # Get all admin users
            admins = AdminUser.query.all()
            print(f"Found {len(admins)} admin users:")
            
            for admin in admins:
                print(f"- ID: {admin.id}, Username: {admin.username}, Super: {admin.is_super_admin}, Active: {admin.is_active}")
            
            print("\n🔧 UPDATING ADMIN USERNAMES TO EMAIL FORMAT...")
            print("="*50)
            
            # Define the mapping of old usernames to new email addresses
            username_mapping = {
                'admin': '<EMAIL>',
                'admin2': '<EMAIL>', 
                'admin3': '<EMAIL>',
                'limited_admin': '<EMAIL>'  # Map limited_admin to admin2 email
            }
            
            # Update each admin
            for admin in admins:
                old_username = admin.username
                
                if old_username in username_mapping:
                    new_email = username_mapping[old_username]
                    
                    # Check if the new email already exists (avoid duplicates)
                    existing = AdminUser.query.filter(
                        AdminUser.username == new_email,
                        AdminUser.id != admin.id
                    ).first()
                    
                    if existing:
                        print(f"⚠️  Email {new_email} already exists for another admin. Skipping {old_username}")
                        continue
                    
                    # Update the username
                    admin.username = new_email
                    print(f"✅ Updated: {old_username} → {new_email}")
                    
                    # Ensure admin is active and has proper privileges
                    admin.is_active = True
                    
                    # Set specific privileges based on email
                    if new_email == '<EMAIL>':
                        admin.is_super_admin = True
                        print(f"   → Set as Super Admin")
                    elif new_email in ['<EMAIL>', '<EMAIL>']:
                        admin.is_super_admin = True  # All admins are super admins for now
                        print(f"   → Set as Super Admin")
                else:
                    print(f"⚠️  No mapping found for username: {old_username}")
            
            # Create any missing admin accounts
            print("\n🆕 CREATING MISSING ADMIN ACCOUNTS...")
            print("="*50)
            
            required_admins = [
                ('<EMAIL>', True, 'admin123'),
                ('<EMAIL>', True, 'admin123'),
                ('<EMAIL>', True, 'admin123')
            ]
            
            for email, is_super, password in required_admins:
                existing = AdminUser.query.filter_by(username=email).first()
                if not existing:
                    print(f"Creating new admin: {email}")
                    new_admin = AdminUser(
                        username=email,
                        password=password,
                        is_super_admin=is_super,
                        is_active=True
                    )
                    db.session.add(new_admin)
                    print(f"✅ Created: {email} (Super Admin: {is_super})")
                else:
                    print(f"✅ Already exists: {email}")
            
            # Commit all changes
            db.session.commit()
            print("\n✅ ALL ADMIN USERNAMES UPDATED SUCCESSFULLY!")
            
            # Verify the changes
            print("\n🧪 VERIFICATION:")
            print("="*50)
            updated_admins = AdminUser.query.all()
            
            for admin in updated_admins:
                print(f"✅ {admin.username}")
                print(f"   - ID: {admin.id}")
                print(f"   - Super Admin: {admin.is_super_admin}")
                print(f"   - Active: {admin.is_active}")
                print(f"   - Created: {admin.created_at}")
                
                # Test password
                if admin.check_password('admin123'):
                    print(f"   - Password: ✅ 'admin123' works")
                else:
                    print(f"   - Password: ❌ 'admin123' does not work")
                print()
            
            print("🎯 ADMIN LOGIN CREDENTIALS:")
            print("="*50)
            for admin in updated_admins:
                if admin.is_active:
                    print(f"Email: {admin.username}")
                    print(f"Password: admin123")
                    print(f"Type: {'Super Admin' if admin.is_super_admin else 'Limited Admin'}")
                    print("-" * 30)
            
            return True
            
        except Exception as e:
            print(f"❌ Error updating admin usernames: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    print("🚀 STARTING ADMIN USERNAME UPDATE...")
    print("="*50)
    
    success = update_admin_usernames()
    
    if success:
        print("\n🎉 ADMIN USERNAME UPDATE COMPLETED SUCCESSFULLY!")
        print("\nNext steps:")
        print("1. Test admin login with new email addresses")
        print("2. Implement email validation for password changes")
        print("3. Add email modification functionality")
    else:
        print("\n❌ ADMIN USERNAME UPDATE FAILED!")
        print("Please check the error messages above and try again.")
