#!/usr/bin/env python3
"""
Balanced 90%+ Accuracy with 90%+ Selectivity ML System
Advanced ensemble approach with dynamic thresholds
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report
from sklearn.feature_selection import SelectKBest, f_classif
from sklearn.cluster import KMeans
import requests
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

BASE_URL = 'https://fapi.binance.com'

class Balanced90x90Predictor:
    """Balanced predictor targeting both 90%+ accuracy AND 90%+ selectivity"""
    
    def __init__(self):
        # Multi-tier confidence system
        self.primary_threshold = 0.55    # Lower threshold for initial filtering
        self.secondary_threshold = 0.75  # Higher threshold for final confirmation
        self.ensemble_threshold = 0.70   # Ensemble agreement threshold
        
        self.feature_selector = SelectKBest(f_classif, k=30)
        self.primary_models = {}
        self.secondary_models = {}
        self.scalers = {}
        
    def create_optimized_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create optimized feature set focusing on most predictive indicators"""
        features_df = df.copy()
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']
        
        # 1. PROVEN HIGH-IMPACT RSI FEATURES
        for period in [7, 14, 21]:
            delta = close.diff()
            gain = delta.clip(lower=0)
            loss = (-delta).clip(lower=0)
            avg_gain = gain.rolling(window=period).mean()
            avg_loss = loss.rolling(window=period).mean()
            rs = avg_gain / (avg_loss + 1e-10)
            rsi = 100 - (100 / (1 + rs))
            features_df[f'rsi_{period}'] = rsi
            
            # RSI-based signals
            features_df[f'rsi_{period}_oversold'] = (rsi < 30).astype(int)
            features_df[f'rsi_{period}_overbought'] = (rsi > 70).astype(int)
            features_df[f'rsi_{period}_bullish'] = (rsi > 50).astype(int)
            features_df[f'rsi_{period}_momentum'] = rsi - rsi.shift(1)
            features_df[f'rsi_{period}_divergence'] = ((rsi > rsi.shift(1)) & (close < close.shift(1))).astype(int)
        
        # RSI convergence (most predictive)
        features_df['rsi_bull_convergence'] = (
            (features_df['rsi_7'] > 50) & 
            (features_df['rsi_14'] > 50) & 
            (features_df['rsi_21'] > 50)
        ).astype(int)
        
        features_df['rsi_bear_convergence'] = (
            (features_df['rsi_7'] < 50) & 
            (features_df['rsi_14'] < 50) & 
            (features_df['rsi_21'] < 50)
        ).astype(int)
        
        # 2. ENHANCED MOMENTUM FEATURES
        for period in [1, 2, 3, 4, 6, 12]:
            momentum = close.pct_change(period)
            features_df[f'momentum_{period}h'] = momentum
            features_df[f'momentum_{period}h_positive'] = (momentum > 0).astype(int)
            features_df[f'momentum_{period}h_strong'] = (abs(momentum) > 0.01).astype(int)
        
        # Momentum acceleration
        features_df['momentum_acceleration'] = features_df['momentum_1h'] - features_df['momentum_1h'].shift(1)
        features_df['momentum_consistency'] = (
            (features_df['momentum_1h'] > 0) & 
            (features_df['momentum_2h'] > 0) & 
            (features_df['momentum_4h'] > 0)
        ).astype(int)
        
        # 3. VOLUME CONFIRMATION SIGNALS
        vol_sma_20 = volume.rolling(20).mean()
        vol_sma_50 = volume.rolling(50).mean()
        
        features_df['volume_surge'] = (volume > vol_sma_20 * 1.5).astype(int)
        features_df['volume_above_avg'] = (volume > vol_sma_20).astype(int)
        features_df['volume_trend'] = (vol_sma_20 > vol_sma_50).astype(int)
        
        # Price-volume confirmation
        price_up = (close > close.shift(1)).astype(int)
        price_down = (close < close.shift(1)).astype(int)
        
        features_df['bullish_volume'] = (price_up & features_df['volume_surge']).astype(int)
        features_df['bearish_volume'] = (price_down & features_df['volume_surge']).astype(int)
        
        # 4. MOVING AVERAGE SIGNALS
        for period in [10, 20, 50]:
            ma = close.rolling(period).mean()
            features_df[f'ma_{period}'] = ma
            features_df[f'above_ma_{period}'] = (close > ma).astype(int)
            features_df[f'ma_{period}_rising'] = (ma > ma.shift(3)).astype(int)
            features_df[f'price_ma_{period}_distance'] = (close - ma) / ma
        
        # MA alignment
        features_df['bullish_ma_alignment'] = (
            (close > features_df['ma_10']) & 
            (features_df['ma_10'] > features_df['ma_20']) & 
            (features_df['ma_20'] > features_df['ma_50'])
        ).astype(int)
        
        features_df['bearish_ma_alignment'] = (
            (close < features_df['ma_10']) & 
            (features_df['ma_10'] < features_df['ma_20']) & 
            (features_df['ma_20'] < features_df['ma_50'])
        ).astype(int)
        
        # 5. VOLATILITY AND BREAKOUT SIGNALS
        # ATR
        prev_close = close.shift(1)
        tr = np.maximum(high - low, np.maximum(abs(high - prev_close), abs(low - prev_close)))
        atr = tr.rolling(14).mean()
        features_df['atr'] = atr
        features_df['atr_expansion'] = (atr > atr.rolling(50).mean() * 1.2).astype(int)
        
        # Bollinger Bands
        bb_middle = close.rolling(20).mean()
        bb_std = close.rolling(20).std()
        bb_upper = bb_middle + (bb_std * 2)
        bb_lower = bb_middle - (bb_std * 2)
        
        features_df['bb_position'] = (close - bb_lower) / (bb_upper - bb_lower)
        features_df['bb_squeeze'] = ((bb_upper - bb_lower) / bb_middle < 0.1).astype(int)
        features_df['bb_breakout_up'] = (close > bb_upper).astype(int)
        features_df['bb_breakout_down'] = (close < bb_lower).astype(int)
        
        # Support/Resistance
        resistance = high.rolling(20).max()
        support = low.rolling(20).min()
        
        features_df['near_resistance'] = (close > resistance * 0.995).astype(int)
        features_df['near_support'] = (close < support * 1.005).astype(int)
        features_df['breakout'] = (close > resistance.shift(1)).astype(int)
        features_df['breakdown'] = (close < support.shift(1)).astype(int)
        
        # 6. MACD SIGNALS
        ema12 = close.ewm(span=12).mean()
        ema26 = close.ewm(span=26).mean()
        macd = ema12 - ema26
        macd_signal = macd.ewm(span=9).mean()
        macd_hist = macd - macd_signal
        
        features_df['macd'] = macd
        features_df['macd_signal'] = macd_signal
        features_df['macd_hist'] = macd_hist
        features_df['macd_bullish'] = (macd > macd_signal).astype(int)
        features_df['macd_cross_up'] = ((macd > macd_signal) & (macd.shift(1) <= macd_signal.shift(1))).astype(int)
        features_df['macd_cross_down'] = ((macd < macd_signal) & (macd.shift(1) >= macd_signal.shift(1))).astype(int)
        
        # 7. TREND STRENGTH
        for period in [5, 10, 20]:
            trend = (close > close.shift(period)).astype(int)
            features_df[f'uptrend_{period}'] = trend
            features_df[f'downtrend_{period}'] = (1 - trend)
        
        features_df['strong_uptrend'] = (
            features_df['uptrend_5'] & features_df['uptrend_10'] & features_df['uptrend_20']
        ).astype(int)
        
        features_df['strong_downtrend'] = (
            features_df['downtrend_5'] & features_df['downtrend_10'] & features_df['downtrend_20']
        ).astype(int)
        
        # 8. MARKET REGIME FEATURES
        returns_24h = close.pct_change(24)
        volatility_7d = returns_24h.rolling(168).std()
        trend_7d = (close / close.rolling(168).mean() - 1)
        
        features_df['trending_market'] = (abs(trend_7d) > 0.02).astype(int)
        features_df['high_volatility'] = (volatility_7d > volatility_7d.rolling(336).mean() * 1.1).astype(int)
        features_df['bull_market'] = (trend_7d > 0.01).astype(int)
        features_df['bear_market'] = (trend_7d < -0.01).astype(int)
        
        return features_df
    
    def train_dual_tier_model(self, df: pd.DataFrame) -> Dict:
        """Train dual-tier model system for balanced accuracy and frequency"""
        
        # Create optimized features
        features_df = self.create_optimized_features(df)
        
        # Create target with moderate threshold for balance
        future_return = features_df['close'].shift(-1) / features_df['close'] - 1
        target = (future_return > 0.002).astype(int)  # 0.2% threshold - balanced
        
        # Clean data
        valid_idx = ~(features_df.isnull().any(axis=1) | target.isnull())
        features_df = features_df[valid_idx]
        target = target[valid_idx]
        
        # Select numeric features
        numeric_features = features_df.select_dtypes(include=[np.number]).columns
        numeric_features = [col for col in numeric_features if col not in ['close', 'open']]
        
        X = features_df[numeric_features].replace([np.inf, -np.inf], np.nan).fillna(0)
        
        # Feature selection
        X_selected = self.feature_selector.fit_transform(X, target)
        selected_features = X.columns[self.feature_selector.get_support()]
        
        print(f"Selected top {len(selected_features)} features:")
        feature_scores = list(zip(selected_features, self.feature_selector.scores_[self.feature_selector.get_support()]))
        feature_scores.sort(key=lambda x: x[1], reverse=True)
        for feature, score in feature_scores[:10]:
            print(f"  {feature}: {score:.2f}")
        
        # Split data
        split_idx = int(0.8 * len(X_selected))
        X_train, X_val = X_selected[:split_idx], X_selected[split_idx:]
        y_train, y_val = target[:split_idx], target[split_idx:]
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)
        
        # Train primary models (optimized for frequency)
        primary_models = {
            'rf_primary': RandomForestClassifier(n_estimators=300, max_depth=10, min_samples_split=5, random_state=42),
            'gb_primary': GradientBoostingClassifier(n_estimators=200, max_depth=6, learning_rate=0.1, random_state=42),
            'lr_primary': LogisticRegression(C=1.0, random_state=42, max_iter=1000)
        }
        
        # Train secondary models (optimized for accuracy)
        secondary_models = {
            'rf_secondary': RandomForestClassifier(n_estimators=500, max_depth=8, min_samples_split=10, random_state=42),
            'svm_secondary': SVC(C=0.5, probability=True, random_state=42),
            'lr_secondary': LogisticRegression(C=0.1, random_state=42, max_iter=1000)
        }
        
        print("\n🎯 Training Primary Models (Frequency-Optimized):")
        primary_results = {}
        for name, model in primary_models.items():
            if 'lr' in name or 'svm' in name:
                model.fit(X_train_scaled, y_train)
                prob = model.predict_proba(X_val_scaled)[:, 1]
            else:
                model.fit(X_train, y_train)
                prob = model.predict_proba(X_val)[:, 1]
            
            # Apply primary threshold
            high_conf_mask = (prob > self.primary_threshold) | (prob < (1 - self.primary_threshold))
            
            if high_conf_mask.sum() > 0:
                pred = (prob[high_conf_mask] > 0.5).astype(int)
                accuracy = accuracy_score(y_val[high_conf_mask], pred)
                frequency = high_conf_mask.sum() / len(y_val) * 100
                
                primary_results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'frequency': frequency,
                    'predictions': prob
                }
                
                print(f"  {name}: {accuracy:.4f} accuracy, {frequency:.1f}% frequency")
        
        print("\n🎯 Training Secondary Models (Accuracy-Optimized):")
        secondary_results = {}
        for name, model in secondary_models.items():
            if 'lr' in name or 'svm' in name:
                model.fit(X_train_scaled, y_train)
                prob = model.predict_proba(X_val_scaled)[:, 1]
            else:
                model.fit(X_train, y_train)
                prob = model.predict_proba(X_val)[:, 1]
            
            # Apply secondary threshold
            high_conf_mask = (prob > self.secondary_threshold) | (prob < (1 - self.secondary_threshold))
            
            if high_conf_mask.sum() > 0:
                pred = (prob[high_conf_mask] > 0.5).astype(int)
                accuracy = accuracy_score(y_val[high_conf_mask], pred)
                frequency = high_conf_mask.sum() / len(y_val) * 100
                
                secondary_results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'frequency': frequency,
                    'predictions': prob
                }
                
                print(f"  {name}: {accuracy:.4f} accuracy, {frequency:.1f}% frequency")
        
        # Create ensemble system
        print("\n🎯 Creating Ensemble System:")
        
        # Select best models from each tier
        best_primary = max(primary_results.items(), key=lambda x: x[1]['accuracy'] * (x[1]['frequency']/100))
        best_secondary = max(secondary_results.items(), key=lambda x: x[1]['accuracy'])
        
        print(f"Best Primary: {best_primary[0]} ({best_primary[1]['accuracy']:.4f} acc, {best_primary[1]['frequency']:.1f}% freq)")
        print(f"Best Secondary: {best_secondary[0]} ({best_secondary[1]['accuracy']:.4f} acc, {best_secondary[1]['frequency']:.1f}% freq)")
        
        # Store models
        self.primary_models['best'] = best_primary[1]['model']
        self.secondary_models['best'] = best_secondary[1]['model']
        self.scalers['scaler'] = scaler
        
        return {
            'primary_results': primary_results,
            'secondary_results': secondary_results,
            'best_primary': best_primary,
            'best_secondary': best_secondary,
            'scaler': scaler
        }
    
    def predict_dual_tier(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Make dual-tier predictions combining frequency and accuracy"""
        
        # Get predictions from both tiers
        primary_model = self.primary_models['best']
        secondary_model = self.secondary_models['best']
        scaler = self.scalers['scaler']
        
        # Primary model predictions (frequency-focused)
        if type(primary_model).__name__ in ['LogisticRegression', 'SVC']:
            X_scaled = scaler.transform(X)
            primary_prob = primary_model.predict_proba(X_scaled)[:, 1]
        else:
            primary_prob = primary_model.predict_proba(X)[:, 1]
        
        # Secondary model predictions (accuracy-focused)
        if type(secondary_model).__name__ in ['LogisticRegression', 'SVC']:
            X_scaled = scaler.transform(X)
            secondary_prob = secondary_model.predict_proba(X_scaled)[:, 1]
        else:
            secondary_prob = secondary_model.predict_proba(X)[:, 1]
        
        # Ensemble logic: Use secondary model when both agree, primary otherwise
        ensemble_prob = np.where(
            (primary_prob > 0.5) == (secondary_prob > 0.5),  # Agreement
            (primary_prob + secondary_prob) / 2,  # Average when agree
            primary_prob  # Use primary when disagree (for frequency)
        )
        
        # Apply ensemble threshold
        high_conf_mask = (ensemble_prob > self.ensemble_threshold) | (ensemble_prob < (1 - self.ensemble_threshold))
        
        return (ensemble_prob > 0.5).astype(int), high_conf_mask

def fetch_binance_data(symbol: str, interval: str, limit: int = 1500) -> pd.DataFrame:
    """Fetch historical data from Binance"""
    try:
        endpoint = '/fapi/v1/klines'
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': min(limit, 1500)
        }
        
        response = requests.get(BASE_URL + endpoint, params=params)
        data = response.json()
        
        if isinstance(data, list) and len(data) > 0:
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            return df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
        else:
            print(f"Error fetching data: {data}")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"Error fetching Binance data: {e}")
        return pd.DataFrame()

def test_balanced_90x90_system():
    """Test the balanced 90%+ accuracy with 90%+ selectivity system"""
    print("=" * 80)
    print("⚖️  TESTING BALANCED 90% ACCURACY + 90% SELECTIVITY ML SYSTEM")
    print("Target: 90%+ accuracy AND 90%+ signal selectivity")
    print("=" * 80)
    
    # Fetch data
    print("📊 Fetching BTC/USDT 1H data...")
    df = fetch_binance_data('BTCUSDT', '1h', 1500)
    
    if df.empty:
        print("❌ Failed to fetch data")
        return 0.0, 0.0
    
    print(f"✅ Fetched {len(df)} data points")
    
    # Initialize balanced system
    balanced_system = Balanced90x90Predictor()
    
    # Train dual-tier model
    print("\n🎯 Training dual-tier model system...")
    results = balanced_system.train_dual_tier_model(df)
    
    # Test performance
    print("\n🏆 Testing balanced performance...")
    
    # Re-create features for testing
    features_df = balanced_system.create_optimized_features(df)
    
    # Create target (same as training)
    future_return = features_df['close'].shift(-1) / features_df['close'] - 1
    target = (future_return > 0.002).astype(int)
    
    # Clean data
    valid_idx = ~(features_df.isnull().any(axis=1) | target.isnull())
    features_df = features_df[valid_idx]
    target = target[valid_idx]
    
    # Select features
    numeric_features = features_df.select_dtypes(include=[np.number]).columns
    numeric_features = [col for col in numeric_features if col not in ['close', 'open']]
    X = features_df[numeric_features].replace([np.inf, -np.inf], np.nan).fillna(0)
    X_selected = balanced_system.feature_selector.transform(X)
    
    # Use last 20% as test data
    split_idx = int(0.8 * len(X_selected))
    X_test, y_test = X_selected[split_idx:], target[split_idx:]
    
    if len(X_test) > 0:
        pred, high_conf_mask = balanced_system.predict_dual_tier(X_test)
        
        if high_conf_mask.sum() > 0:
            final_accuracy = accuracy_score(y_test[high_conf_mask], pred[high_conf_mask])
            signal_selectivity = (high_conf_mask.sum() / len(X_test)) * 100
            
            print(f"\n⚖️  BALANCED RESULTS:")
            print(f"📈 Accuracy: {final_accuracy:.4f} ({final_accuracy*100:.2f}%)")
            print(f"📊 Signal Selectivity: {signal_selectivity:.1f}% ({high_conf_mask.sum()}/{len(X_test)})")
            print(f"🎲 Total signals generated: {high_conf_mask.sum()}")
            
            # Check if we achieved both targets
            accuracy_target = final_accuracy >= 0.90
            frequency_target = signal_selectivity >= 90.0
            
            if accuracy_target and frequency_target:
                print(f"\n🎉 PERFECT BALANCE ACHIEVED!")
                print(f"✅ Accuracy target: {final_accuracy*100:.2f}% (≥90%)")
                print(f"✅ Frequency target: {signal_selectivity:.1f}% (≥90%)")
                print("🚀 Ready for balanced production deployment!")
            elif accuracy_target:
                print(f"\n✅ Accuracy target achieved: {final_accuracy*100:.2f}% (≥90%)")
                print(f"⚠️  Frequency target missed: {signal_selectivity:.1f}% (<90%)")
                print("💡 Consider lowering ensemble threshold for more signals")
            elif frequency_target:
                print(f"\n✅ Frequency target achieved: {signal_selectivity:.1f}% (≥90%)")
                print(f"⚠️  Accuracy target missed: {final_accuracy*100:.2f}% (<90%)")
                print("💡 Consider raising ensemble threshold for better accuracy")
            else:
                print(f"\n⚠️  Both targets missed:")
                print(f"   Accuracy: {final_accuracy*100:.2f}% (<90%)")
                print(f"   Frequency: {signal_selectivity:.1f}% (<90%)")
                print("💡 Need to optimize threshold balance")
            
            return final_accuracy, signal_selectivity
        else:
            print("❌ No high-confidence predictions generated")
            return 0.0, 0.0
    else:
        print("❌ No test data available")
        return 0.0, 0.0

if __name__ == "__main__":
    accuracy, selectivity = test_balanced_90x90_system()
    print(f"\n🏁 Final Balanced Results: {accuracy*100:.2f}% accuracy, {selectivity:.1f}% selectivity")
