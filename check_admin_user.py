#!/usr/bin/env python3
"""
Check admin user credentials and test the 2FA endpoint
"""
import requests
import json

def test_admin_login():
    """Test different admin credentials"""
    BASE_URL = 'http://127.0.0.1:5000'
    
    # Try different credential combinations
    credentials = [
        ('admin', 'admin123'),
        ('admin', '12345'),
        ('test_admin', 'admin123'),
        ('<EMAIL>', 'admin123'),
        ('limited_admin', 'limited123')
    ]
    
    for username, password in credentials:
        print(f"\n🔐 Testing: {username} / {password}")
        
        try:
            response = requests.post(f'{BASE_URL}/api/admin/login', json={
                'username': username,
                'password': password
            })
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                token = data.get('access_token')
                print(f"✅ Login successful! Token: {token[:50]}...")
                
                # Test the 2FA endpoint
                print("\n🔍 Testing 2FA reset endpoint...")
                request_id = '2fedf66a-16c4-497c-80f5-38b40f84a333'
                
                endpoint_response = requests.get(
                    f'{BASE_URL}/api/admin/2fa-reset-requests/{request_id}',
                    headers={'Authorization': f'Bearer {token}'}
                )
                
                print(f"2FA Endpoint Status: {endpoint_response.status_code}")
                if endpoint_response.status_code == 200:
                    print("✅ 2FA endpoint working!")
                    data = endpoint_response.json()
                    print(f"Response keys: {list(data.keys())}")
                else:
                    print(f"❌ 2FA endpoint failed: {endpoint_response.text}")
                
                return True
                
            else:
                data = response.json()
                print(f"❌ Login failed: {data.get('message', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return False

if __name__ == "__main__":
    print("🚀 Testing admin credentials...")
    success = test_admin_login()
    
    if not success:
        print("\n❌ No valid admin credentials found!")
        print("You may need to create an admin user or check the database.")
