#!/usr/bin/env python3
import sqlite3

# Connect to database
conn = sqlite3.connect('backend/instance/deeptrade.db')
cursor = conn.cursor()

# Get all tables
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print("All tables:", [t[0] for t in tables])

# Check specifically for twofa_reset_requests
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='twofa_reset_requests'")
result = cursor.fetchone()
print("twofa_reset_requests exists:", result is not None)

# Check for password_reset_tokens
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='password_reset_tokens'")
result = cursor.fetchone()
print("password_reset_tokens exists:", result is not None)

conn.close()
