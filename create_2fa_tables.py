#!/usr/bin/env python3
"""
Direct table creation script for 2FA reset functionality
This bypasses the configuration issues and creates tables directly
"""

import sys
import os

# Add backend directory to path
backend_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
sys.path.append(backend_dir)

# Set environment to use SQLite for this script
os.environ['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///instance/deeptrade.db'

from app import create_app, db
from app.models.password_reset import PasswordResetToken, TwoFAResetRequest

def create_tables_directly():
    """Create tables directly using SQLite"""
    
    # Override the database URI to use SQLite
    app = create_app()
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///instance/deeptrade.db'
    
    with app.app_context():
        try:
            print("🔄 Creating 2FA reset tables directly...")
            print(f"Database URI: {app.config['SQLALCHEMY_DATABASE_URI']}")

            # Import models to ensure they're registered
            from app.models.password_reset import PasswordResetToken, TwoFAResetRequest
            
            # Create only the specific tables we need
            PasswordResetToken.__table__.create(db.engine, checkfirst=True)
            TwoFAResetRequest.__table__.create(db.engine, checkfirst=True)
            
            print("✅ Successfully created tables:")
            print("   - password_reset_tokens")
            print("   - twofa_reset_requests")
            
            # Verify tables were created
            import sqlite3
            # Use the same path as Flask app (relative to backend directory)
            db_path = os.path.join(backend_dir, 'instance', 'deeptrade.db')
            print(f"Checking database at: {db_path}")
            print(f"Database exists: {os.path.exists(db_path)}")
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='twofa_reset_requests'")
            result = cursor.fetchone()
            print(f"✅ twofa_reset_requests table exists: {result is not None}")
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='password_reset_tokens'")
            result = cursor.fetchone()
            print(f"✅ password_reset_tokens table exists: {result is not None}")
            
            if result:
                # Show table schema
                cursor.execute("PRAGMA table_info(twofa_reset_requests)")
                schema = cursor.fetchall()
                print("\n📋 twofa_reset_requests table schema:")
                for col in schema:
                    print(f"   - {col[1]} ({col[2]})")
            
            conn.close()
            
            print("\n🎉 Table creation completed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Error creating tables: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = create_tables_directly()
    if success:
        print("\n✅ You can now test the 2FA reset functionality!")
    else:
        print("\n❌ Table creation failed!")
        sys.exit(1)
