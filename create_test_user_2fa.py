#!/usr/bin/env python3
"""
Create a test user with 2FA enabled for testing the 2FA reset functionality
"""

import sys
import os

# Add backend directory to path
backend_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
sys.path.append(backend_dir)

from app import create_app, db
from app.models.user import User

def create_test_user():
    """Create a test user with 2FA enabled"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 Creating test user with 2FA enabled...")
            
            # Check if user already exists
            existing_user = User.query.filter_by(email='<EMAIL>').first()
            if existing_user:
                print("✅ Test user already exists")
                print(f"   Email: {existing_user.email}")
                print(f"   2FA Enabled: {existing_user.two_fa_enabled}")
                
                # Enable 2FA if not already enabled
                if not existing_user.two_fa_enabled:
                    existing_user.enable_2fa('JBSWY3DPEHPK3PXP')  # Test secret
                    db.session.commit()
                    print("✅ 2FA enabled for existing user")
                
                return existing_user
            
            # Create new test user
            test_user = User(
                email='<EMAIL>',
                full_name='Test User 2FA',
                google_id='test_google_id_2fa'
            )

            db.session.add(test_user)
            db.session.commit()  # Commit first to get the ID

            # Enable 2FA after user is saved and has an ID
            test_user.enable_2fa('JBSWY3DPEHPK3PXP')  # Test secret
            db.session.commit()
            
            print("✅ Test user created successfully!")
            print(f"   ID: {test_user.id}")
            print(f"   Email: {test_user.email}")
            print(f"   Full Name: {test_user.full_name}")
            print(f"   2FA Enabled: {test_user.two_fa_enabled}")
            
            return test_user
            
        except Exception as e:
            print(f"❌ Error creating test user: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

if __name__ == "__main__":
    user = create_test_user()
    if user:
        print("\n✅ Test user ready for 2FA reset testing!")
        print("You can now test the 2FA reset functionality with:")
        print("  Email: <EMAIL>")
    else:
        print("\n❌ Failed to create test user!")
        sys.exit(1)
