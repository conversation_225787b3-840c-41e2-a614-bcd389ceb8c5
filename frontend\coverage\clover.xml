<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1752117164538" clover="3.2.0">
  <project timestamp="1752117164538" name="All files">
    <metrics statements="532" coveredstatements="0" conditionals="163" coveredconditionals="0" methods="158" coveredmethods="0" elements="853" coveredelements="0" complexity="0" loc="532" ncloc="532" packages="7" files="16" classes="16"/>
    <package name="src">
      <metrics statements="16" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <file name="config.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\config.ts">
        <metrics statements="3" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="2" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="5" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
      </file>
      <file name="setupTests.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\setupTests.ts">
        <metrics statements="13" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.__tests__">
      <metrics statements="47" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="15" coveredmethods="0"/>
      <file name="simple.test.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\__tests__\simple.test.ts">
        <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
      </file>
      <file name="smoke.test.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\__tests__\smoke.test.ts">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="trading.service.test.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\__tests__\trading.service.test.ts">
        <metrics statements="31" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.api">
      <metrics statements="88" coveredstatements="0" conditionals="25" coveredconditionals="0" methods="16" coveredmethods="0"/>
      <file name="auth.service.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\api\auth.service.ts">
        <metrics statements="62" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="65" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
      </file>
      <file name="trading.service.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\api\trading.service.ts">
        <metrics statements="26" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.ui">
      <metrics statements="53" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="18" coveredmethods="0"/>
      <file name="use-toast.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\components\ui\use-toast.ts">
        <metrics statements="53" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="79" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="181" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.lib">
      <metrics statements="30" coveredstatements="0" conditionals="18" coveredconditionals="0" methods="9" coveredmethods="0"/>
      <file name="utils.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\lib\utils.ts">
        <metrics statements="30" coveredstatements="0" conditionals="18" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="9" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="26" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.services">
      <metrics statements="126" coveredstatements="0" conditionals="73" coveredconditionals="0" methods="30" coveredmethods="0"/>
      <file name="api.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\services\api.ts">
        <metrics statements="19" coveredstatements="0" conditionals="15" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="2" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
      </file>
      <file name="market.service.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\services\market.service.ts">
        <metrics statements="13" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
      </file>
      <file name="nft.service.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\services\nft.service.ts">
        <metrics statements="37" coveredstatements="0" conditionals="28" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="84" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
      </file>
      <file name="realtime.service.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\services\realtime.service.ts">
        <metrics statements="57" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.services.__tests__">
      <metrics statements="172" coveredstatements="0" conditionals="15" coveredconditionals="0" methods="64" coveredmethods="0"/>
      <file name="api.clean.test.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\services\__tests__\api.clean.test.ts">
        <metrics statements="38" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="31" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
      </file>
      <file name="api.test.new.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\services\__tests__\api.test.new.ts">
        <metrics statements="53" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="35" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
      </file>
      <file name="api.test.ts" path="C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade2 - Copy\frontend\src\services\__tests__\api.test.ts">
        <metrics statements="81" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="32" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="30" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
