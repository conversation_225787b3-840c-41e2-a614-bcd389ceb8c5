{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "cross-env NODE_OPTIONS=--experimental-vm-modules jest --config=jest.config.mjs", "test:watch": "cross-env NODE_OPTIONS=--experimental-vm-modules jest --config=jest.config.mjs --watch", "test:coverage": "cross-env NODE_OPTIONS=--experimental-vm-modules jest --config=jest.config.mjs --coverage", "test:verbose": "cross-env NODE_OPTIONS=--experimental-vm-modules jest --config=jest.config.mjs --verbose", "test:esm": "node --experimental-vm-modules --no-warnings run-esm-tests.mjs"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@solana/spl-token": "^0.3.11", "@solana/wallet-adapter-base": "^0.9.27", "@solana/wallet-adapter-react": "^0.15.39", "@solana/wallet-adapter-react-ui": "^0.9.39", "@solana/wallet-adapter-wallets": "^0.19.37", "@solana/web3.js": "^1.98.2", "@tanstack/react-query": "^5.81.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.523.0", "next-themes": "^0.4.6", "plotly.js": "^3.0.1", "react-i18next": "^15.6.0", "react-plotly.js": "^2.6.0", "react-router-dom": "^7.6.2", "tailwind-merge": "^3.3.1", "ws": "^8.18.3"}, "devDependencies": {"@eslint/js": "^9.29.0", "@testing-library/jest-dom": "^6.6.3", "@types/jest": "^30.0.0", "@types/node": "^24.0.4", "@types/plotly.js": "^3.0.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-plotly.js": "^2.6.3", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "jest": "^30.0.3", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}