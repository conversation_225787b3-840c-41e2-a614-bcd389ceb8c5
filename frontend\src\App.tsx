import React from 'react';
import { Routes, Route, Outlet, Navigate } from 'react-router-dom';
import { Toaster } from './components/ui/toaster';
import { ThemeProvider } from './components/theme-provider';
import { SolanaWalletContext } from "./contexts/SolanaWalletProvider";
import { useAuth } from './contexts/AuthContext';

// Layouts
import DashboardLayout from './layouts/DashboardLayout';
import AuthLayout from './layouts/AuthLayout';

// Pages
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import OAuthCallback from './pages/OAuthCallback';
import Register from './pages/Register';
import SignUp from './pages/SignUp';
import Verify2FA from './pages/Verify2FA';
import VerifyEmail from './pages/VerifyEmail';
import VerifyEmailChange from './pages/VerifyEmailChange';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import TwoFAResetRequest from './pages/TwoFAResetRequest';
import Settings from './pages/Settings';
import AccessSecurity from './pages/AccessSecurity';
import ApiCredentials from './pages/ApiCredentials';
import TierManagement from './pages/tier';
import ReferralDashboard from './pages/referral';
import Help from './pages/Help';
import RedeemCouponPage from './pages/redeem-coupon';
import EmailVerificationRequired from './components/auth/EmailVerificationRequired';

// Protected route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading, user } = useAuth();

  if (loading) {
    return <div>Loading...</div>; // Or a loading spinner
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Check email verification for traditional users (skip for Google OAuth users)
  if (user && !user.email_verified && user.registration_type === 'traditional') {
    return <EmailVerificationRequired userEmail={user.email} />;
  }

  return <>{children}</>;
};

// Public route component
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>; // Or a loading spinner
  }

  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

const App: React.FC = () => {
  return (
      <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
        <SolanaWalletContext>
          <div className="min-h-screen bg-background">
            <Routes>
              {/* Public routes */}
              <Route
                path="/login"
                element={
                  <PublicRoute>
                    <AuthLayout>
                      <Login />
                    </AuthLayout>
                  </PublicRoute>
                }
              />
              <Route
                path="/register"
                element={
                  <PublicRoute>
                    <AuthLayout>
                      <Register />
                    </AuthLayout>
                  </PublicRoute>
                }
              />
              <Route
                path="/signup"
                element={
                  <PublicRoute>
                    <AuthLayout>
                      <SignUp />
                    </AuthLayout>
                  </PublicRoute>
                }
              />
              <Route
                path="/verify-2fa"
                element={
                  <PublicRoute>
                    <AuthLayout>
                      <Verify2FA />
                    </AuthLayout>
                  </PublicRoute>
                }
              />
              {/* Email verification route */}
              <Route
                path="/verify-email"
                element={
                  <PublicRoute>
                    <AuthLayout>
                      <VerifyEmail />
                    </AuthLayout>
                  </PublicRoute>
                }
              />
              {/* Email change verification route */}
              <Route
                path="/verify-email-change"
                element={
                  <PublicRoute>
                    <AuthLayout>
                      <VerifyEmailChange />
                    </AuthLayout>
                  </PublicRoute>
                }
              />
              {/* Password reset routes */}
              <Route
                path="/forgot-password"
                element={
                  <PublicRoute>
                    <AuthLayout>
                      <ForgotPassword />
                    </AuthLayout>
                  </PublicRoute>
                }
              />
              <Route
                path="/reset-password"
                element={
                  <PublicRoute>
                    <AuthLayout>
                      <ResetPassword />
                    </AuthLayout>
                  </PublicRoute>
                }
              />
              <Route
                path="/2fa-reset-request"
                element={
                  <PublicRoute>
                    <AuthLayout>
                      <TwoFAResetRequest />
                    </AuthLayout>
                  </PublicRoute>
                }
              />
              {/* OAuth callback route */}
              <Route
                path="/auth/callback"
                element={
                  <PublicRoute>
                    <OAuthCallback />
                  </PublicRoute>
                }
              />

              {/* Protected routes */}
              <Route
                path="/"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <Outlet />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              >
                <Route index element={<Dashboard />} />
                <Route path="settings" element={<Settings />} />
                <Route path="access-security" element={<AccessSecurity />} />
                <Route path="api-credentials" element={<ApiCredentials />} />
                <Route path="tier" element={<TierManagement />} />
                <Route path="redeem-coupon" element={<RedeemCouponPage />} />
                <Route path="referral-dashboard" element={<ReferralDashboard />} />
                <Route path="help" element={<Help />} />
              </Route>

              {/* Redirect /dashboard to / for backward compatibility */}
              <Route path="/dashboard" element={<Navigate to="/" replace />} />
  
              {/* 404 route */}
              <Route path="*" element={<div>404 - Page not found</div>} />
            </Routes>
            <Toaster />
          </div>
        </SolanaWalletContext>
      </ThemeProvider>
    );
};

export default App;
