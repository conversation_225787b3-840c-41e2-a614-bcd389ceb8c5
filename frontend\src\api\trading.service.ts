import axios from 'axios';

// Axios interceptor to attach access_token to every request if present
axios.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token') || sessionStorage.getItem('access_token');
  if (token) {
    // Create headers object if it doesn't exist
    if (!config.headers) {
      config.headers = axios.defaults.headers as any;
    }
    // Use bracket notation to avoid TypeScript errors
    (config.headers as any)['Authorization'] = `Bearer ${token}`;
  }
  // Debug log for Authorization header
  // eslint-disable-next-line no-console
  console.log("[Axios] Request to", config.url, "Authorization:", config.headers?.Authorization);
  // Debug log for Authorization header and URL
  // eslint-disable-next-line no-console
  console.log("[Axios DEBUG] Request to", config.url, "Authorization:", config.headers?.Authorization);
  return config;
});

// Dashboard stats (balance, pnl, monthly rate, win rate)
export async function getDashboardStats() {
  const res = await axios.get("/api/trading/dashboard_stats");
  return res.data;
}
// Fetch trading statistics (PnL, Monthly Rate, Win Rate)
export async function getTradingStatistics() {
  const res = await axios.get("/api/trading/statistics");
  return res.data.statistics;
}
// Fetch real balance from user's API credentials
export async function getRealBalance() {
  const res = await axios.get("/api/trading/real-balance");
  return res.data;
}

// Active open trading position
export async function getActivePosition() {
  const res = await axios.get("/api/trading/active_position");
  return res.data;
}

// Paginated trading history
export async function getTradingHistory(page = 1) {
  const res = await axios.get(`/api/trading/history?page=${page}`);
  return res.data.trades || [];
}

// Risk settings API calls
export async function getRiskSettings() {
  const res = await axios.get("/api/trading/risk-settings");
  return res.data;
}

export async function saveRiskSettings(settings: {
  investment_percentage: number;
  leverage: number;
  exchange: string;
  account_type: string;
}) {
  const res = await axios.post("/api/trading/risk-settings", settings);
  return res.data;
}

export default axios;