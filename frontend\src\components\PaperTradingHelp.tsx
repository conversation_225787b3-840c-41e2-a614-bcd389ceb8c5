/**
 * Paper Trading Help Component
 * 
 * Provides educational content, tooltips, and help information about paper trading
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';
import { 
  HelpCircle, 
  Info, 
  CheckCircle, 
  AlertTriangle, 
  BookOpen, 
  TrendingUp,
  Shield,
  Zap,
  X
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useMobile } from '../hooks/useResponsiveDesign';

interface PaperTradingHelpProps {
  isOpen: boolean;
  onClose: () => void;
}

export const PaperTradingHelp: React.FC<PaperTradingHelpProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const { isMobile } = useMobile();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-xl ${isMobile ? 'w-full max-w-sm max-h-[90vh]' : 'w-full max-w-2xl max-h-[80vh]'} overflow-y-auto`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className={`${isMobile ? 'text-lg' : 'text-xl'} font-semibold flex items-center`}>
            <BookOpen className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} mr-2 text-orange-600`} />
            {t('paperTradingHelp.title')}
          </h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-4 space-y-6">
          {/* What is Paper Trading */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'} flex items-center text-blue-600 dark:text-blue-400`}>
                <Info className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} mr-2`} />
                {t('paperTradingHelp.whatIsPaperTrading')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className={`${isMobile ? 'text-sm' : 'text-base'} text-gray-700 dark:text-gray-300`}>
                {t('paperTradingHelp.whatIsPaperTradingDesc')}
              </p>
              <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-blue-800 dark:text-blue-200 font-medium`}>
                  {t('paperTradingHelp.perfectForBeginners')}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Benefits */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'} flex items-center text-green-600 dark:text-green-400`}>
                <CheckCircle className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} mr-2`} />
                {t('paperTradingHelp.benefits.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <Shield className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className={`${isMobile ? 'text-sm' : 'text-base'} font-medium`}>{t('paperTradingHelp.benefits.riskFreeLearning')}</h4>
                    <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600 dark:text-gray-400`}>
                      {t('paperTradingHelp.benefits.riskFreeLearningDesc')}
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <TrendingUp className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className={`${isMobile ? 'text-sm' : 'text-base'} font-medium`}>{t('paperTradingHelp.benefits.strategyTesting')}</h4>
                    <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600 dark:text-gray-400`}>
                      {t('paperTradingHelp.benefits.strategyTestingDesc')}
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Zap className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className={`${isMobile ? 'text-sm' : 'text-base'} font-medium`}>{t('paperTradingHelp.benefits.realMarketData')}</h4>
                    <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600 dark:text-gray-400`}>
                      {t('paperTradingHelp.benefits.realMarketDataDesc')}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* How It Works */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'} flex items-center text-purple-600 dark:text-purple-400`}>
                <HelpCircle className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} mr-2`} />
                {t('paperTradingHelp.howItWorksModal.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-xs font-bold text-purple-600 dark:text-purple-400">1</span>
                  </div>
                  <p className={`${isMobile ? 'text-sm' : 'text-base'}`}>
                    {t('paperTradingHelp.howItWorksModal.step1')}
                  </p>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-xs font-bold text-purple-600 dark:text-purple-400">2</span>
                  </div>
                  <p className={`${isMobile ? 'text-sm' : 'text-base'}`}>
                    {t('paperTradingHelp.howItWorksModal.step2')}
                  </p>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-xs font-bold text-purple-600 dark:text-purple-400">3</span>
                  </div>
                  <p className={`${isMobile ? 'text-sm' : 'text-base'}`}>
                    {t('paperTradingHelp.howItWorksModal.step3')}
                  </p>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-xs font-bold text-purple-600 dark:text-purple-400">4</span>
                  </div>
                  <p className={`${isMobile ? 'text-sm' : 'text-base'}`}>
                    {t('paperTradingHelp.howItWorksModal.step4')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Important Notes */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'} flex items-center text-amber-600 dark:text-amber-400`}>
                <AlertTriangle className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} mr-2`} />
                {t('paperTradingHelp.importantNotes')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
                  <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-amber-800 dark:text-amber-200`}>
                    <strong>{t('paperTradingHelp.importantNotesDetails.noRealMoneyTitle')}</strong> {t('paperTradingHelp.importantNotesDetails.noRealMoneyDesc')}
                  </p>
                </div>
                <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
                  <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-amber-800 dark:text-amber-200`}>
                    <strong>{t('paperTradingHelp.importantNotesDetails.noFeesTitle')}</strong> {t('paperTradingHelp.importantNotesDetails.noFeesDesc')}
                  </p>
                </div>
                <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
                  <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-amber-800 dark:text-amber-200`}>
                    <strong>{t('paperTradingHelp.importantNotesDetails.resetLimitTitle')}</strong> {t('paperTradingHelp.importantNotesDetails.resetLimitDesc')}
                  </p>
                </div>
                <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
                  <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-amber-800 dark:text-amber-200`}>
                    <strong>{t('paperTradingHelp.availableToAllTitle')}</strong> {t('paperTradingHelp.availableToAllDesc')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Getting Started */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'} text-gray-800 dark:text-gray-200`}>
                {t('paperTradingHelp.gettingStarted.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <p className={`${isMobile ? 'text-sm' : 'text-base'} text-gray-700 dark:text-gray-300`}>
                  {t('paperTradingHelp.gettingStarted.description')}
                </p>
                <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                  <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-green-800 dark:text-green-200 font-medium`}>
                    {t('paperTradingHelp.gettingStarted.buildConfidenceFooter')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <Button onClick={onClose} className="w-full">
            {t('paperTradingHelp.gotItThanks')}
          </Button>
        </div>
      </div>
    </div>
  );
};

// Tooltip component for inline help
export const PaperTradingTooltip: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div className="relative inline-block">
      <button
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        onClick={() => setIsVisible(!isVisible)}
        className="text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300"
      >
        <HelpCircle className="w-4 h-4" />
      </button>
      {isVisible && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg shadow-lg z-10 w-64">
          {children}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
        </div>
      )}
    </div>
  );
};

export default PaperTradingHelp;
