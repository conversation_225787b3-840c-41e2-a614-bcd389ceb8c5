import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';

interface EmailVerificationRequiredProps {
  userEmail: string;
}

export default function EmailVerificationRequired({ userEmail }: EmailVerificationRequiredProps) {
  const [isResending, setIsResending] = useState(false);
  const { resendVerification, logout } = useAuth();

  const handleResendVerification = async () => {
    setIsResending(true);
    try {
      await resendVerification(userEmail);
      toast({
        title: 'Verification Email Sent',
        description: 'Please check your email for the verification link.',
      });
    } catch (error: any) {
      console.error('Resend verification failed:', error);
      toast({
        title: 'Failed to resend email',
        description: error.message || 'Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setIsResending(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
        <div className="flex flex-col space-y-2 text-center">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
            <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
            </svg>
          </div>
          <h1 className="text-2xl font-semibold tracking-tight">Email Verification Required</h1>
          <p className="text-sm text-muted-foreground">
            Please verify your email address to access DeepTrade
          </p>
        </div>
        
        <div className="grid gap-6">
          <div className="grid gap-4">
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-2">Verification Required</h3>
              <p className="text-sm text-yellow-800 mb-3">
                We've sent a verification email to <strong>{userEmail}</strong>. 
                Please check your inbox and click the verification link to access your account.
              </p>
              <div className="text-sm text-yellow-800">
                <p className="mb-1">• Check your spam/junk folder if you don't see the email</p>
                <p>• The verification link expires in 24 hours</p>
              </div>
            </div>
            
            <Button
              type="button"
              disabled={isResending}
              onClick={handleResendVerification}
              className="w-full"
            >
              {isResending ? 'Sending...' : 'Resend Verification Email'}
            </Button>
            
            <Button
              type="button"
              variant="outline"
              onClick={handleLogout}
              className="w-full"
            >
              Sign Out
            </Button>
          </div>
          
          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              Having trouble? Contact support for assistance.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
