/**
 * Mobile API Credentials Component for DeepTrade
 * 
 * Provides mobile-optimized API credentials management with touch-friendly
 * interactions, secure credential handling, and responsive design.
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faKey,
  faPlus,
  faEdit,
  faTrash,
  faEye,
  faEyeSlash,
  faSave,
  faTimes,
  faExclamationTriangle,
  faCheckCircle
} from '@fortawesome/free-solid-svg-icons';
import { useMobile } from '../../hooks/useResponsiveDesign';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from '../../hooks/useTranslation';
import { toastSuccess, toastError } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import MobileTopBar from './MobileTopBar';

interface ApiCredential {
  id: string;
  exchange: string;
  api_key: string;
  api_secret: string;
  passphrase?: string;
  is_active: boolean;
  is_testnet: boolean;
  created_at: string;
  updated_at: string;
}

interface MobileApiCredentialsProps {
  // Props are optional as we'll fetch real data
}

const MobileApiCredentials: React.FC<MobileApiCredentialsProps> = () => {
  const { isMobile } = useMobile();
  const { user } = useAuth();
  const { t } = useTranslation();
  const [credentials, setCredentials] = useState<ApiCredential[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [showSecrets, setShowSecrets] = useState<{ [key: string]: boolean }>({});
  const [formData, setFormData] = useState({
    exchange: '',
    api_key: '',
    api_secret: '',
    passphrase: '',
    is_testnet: false
  });

  if (!isMobile) return null;

  // Fetch API credentials
  const fetchCredentials = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/trading/credentials', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        setCredentials(data.credentials || []);
      } else {
        throw new Error('Failed to fetch credentials');
      }
    } catch (error) {
      console.error('Error fetching credentials:', error);
      toastError({
        title: 'Error',
        description: 'Failed to load API credentials',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchCredentials();
    }
  }, [user]);

  const handleSave = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const url = editingId 
        ? `/api/trading/credentials/${editingId}`
        : '/api/trading/credentials';
      const method = editingId ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        toastSuccess({
          title: 'Success',
          description: editingId ? 'Credentials updated successfully' : 'Credentials added successfully',
        });
        setShowAddForm(false);
        setEditingId(null);
        setFormData({
          exchange: '',
          api_key: '',
          api_secret: '',
          passphrase: '',
          is_testnet: false
        });
        fetchCredentials();
      } else {
        throw new Error('Failed to save credentials');
      }
    } catch (error) {
      toastError({
        title: 'Error',
        description: 'Failed to save credentials',
      });
    }
  };

  const handleEdit = (credential: ApiCredential) => {
    setFormData({
      exchange: credential.exchange,
      api_key: credential.api_key,
      api_secret: credential.api_secret,
      passphrase: credential.passphrase || '',
      is_testnet: credential.is_testnet
    });
    setEditingId(credential.id);
    setShowAddForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete these credentials?')) return;

    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`/api/trading/credentials/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        toastSuccess({
          title: 'Success',
          description: 'Credentials deleted successfully',
        });
        fetchCredentials();
      } else {
        throw new Error('Failed to delete credentials');
      }
    } catch (error) {
      toastError({
        title: 'Error',
        description: 'Failed to delete credentials',
      });
    }
  };

  const toggleSecretVisibility = (id: string) => {
    setShowSecrets(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const handleCancel = () => {
    setShowAddForm(false);
    setEditingId(null);
    setFormData({
      exchange: '',
      api_key: '',
      api_secret: '',
      passphrase: '',
      is_testnet: false
    });
  };

  const exchanges = [
    { value: 'binance', label: 'Binance', type: 'Futures' },
    { value: 'binance_us', label: 'Binance US', type: 'Spot' },
    { value: 'kraken', label: 'Kraken', type: 'Futures' },
    { value: 'bybit', label: 'Bybit', type: 'Futures' },
    { value: 'hyperliquid', label: 'Hyperliquid', type: 'Futures' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <MobileTopBar />
      
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
              <FontAwesomeIcon icon={faKey} className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
              API Credentials
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Manage your exchange API credentials
            </p>
          </div>
          <Button
            onClick={() => setShowAddForm(true)}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <FontAwesomeIcon icon={faPlus} className="w-4 h-4 mr-1" />
            Add
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="p-3 pb-20">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {/* Add/Edit Form */}
            {showAddForm && (
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700 mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {editingId ? 'Edit Credentials' : 'Add New Credentials'}
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="exchange">Exchange</Label>
                    <select
                      id="exchange"
                      value={formData.exchange}
                      onChange={(e) => setFormData(prev => ({ ...prev, exchange: e.target.value }))}
                      className="w-full mt-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      required
                    >
                      <option value="">Select Exchange</option>
                      {exchanges.map(exchange => (
                        <option key={exchange.value} value={exchange.value}>
                          {exchange.label} ({exchange.type})
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="api_key">API Key</Label>
                    <Input
                      id="api_key"
                      type="text"
                      value={formData.api_key}
                      onChange={(e) => setFormData(prev => ({ ...prev, api_key: e.target.value }))}
                      placeholder="Enter your API key"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="api_secret">API Secret</Label>
                    <Input
                      id="api_secret"
                      type="password"
                      value={formData.api_secret}
                      onChange={(e) => setFormData(prev => ({ ...prev, api_secret: e.target.value }))}
                      placeholder="Enter your API secret"
                      required
                    />
                  </div>

                  {(formData.exchange === 'kraken') && (
                    <div>
                      <Label htmlFor="passphrase">Passphrase</Label>
                      <Input
                        id="passphrase"
                        type="password"
                        value={formData.passphrase}
                        onChange={(e) => setFormData(prev => ({ ...prev, passphrase: e.target.value }))}
                        placeholder="Enter passphrase (if required)"
                      />
                    </div>
                  )}

                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="is_testnet"
                      checked={formData.is_testnet}
                      onChange={(e) => setFormData(prev => ({ ...prev, is_testnet: e.target.checked }))}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <Label htmlFor="is_testnet" className="text-sm">
                      Use Testnet/Sandbox
                    </Label>
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      onClick={handleSave}
                      className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                    >
                      <FontAwesomeIcon icon={faSave} className="w-4 h-4 mr-2" />
                      Save
                    </Button>
                    <Button
                      onClick={handleCancel}
                      variant="outline"
                      className="flex-1"
                    >
                      <FontAwesomeIcon icon={faTimes} className="w-4 h-4 mr-2" />
                      Cancel
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Credentials List */}
            {credentials.length === 0 ? (
              <div className="text-center py-12">
                <FontAwesomeIcon icon={faKey} className="w-12 h-12 text-gray-400 dark:text-gray-500 mb-3" />
                <p className="text-gray-500 dark:text-gray-400 mb-3">No API credentials configured</p>
                <p className="text-sm text-gray-400 dark:text-gray-500">Add your exchange credentials to start trading</p>
              </div>
            ) : (
              <div className="space-y-3">
                {credentials.map((credential) => (
                  <div key={credential.id} className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white capitalize">
                          {credential.exchange.replace('_', ' ')}
                        </h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            credential.is_active 
                              ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400'
                              : 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400'
                          }`}>
                            {credential.is_active ? 'Active' : 'Inactive'}
                          </span>
                          {credential.is_testnet && (
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400">
                              Testnet
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEdit(credential)}
                          className="p-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded"
                        >
                          <FontAwesomeIcon icon={faEdit} className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(credential.id)}
                          className="p-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                        >
                          <FontAwesomeIcon icon={faTrash} className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">API Key:</span>
                        <div className="flex items-center space-x-2 mt-1">
                          <code className="flex-1 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
                            {showSecrets[credential.id] 
                              ? credential.api_key 
                              : '••••••••••••••••••••••••••••••••'
                            }
                          </code>
                          <button
                            onClick={() => toggleSecretVisibility(credential.id)}
                            className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                          >
                            <FontAwesomeIcon 
                              icon={showSecrets[credential.id] ? faEyeSlash : faEye} 
                              className="w-4 h-4" 
                            />
                          </button>
                        </div>
                      </div>
                      
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Created: {new Date(credential.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default MobileApiCredentials;
