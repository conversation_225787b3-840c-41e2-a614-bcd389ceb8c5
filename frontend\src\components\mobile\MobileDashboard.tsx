/**
 * Mobile Dashboard Component for DeepTrade
 *
 * Provides mobile-optimized dashboard layout with touch-friendly
 * interactions, swipeable cards, and responsive metrics display.
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChartLine,
  faDollarSign,
  faArrowTrendUp,
  faBriefcase,
  faStar,
  faGift,
  faCrown
} from '@fortawesome/free-solid-svg-icons';
import { useMobile } from '../../hooks/useResponsiveDesign';
import { useAuth } from '../../contexts/AuthContext';
import { usePaperTrading } from '../../hooks/usePaperTrading';

interface MobileDashboardProps {
  // Props are now optional as we'll fetch real data
}

const MobileDashboard: React.FC<MobileDashboardProps> = () => {
  const { isMobile } = useMobile();
  const { user } = useAuth();
  const { isPaperMode, balance: paperBalance } = usePaperTrading();
  const [activeTab, setActiveTab] = useState<'overview' | 'trading' | 'positions'>('overview');

  // Real data states
  const [balance, setBalance] = useState<string>('$1,250.75');
  const [stats, setStats] = useState<any>({
    total_pnl: 5.2,
    monthly_rate: 12.8,
    win_rate: 68.5,
    active_signals: 3,
    total_trades: 47
  });
  const [tierStatus, setTierStatus] = useState<any>({ tier_2: true });
  const [loading, setLoading] = useState(false);

  // Data fetching function
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('access_token');
      
      // Fetch balance
      const balanceResponse = await fetch('/api/trading/balance', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (balanceResponse.ok) {
        const balanceData = await balanceResponse.json();
        setBalance(balanceData?.balance ? `$${Number(balanceData.balance).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 8})}` : "$1,250.75");
      }

      // Fetch trading statistics
      const statsResponse = await fetch('/api/trading/statistics', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }

      // Fetch tier status
      const tierResponse = await fetch('/api/trading/tier/status', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (tierResponse.ok) {
        const tierData = await tierResponse.json();
        setTierStatus(tierData);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user]);

  if (!isMobile) return null;

  const tabs = [
    { id: 'overview', label: 'Overview', icon: faChartLine },
    { id: 'trading', label: 'Trading', icon: faArrowTrendUp },
    { id: 'positions', label: 'Positions', icon: faBriefcase },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 overflow-x-hidden w-full max-w-full flex flex-col">
      {/* Tab Navigation - Redesigned for mobile */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-20 w-full overflow-x-hidden flex-shrink-0 max-w-full">
        <div className="flex h-14 w-full max-w-full">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 flex flex-col items-center justify-center py-2 px-0.5 text-xs font-medium transition-colors min-h-[56px] min-w-0 max-w-full ${
                activeTab === tab.id
                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 bg-blue-50/50 dark:bg-blue-900/20'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50'
              }`}
            >
              <FontAwesomeIcon
                icon={tab.icon}
                className={`w-3.5 h-3.5 flex-shrink-0 mb-1 ${activeTab === tab.id ? 'opacity-100' : 'opacity-70'}`}
              />
              <span className="text-xs font-medium leading-tight text-center truncate max-w-full">
                {tab.label}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-2 w-full overflow-x-hidden max-w-full box-border">
        {/* Paper Trading Mode Banner */}
        {isPaperMode && (
          <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-3 mb-3">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse flex-shrink-0"></div>
              <div className="flex-1 min-w-0">
                <h3 className="text-xs font-medium text-orange-800 dark:text-orange-200">
                  Paper Trading Active
                </h3>
                <p className="text-xs text-orange-600 dark:text-orange-300 truncate">
                  Virtual Balance: ${(paperBalance || 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {activeTab === 'overview' && (
              <div className="space-y-3 w-full overflow-x-hidden max-w-full box-border">
                {/* Quick Stats */}
                <div className="grid grid-cols-2 gap-2 w-full max-w-full">
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700 min-w-0 overflow-hidden max-w-full box-border">
                    <div className="flex items-center space-x-1.5 mb-1.5">
                      <FontAwesomeIcon
                        icon={faDollarSign}
                        className={`w-3.5 h-3.5 flex-shrink-0 ${isPaperMode ? 'text-orange-600 dark:text-orange-400' : 'text-green-600 dark:text-green-400'}`}
                      />
                      <h3 className="text-xs font-medium text-gray-600 dark:text-gray-400 truncate">
                        {isPaperMode ? 'Virtual Balance' : 'Balance'}
                      </h3>
                    </div>
                    <p className={`text-sm font-bold truncate ${isPaperMode ? 'text-orange-600 dark:text-orange-400' : 'text-gray-900 dark:text-white'}`}>
                      {isPaperMode
                        ? `$${(paperBalance || 0).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`
                        : balance
                      }
                    </p>
                    {isPaperMode && (
                      <p className="text-xs text-orange-500 dark:text-orange-400 mt-0.5">Paper</p>
                    )}
                  </div>
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700 min-w-0 overflow-hidden max-w-full box-border">
                    <div className="flex items-center space-x-1.5 mb-1.5">
                      <FontAwesomeIcon icon={faArrowTrendUp} className="w-3.5 h-3.5 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                      <h3 className="text-xs font-medium text-gray-600 dark:text-gray-400 truncate">P&L Today</h3>
                    </div>
                    <p className={`text-sm font-bold truncate ${stats.total_pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {stats.total_pnl >= 0 ? '+' : ''}{stats.total_pnl}%
                    </p>
                  </div>
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700 min-w-0 overflow-hidden max-w-full box-border">
                    <div className="flex items-center space-x-1.5 mb-1.5">
                      <FontAwesomeIcon icon={faChartLine} className="w-3.5 h-3.5 text-purple-600 dark:text-purple-400 flex-shrink-0" />
                      <h3 className="text-xs font-medium text-gray-600 dark:text-gray-400 truncate">Win Rate</h3>
                    </div>
                    <p className="text-sm font-bold text-purple-600 dark:text-purple-400 truncate">{stats.win_rate}%</p>
                  </div>
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700 min-w-0 overflow-hidden max-w-full box-border">
                    <div className="flex items-center space-x-1.5 mb-1.5">
                      <FontAwesomeIcon icon={faChartLine} className="w-3.5 h-3.5 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                      <h3 className="text-xs font-medium text-gray-600 dark:text-gray-400 truncate">Active Signals</h3>
                    </div>
                    <p className="text-sm font-bold text-blue-600 dark:text-blue-400 truncate">{stats.active_signals}</p>
                  </div>
                </div>

                {/* Tier Status */}
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800 w-full overflow-hidden max-w-full box-border">
                  <div className="flex items-center justify-between mb-1.5">
                    <h3 className="text-sm font-semibold text-gray-900 dark:text-white truncate">Current Tier</h3>
                    <div className="flex items-center justify-center w-6 h-6 flex-shrink-0">
                      <FontAwesomeIcon
                        icon={tierStatus?.tier_3 ? faCrown : tierStatus?.tier_2 ? faStar : faGift}
                        className={`w-4 h-4 ${
                          tierStatus?.tier_3 ? 'text-yellow-500' :
                          tierStatus?.tier_2 ? 'text-blue-500' :
                          'text-gray-500'
                        }`}
                      />
                    </div>
                  </div>
                  <p className="text-base font-bold text-blue-600 dark:text-blue-400 mb-1 truncate">
                    {tierStatus?.tier_3 ? 'Premium Tier' : tierStatus?.tier_2 ? 'Pro Tier' : 'Free Tier'}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                    {tierStatus?.tier_3 ? 'Premium features with NFT access' :
                     tierStatus?.tier_2 ? 'Advanced trading features' :
                     'Basic trading signals'}
                  </p>
                </div>

                {/* Actions */}
                <div className="grid grid-cols-2 gap-2 w-full max-w-full">
                  <button
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white p-2.5 rounded-lg font-medium transition-all duration-200 active:scale-95 min-h-[44px] min-w-0 overflow-hidden max-w-full box-border"
                    onClick={() => setActiveTab('trading')}
                  >
                    <div className="flex items-center justify-center space-x-1.5">
                      <FontAwesomeIcon icon={faArrowTrendUp} className="w-3.5 h-3.5 flex-shrink-0" />
                      <span className="truncate text-sm">View Signals</span>
                    </div>
                  </button>
                  <button
                    className="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-white p-2.5 rounded-lg font-medium transition-all duration-200 active:scale-95 min-h-[44px] min-w-0 overflow-hidden max-w-full box-border"
                    onClick={() => setActiveTab('positions')}
                  >
                    <div className="flex items-center justify-center space-x-1.5">
                      <FontAwesomeIcon icon={faBriefcase} className="w-3.5 h-3.5 flex-shrink-0" />
                      <span className="truncate text-sm">Positions</span>
                    </div>
                  </button>
                </div>
              </div>
            )}

            {activeTab === 'trading' && (
              <div className="space-y-4 w-full overflow-x-hidden max-w-full box-border">
                <div className="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700 w-full overflow-hidden max-w-full box-border">
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3 truncate">Trading Stats</h3>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="text-center min-w-0">
                      <p className="text-lg font-bold text-blue-600 dark:text-blue-400 truncate">{stats.active_signals}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">Active Signals</p>
                    </div>
                    <div className="text-center min-w-0">
                      <p className="text-lg font-bold text-purple-600 dark:text-purple-400 truncate">{stats.total_trades}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">Total Trades</p>
                    </div>
                  </div>
                </div>

                {/* Auto Trading Toggle */}
                <div className="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700 w-full overflow-hidden max-w-full box-border">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-semibold text-gray-900 dark:text-white truncate">Auto Trading</h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
                        Automatically execute trading signals
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer ml-2 flex-shrink-0">
                      <input type="checkbox" className="sr-only peer" />
                      <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>

                <button className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white p-3 rounded-lg font-medium transition-all duration-200 active:scale-95 min-h-[44px] overflow-hidden max-w-full box-border">
                  <div className="flex items-center justify-center space-x-1.5">
                    <FontAwesomeIcon icon={faChartLine} className="w-3.5 h-3.5 flex-shrink-0" />
                    <span className="truncate text-sm">View All Signals</span>
                  </div>
                </button>
              </div>
            )}

            {activeTab === 'positions' && (
              <div className="space-y-4 w-full overflow-x-hidden max-w-full box-border">
                <div className="text-center py-6">
                  <div className="flex items-center justify-center mb-3">
                    <FontAwesomeIcon icon={faBriefcase} className="w-10 h-10 text-gray-400 dark:text-gray-500" />
                  </div>
                  <p className="text-gray-500 dark:text-gray-400 mb-3 text-sm">No active positions</p>
                  <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2.5 rounded-lg font-medium transition-colors active:scale-95 text-sm">
                    Start Trading
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default MobileDashboard;
