/**
 * Mobile Navigation Component for DeepTrade
 * 
 * Provides mobile-optimized navigation with hamburger menu,
 * collapsible sections, and touch-friendly interactions.
 */

import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useMobile } from '../../hooks/useResponsiveDesign';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '@solana/wallet-adapter-react';
import { useTranslation } from '../../hooks/useTranslation';
import LanguageSelector from '../ui/LanguageSelector';

interface MobileNavigationProps {
  isOpen: boolean;
  onClose: () => void;
}

interface NavItem {
  label: string;
  path: string;
  icon: string;
  requiresAuth?: boolean;
  requiresTier?: number;
  children?: NavItem[];
}

const MobileNavigation: React.FC<MobileNavigationProps> = ({ isOpen, onClose }) => {
  const { isMobile } = useMobile();
  const { user, logout } = useAuth();
  const { publicKey, disconnect } = useWallet();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  const navItems: NavItem[] = [
    {
      label: t('navigation.dashboard'),
      path: '/dashboard',
      icon: '📊',
      requiresAuth: true,
    },
    {
      label: t('navigation.trading'),
      path: '/trading',
      icon: '📈',
      requiresAuth: true,
      children: [
        { label: t('navigation.signals'), path: '/trading/signals', icon: '🎯' },
        { label: t('navigation.apiCredentials'), path: '/api-credentials', icon: '🔑' },
        { label: t('navigation.autoTrading'), path: '/trading/auto', icon: '🤖' },
      ],
    },
    {
      label: t('navigation.tierManagement'),
      path: '/tier',
      icon: '⭐',
      requiresAuth: true,
    },
    {
      label: t('navigation.referrals'),
      path: '/referral-dashboard',
      icon: '👥',
      requiresAuth: true,
    },
    {
      label: t('navigation.settings'),
      path: '/settings',
      icon: '⚙️',
      requiresAuth: true,
    },
    {
      label: t('navigation.help'),
      path: '/help',
      icon: '❓',
    },
  ];

  const publicNavItems: NavItem[] = [
    {
      label: t('navigation.home'),
      path: '/',
      icon: '🏠',
    },
    {
      label: t('navigation.login'),
      path: '/login',
      icon: '🔐',
    },
    {
      label: t('navigation.register'),
      path: '/register',
      icon: '📝',
    },
    {
      label: t('navigation.help'),
      path: '/help',
      icon: '❓',
    },
  ];

  const toggleSection = (sectionLabel: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionLabel)) {
      newExpanded.delete(sectionLabel);
    } else {
      newExpanded.add(sectionLabel);
    }
    setExpandedSections(newExpanded);
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    onClose();
  };

  const handleLogout = async () => {
    try {
      if (publicKey) {
        await disconnect();
      }
      await logout();
      onClose();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const formatWalletAddress = (address: string) => {
    if (!address) return '';
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  // Close menu when route changes
  useEffect(() => {
    onClose();
  }, [location.pathname, onClose]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isMobile) return null;

  const currentNavItems = user ? navItems : publicNavItems;

  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"
          onClick={onClose}
        />
      )}

      {/* Mobile Menu */}
      <div
        className={`fixed top-0 left-0 h-full w-80 max-w-[85vw] bg-white dark:bg-gray-900 shadow-xl z-50 transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2">
            <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              DeepTrade
            </span>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            aria-label="Close menu"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* User Info */}
        {user && (
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                {user.full_name?.charAt(0) || user.email.charAt(0).toUpperCase()}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {user.full_name || user.email}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  {user.email}
                </p>
                {publicKey && (
                  <p className="text-xs text-green-600 dark:text-green-400 truncate">
                    🔗 {formatWalletAddress(publicKey.toString())}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Navigation Items */}
        <div className="flex-1 overflow-y-auto py-4">
          <nav className="space-y-1 px-2">
            {currentNavItems.map((item) => (
              <div key={item.label}>
                {item.children ? (
                  // Expandable section
                  <div>
                    <button
                      onClick={() => toggleSection(item.label)}
                      className={`w-full flex items-center justify-between px-3 py-2 text-left rounded-lg transition-colors ${
                        location.pathname.startsWith(item.path)
                          ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{item.icon}</span>
                        <span className="font-medium">{item.label}</span>
                      </div>
                      <svg
                        className={`w-4 h-4 transition-transform ${
                          expandedSections.has(item.label) ? 'rotate-180' : ''
                        }`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                    
                    {/* Submenu */}
                    {expandedSections.has(item.label) && (
                      <div className="ml-6 mt-1 space-y-1">
                        {item.children.map((child) => (
                          <button
                            key={child.path}
                            onClick={() => handleNavigation(child.path)}
                            className={`w-full flex items-center space-x-3 px-3 py-2 text-left rounded-lg transition-colors ${
                              location.pathname === child.path
                                ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
                            }`}
                          >
                            <span className="text-sm">{child.icon}</span>
                            <span className="text-sm">{child.label}</span>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  // Regular nav item
                  <button
                    onClick={() => handleNavigation(item.path)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 text-left rounded-lg transition-colors ${
                      location.pathname === item.path
                        ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                    }`}
                  >
                    <span className="text-lg">{item.icon}</span>
                    <span className="font-medium">{item.label}</span>
                  </button>
                )}
              </div>
            ))}
          </nav>
        </div>

        {/* Footer Actions */}
        <div className="border-t border-gray-200 dark:border-gray-700 p-4 space-y-2">
          {/* Language Selector */}
          <div className="mb-2">
            <LanguageSelector variant="mobile" showLabel={true} />
          </div>

          {user && (
            <button
              onClick={handleLogout}
              className="w-full flex items-center space-x-3 px-3 py-2 text-left rounded-lg text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
            >
              <span className="text-lg">🚪</span>
              <span className="font-medium">{t('navigation.logout')}</span>
            </button>
          )}
        </div>
      </div>
    </>
  );
};

export default MobileNavigation;
