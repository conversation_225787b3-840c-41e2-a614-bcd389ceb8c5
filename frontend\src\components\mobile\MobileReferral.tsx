/**
 * Mobile Referral Component for DeepTrade
 * 
 * Provides mobile-optimized referral dashboard with touch-friendly
 * interactions, swipeable cards, and responsive metrics display.
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faDollarSign,
  faShare,
  faCopy,
  faWallet,
  faCog,
  faChartLine,
  faGift
} from '@fortawesome/free-solid-svg-icons';
import { useMobile } from '../../hooks/useResponsiveDesign';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from '../../hooks/useTranslation';
import { toastSuccess, toastError } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import MobileTopBar from './MobileTopBar';

interface ReferralProfile {
  id: string;
  referral_code: string;
  is_active: boolean;
  solana_wallet_address: string | null;
  auto_payment_enabled: boolean;
  minimum_payout: number;
  total_referrals: number;
  active_referrals: number;
  total_earnings: number;
  total_paid: number;
  pending_payout: number;
  eligible_for_payout: boolean;
}

interface MobileReferralProps {
  // Props are optional as we'll fetch real data
}

const MobileReferral: React.FC<MobileReferralProps> = () => {
  const { isMobile } = useMobile();
  const { user } = useAuth();
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'overview' | 'referrals' | 'earnings' | 'settings'>('overview');
  const [profile, setProfile] = useState<ReferralProfile | null>(null);
  const [referrals, setReferrals] = useState<any[]>([]);
  const [earnings, setEarnings] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [referralLink, setReferralLink] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [autoPayment, setAutoPayment] = useState(false);
  const [minimumPayout, setMinimumPayout] = useState(10);

  if (!isMobile) return null;

  // Fetch referral data
  const fetchReferralData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('access_token');
      
      // Fetch referral profile
      const profileResponse = await fetch('/api/referral/profile', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (profileResponse.ok) {
        const profileData = await profileResponse.json();
        setProfile(profileData);
        setReferralLink(`${window.location.origin}/register?ref=${profileData.referral_code}`);
        setWalletAddress(profileData.solana_wallet_address || '');
        setAutoPayment(profileData.auto_payment_enabled);
        setMinimumPayout(profileData.minimum_payout);
      }

      // Fetch referrals
      const referralsResponse = await fetch('/api/referral/referrals', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (referralsResponse.ok) {
        const referralsData = await referralsResponse.json();
        setReferrals(referralsData.referrals || []);
      }

      // Fetch earnings
      const earningsResponse = await fetch('/api/referral/earnings', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (earningsResponse.ok) {
        const earningsData = await earningsResponse.json();
        setEarnings(earningsData.earnings || []);
      }
    } catch (error) {
      console.error('Error fetching referral data:', error);
      toastError({
        title: 'Error',
        description: 'Failed to load referral data',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchReferralData();
    }
  }, [user]);

  const copyReferralLink = () => {
    navigator.clipboard.writeText(referralLink);
    toastSuccess({
      title: 'Copied!',
      description: 'Referral link copied to clipboard',
    });
  };

  const handleSaveSettings = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/referral/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          solana_wallet_address: walletAddress,
          auto_payment_enabled: autoPayment,
          minimum_payout: minimumPayout
        })
      });

      if (response.ok) {
        toastSuccess({
          title: 'Settings Saved',
          description: 'Your referral settings have been updated',
        });
        fetchReferralData(); // Refresh data
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (error) {
      toastError({
        title: 'Error',
        description: 'Failed to save settings',
      });
    }
  };

  const requestPayout = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/referral/payout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        toastSuccess({
          title: 'Payout Requested',
          description: 'Your payout request has been submitted',
        });
        fetchReferralData(); // Refresh data
      } else {
        throw new Error('Failed to request payout');
      }
    } catch (error) {
      toastError({
        title: 'Error',
        description: 'Failed to request payout',
      });
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: faChartLine },
    { id: 'referrals', label: 'Referrals', icon: faUsers },
    { id: 'earnings', label: 'Earnings', icon: faDollarSign },
    { id: 'settings', label: 'Settings', icon: faCog },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <MobileTopBar />
      
      {/* Tab Navigation */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-16 z-20">
        <div className="flex h-12">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 flex flex-col items-center justify-center py-2 px-1 text-xs font-medium transition-colors min-h-[48px] ${
                activeTab === tab.id
                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 bg-blue-50/50 dark:bg-blue-900/20'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50'
              }`}
            >
              <FontAwesomeIcon
                icon={tab.icon}
                className={`w-3 h-3 flex-shrink-0 mb-1 ${activeTab === tab.id ? 'opacity-100' : 'opacity-70'}`}
              />
              <span className="text-xs font-medium leading-tight text-center">
                {tab.label}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="p-3 pb-20">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {activeTab === 'overview' && (
              <div className="space-y-4">
                {/* Stats Cards */}
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center space-x-2 mb-2">
                      <FontAwesomeIcon icon={faUsers} className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      <span className="text-xs text-gray-600 dark:text-gray-400">Total Referrals</span>
                    </div>
                    <div className="text-xl font-bold text-gray-900 dark:text-white">
                      {profile?.total_referrals || 0}
                    </div>
                  </div>
                  
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center space-x-2 mb-2">
                      <FontAwesomeIcon icon={faDollarSign} className="w-4 h-4 text-green-600 dark:text-green-400" />
                      <span className="text-xs text-gray-600 dark:text-gray-400">Total Earnings</span>
                    </div>
                    <div className="text-xl font-bold text-gray-900 dark:text-white">
                      ${profile?.total_earnings?.toFixed(6) || '0.000000'}
                    </div>
                  </div>
                  
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center space-x-2 mb-2">
                      <FontAwesomeIcon icon={faWallet} className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                      <span className="text-xs text-gray-600 dark:text-gray-400">Pending Payout</span>
                    </div>
                    <div className="text-xl font-bold text-gray-900 dark:text-white">
                      ${profile?.pending_payout?.toFixed(6) || '0.000000'}
                    </div>
                  </div>
                  
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center space-x-2 mb-2">
                      <FontAwesomeIcon icon={faGift} className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                      <span className="text-xs text-gray-600 dark:text-gray-400">Active Referrals</span>
                    </div>
                    <div className="text-xl font-bold text-gray-900 dark:text-white">
                      {profile?.active_referrals || 0}
                    </div>
                  </div>
                </div>

                {/* Referral Link */}
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                    <FontAwesomeIcon icon={faShare} className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
                    Your Referral Link
                  </h3>
                  <div className="flex space-x-2">
                    <Input
                      value={referralLink}
                      readOnly
                      className="flex-1 text-sm"
                    />
                    <Button
                      onClick={copyReferralLink}
                      size="sm"
                      className="px-3"
                    >
                      <FontAwesomeIcon icon={faCopy} className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Payout Button */}
                {profile?.eligible_for_payout && (
                  <Button
                    onClick={requestPayout}
                    className="w-full bg-green-600 hover:bg-green-700 text-white"
                  >
                    Request Payout (${profile.pending_payout?.toFixed(6)})
                  </Button>
                )}
              </div>
            )}

            {activeTab === 'referrals' && (
              <div className="space-y-3">
                {referrals.length === 0 ? (
                  <div className="text-center py-8">
                    <FontAwesomeIcon icon={faUsers} className="w-12 h-12 text-gray-400 dark:text-gray-500 mb-3" />
                    <p className="text-gray-500 dark:text-gray-400 mb-3">No referrals yet</p>
                    <p className="text-sm text-gray-400 dark:text-gray-500">Share your referral link to start earning!</p>
                  </div>
                ) : (
                  referrals.map((referral, index) => (
                    <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {referral.referred_user_email || 'Anonymous User'}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            Joined: {new Date(referral.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                          referral.is_verified
                            ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400'
                            : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400'
                        }`}>
                          {referral.is_verified ? 'Verified' : 'Pending'}
                        </div>
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        Tier: {referral.current_tier || 1} | Earnings: ${referral.total_earnings?.toFixed(6) || '0.000000'}
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}

            {activeTab === 'earnings' && (
              <div className="space-y-3">
                {earnings.length === 0 ? (
                  <div className="text-center py-8">
                    <FontAwesomeIcon icon={faDollarSign} className="w-12 h-12 text-gray-400 dark:text-gray-500 mb-3" />
                    <p className="text-gray-500 dark:text-gray-400 mb-3">No earnings yet</p>
                    <p className="text-sm text-gray-400 dark:text-gray-500">Start referring users to earn commissions!</p>
                  </div>
                ) : (
                  earnings.map((earning, index) => (
                    <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            ${earning.amount?.toFixed(6) || '0.000000'}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {new Date(earning.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                          earning.status === 'paid'
                            ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400'
                            : earning.status === 'pending'
                            ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400'
                            : 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400'
                        }`}>
                          {earning.status || 'pending'}
                        </div>
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        From: {earning.referred_user_email || 'Anonymous'} | Type: {earning.earning_type || 'commission'}
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="space-y-4">
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <FontAwesomeIcon icon={faWallet} className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
                    Payout Settings
                  </h3>

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="wallet">Solana Wallet Address</Label>
                      <Input
                        id="wallet"
                        value={walletAddress}
                        onChange={(e) => setWalletAddress(e.target.value)}
                        placeholder="Enter your Solana wallet address"
                        className="mt-1"
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Payouts will be sent to this address
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="minimum">Minimum Payout Amount ($)</Label>
                      <Input
                        id="minimum"
                        type="number"
                        value={minimumPayout}
                        onChange={(e) => setMinimumPayout(Number(e.target.value))}
                        min="10"
                        className="mt-1"
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Minimum $10 plus gas fees
                      </p>
                    </div>

                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="auto-payment"
                        checked={autoPayment}
                        onChange={(e) => setAutoPayment(e.target.checked)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                      />
                      <Label htmlFor="auto-payment" className="text-sm">
                        Enable automatic payments
                      </Label>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      When enabled, payouts will be processed automatically when minimum is reached
                    </p>

                    <Button
                      onClick={handleSaveSettings}
                      className="w-full"
                    >
                      Save Settings
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default MobileReferral;
