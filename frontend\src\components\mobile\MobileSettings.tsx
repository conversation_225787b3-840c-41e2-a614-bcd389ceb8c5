/**
 * Mobile Settings Component for DeepTrade
 *
 * Provides mobile-optimized settings and account management with
 * touch-friendly forms, organized sections, and professional styling.
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUser,
  faChartLine,
  faLock,
  faQuestionCircle,
  faBook,
  faComments,
  faFileAlt,
  faInfoCircle,
  faSignOutAlt,
  faKey,
  faShield,
  faDownload
} from '@fortawesome/free-solid-svg-icons';
import { useMobile } from '../../hooks/useResponsiveDesign';
import { useAuth } from '../../contexts/AuthContext';

interface MobileSettingsProps {
  // Props are optional as we'll fetch real data
}

const MobileSettings: React.FC<MobileSettingsProps> = () => {
  const { isMobile } = useMobile();
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState<'account' | 'trading' | 'security' | 'help'>('account');
  
  // Settings states
  const [profile, setProfile] = useState({
    full_name: user?.full_name || '',
    email: user?.email || '',
    phone: '',
    timezone: 'UTC',
    language: 'en'
  });
  
  const [tradingSettings, setTradingSettings] = useState({
    auto_trading: false,
    position_size: 5,
    max_leverage: 3,
    risk_level: 'medium',
    notifications: true
  });

  const [loading, setLoading] = useState(false);

  if (!isMobile) return null;

  const tabs = [
    { id: 'account', label: 'Account', icon: faUser },
    { id: 'trading', label: 'Trading', icon: faChartLine },
    { id: 'security', label: 'Security', icon: faLock },
    { id: 'help', label: 'Help', icon: faQuestionCircle },
  ];

  const handleSaveProfile = async () => {
    setLoading(true);
    try {
      // API call to save profile
      await new Promise(resolve => setTimeout(resolve, 1000)); // Mock delay
      console.log('Profile saved:', profile);
    } catch (error) {
      console.error('Error saving profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveTradingSettings = async () => {
    setLoading(true);
    try {
      // API call to save trading settings
      await new Promise(resolve => setTimeout(resolve, 1000)); // Mock delay
      console.log('Trading settings saved:', tradingSettings);
    } catch (error) {
      console.error('Error saving trading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    if (window.confirm('Are you sure you want to logout?')) {
      await logout();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Tab Navigation - Redesigned for mobile */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-20">
        <div className="flex h-12">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 flex flex-col items-center justify-center py-2 px-1 text-xs font-medium transition-colors min-h-[48px] ${
                activeTab === tab.id
                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 bg-blue-50/50 dark:bg-blue-900/20'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50'
              }`}
            >
              <FontAwesomeIcon
                icon={tab.icon}
                className={`w-3 h-3 flex-shrink-0 mb-1 ${activeTab === tab.id ? 'opacity-100' : 'opacity-70'}`}
              />
              <span className="text-xs font-medium leading-tight text-center">
                {tab.label}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="p-3 pb-20">
        {activeTab === 'account' && (
          <div className="space-y-6">
            {/* Profile Section */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <FontAwesomeIcon icon={faUser} className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
                Profile Information
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    value={profile.full_name}
                    onChange={(e) => setProfile({...profile, full_name: e.target.value})}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white min-h-[48px]"
                    placeholder="Enter your full name"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={profile.email}
                    onChange={(e) => setProfile({...profile, email: e.target.value})}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white min-h-[48px]"
                    placeholder="Enter your email"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={profile.phone}
                    onChange={(e) => setProfile({...profile, phone: e.target.value})}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white min-h-[48px]"
                    placeholder="Enter your phone number"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Timezone
                    </label>
                    <select
                      value={profile.timezone}
                      onChange={(e) => setProfile({...profile, timezone: e.target.value})}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white min-h-[48px]"
                    >
                      <option value="UTC">UTC</option>
                      <option value="EST">EST</option>
                      <option value="PST">PST</option>
                      <option value="CET">CET</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Language
                    </label>
                    <select
                      value={profile.language}
                      onChange={(e) => setProfile({...profile, language: e.target.value})}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white min-h-[48px]"
                    >
                      <option value="en">🇺🇸 English</option>
                      <option value="es">🇪🇸 Español</option>
                      <option value="fr">🇫🇷 Français</option>
                      <option value="de">🇩🇪 Deutsch</option>
                    </select>
                  </div>
                </div>
              </div>
              
              <button
                onClick={handleSaveProfile}
                disabled={loading}
                className="w-full mt-6 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white p-3 rounded-lg font-medium transition-colors active:scale-95 min-h-[48px]"
              >
                {loading ? 'Saving...' : 'Save Profile'}
              </button>
            </div>

            {/* Account Actions */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <FontAwesomeIcon icon={faDownload} className="w-5 h-5 mr-2 text-yellow-600 dark:text-yellow-400" />
                Account Actions
              </h3>

              <div className="space-y-3">
                <button className="w-full bg-yellow-600 hover:bg-yellow-700 text-white p-3 rounded-lg font-medium transition-colors active:scale-95 min-h-[48px]">
                  <div className="flex items-center justify-center space-x-2">
                    <FontAwesomeIcon icon={faDownload} className="w-4 h-4" />
                    <span>Export Account Data</span>
                  </div>
                </button>
                <button className="w-full bg-red-600 hover:bg-red-700 text-white p-3 rounded-lg font-medium transition-colors active:scale-95 min-h-[48px]">
                  <div className="flex items-center justify-center space-x-2">
                    <FontAwesomeIcon icon={faUser} className="w-4 h-4" />
                    <span>Deactivate Account</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'trading' && (
          <div className="space-y-6">
            {/* Trading Preferences */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <FontAwesomeIcon icon={faChartLine} className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
                Trading Preferences
              </h3>
              
              <div className="space-y-6">
                {/* Auto Trading Toggle */}
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-white">Auto Trading</h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Automatically execute trading signals
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer ml-4">
                    <input 
                      type="checkbox" 
                      className="sr-only peer" 
                      checked={tradingSettings.auto_trading}
                      onChange={(e) => setTradingSettings({...tradingSettings, auto_trading: e.target.checked})}
                    />
                    <div className="w-14 h-8 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-7 after:w-7 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                {/* Position Size */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Position Size: {tradingSettings.position_size}% of balance
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="10"
                    value={tradingSettings.position_size}
                    onChange={(e) => setTradingSettings({...tradingSettings, position_size: parseInt(e.target.value)})}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                  />
                  <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <span>1%</span>
                    <span>5%</span>
                    <span>10%</span>
                  </div>
                </div>

                {/* Max Leverage */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Maximum Leverage
                  </label>
                  <select
                    value={tradingSettings.max_leverage}
                    onChange={(e) => setTradingSettings({...tradingSettings, max_leverage: parseInt(e.target.value)})}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white min-h-[48px]"
                  >
                    <option value="1">1x (No Leverage)</option>
                    <option value="2">2x</option>
                    <option value="3">3x</option>
                    <option value="5">5x</option>
                    <option value="10">10x</option>
                  </select>
                </div>

                {/* Risk Level */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Risk Level
                  </label>
                  <div className="grid grid-cols-3 gap-2">
                    {['low', 'medium', 'high'].map((level) => (
                      <button
                        key={level}
                        onClick={() => setTradingSettings({...tradingSettings, risk_level: level})}
                        className={`p-3 rounded-lg font-medium transition-colors min-h-[48px] ${
                          tradingSettings.risk_level === level
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600'
                        }`}
                      >
                        {level.charAt(0).toUpperCase() + level.slice(1)}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Notifications */}
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-white">Trading Notifications</h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Receive alerts for trading signals and updates
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer ml-4">
                    <input 
                      type="checkbox" 
                      className="sr-only peer" 
                      checked={tradingSettings.notifications}
                      onChange={(e) => setTradingSettings({...tradingSettings, notifications: e.target.checked})}
                    />
                    <div className="w-14 h-8 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-7 after:w-7 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>
              
              <button
                onClick={handleSaveTradingSettings}
                disabled={loading}
                className="w-full mt-6 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white p-3 rounded-lg font-medium transition-colors active:scale-95 min-h-[48px]"
              >
                {loading ? 'Saving...' : 'Save Trading Settings'}
              </button>
            </div>
          </div>
        )}

        {activeTab === 'security' && (
          <div className="space-y-6">
            {/* Security Settings */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <FontAwesomeIcon icon={faLock} className="w-5 h-5 mr-2 text-red-600 dark:text-red-400" />
                Security Settings
              </h3>
              
              <div className="space-y-4">
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-lg font-medium transition-colors active:scale-95 min-h-[48px] text-left">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <FontAwesomeIcon icon={faLock} className="w-5 h-5" />
                      <div>
                        <div className="font-medium">Change Password</div>
                        <div className="text-sm opacity-80">Update your account password</div>
                      </div>
                    </div>
                    <span>→</span>
                  </div>
                </button>

                <button className="w-full bg-green-600 hover:bg-green-700 text-white p-3 rounded-lg font-medium transition-colors active:scale-95 min-h-[48px] text-left">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <FontAwesomeIcon icon={faShield} className="w-5 h-5" />
                      <div>
                        <div className="font-medium">Enable 2FA</div>
                        <div className="text-sm opacity-80">Add extra security to your account</div>
                      </div>
                    </div>
                    <span>→</span>
                  </div>
                </button>

                <button className="w-full bg-yellow-600 hover:bg-yellow-700 text-white p-3 rounded-lg font-medium transition-colors active:scale-95 min-h-[48px] text-left">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <FontAwesomeIcon icon={faKey} className="w-5 h-5" />
                      <div>
                        <div className="font-medium">API Keys</div>
                        <div className="text-sm opacity-80">Manage your trading API keys</div>
                      </div>
                    </div>
                    <span>→</span>
                  </div>
                </button>
              </div>
            </div>

            {/* Logout */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
              <button
                onClick={handleLogout}
                className="w-full bg-red-600 hover:bg-red-700 text-white p-3 rounded-lg font-medium transition-colors active:scale-95 min-h-[48px]"
              >
                <div className="flex items-center justify-center space-x-2">
                  <FontAwesomeIcon icon={faSignOutAlt} className="w-4 h-4" />
                  <span>Logout</span>
                </div>
              </button>
            </div>
          </div>
        )}

        {activeTab === 'help' && (
          <div className="space-y-6">
            {/* Help & Support */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <FontAwesomeIcon icon={faQuestionCircle} className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
                Help & Support
              </h3>

              <div className="space-y-4">
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-lg font-medium transition-colors active:scale-95 min-h-[48px] text-left">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <FontAwesomeIcon icon={faBook} className="w-5 h-5" />
                      <div>
                        <div className="font-medium">User Guide</div>
                        <div className="text-sm opacity-80">Learn how to use DeepTrade</div>
                      </div>
                    </div>
                    <span>→</span>
                  </div>
                </button>

                <button className="w-full bg-green-600 hover:bg-green-700 text-white p-3 rounded-lg font-medium transition-colors active:scale-95 min-h-[48px] text-left">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <FontAwesomeIcon icon={faComments} className="w-5 h-5" />
                      <div>
                        <div className="font-medium">Contact Support</div>
                        <div className="text-sm opacity-80">Get help from our team</div>
                      </div>
                    </div>
                    <span>→</span>
                  </div>
                </button>

                <button className="w-full bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-lg font-medium transition-colors active:scale-95 min-h-[48px] text-left">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <FontAwesomeIcon icon={faFileAlt} className="w-5 h-5" />
                      <div>
                        <div className="font-medium">Terms & Privacy</div>
                        <div className="text-sm opacity-80">Legal information and policies</div>
                      </div>
                    </div>
                    <span>→</span>
                  </div>
                </button>

                <button className="w-full bg-gray-600 hover:bg-gray-700 text-white p-3 rounded-lg font-medium transition-colors active:scale-95 min-h-[48px] text-left">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <FontAwesomeIcon icon={faInfoCircle} className="w-5 h-5" />
                      <div>
                        <div className="font-medium">About DeepTrade</div>
                        <div className="text-sm opacity-80">Version 1.0.0</div>
                      </div>
                    </div>
                    <span>→</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileSettings;
