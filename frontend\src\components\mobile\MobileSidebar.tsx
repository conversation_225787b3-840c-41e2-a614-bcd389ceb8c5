/**
 * Mobile Navigation Component for DeepTrade
 *
 * Provides mobile-optimized navigation with hamburger menu,
 * collapsible sections, and touch-friendly interactions.
 */

import React, { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Settings,
  LogOut,
  ChevronDown,
  ChevronUp,
  HelpCircle,
  Home,
  Lock,
  Edit,
  LayoutDashboard,
  Wallet,
  Trophy,
  Users
} from 'lucide-react';
import { useMobile } from '../../hooks/useResponsiveDesign';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '@solana/wallet-adapter-react';
import { useTranslation } from '../../hooks/useTranslation';

interface MobileNavigationProps {
  isOpen: boolean;
  onClose: () => void;
}

interface NavItem {
  label: string;
  path: string;
  icon: any;
  requiresAuth?: boolean;
  requiresTier?: number;
  children?: NavItem[];
}

const MobileSidebar: React.FC<MobileNavigationProps> = ({ isOpen, onClose }) => {
  const { isMobile } = useMobile();
  const { user, logout } = useAuth();
  const { publicKey, disconnect } = useWallet();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const previousPathRef = useRef<string>(location.pathname);

  const navItems: NavItem[] = [
    {
      label: t('navigation.dashboard'),
      path: '/',
      icon: LayoutDashboard,
      requiresAuth: true,
    },
    {
      label: t('navigation.apiCredentials'),
      path: '/api-credentials',
      icon: Wallet,
      requiresAuth: true,
    },
    {
      label: t('navigation.tierManagement'),
      path: '/tier',
      icon: Trophy,
      requiresAuth: true,
    },
    {
      label: t('navigation.referrals'),
      path: '/referral-dashboard',
      icon: Users,
      requiresAuth: true,
    },
    {
      label: t('navigation.settings'),
      path: '/settings',
      icon: Settings,
      requiresAuth: true,
    },
    {
      label: t('navigation.help'),
      path: '/help',
      icon: HelpCircle,
    },
  ];

  const publicNavItems: NavItem[] = [
    {
      label: t('navigation.home'),
      path: '/',
      icon: Home,
    },
    {
      label: t('navigation.login'),
      path: '/login',
      icon: Lock,
    },
    {
      label: t('navigation.register'),
      path: '/register',
      icon: Edit,
    },
    {
      label: t('navigation.help'),
      path: '/help',
      icon: HelpCircle,
    },
  ];

  const toggleSection = (sectionLabel: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionLabel)) {
      newExpanded.delete(sectionLabel);
    } else {
      newExpanded.add(sectionLabel);
    }
    setExpandedSections(newExpanded);
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    onClose();
  };

  const handleLogout = async () => {
    try {
      if (publicKey) {
        await disconnect();
      }
      await logout();
      onClose();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };



  // Close menu when route actually changes (not on initial render)
  useEffect(() => {
    if (previousPathRef.current !== location.pathname) {
      onClose();
      previousPathRef.current = location.pathname;
    }
  }, [location.pathname, onClose]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isMobile) return null;

  const currentNavItems = user ? navItems : publicNavItems;

  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"
          onClick={onClose}
        />
      )}

      {/* Mobile Menu - Enhanced with backdrop blur and better animations */}
      <div
        className={`fixed top-0 left-0 h-full w-72 max-w-[80vw] bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-2xl z-50 transform transition-all duration-300 ease-out ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        {/* Header with Logo and Close Button */}
        <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700">
          <div className="flex items-center space-x-3">
            <img
              src="/icons/deeptrade.png"
              alt="DeepTrade Logo"
              className="h-8 w-auto flex-shrink-0 object-contain"
            />
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 active:bg-gray-200 dark:active:bg-gray-700 transition-all duration-200 min-w-[40px] min-h-[40px] flex items-center justify-center"
            aria-label="Close menu"
          >
            <svg className="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>



        {/* Navigation Items */}
        <div className="flex-1 overflow-y-auto py-4">
          <nav className="space-y-1 px-2">
            {currentNavItems.map((item) => (
              <div key={item.label}>
                {item.children ? (
                  // Expandable section
                  <div>
                    <button
                      onClick={() => toggleSection(item.label)}
                      className={`w-full flex items-center justify-between px-3 py-3 text-left rounded-lg transition-all duration-200 min-h-[44px] ${
                        location.pathname.startsWith(item.path)
                          ? 'bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 text-blue-700 dark:text-blue-300 shadow-sm'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 active:bg-gray-200 dark:active:bg-gray-700'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-6 h-6 flex items-center justify-center">
                          <item.icon className="w-4 h-4" />
                        </div>
                        <span className="font-medium text-sm">{item.label}</span>
                      </div>
                      {expandedSections.has(item.label) ? (
                        <ChevronUp className="w-3 h-3 transition-transform" />
                      ) : (
                        <ChevronDown className="w-3 h-3 transition-transform" />
                      )}
                    </button>
                    
                    {/* Submenu */}
                    {expandedSections.has(item.label) && (
                      <div className="ml-4 mt-1 space-y-1">
                        {item.children.map((child) => (
                          <button
                            key={child.path}
                            onClick={() => handleNavigation(child.path)}
                            className={`w-full flex items-center space-x-2 px-2 py-2 text-left rounded-md transition-colors ${
                              location.pathname === child.path
                                ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
                            }`}
                          >
                            <child.icon className="w-3 h-3" />
                            <span className="text-xs">{child.label}</span>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  // Regular nav item
                  <button
                    onClick={() => handleNavigation(item.path)}
                    className={`w-full flex items-center space-x-3 px-3 py-3 text-left rounded-lg transition-all duration-200 min-h-[44px] ${
                      location.pathname === item.path
                        ? 'bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 text-blue-700 dark:text-blue-300 shadow-sm'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 active:bg-gray-200 dark:active:bg-gray-700'
                    }`}
                  >
                    <div className="w-6 h-6 flex items-center justify-center">
                      <item.icon className="w-4 h-4" />
                    </div>
                    <span className="font-medium text-sm">{item.label}</span>
                  </button>
                )}
              </div>
            ))}
          </nav>
        </div>

        {/* Bottom Section - User Info & Logout */}
        {user && (
          <div className="p-3 border-t border-gray-200 dark:border-gray-700">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700 space-y-2">
              {/* User Info */}
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                  {user.full_name?.charAt(0) || user.email.charAt(0).toUpperCase()}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-xs font-medium text-gray-900 dark:text-white truncate">
                    {user.full_name || user.email}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                    {user.email}
                  </p>
                </div>
              </div>

              {/* Logout Button */}
              <button
                onClick={handleLogout}
                className="w-full flex items-center justify-center space-x-2 px-3 py-2 text-center rounded-md text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 active:bg-red-100 dark:active:bg-red-900/30 transition-all duration-200 border border-red-200 dark:border-red-800/50"
              >
                <LogOut className="w-3 h-3" />
                <span className="font-medium text-xs">{t('navigation.logout')}</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default MobileSidebar;
