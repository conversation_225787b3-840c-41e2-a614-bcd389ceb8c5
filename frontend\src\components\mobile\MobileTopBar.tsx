/**
 * Mobile Top Navigation Bar Component for DeepTrade
 *
 * Redesigned mobile navigation using desktop components with mobile-optimized layout
 * to prevent horizontal scrolling and provide better user experience.
 */

import React from 'react';
import { Menu, LogOut, Sun, Moon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useWallet } from '@solana/wallet-adapter-react';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../theme-provider';
import { useTranslation } from '../../hooks/useTranslation';
import { useMobile } from '../../hooks/useResponsiveDesign';
import NavbarWalletButton from '../solana/NavbarWalletButton';
import SolanaBranding from '../ui/SolanaBranding';
import LanguageSelector from '../ui/LanguageSelector';

interface MobileTopBarProps {
  balance?: string;
  autoTradingEnabled?: boolean;
  onMenuClick?: () => void;
}

const MobileTopBar: React.FC<MobileTopBarProps> = ({
  balance,
  autoTradingEnabled,
  onMenuClick
}) => {
  const { isMobile } = useMobile();
  const { theme, setTheme } = useTheme();
  const { user, logout } = useAuth();
  const { publicKey, disconnect } = useWallet();
  const navigate = useNavigate();

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };
  const { t } = useTranslation();

  if (!isMobile) return null;

  const handleMenuClick = () => {
    if (onMenuClick) {
      onMenuClick();
    }
  };

  const handleLogout = async () => {
    try {
      if (publicKey) {
        await disconnect();
      }
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    /* Mobile Top Navigation Bar */
    <header className="sticky top-0 z-40 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
        {/* Two-row layout for mobile devices, single row for desktop+ */}
        <div className="lg:hidden w-full">
          {/* Small Device Layout - Two Rows */}
          {/* First Row - Menu, Small Logo Text, and Logout */}
          <div className="flex items-center justify-between px-2 py-1.5 h-10 w-full">
            <button
              onClick={handleMenuClick}
              className="p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 active:bg-gray-200 dark:active:bg-gray-700 transition-all duration-200 w-8 h-8 flex items-center justify-center flex-shrink-0"
              aria-label="Open menu"
            >
              <Menu className="w-4 h-4 text-gray-700 dark:text-gray-300" />
            </button>

            <div className="flex items-center justify-center flex-1 min-w-0">
              <img
                src="/icons/deeptrade.png"
                alt="DeepTrade Logo"
                className="h-5 w-auto flex-shrink-0 object-contain"
              />
            </div>

            {user ? (
              <button
                onClick={handleLogout}
                className="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 active:bg-gray-200 dark:active:bg-gray-700 transition-all duration-200 flex items-center space-x-1 flex-shrink-0"
                aria-label="Logout"
              >
                <LogOut className="w-3 h-3 text-red-600 dark:text-red-400" />
                <span className="text-[10px] font-medium text-red-600 dark:text-red-400">Logout</span>
              </button>
            ) : (
              <div className="w-12 flex-shrink-0"></div> /* Spacer for symmetry when not logged in */
            )}
          </div>

          {/* Second Row - Controls */}
          <div className="flex items-center justify-between px-2 py-1.5 h-10 bg-gray-50/50 dark:bg-gray-800/50 w-full">
            <div className="flex items-center min-w-0 flex-1 mr-2">
              <div className="flex items-center">
                <div className="scale-[0.85] origin-left transform-gpu flex-shrink-0">
                  <NavbarWalletButton />
                </div>

                {/* Solana Logo - Right beside wallet button */}
                <div className="flex items-center flex-shrink-0 -ml-1">
                  <SolanaBranding logoSize={14} showText={false} forceShow={true} showTooltip={false} />
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-1 flex-shrink-0">
              <button
                onClick={toggleTheme}
                className="p-1.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 active:bg-gray-200 dark:active:bg-gray-700 transition-all duration-200 w-7 h-7 flex items-center justify-center bg-gradient-to-br from-yellow-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 border border-gray-200 dark:border-gray-600"
                aria-label="Toggle theme"
              >
                {theme === 'dark' ? (
                  <Sun className="w-3.5 h-3.5 text-yellow-500 transition-colors" />
                ) : (
                  <Moon className="w-3.5 h-3.5 text-blue-600 transition-colors" />
                )}
              </button>

              <div className="scale-[0.7] origin-right transform-gpu">
                <LanguageSelector variant="navbar" />
              </div>
            </div>
          </div>
        </div>

        {/* Desktop+ Device Layout - Single Row */}
        <div className="hidden lg:flex items-center justify-between px-4 py-2 h-16">
          {/* Left Section - Menu Button */}
          <div className="flex items-center">
            <button
              onClick={handleMenuClick}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 active:bg-gray-200 dark:active:bg-gray-700 transition-all duration-200 min-w-[40px] min-h-[40px] flex items-center justify-center"
              aria-label="Open menu"
            >
              <Menu className="w-5 h-5 text-gray-700 dark:text-gray-300" />
            </button>
          </div>

          {/* Center Section - Logo and Branding */}
          <div className="flex-1 flex items-center justify-center px-2 min-w-0">
            <div className="flex items-center space-x-1.5 max-w-full">
              <span className="text-lg font-bold text-blue-600 dark:text-blue-400 whitespace-nowrap">
                DeepTrade
              </span>

              <SolanaBranding
                logoSize={16}
                showText={false}
                className="flex-shrink-0"
              />
            </div>
          </div>

          {/* Right Section - Wallet and Controls */}
          <div className="flex items-center space-x-2">
            <div className="scale-95">
              <NavbarWalletButton />
            </div>

            <button
              onClick={toggleTheme}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 active:bg-gray-200 dark:active:bg-gray-700 transition-all duration-200 min-w-[36px] min-h-[36px] flex items-center justify-center"
              aria-label="Toggle theme"
            >
              {theme === 'dark' ? (
                <Sun className="w-4 h-4 text-gray-700 dark:text-gray-300" />
              ) : (
                <Moon className="w-4 h-4 text-gray-700 dark:text-gray-300" />
              )}
            </button>

            <div className="scale-95">
              <LanguageSelector variant="navbar" />
            </div>

            {user && (
              <button
                onClick={handleLogout}
                className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 active:bg-gray-200 dark:active:bg-gray-700 transition-all duration-200 min-w-[36px] min-h-[36px] flex items-center justify-center"
                aria-label="Logout"
              >
                <LogOut className="w-4 h-4 text-red-600 dark:text-red-400" />
              </button>
            )}
          </div>
        </div>

        {/* Optional Balance/Status Bar - Only for desktop+ devices */}
        {(balance || autoTradingEnabled !== undefined) && (
          <div className="hidden lg:block px-4 py-1 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between text-xs">
              {balance && (
                <span className="text-gray-600 dark:text-gray-400 font-medium">
                  {balance}
                </span>
              )}
              {autoTradingEnabled !== undefined && (
                <div className="flex items-center space-x-2">
                  <span className="text-gray-600 dark:text-gray-400">
                    {t('trading.autoTrading')}:
                  </span>
                  <div className={`w-2 h-2 rounded-full ${
                    autoTradingEnabled
                      ? 'bg-green-500'
                      : 'bg-gray-400 dark:bg-gray-500'
                  }`} />
                  <span className={`font-medium ${
                    autoTradingEnabled
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {autoTradingEnabled ? t('common.on') : t('common.off')}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Balance/Status integrated into small device layout */}
        {(balance || autoTradingEnabled !== undefined) && (
          <div className="md:hidden px-2 py-1 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 w-full">
            <div className="flex items-center justify-between text-xs min-w-0">
              {balance && (
                <span className="text-gray-600 dark:text-gray-400 font-medium truncate text-xs min-w-0 flex-1 mr-2">
                  {balance}
                </span>
              )}
              {autoTradingEnabled !== undefined && (
                <div className="flex items-center space-x-1 flex-shrink-0">
                  <span className="text-gray-600 dark:text-gray-400 text-xs">
                    Auto:
                  </span>
                  <div className={`w-2 h-2 rounded-full ${
                    autoTradingEnabled
                      ? 'bg-green-500'
                      : 'bg-gray-400 dark:bg-gray-500'
                  }`} />
                  <span className={`font-medium text-xs ${
                    autoTradingEnabled
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {autoTradingEnabled ? t('common.on') : t('common.off')}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </header>
  );
};

export default MobileTopBar;
