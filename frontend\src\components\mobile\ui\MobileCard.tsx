/**
 * Mobile Card Component for DeepTrade
 * 
 * Provides mobile-optimized card layouts with proper touch targets,
 * consistent spacing, and responsive design patterns.
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { useMobile } from '@/hooks/useResponsiveDesign';

interface MobileCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  size?: 'sm' | 'md' | 'lg';
  interactive?: boolean;
  onClick?: () => void;
  disabled?: boolean;
}

interface MobileCardHeaderProps {
  children: React.ReactNode;
  className?: string;
  icon?: React.ReactNode;
  action?: React.ReactNode;
}

interface MobileCardContentProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

interface MobileCardFooterProps {
  children: React.ReactNode;
  className?: string;
  justify?: 'start' | 'center' | 'end' | 'between';
}

const cardVariants = {
  default: "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm",
  elevated: "bg-white dark:bg-gray-800 shadow-lg border-0",
  outlined: "bg-transparent border-2 border-gray-300 dark:border-gray-600 shadow-none",
  filled: "bg-gray-50 dark:bg-gray-700 border-0 shadow-none",
};

const cardSizes = {
  sm: "rounded-lg",
  md: "rounded-xl",
  lg: "rounded-2xl",
};

const MobileCard: React.FC<MobileCardProps> = ({
  children,
  className,
  variant = 'default',
  size = 'md',
  interactive = false,
  onClick,
  disabled = false,
}) => {
  const { isMobile } = useMobile();

  const baseClasses = cn(
    "transition-all duration-200 ease-in-out",
    cardVariants[variant],
    cardSizes[size],
    {
      // Interactive states for mobile
      "active:scale-[0.98] active:shadow-sm": interactive && isMobile && !disabled,
      "hover:shadow-md": interactive && !isMobile && !disabled,
      "cursor-pointer": interactive && !disabled,
      "opacity-50 cursor-not-allowed": disabled,
      // Mobile-specific spacing
      "mx-4 my-2": isMobile,
      "m-2": !isMobile,
    },
    className
  );

  const Component = interactive ? 'button' : 'div';

  return (
    <Component
      className={baseClasses}
      onClick={interactive && !disabled ? onClick : undefined}
      disabled={disabled}
      type={interactive ? 'button' : undefined}
    >
      {children}
    </Component>
  );
};

const MobileCardHeader: React.FC<MobileCardHeaderProps> = ({
  children,
  className,
  icon,
  action,
}) => {
  const { isMobile } = useMobile();

  return (
    <div className={cn(
      "flex items-center justify-between",
      isMobile ? "p-4 pb-2" : "p-6 pb-3",
      className
    )}>
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        {icon && (
          <div className={cn(
            "flex-shrink-0",
            isMobile ? "text-lg" : "text-xl"
          )}>
            {icon}
          </div>
        )}
        <div className="flex-1 min-w-0">
          {children}
        </div>
      </div>
      {action && (
        <div className="flex-shrink-0 ml-3">
          {action}
        </div>
      )}
    </div>
  );
};

const MobileCardContent: React.FC<MobileCardContentProps> = ({
  children,
  className,
  padding = 'md',
}) => {
  const { isMobile } = useMobile();

  const paddingClasses = {
    none: "",
    sm: isMobile ? "px-4 py-2" : "px-6 py-3",
    md: isMobile ? "px-4 py-3" : "px-6 py-4",
    lg: isMobile ? "px-4 py-4" : "px-6 py-6",
  };

  return (
    <div className={cn(
      paddingClasses[padding],
      className
    )}>
      {children}
    </div>
  );
};

const MobileCardFooter: React.FC<MobileCardFooterProps> = ({
  children,
  className,
  justify = 'end',
}) => {
  const { isMobile } = useMobile();

  const justifyClasses = {
    start: "justify-start",
    center: "justify-center",
    end: "justify-end",
    between: "justify-between",
  };

  return (
    <div className={cn(
      "flex items-center",
      justifyClasses[justify],
      isMobile ? "p-4 pt-2" : "p-6 pt-3",
      "border-t border-gray-100 dark:border-gray-700",
      className
    )}>
      {children}
    </div>
  );
};

// Metric Card specifically for dashboard metrics
interface MobileMetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'gray';
  onClick?: () => void;
}

const MobileMetricCard: React.FC<MobileMetricCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  color = 'blue',
  onClick,
}) => {
  const { isMobile } = useMobile();

  const colorClasses = {
    blue: "border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20",
    green: "border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20",
    red: "border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20",
    yellow: "border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20",
    purple: "border-purple-200 dark:border-purple-800 bg-purple-50 dark:bg-purple-900/20",
    gray: "border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800",
  };

  const iconColorClasses = {
    blue: "text-blue-600 dark:text-blue-400",
    green: "text-green-600 dark:text-green-400",
    red: "text-red-600 dark:text-red-400",
    yellow: "text-yellow-600 dark:text-yellow-400",
    purple: "text-purple-600 dark:text-purple-400",
    gray: "text-gray-600 dark:text-gray-400",
  };

  return (
    <MobileCard
      variant="outlined"
      interactive={!!onClick}
      onClick={onClick}
      className={cn(
        colorClasses[color],
        "border-2",
        isMobile ? "min-h-[120px]" : "min-h-[140px]"
      )}
    >
      <MobileCardContent padding="md">
        <div className="flex items-start justify-between h-full">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              {icon && (
                <div className={cn("text-lg", iconColorClasses[color])}>
                  {icon}
                </div>
              )}
              <h3 className={cn(
                "font-medium text-gray-600 dark:text-gray-400 truncate",
                isMobile ? "text-sm" : "text-base"
              )}>
                {title}
              </h3>
            </div>
            
            <div className={cn(
              "font-bold text-gray-900 dark:text-white mb-1",
              isMobile ? "text-xl" : "text-2xl"
            )}>
              {value}
            </div>
            
            {subtitle && (
              <p className={cn(
                "text-gray-500 dark:text-gray-400",
                isMobile ? "text-xs" : "text-sm"
              )}>
                {subtitle}
              </p>
            )}
          </div>
          
          {trend && (
            <div className={cn(
              "flex items-center space-x-1 text-sm font-medium",
              trend.isPositive ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
            )}>
              <span>{trend.isPositive ? '↗' : '↘'}</span>
              <span>{Math.abs(trend.value)}%</span>
            </div>
          )}
        </div>
      </MobileCardContent>
    </MobileCard>
  );
};

export {
  MobileCard,
  MobileCardHeader,
  MobileCardContent,
  MobileCardFooter,
  MobileMetricCard,
};
