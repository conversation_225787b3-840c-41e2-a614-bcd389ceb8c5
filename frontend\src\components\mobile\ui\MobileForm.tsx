/**
 * Mobile Form Components for DeepTrade
 * 
 * Provides mobile-optimized form layouts with proper touch targets,
 * improved spacing, and accessibility features.
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { useMobile } from '@/hooks/useResponsiveDesign';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/Button';

interface MobileFormProps {
  children: React.ReactNode;
  className?: string;
  onSubmit?: (e: React.FormEvent) => void;
}

interface MobileFormSectionProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  className?: string;
}

interface MobileFormFieldProps {
  label: string;
  children: React.ReactNode;
  error?: string;
  required?: boolean;
  description?: string;
  className?: string;
}

interface MobileInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  required?: boolean;
  description?: string;
  icon?: React.ReactNode;
  action?: React.ReactNode;
}

interface MobileSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label: string;
  options: { value: string; label: string }[];
  error?: string;
  required?: boolean;
  description?: string;
  placeholder?: string;
}

interface MobileTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  error?: string;
  required?: boolean;
  description?: string;
}

const MobileForm: React.FC<MobileFormProps> = ({
  children,
  className,
  onSubmit,
}) => {
  const { isMobile } = useMobile();

  return (
    <form
      onSubmit={onSubmit}
      className={cn(
        "space-y-6",
        isMobile ? "px-4 py-6" : "px-6 py-8",
        className
      )}
    >
      {children}
    </form>
  );
};

const MobileFormSection: React.FC<MobileFormSectionProps> = ({
  children,
  title,
  description,
  icon,
  className,
}) => {
  const { isMobile } = useMobile();

  return (
    <div className={cn(
      "bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700",
      isMobile ? "p-4" : "p-6",
      className
    )}>
      {(title || description) && (
        <div className={cn(
          "border-b border-gray-100 dark:border-gray-700",
          isMobile ? "pb-4 mb-4" : "pb-6 mb-6"
        )}>
          {title && (
            <div className="flex items-center space-x-3 mb-2">
              {icon && (
                <div className="text-blue-600 dark:text-blue-400 text-xl">
                  {icon}
                </div>
              )}
              <h2 className={cn(
                "font-semibold text-gray-900 dark:text-white",
                isMobile ? "text-lg" : "text-xl"
              )}>
                {title}
              </h2>
            </div>
          )}
          {description && (
            <p className={cn(
              "text-gray-600 dark:text-gray-400",
              isMobile ? "text-sm" : "text-base"
            )}>
              {description}
            </p>
          )}
        </div>
      )}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
};

const MobileFormField: React.FC<MobileFormFieldProps> = ({
  label,
  children,
  error,
  required,
  description,
  className,
}) => {
  const { isMobile } = useMobile();

  return (
    <div className={cn("space-y-2", className)}>
      <Label className={cn(
        "font-medium text-gray-900 dark:text-white",
        isMobile ? "text-sm" : "text-base"
      )}>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      
      {description && (
        <p className={cn(
          "text-gray-500 dark:text-gray-400",
          isMobile ? "text-xs" : "text-sm"
        )}>
          {description}
        </p>
      )}
      
      {children}
      
      {error && (
        <p className={cn(
          "text-red-600 dark:text-red-400 font-medium",
          isMobile ? "text-xs" : "text-sm"
        )}>
          {error}
        </p>
      )}
    </div>
  );
};

const MobileInput: React.FC<MobileInputProps> = ({
  label,
  error,
  required,
  description,
  icon,
  action,
  className,
  ...props
}) => {
  const { isMobile } = useMobile();

  return (
    <MobileFormField
      label={label}
      error={error}
      required={required}
      description={description}
    >
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {icon}
          </div>
        )}
        <Input
          className={cn(
            "transition-all duration-200",
            isMobile ? "h-12 text-base" : "h-10 text-sm",
            icon ? "pl-10" : "",
            action ? "pr-12" : "",
            error ? "border-red-300 focus:border-red-500 focus:ring-red-500" : "",
            className
          )}
          {...props}
        />
        {action && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {action}
          </div>
        )}
      </div>
    </MobileFormField>
  );
};

const MobileSelect: React.FC<MobileSelectProps> = ({
  label,
  options,
  error,
  required,
  description,
  placeholder,
  className,
  ...props
}) => {
  const { isMobile } = useMobile();

  return (
    <MobileFormField
      label={label}
      error={error}
      required={required}
      description={description}
    >
      <select
        className={cn(
          "w-full rounded-lg border border-gray-300 dark:border-gray-600",
          "bg-white dark:bg-gray-700 text-gray-900 dark:text-white",
          "focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20",
          "transition-all duration-200",
          isMobile ? "h-12 text-base px-4" : "h-10 text-sm px-3",
          error ? "border-red-300 focus:border-red-500 focus:ring-red-500/20" : "",
          className
        )}
        {...props}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </MobileFormField>
  );
};

const MobileTextarea: React.FC<MobileTextareaProps> = ({
  label,
  error,
  required,
  description,
  className,
  ...props
}) => {
  const { isMobile } = useMobile();

  return (
    <MobileFormField
      label={label}
      error={error}
      required={required}
      description={description}
    >
      <textarea
        className={cn(
          "w-full rounded-lg border border-gray-300 dark:border-gray-600",
          "bg-white dark:bg-gray-700 text-gray-900 dark:text-white",
          "focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20",
          "transition-all duration-200 resize-vertical",
          isMobile ? "min-h-[120px] text-base px-4 py-3" : "min-h-[100px] text-sm px-3 py-2",
          error ? "border-red-300 focus:border-red-500 focus:ring-red-500/20" : "",
          className
        )}
        {...props}
      />
    </MobileFormField>
  );
};

// Mobile-specific button group for form actions
interface MobileFormActionsProps {
  children: React.ReactNode;
  className?: string;
  layout?: 'horizontal' | 'vertical' | 'stacked';
}

const MobileFormActions: React.FC<MobileFormActionsProps> = ({
  children,
  className,
  layout = 'horizontal',
}) => {
  const { isMobile } = useMobile();

  const layoutClasses = {
    horizontal: "flex space-x-3",
    vertical: "flex flex-col space-y-3",
    stacked: isMobile ? "flex flex-col space-y-3" : "flex space-x-3",
  };

  return (
    <div className={cn(
      "pt-4 border-t border-gray-100 dark:border-gray-700",
      layoutClasses[layout],
      className
    )}>
      {children}
    </div>
  );
};

export {
  MobileForm,
  MobileFormSection,
  MobileFormField,
  MobileInput,
  MobileSelect,
  MobileTextarea,
  MobileFormActions,
};
