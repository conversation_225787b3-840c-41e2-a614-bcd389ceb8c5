/**
 * Mobile Layout Components for DeepTrade
 * 
 * Provides mobile-optimized layout patterns with proper spacing,
 * touch-friendly interactions, and responsive design.
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { useMobile } from '@/hooks/useResponsiveDesign';

interface MobileContainerProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

interface MobileHeaderProps {
  title: string;
  subtitle?: string;
  leftAction?: React.ReactNode;
  rightAction?: React.ReactNode;
  className?: string;
  sticky?: boolean;
}

interface MobileTabsProps {
  tabs: Array<{
    id: string;
    label: string;
    icon?: React.ReactNode;
    count?: number;
    disabled?: boolean;
  }>;
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
  variant?: 'default' | 'pills' | 'underline';
}

interface MobileGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

interface MobileStackProps {
  children: React.ReactNode;
  spacing?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  divider?: boolean;
}

const MobileContainer: React.FC<MobileContainerProps> = ({
  children,
  className,
  padding = 'md',
  maxWidth = 'full',
}) => {
  const { isMobile } = useMobile();

  const paddingClasses = {
    none: "",
    sm: isMobile ? "px-3 py-2" : "px-4 py-3",
    md: isMobile ? "px-4 py-4" : "px-6 py-6",
    lg: isMobile ? "px-6 py-6" : "px-8 py-8",
  };

  const maxWidthClasses = {
    sm: "max-w-sm",
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    full: "w-full",
  };

  return (
    <div className={cn(
      "mx-auto",
      maxWidthClasses[maxWidth],
      paddingClasses[padding],
      className
    )}>
      {children}
    </div>
  );
};

const MobileHeader: React.FC<MobileHeaderProps> = ({
  title,
  subtitle,
  leftAction,
  rightAction,
  className,
  sticky = false,
}) => {
  const { isMobile } = useMobile();

  return (
    <header className={cn(
      "bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",
      sticky ? "sticky top-0 z-30" : "",
      isMobile ? "px-4 py-4" : "px-6 py-6",
      className
    )}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          {leftAction && (
            <div className="flex-shrink-0">
              {leftAction}
            </div>
          )}
          <div className="flex-1 min-w-0">
            <h1 className={cn(
              "font-bold text-gray-900 dark:text-white truncate",
              isMobile ? "text-xl" : "text-2xl"
            )}>
              {title}
            </h1>
            {subtitle && (
              <p className={cn(
                "text-gray-600 dark:text-gray-400 truncate",
                isMobile ? "text-sm mt-1" : "text-base mt-2"
              )}>
                {subtitle}
              </p>
            )}
          </div>
        </div>
        {rightAction && (
          <div className="flex-shrink-0 ml-3">
            {rightAction}
          </div>
        )}
      </div>
    </header>
  );
};

const MobileTabs: React.FC<MobileTabsProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className,
  variant = 'default',
}) => {
  const { isMobile } = useMobile();

  const getTabClasses = (tab: any, isActive: boolean) => {
    const baseClasses = cn(
      "flex items-center justify-center space-x-2 font-medium transition-all duration-200",
      isMobile ? "px-4 py-3 text-sm min-h-[44px]" : "px-6 py-2 text-base",
      tab.disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
    );

    switch (variant) {
      case 'pills':
        return cn(
          baseClasses,
          "rounded-full",
          isActive
            ? "bg-blue-600 text-white shadow-sm"
            : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
        );
      case 'underline':
        return cn(
          baseClasses,
          "border-b-2 rounded-none",
          isActive
            ? "border-blue-600 text-blue-600 dark:text-blue-400"
            : "border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
        );
      default:
        return cn(
          baseClasses,
          "rounded-lg",
          isActive
            ? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
            : "text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-200"
        );
    }
  };

  return (
    <div className={cn(
      "flex",
      variant === 'underline' ? "border-b border-gray-200 dark:border-gray-700" : "space-x-1",
      isMobile ? "overflow-x-auto scrollbar-hide" : "",
      className
    )}>
      {tabs.map((tab) => {
        const isActive = activeTab === tab.id;
        return (
          <button
            key={tab.id}
            onClick={() => !tab.disabled && onTabChange(tab.id)}
            className={getTabClasses(tab, isActive)}
            disabled={tab.disabled}
          >
            {tab.icon && (
              <span className="text-lg">
                {tab.icon}
              </span>
            )}
            <span className="whitespace-nowrap">
              {tab.label}
            </span>
            {tab.count !== undefined && (
              <span className={cn(
                "rounded-full text-xs font-bold min-w-[20px] h-5 flex items-center justify-center",
                isActive
                  ? "bg-white/20 text-current"
                  : "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300"
              )}>
                {tab.count}
              </span>
            )}
          </button>
        );
      })}
    </div>
  );
};

const MobileGrid: React.FC<MobileGridProps> = ({
  children,
  columns = 2,
  gap = 'md',
  className,
}) => {
  const { isMobile } = useMobile();

  const gapClasses = {
    sm: "gap-2",
    md: "gap-4",
    lg: "gap-6",
  };

  const columnClasses = {
    1: "grid-cols-1",
    2: isMobile ? "grid-cols-1 sm:grid-cols-2" : "grid-cols-2",
    3: isMobile ? "grid-cols-1 sm:grid-cols-2 md:grid-cols-3" : "grid-cols-3",
    4: isMobile ? "grid-cols-2 sm:grid-cols-2 md:grid-cols-4" : "grid-cols-4",
  };

  return (
    <div className={cn(
      "grid",
      columnClasses[columns],
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  );
};

const MobileStack: React.FC<MobileStackProps> = ({
  children,
  spacing = 'md',
  className,
  divider = false,
}) => {
  const spacingClasses = {
    sm: "space-y-2",
    md: "space-y-4",
    lg: "space-y-6",
    xl: "space-y-8",
  };

  const childrenArray = React.Children.toArray(children);

  return (
    <div className={cn(
      divider ? "divide-y divide-gray-200 dark:divide-gray-700" : spacingClasses[spacing],
      className
    )}>
      {divider
        ? childrenArray.map((child, index) => (
            <div key={index} className={index > 0 ? "pt-4" : ""}>
              {child}
            </div>
          ))
        : children
      }
    </div>
  );
};

// Mobile-specific floating action button
interface MobileFABProps {
  onClick: () => void;
  icon: React.ReactNode;
  label?: string;
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const MobileFAB: React.FC<MobileFABProps> = ({
  onClick,
  icon,
  label,
  position = 'bottom-right',
  size = 'md',
  className,
}) => {
  const { isMobile } = useMobile();

  if (!isMobile) return null;

  const positionClasses = {
    'bottom-right': "bottom-6 right-6",
    'bottom-left': "bottom-6 left-6",
    'bottom-center': "bottom-6 left-1/2 transform -translate-x-1/2",
  };

  const sizeClasses = {
    sm: "w-12 h-12",
    md: "w-14 h-14",
    lg: "w-16 h-16",
  };

  return (
    <button
      onClick={onClick}
      className={cn(
        "fixed z-50 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg",
        "flex items-center justify-center transition-all duration-200",
        "active:scale-95 hover:shadow-xl",
        positionClasses[position],
        sizeClasses[size],
        className
      )}
      aria-label={label}
    >
      <div className="text-xl">
        {icon}
      </div>
    </button>
  );
};

export {
  MobileContainer,
  MobileHeader,
  MobileTabs,
  MobileGrid,
  MobileStack,
  MobileFAB,
};
