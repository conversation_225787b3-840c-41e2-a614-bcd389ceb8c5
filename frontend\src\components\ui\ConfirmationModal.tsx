import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { AlertTriangle, X } from 'lucide-react';
import { Button } from './Button';
import { Input } from './input';
import { Label } from './label';
import { cn } from '@/lib/utils';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: ConfirmationData) => void;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'danger' | 'warning' | 'info';
  requirePassword?: boolean;
  requireTypedConfirmation?: boolean;
  typedConfirmationText?: string;
  children?: React.ReactNode;
  isLoading?: boolean;
}

interface ConfirmationData {
  password?: string;
  typedConfirmation?: string;
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'danger',
  requirePassword = false,
  requireTypedConfirmation = false,
  typedConfirmationText = 'DELETE',
  children,
  isLoading = false,
}) => {
  const [password, setPassword] = useState('');
  const [typedConfirmation, setTypedConfirmation] = useState('');

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setPassword('');
      setTypedConfirmation('');
    }
  }, [isOpen]);

  const handleConfirm = () => {
    const data: ConfirmationData = {};
    
    if (requirePassword) {
      data.password = password;
    }
    
    if (requireTypedConfirmation) {
      data.typedConfirmation = typedConfirmation;
    }
    
    onConfirm(data);
  };

  const isConfirmDisabled = () => {
    if (isLoading) return true;
    
    if (requirePassword && !password.trim()) return true;
    
    if (requireTypedConfirmation && typedConfirmation !== typedConfirmationText) return true;
    
    return false;
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'danger':
        return {
          icon: 'text-red-600 dark:text-red-400',
          iconBg: 'bg-red-100 dark:bg-red-900/30',
          border: 'border-red-200 dark:border-red-800',
          confirmButton: 'destructive',
        };
      case 'warning':
        return {
          icon: 'text-yellow-600 dark:text-yellow-400',
          iconBg: 'bg-yellow-100 dark:bg-yellow-900/30',
          border: 'border-yellow-200 dark:border-yellow-800',
          confirmButton: 'default',
        };
      case 'info':
        return {
          icon: 'text-blue-600 dark:text-blue-400',
          iconBg: 'bg-blue-100 dark:bg-blue-900/30',
          border: 'border-blue-200 dark:border-blue-800',
          confirmButton: 'default',
        };
      default:
        return {
          icon: 'text-red-600 dark:text-red-400',
          iconBg: 'bg-red-100 dark:bg-red-900/30',
          border: 'border-red-200 dark:border-red-800',
          confirmButton: 'destructive',
        };
    }
  };

  const styles = getVariantStyles();

  if (!isOpen) return null;

  const modalContent = (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-start p-6 pb-4">
          <div className={cn('flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-4', styles.iconBg)}>
            <AlertTriangle className={cn('w-6 h-6', styles.icon)} />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              {title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {description}
            </p>
          </div>
          <button
            onClick={onClose}
            className="flex-shrink-0 ml-4 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
            disabled={isLoading}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="px-6 pb-4">
          {children && (
            <div className={cn('p-4 rounded-lg mb-4', styles.border, 'bg-gray-50 dark:bg-gray-700')}>
              {children}
            </div>
          )}

          {/* Password Input */}
          {requirePassword && (
            <div className="mb-4">
              <Label htmlFor="confirmation-password" required>
                Current Password
              </Label>
              <Input
                id="confirmation-password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your current password"
                disabled={isLoading}
                className="mt-1"
              />
            </div>
          )}

          {/* Typed Confirmation */}
          {requireTypedConfirmation && (
            <div className="mb-4">
              <Label htmlFor="typed-confirmation" required>
                Type "{typedConfirmationText}" to confirm
              </Label>
              <Input
                id="typed-confirmation"
                type="text"
                value={typedConfirmation}
                onChange={(e) => setTypedConfirmation(e.target.value)}
                placeholder={`Type ${typedConfirmationText} here`}
                disabled={isLoading}
                className="mt-1"
              />
              {typedConfirmation && typedConfirmation !== typedConfirmationText && (
                <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                  Please type "{typedConfirmationText}" exactly as shown
                </p>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 pt-0">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            {cancelText}
          </Button>
          <Button
            variant={styles.confirmButton as any}
            onClick={handleConfirm}
            disabled={isConfirmDisabled()}
          >
            {isLoading ? 'Processing...' : confirmText}
          </Button>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

export default ConfirmationModal;
