import React from 'react';
import { Check, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PasswordStrengthIndicatorProps {
  password: string;
  className?: string;
}

interface PasswordRequirement {
  label: string;
  test: (password: string) => boolean;
}

const passwordRequirements: PasswordRequirement[] = [
  {
    label: 'At least 8 characters',
    test: (password) => password.length >= 8,
  },
  {
    label: 'Contains uppercase letter',
    test: (password) => /[A-Z]/.test(password),
  },
  {
    label: 'Contains lowercase letter',
    test: (password) => /[a-z]/.test(password),
  },
  {
    label: 'Contains number',
    test: (password) => /\d/.test(password),
  },
  {
    label: 'Contains special character',
    test: (password) => /[!@#$%^&*(),.?":{}|<>]/.test(password),
  },
];

const calculatePasswordStrength = (password: string): number => {
  if (!password) return 0;
  
  let score = 0;
  const maxScore = passwordRequirements.length;
  
  passwordRequirements.forEach((requirement) => {
    if (requirement.test(password)) {
      score += 1;
    }
  });
  
  return (score / maxScore) * 100;
};

const getStrengthLabel = (strength: number): string => {
  if (strength === 0) return '';
  if (strength < 40) return 'Weak';
  if (strength < 60) return 'Fair';
  if (strength < 80) return 'Good';
  return 'Strong';
};

const getStrengthColor = (strength: number): string => {
  if (strength === 0) return 'bg-gray-200 dark:bg-gray-700';
  if (strength < 40) return 'bg-red-500';
  if (strength < 60) return 'bg-orange-500';
  if (strength < 80) return 'bg-yellow-500';
  return 'bg-green-500';
};

const getStrengthTextColor = (strength: number): string => {
  if (strength === 0) return 'text-gray-500 dark:text-gray-400';
  if (strength < 40) return 'text-red-600 dark:text-red-400';
  if (strength < 60) return 'text-orange-600 dark:text-orange-400';
  if (strength < 80) return 'text-yellow-600 dark:text-yellow-400';
  return 'text-green-600 dark:text-green-400';
};

export const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
  className,
}) => {
  const strength = calculatePasswordStrength(password);
  const strengthLabel = getStrengthLabel(strength);
  const strengthColor = getStrengthColor(strength);
  const strengthTextColor = getStrengthTextColor(strength);

  if (!password) {
    return null;
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Strength Meter */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Password Strength</span>
          {strengthLabel && (
            <span className={cn('text-sm font-medium', strengthTextColor)}>
              {strengthLabel}
            </span>
          )}
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className={cn('h-2 rounded-full transition-all duration-300', strengthColor)}
            style={{ width: `${strength}%` }}
          />
        </div>
      </div>

      {/* Requirements Checklist */}
      <div className="space-y-2">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Requirements:</span>
        <div className="space-y-1">
          {passwordRequirements.map((requirement, index) => {
            const isValid = requirement.test(password);
            return (
              <div key={index} className="flex items-center space-x-2">
                <div
                  className={cn(
                    'flex items-center justify-center w-4 h-4 rounded-full',
                    isValid
                      ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500'
                  )}
                >
                  {isValid ? (
                    <Check className="w-3 h-3" />
                  ) : (
                    <X className="w-3 h-3" />
                  )}
                </div>
                <span
                  className={cn(
                    'text-sm',
                    isValid ? 'text-green-600 dark:text-green-400' : 'text-gray-500 dark:text-gray-400'
                  )}
                >
                  {requirement.label}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default PasswordStrengthIndicator;
