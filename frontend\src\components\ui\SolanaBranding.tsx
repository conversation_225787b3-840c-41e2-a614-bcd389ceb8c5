import React from 'react';
import <PERSON>ana<PERSON>ogo from './SolanaLogo';
import { useTranslation } from '@/hooks/useTranslation';
import { useScreenSize } from '@/hooks/useScreenSize';

interface SolanaBrandingProps {
  className?: string;
  showText?: boolean;
  logoSize?: number;
  showTooltip?: boolean;
  forceShow?: boolean; // Force show even in mobile portrait mode
}

const SolanaBranding: React.FC<SolanaBrandingProps> = ({
  className = '',
  showText = true,
  logoSize = 20,
  showTooltip = true,
  forceShow = false
}) => {
  const { t } = useTranslation();
  const { isSmallMobile, isPortrait, isDesktop, isMediumMobile, isLandscape } = useScreenSize();

  // Don't render on small mobile devices in any orientation to prevent overlap (unless forceShow is true)
  if (isSmallMobile && !forceShow) {
    return null;
  }

  // Don't render on mobile portrait mode to save space (unless forceShow is true)
  if (!isDesktop && isPortrait && !forceShow) {
    return null;
  }

  // Hide text on mobile landscape to prevent horizontal scrolling
  const shouldShowText = showText && isDesktop;

  return (
    <div
      className={`flex items-center space-x-2 group cursor-default relative ${className}`}
      title={showTooltip ? t('wallet.solanaTooltip') : undefined}
    >
      {/* Solana Logo */}
      <div className="flex-shrink-0">
        <SolanaLogo
          size={logoSize}
          className="text-purple-600 dark:text-purple-400 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors duration-200"
        />
      </div>
      
      {/* Text - Conditionally shown based on screen size */}
      {shouldShowText && (
        <span className="text-xs font-medium text-gray-600 dark:text-gray-300 group-hover:text-gray-700 dark:group-hover:text-gray-200 whitespace-nowrap transition-colors duration-200">
          {t('wallet.solanaBlockchain')}
        </span>
      )}
    </div>
  );
};

export default SolanaBranding;
