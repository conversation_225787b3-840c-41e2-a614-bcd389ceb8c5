import React, { createContext, useContext, useCallback, useState, useEffect } from 'react';
import { useNFTVerification } from '@/hooks/useNFTVerification';
import { toastError } from '@/components/ui/use-toast';

interface TierStatus {
  tier_1?: boolean;
  tier_2?: boolean;
  tier_3?: boolean;
  profit_share_owed?: number;
}

interface NFTVerificationContextType {
  isVerifying: boolean;
  nftStatus: {
    owns_nft: boolean;
    is_expired: boolean;
  };
  tierStatus: TierStatus | null;
  refreshTierStatus: () => Promise<void>;
}

const NFTVerificationContext = createContext<NFTVerificationContextType | undefined>(undefined);

export const useNFTVerificationContext = () => {
  const context = useContext(NFTVerificationContext);
  if (context === undefined) {
    throw new Error('useNFTVerificationContext must be used within an NFTVerificationProvider');
  }
  return context;
};

interface NFTVerificationProviderProps {
  children: React.ReactNode;
}

export const NFTVerificationProvider: React.FC<NFTVerificationProviderProps> = ({ children }) => {

  const [tierStatus, setTierStatus] = useState<TierStatus | null>(null);
  const [isDowngrading, setIsDowngrading] = useState(false);
  const [hasShownNFTError, setHasShownNFTError] = useState(false);

  // Function to fetch tier status from API
  const fetchTierStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/trading/tier/status', {
        credentials: 'include',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });
      if (response.ok) {
        const data = await response.json();
        setTierStatus(data);
        return data;
      }
    } catch (error) {
      console.error('Error fetching tier status:', error);
    }
    return null;
  }, []);

  // Function to downgrade Tier 3 user to Tier 1 when NFT is lost
  const downgradeTier3User = useCallback(async () => {
    if (isDowngrading) return; // Prevent multiple simultaneous downgrades
    
    setIsDowngrading(true);
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/trading/tier/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ tier_1: true, tier_2: false, tier_3: false })
      });

      if (response.ok) {
        const data = await response.json();
        setTierStatus(data);
        
        toastError({
          title: 'Tier Downgraded to Starter',
          description: 'You have been automatically downgraded to Tier 1 because you no longer own the required NFT from the Capitol Chilax collection for Tier 3 access.'
        });
      } else {
        throw new Error('Failed to downgrade tier');
      }
    } catch (error) {
      console.error('Error downgrading tier:', error);
      toastError({
        title: 'Error',
        description: 'Failed to update tier status. Please refresh the page.'
      });
    } finally {
      setIsDowngrading(false);
    }
  }, [isDowngrading]);

  // Create stable callback for NFT error handling
  const handleNFTError = useCallback((error: Error) => {
    console.error('NFT verification error:', error);
    const isCurrentlyTier3 = tierStatus?.tier_3 === true;
    
    // Show error toast only once to avoid spam
    if (!hasShownNFTError && isCurrentlyTier3) {
      setHasShownNFTError(true);
      toastError({
        title: 'NFT Verification Error',
        description: 'Unable to verify NFT ownership. Please check your connection and refresh the page.'
      });
    }
  }, [hasShownNFTError, tierStatus?.tier_3]);

  // NFT verification hook - only enabled for Tier 3 users
  const isCurrentlyTier3 = tierStatus?.tier_3 === true;
  const { nftStatus, isCheckingNFT, error: nftError } = useNFTVerification({
    enabled: isCurrentlyTier3,
    onNFTLost: downgradeTier3User,
    onError: handleNFTError
  });

  // Reset error flag when tier changes or verification succeeds
  useEffect(() => {
    if (!isCurrentlyTier3 || (nftStatus.owns_nft && !nftError)) {
      setHasShownNFTError(false);
    }
  }, [isCurrentlyTier3, nftStatus.owns_nft, nftError]);

  // Fetch tier status on mount
  useEffect(() => {
    fetchTierStatus();
  }, [fetchTierStatus]);

  const value: NFTVerificationContextType = {
    isVerifying: isCheckingNFT || isDowngrading,
    nftStatus,
    tierStatus,
    refreshTierStatus: fetchTierStatus,
  };

  return (
    <NFTVerificationContext.Provider value={value}>
      {children}
    </NFTVerificationContext.Provider>
  );
};
