import { useMemo } from "react";
import type { FC, ReactNode } from "react";
import { ConnectionProvider, WalletProvider } from "@solana/wallet-adapter-react";
import { WalletAdapterNetwork } from "@solana/wallet-adapter-base";
import {
    PhantomWalletAdapter,
    SolflareWalletAdapter,
    TorusWalletAdapter,
} from "@solana/wallet-adapter-wallets";
import { WalletModalProvider } from "@solana/wallet-adapter-react-ui";

// Default styles that can be overridden by your app
import "@solana/wallet-adapter-react-ui/styles.css";

export const SolanaWalletContext: FC<{ children: ReactNode }> = ({ children }) => {
    // Using Solana mainnet with fallback RPC endpoints
    const network = WalletAdapterNetwork.Mainnet;
    const endpoint = useMemo(() => {
        // List of reliable RPC endpoints with fallbacks
        const endpoints = [
            'https://proportionate-proud-energy.solana-mainnet.quiknode.pro/84bea58adcd3938b105ed39bcba1bab29e4aba14',
            'https://solana-api.projectserum.com',
            'https://solana-mainnet.rpc.extrnode.com',
            'https://api.mainnet-beta.solana.com'  // Keep as last resort
        ];
        
        // For now, just return the first one (QuickNode)
        // In a production app, you might want to implement a fallback mechanism
        return endpoints[0];
    }, [network]);

    const wallets = useMemo(
        () => {
            // Create wallet instances with unique names
            const walletInstances = [
                new PhantomWalletAdapter(),
                new SolflareWalletAdapter({ network }),
                new TorusWalletAdapter(),
            ];

            // Create a map to track name occurrences
            const nameCount = new Map<string, number>();
            
            return walletInstances.map(wallet => {
                const baseName = wallet.name;
                const count = (nameCount.get(baseName) || 0) + 1;
                nameCount.set(baseName, count);
                
                // Only modify the name if it's a duplicate
                if (count > 1) {
                    // Create a proxy to intercept the name property
                    return new Proxy(wallet, {
                        get(target, prop) {
                            // Return the modified name for the 'name' property
                            if (prop === 'name') {
                                return `${baseName} (${count})`;
                            }
                            // For all other properties, return the original value
                            return (target as any)[prop];
                        }
                    });
                }
                
                return wallet;
            });
        },
        [network]
    );

    return (
        <ConnectionProvider endpoint={endpoint}>
            <WalletProvider wallets={wallets} autoConnect>
                <WalletModalProvider>
                    {children}
                </WalletModalProvider>
            </WalletProvider>
        </ConnectionProvider>
    );
};