import { useState, useEffect } from 'react';
import { Link, Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '../components/ui/Button';
import { useTheme } from '../components/theme-provider';
import { NFTVerificationProvider } from '../contexts/NFTVerificationContext';
import NavbarWalletButton from '../components/solana/NavbarWalletButton';
import SolanaBranding from '../components/ui/SolanaBranding';
import LanguageSelector from '../components/ui/LanguageSelector';
import { useTranslation } from '../hooks/useTranslation';
import { useMobile } from '../hooks/useResponsiveDesign';
import MobileTopBar from '../components/mobile/MobileTopBar';
import MobileSidebar from '../components/mobile/MobileSidebar';
import {
  LayoutDashboard,
  Wallet,
  Settings,
  LogOut as LogOutIcon,
  Menu as MenuIcon,
  X as XIcon,
  Sun,
  Moon,
  Users,
  HelpCircle,
  Trophy,
  Shield
} from 'lucide-react';

// Custom hook for responsive sizing
const useResponsiveSize = () => {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
  });

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const getLogoSize = () => {
    if (windowSize.width >= 1536) return { width: '32px', height: '32px' }; // 2xl
    if (windowSize.width >= 1280) return { width: '30px', height: '30px' }; // xl
    if (windowSize.width >= 1024) return { width: '28px', height: '28px' }; // lg
    if (windowSize.width >= 768) return { width: '26px', height: '26px' }; // md
    return { width: '24px', height: '24px' }; // Default for smaller screens
  };

  const getTextSize = () => {
    if (windowSize.width >= 1536) return '28px'; // 2xl
    if (windowSize.width >= 1280) return '26px'; // xl
    if (windowSize.width >= 1024) return '24px'; // lg
    if (windowSize.width >= 768) return '22px'; // md
    return '20px'; // Default for smaller screens
  };





  return { getLogoSize, getTextSize };
};

interface DashboardLayoutProps {
  children?: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, logout } = useAuth();
  useResponsiveSize(); // Using the hook for any side effects it might have
  const { theme, setTheme } = useTheme();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const [activePath, setActivePath] = useState('');
  const { isMobile } = useMobile();

  // Navigation items with translation keys
  const navigation = [
    { name: t('navigation.dashboard') || 'Dashboard', href: '/', icon: LayoutDashboard },
    { name: t('navigation.apiCredentials') || 'API Credentials', href: '/api-credentials', icon: Wallet },
    { name: t('navigation.tierManagement') || 'Tier Management', href: '/tier', icon: Trophy },
    { name: t('navigation.referrals') || 'Referrals', href: '/referral-dashboard', icon: Users },
    { name: t('navigation.accessSecurity') || 'Access & Security', href: '/access-security', icon: Shield },
    { name: t('navigation.settings') || 'Settings', href: '/settings', icon: Settings },
    { name: t('navigation.help') || 'Help & Support', href: '/help', icon: HelpCircle },
  ];



  useEffect(() => {
    setActivePath(location.pathname);
  }, [location]);

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Failed to log out', error);
    }
  };

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  // Removed getUserInitials as it's no longer needed

  return (
    <NFTVerificationProvider>
      <div className="min-h-screen bg-background text-foreground">
        {/* Mobile Navigation - Use MobileTopBar for mobile devices */}
        {isMobile ? (
          <MobileTopBar onMenuClick={() => setSidebarOpen(true)} />
        ) : (
          /* Desktop Top Navigation Bar */
          <header className="fixed top-0 left-0 right-0 z-50 w-full border-b bg-background/95 backdrop-blur">
            <div className="container flex h-14 sm:h-16 md:h-16 lg:h-16 items-center justify-between px-2 xxs:px-3 sm:px-4">
              {/* Left side - Logo and mobile menu button */}
              <div className="flex items-center space-x-2 sm:space-x-4 min-w-0 flex-shrink-0">
                <Button
                  variant="ghost"
                  size="icon"
                  className="lg:hidden p-1 xxs:p-1.5 sm:p-2"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                >
                  {sidebarOpen ? <XIcon className="h-4 w-4 xxs:h-5 xxs:w-5 sm:h-6 sm:w-6" /> : <MenuIcon className="h-4 w-4 xxs:h-5 xxs:w-5 sm:h-6 sm:w-6" />}
                </Button>
                <Link to="/" className="flex items-center space-x-2 min-w-0 flex-shrink-0">
                  {/* Logo - Hidden on iPhone SE portrait, visible on larger screens */}
                  <div className="hidden md:block">
                    <img
                      src="/icons/deeptrade.png"
                      alt="DeepTrade Logo"
                      className="h-11 w-auto flex-shrink-0 object-contain" />
                  </div>
                  <div className="md:hidden">
                    <img
                      src="/icons/deeptrade.png"
                      alt="DeepTrade Logo"
                      className="h-3 w-auto xxs:h-4 xs:h-5 flex-shrink-0 object-contain" />
                  </div>
                </Link>
              </div>

              {/* Right side - Solana branding, Wallet, Language, Theme toggle and user menu */}
              <div className="flex items-center space-x-1 xs:space-x-1.5 sm:space-x-2 md:space-x-4 flex-shrink-0">
                {/* Solana Blockchain Branding - Smart responsive display */}
                <SolanaBranding logoSize={20} />

                {/* Wallet Connect Button - Scaled down on mobile */}
                <div className="scale-75 xs:scale-85 sm:scale-90 md:scale-100 origin-right">
                  <NavbarWalletButton />
                </div>

                {/* Language Selector - Always visible but scaled */}
                <div className="scale-75 xs:scale-80 sm:scale-90 md:scale-100">
                  <LanguageSelector variant="navbar" />
                </div>

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleTheme}
                  className="rounded-full p-1 xs:p-1.5 sm:p-2 min-w-[28px] xs:min-w-[32px] sm:min-w-[36px]"
                  aria-label="Toggle theme"
                >
                  {theme === 'dark' ? <Sun className="h-3.5 w-3.5 xs:h-4 xs:w-4 sm:h-5 sm:w-5" /> : <Moon className="h-3.5 w-3.5 xs:h-4 xs:w-4 sm:h-5 sm:w-5" />}
                </Button>

                {user ? (
                  <div className="flex items-center space-x-2 sm:space-x-4">
                    {/* Username - Hidden on mobile screens */}
                    <div className="hidden md:flex items-center space-x-2">
                      <span className="text-sm font-medium">
                        {user.username || user.email?.split('@')[0] || 'User'}
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleLogout}
                      className="text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300 p-1 xs:p-1.5 sm:p-2 min-w-[28px] xs:min-w-[32px] sm:min-w-auto"
                    >
                      <LogOutIcon className="h-3.5 w-3.5 xs:h-4 xs:w-4 sm:mr-2" />
                      <span className="hidden sm:inline">{t('navigation.logout')}</span>
                    </Button>
                  </div>
                ) : (
                  <Button asChild variant="outline" className="ml-2">
                    <Link to="/login">Sign In</Link>
                  </Button>
                )}
              </div>
            </div>
          </header>
        )}

        <div className="flex flex-1">
        {/* Mobile sidebar - Use MobileSidebar component for mobile devices */}
        {isMobile ? (
          <MobileSidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
        ) : (
          /* Tablet sidebar for non-mobile, non-desktop devices */
          <div className="lg:hidden">
            <div className={`fixed inset-0 z-40 flex ${sidebarOpen ? 'block' : 'hidden'}`}>
              {sidebarOpen && (
                <div
                  className="fixed inset-0 bg-background/80 backdrop-blur-sm"
                  onClick={() => setSidebarOpen(false)}
                />
              )}
              <div
                className={`relative flex w-64 flex-1 flex-col bg-card shadow-lg transition-transform duration-300 ease-in-out ${
                  sidebarOpen ? 'translate-x-0' : '-translate-x-full'
                }`}
              >
                <div className="flex h-20 items-start justify-between border-b px-6 pt-4 pb-2">
                  <Link to="/" className="flex items-center space-x-2">
                    <img src="/icons/deeptrade.png" alt="DeepTrade Logo" className="h-10 w-10" />
                  </Link>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 mt-1"
                    onClick={() => setSidebarOpen(false)}
                  >
                    <XIcon className="h-5 w-5" />
                  </Button>
                </div>
                <nav className="flex-1 space-y-1 overflow-y-auto p-4">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      to={item.href}
                      className={`flex items-center rounded-md px-4 py-3 text-sm font-medium transition-colors ${
                        activePath === item.href
                          ? 'bg-primary/10 text-primary'
                          : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                      }`}
                      onClick={() => setSidebarOpen(false)}
                    >
                      <item.icon className="mr-3 h-5 w-5" />
                      {item.name}
                    </Link>
                  ))}
                </nav>
                {user && (
                  <div className="border-t p-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex-1 min-w-0">
                        <span className="text-sm font-medium">
                          {user.username || user.email?.split('@')[0] || 'User'}
                        </span>
                        <p className="truncate text-xs text-muted-foreground">{user.email}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Desktop sidebar */}
        <div className="hidden lg:fixed lg:top-16 lg:bottom-0 lg:left-0 lg:flex lg:w-64 lg:flex-col lg:z-40">
          <div className="flex min-h-0 flex-1 flex-col border-r bg-card">
            <nav className="flex-1 space-y-1 overflow-y-auto p-4 pt-6">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`flex items-center rounded-md px-4 py-3 text-sm font-medium transition-colors ${
                    activePath === item.href
                      ? 'bg-primary/10 text-primary'
                      : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                  }`}
                >
                  <item.icon className="mr-3 h-5 w-5" />
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>
        </div>

        {/* Main content */}
        <div className="flex flex-1 flex-col lg:pl-64">
          <main className="flex-1 pt-14 sm:pt-16 md:pt-16 lg:pt-16">
            <div className="py-6">
              <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
                {children || <Outlet />}
              </div>
            </div>
          </main>
        </div>
        </div>
      </div>
    </NFTVerificationProvider>
  );
}
