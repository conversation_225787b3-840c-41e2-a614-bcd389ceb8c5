import { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toastSuccess, toastError } from '@/components/ui/use-toast';
import { useTranslation } from '@/hooks/useTranslation';
import { TermsOfServiceModal } from '@/components/modals/TermsOfServiceModal';
import { PrivacyPolicyModal } from '@/components/modals/PrivacyPolicyModal';

export default function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const { login } = useAuth();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleGoogleLogin = async () => {
    try {
      setIsLoading(true);
      const redirect_uri = `${window.location.origin}/auth/callback`;
      console.log('Frontend redirect_uri:', redirect_uri);
      
      // Call the backend to get the Google OAuth URL
      const response = await fetch('http://localhost:5000/api/auth/login/google', {
        method: 'POST',
        credentials: 'include',  // Include cookies in the request
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          redirect_uri: redirect_uri
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to initiate Google login');
      }
      
      const data = await response.json();
      
      // Redirect to Google's OAuth page
      window.location.href = data.authorization_url;
      
    } catch (error) {
      console.error('Google login failed:', error);
      toastError({
        title: t('errors.generic'),
        description: error instanceof Error ? error.message : t('errors.generic'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      toastError({
        title: t('common.error'),
        description: t('auth.login.fillAllFields'),
      });
      return;
    }

    setIsLoading(true);
    try {
      const result = await login(email, password);

      // Only navigate to dashboard if 2FA is not required
      if (!result.requires2FA) {
        navigate('/');
        toastSuccess({
          title: t('common.success'),
          description: t('auth.login.loginSuccess'),
        });
      }
      // If 2FA is required, the login function already handled the navigation
    } catch (error) {
      console.error('Login failed:', error);
      toastError({
        title: t('auth.login.loginFailed'),
        description: error instanceof Error ? error.message : t('auth.login.loginFailedDescription'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">{t('auth.login.title')}</h1>
        <p className="text-sm text-muted-foreground">
          {t('auth.login.subtitle')}
        </p>
      </div>
      <div className="grid gap-6">
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="email">{t('auth.login.email')}</Label>
              <Input
                id="email"
                placeholder={t('auth.login.emailPlaceholder')}
                type="email"
                autoCapitalize="none"
                autoComplete="email"
                autoCorrect="off"
                disabled={isLoading}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <div className="flex items-center">
                <Label htmlFor="password">{t('auth.login.password')}</Label>
                <Link
                  to="/forgot-password"
                  className="ml-auto inline-block text-sm underline"
                >
                  {t('auth.login.forgotPassword')}
                </Link>
              </div>
              <Input
                id="password"
                placeholder={t('auth.login.passwordPlaceholder')}
                type="password"
                autoComplete="current-password"
                disabled={isLoading}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? t('common.loading') : t('auth.login.signIn')}
            </Button>
          </div>
        </form>
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              {t('auth.login.orContinueWith')}
            </span>
          </div>
        </div>
        <Button
          variant="outline"
          type="button"
          disabled={isLoading}
          onClick={handleGoogleLogin}
          className="w-full flex items-center justify-center"
        >
          <svg
            className="mr-2 h-4 w-4"
            aria-hidden="true"
            focusable="false"
            data-prefix="fab"
            data-icon="google"
            role="img"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
          >
            <path
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              fill="#4285F4"
            />
            <path
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              fill="#34A853"
            />
            <path
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l3.66-2.84z"
              fill="#FBBC05"
            />
            <path
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              fill="#EA4335"
            />
          </svg>
          {t('auth.login.googleSignIn')}
        </Button>
      </div>

      <div className="px-8 text-center text-sm text-muted-foreground space-y-2">
        <p>
          By signing in, you agree to our{' '}
          <button
            type="button"
            onClick={() => setShowTermsModal(true)}
            className="underline underline-offset-4 hover:text-primary text-sm"
          >
            Terms of Service
          </button>{' '}
          and{' '}
          <button
            type="button"
            onClick={() => setShowPrivacyModal(true)}
            className="underline underline-offset-4 hover:text-primary text-sm"
          >
            Privacy Policy
          </button>
          .
        </p>
        <p>
          {t('auth.login.noAccount')}{' '}
          <Link
            to="/register"
            className="underline underline-offset-4 hover:text-primary"
          >
            {t('auth.login.signUp')}
          </Link>
        </p>
      </div>

      {/* Terms of Service Modal */}
      <TermsOfServiceModal
        isOpen={showTermsModal}
        onClose={() => setShowTermsModal(false)}
      />

      {/* Privacy Policy Modal */}
      <PrivacyPolicyModal
        isOpen={showPrivacyModal}
        onClose={() => setShowPrivacyModal(false)}
      />
    </div>
  );
}
