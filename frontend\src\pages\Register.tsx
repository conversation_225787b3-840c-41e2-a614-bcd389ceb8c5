import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Register.tsx - Enhanced to redirect to Settings
 *
 * As requested, this page now serves as the entry point to the comprehensive
 * user account management system. It redirects authenticated users to the
 * Settings page where they can manage their account settings.
 */
export default function Register() {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (isAuthenticated) {
      // Redirect authenticated users to the settings page
      navigate('/settings');
    } else {
      // Redirect unauthenticated users to sign up
      navigate('/signup');
    }
  }, [isAuthenticated, navigate]);

  // This component just handles redirection
  return null;
}
