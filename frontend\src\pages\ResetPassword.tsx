import { useState, useEffect } from 'react';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PasswordStrengthIndicator } from '@/components/ui/PasswordStrengthIndicator';
import { toastSuccess, toastError } from '@/components/ui/use-toast';
import { useTranslation } from '@/hooks/useTranslation';

export default function ResetPassword() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const token = searchParams.get('token');
  
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(true);
  const [tokenValid, setTokenValid] = useState(false);
  const [userEmail, setUserEmail] = useState('');
  const [expiresAt, setExpiresAt] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);

  const { t } = useTranslation();

  // Password validation function (same as SignUp page)
  const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  useEffect(() => {
    if (!token) {
      navigate('/forgot-password');
      return;
    }

    validateToken();
  }, [token, navigate]);

  const validateToken = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/auth/validate-reset-token', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({ token })
      });

      const data = await response.json();

      if (response.ok && data.valid) {
        setTokenValid(true);
        setUserEmail(data.email);
        setExpiresAt(data.expires_at);
      } else {
        setTokenValid(false);
        toastError({
          title: 'Invalid Token',
          description: data.message || 'This password reset link is invalid or has expired.',
        });
      }
    } catch (error) {
      console.error('Token validation failed:', error);
      setTokenValid(false);
      toastError({
        title: 'Validation Failed',
        description: 'Failed to validate reset token. Please try again.',
      });
    } finally {
      setIsValidating(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!password || !confirmPassword) {
      toastError({
        title: t('common.error'),
        description: 'Please fill in all fields',
      });
      return;
    }

    if (password !== confirmPassword) {
      toastError({
        title: t('common.error'),
        description: 'Passwords do not match',
      });
      return;
    }

    // Validate password requirements (same as SignUp page)
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      toastError({
        title: 'Password Requirements Not Met',
        description: passwordValidation.errors.join('. '),
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/auth/reset-password', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({ 
          token,
          password 
        })
      });

      const data = await response.json();

      if (!response.ok) {
        if (data.errors && Array.isArray(data.errors)) {
          // Show password validation errors from backend
          toastError({
            title: 'Password Requirements Not Met',
            description: data.errors.join('. '),
          });
        } else {
          throw new Error(data.message || 'Failed to reset password');
        }
        return;
      }

      setIsSuccess(true);
      toastSuccess({
        title: 'Password Reset Successful',
        description: data.message,
      });

    } catch (error) {
      console.error('Password reset failed:', error);
      toastError({
        title: 'Reset Failed',
        description: error instanceof Error ? error.message : 'Failed to reset password',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isValidating) {
    return (
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Validating Reset Link</h1>
          <p className="text-sm text-muted-foreground">
            Please wait while we validate your password reset link...
          </p>
        </div>
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!tokenValid) {
    return (
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Invalid Reset Link</h1>
          <p className="text-sm text-muted-foreground">
            This password reset link is invalid or has expired.
          </p>
        </div>
        
        <div className="grid gap-6">
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg dark:bg-red-900/20 dark:border-red-800">
            <h3 className="font-medium text-red-900 dark:text-red-100 mb-2">Link Invalid</h3>
            <div className="text-sm text-red-800 dark:text-red-200">
              <p className="mb-1">• The reset link may have expired (links expire after 1 hour)</p>
              <p className="mb-1">• The link may have already been used</p>
              <p>• The link may be malformed or incomplete</p>
            </div>
          </div>
          
          <div className="flex flex-col space-y-2">
            <Button asChild className="w-full">
              <Link to="/forgot-password">Request New Reset Link</Link>
            </Button>
            
            <Button asChild variant="ghost" className="w-full">
              <Link to="/login">Back to Login</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (isSuccess) {
    return (
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Password Reset Successful</h1>
          <p className="text-sm text-muted-foreground">
            Your password has been successfully updated.
          </p>
        </div>
        
        <div className="grid gap-6">
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg dark:bg-green-900/20 dark:border-green-800">
            <h3 className="font-medium text-green-900 dark:text-green-100 mb-2">✓ Password Updated</h3>
            <p className="text-sm text-green-800 dark:text-green-200">
              You can now log in with your new password. For security, you've been logged out of all devices.
            </p>
          </div>
          
          <Button asChild className="w-full">
            <Link to="/login">Continue to Login</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">Reset Your Password</h1>
        <p className="text-sm text-muted-foreground">
          Enter your new password for <strong>{userEmail}</strong>
        </p>
      </div>
      
      <div className="grid gap-6">
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="password">New Password</Label>
              <Input
                id="password"
                placeholder="Enter your new password"
                type="password"
                autoComplete="new-password"
                disabled={isLoading}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
              {password && (
                <PasswordStrengthIndicator password={password} className="mt-2" />
              )}
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="confirmPassword">Confirm New Password</Label>
              <Input
                id="confirmPassword"
                placeholder="Confirm your new password"
                type="password"
                autoComplete="new-password"
                disabled={isLoading}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
              />
            </div>
            
            <Button disabled={isLoading} className="w-full">
              {isLoading ? 'Updating Password...' : 'Update Password'}
            </Button>
          </div>
        </form>
        
        <div className="flex flex-col space-y-2 text-center">
          <Button asChild variant="ghost" className="w-full">
            <Link to="/login">Back to Login</Link>
          </Button>
        </div>
      </div>
      
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
        <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Password Requirements</h3>
        <div className="text-sm text-blue-800 dark:text-blue-200">
          <p className="mb-1">• At least 8 characters long</p>
          <p className="mb-1">• Include uppercase and lowercase letters</p>
          <p className="mb-1">• Include at least one number</p>
          <p>• Include at least one special character (!@#$%^&*(),.?&quot;:&#123;&#125;|&lt;&gt;)</p>
        </div>
      </div>
      
      {expiresAt && (
        <div className="text-center text-sm text-muted-foreground">
          This reset link expires at {new Date(expiresAt).toLocaleString()}
        </div>
      )}
    </div>
  );
}
