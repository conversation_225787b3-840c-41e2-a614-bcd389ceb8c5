import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from "@/components/ui/card";
import { Copy, Users, DollarSign, TrendingUp, Wallet, Settings } from 'lucide-react';
import { toastSuccess, toastError } from "@/components/ui/use-toast";
import { useTranslation } from '@/hooks/useTranslation';
import { useMobile } from '@/hooks/useResponsiveDesign';

interface ReferralProfile {
  id: string;
  referral_code: string;
  is_active: boolean;
  solana_wallet_address: string | null;
  auto_payment_enabled: boolean;
  minimum_payout: number;
  total_referrals: number;
  active_referrals: number;
  total_earnings: number;
  total_paid: number;
  pending_payout: number;
  eligible_for_payout: boolean;
}

interface ReferralStats {
  period: string;
  statistics: {
    total_referrals: number;
    verified_referrals: number;
    active_referrals: number;
    verification_rate: number;
    total_earnings: number;
    paid_earnings: number;
    pending_earnings: number;
    current_tier: number;
    earning_rate: number;
    average_earning_per_referral: number;
  };
}

export default function ReferralDashboard() {
  const { t } = useTranslation();
  const { isMobile } = useMobile();
  const [profile, setProfile] = useState<ReferralProfile | null>(null);
  const [stats, setStats] = useState<ReferralStats | null>(null);
  const [referrals, setReferrals] = useState<any[]>([]);
  const [earnings, setEarnings] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [referralLink, setReferralLink] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [walletAddress, setWalletAddress] = useState('');
  const [minPayout, setMinPayout] = useState(10);
  const [autoPayment, setAutoPayment] = useState(true);

  useEffect(() => {
    fetchReferralData();
  }, []);

  const fetchReferralData = async () => {
    try {
      const token = localStorage.getItem('access_token');
      
      // Fetch profile
      const profileResponse = await fetch('/api/referrals/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (profileResponse.ok) {
        const profileData = await profileResponse.json();
        setProfile(profileData.profile);
        setReferrals(profileData.referrals);
        setReferralLink(profileData.referral_link);
        setWalletAddress(profileData.profile.solana_wallet_address || '');
        setMinPayout(Math.max(profileData.profile.minimum_payout || 10, 10));
        setAutoPayment(profileData.profile.auto_payment_enabled);
      }

      // Fetch statistics
      const statsResponse = await fetch('/api/referrals/statistics?period=30d', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }

      // Fetch recent earnings
      const earningsResponse = await fetch('/api/referrals/earnings?per_page=10', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (earningsResponse.ok) {
        const earningsData = await earningsResponse.json();
        setEarnings(earningsData.earnings);
      }

    } catch (error) {
      console.error('Error fetching referral data:', error);
      toastError({
        title: "Error",
        description: "Failed to load referral data"
      });
    } finally {
      setLoading(false);
    }
  };

  const copyReferralLink = () => {
    navigator.clipboard.writeText(referralLink);
    toastSuccess({
      title: "Copied!",
      description: "Referral link copied to clipboard",
    });
  };

  const generateNewCode = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/referrals/code/generate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setReferralLink(data.referral_link);
        toastSuccess({
          title: "Success",
          description: "New referral code generated",
        });
      }
    } catch (error) {
      toastError({
        title: "Error",
        description: "Failed to generate new code"
      });
    }
  };

  const updateProfile = async () => {
    // Validate minimum payout
    if (minPayout < 10) {
      toastError({
        title: "Invalid Minimum Payout",
        description: "Minimum payout must be at least $10.00"
      });
      return;
    }

    // Validate wallet address format (basic Solana address validation)
    if (walletAddress && (walletAddress.length < 32 || walletAddress.length > 44)) {
      toastError({
        title: "Invalid Wallet Address",
        description: "Please enter a valid Solana wallet address"
      });
      return;
    }

    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/referrals/profile', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          solana_wallet_address: walletAddress,
          minimum_payout: Math.max(minPayout, 10), // Ensure minimum is 10
          auto_payment_enabled: autoPayment
        })
      });

      if (response.ok) {
        toastSuccess({
          title: "Success",
          description: "Profile updated successfully",
        });
        setShowSettings(false);
        fetchReferralData();
      }
    } catch (error) {
      toastError({
        title: "Error",
        description: "Failed to update profile"
      });
    }
  };

  const requestPayout = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/referrals/payout/request', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        toastSuccess({
          title: "Success",
          description: "Payout request submitted",
        });
        fetchReferralData();
      } else {
        const error = await response.json();
        toastError({
          title: "Error",
          description: error.error || "Failed to request payout"
        });
      }
    } catch (error) {
      toastError({
        title: "Error",
        description: "Failed to request payout"
      });
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center">Loading referral dashboard...</div>
      </div>
    );
  }

  const tierRates = {
    1: { rate: 0.5, name: 'Tier 1' },
    2: { rate: 1.0, name: 'Tier 2' },
    3: { rate: 2.0, name: 'Tier 3' }
  };

  const currentTier = stats?.statistics.current_tier || 1;
  const currentRate = tierRates[currentTier as keyof typeof tierRates];

  // Note: Removed mobile redirect to ensure feature parity between desktop and mobile

  return (
    <div className={`${isMobile ? 'p-3 space-y-4' : 'p-6 space-y-6'} max-w-full overflow-x-hidden`}>
      <div className={`flex ${isMobile ? 'flex-col gap-3' : 'justify-between items-center'}`}>
        <h1 className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold`}>{t('referrals.title')}</h1>
        <Button
          variant="outline"
          size={isMobile ? "sm" : "sm"}
          onClick={() => setShowSettings(!showSettings)}
          className={isMobile ? 'self-start' : ''}
        >
          <Settings className="w-4 h-4 mr-2" />
          Settings
        </Button>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <Card>
          <CardContent className={`${isMobile ? 'p-3' : 'p-6'}`}>
            <h3 className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold ${isMobile ? 'mb-3' : 'mb-4'}`}>Referral Settings</h3>
            <div className={`${isMobile ? 'space-y-3' : 'space-y-4'}`}>
              <div>
                <label className={`block ${isMobile ? 'text-xs' : 'text-sm'} font-medium ${isMobile ? 'mb-1' : 'mb-2'}`}>Solana Wallet Address</label>
                <input
                  type="text"
                  value={walletAddress}
                  onChange={(e) => setWalletAddress(e.target.value)}
                  placeholder="Enter your Solana wallet address for payments"
                  className={`w-full ${isMobile ? 'p-2 text-sm' : 'p-2'} border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400`}
                />
                <div className={`mt-2 ${isMobile ? 'p-2' : 'p-3'} bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md`}>
                  <div className="flex items-start gap-2">
                    <span className={`text-red-600 dark:text-red-400 ${isMobile ? 'text-xs' : 'text-sm'} font-bold mt-0.5`}>⚠️</span>
                    <div className={`${isMobile ? 'text-xs' : 'text-sm'} text-red-700 dark:text-red-300`}>
                      <p className="font-semibold mb-1">Important Warning:</p>
                      <ul className="space-y-1">
                        <li>• Please ensure your Solana wallet address is correct</li>
                        <li>• Double-check the address before saving</li>
                        <li>• DeepTrade is NOT responsible for payments sent to wrong or invalid addresses</li>
                        <li>• Lost funds due to incorrect addresses cannot be recovered</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <label className={`block ${isMobile ? 'text-xs' : 'text-sm'} font-medium ${isMobile ? 'mb-1' : 'mb-2'}`}>Minimum Payout ($)</label>
                <input
                  type="number"
                  value={minPayout}
                  onChange={(e) => {
                    const value = Number(e.target.value);
                    if (value >= 10) {
                      setMinPayout(value);
                    }
                  }}
                  min="10"
                  max="1000"
                  className={`w-full ${isMobile ? 'p-2 text-sm' : 'p-2'} border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100`}
                />
                <div className={`mt-2 ${isMobile ? 'p-2' : 'p-3'} bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md`}>
                  <div className={`${isMobile ? 'text-xs' : 'text-sm'} text-blue-700 dark:text-blue-300`}>
                    <p className="font-semibold mb-1">Minimum Payout Requirements:</p>
                    <ul className="space-y-1">
                      <li>• Minimum payout amount: $10.00 USDT</li>
                      <li>• Solana network gas fees will be deducted from payout</li>
                      <li>• Estimated gas fee: ~0.001 SOL (~$0.10-$0.50)</li>
                      <li>• Payouts are processed in USDT on Solana network</li>
                    </ul>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={autoPayment}
                    onChange={(e) => setAutoPayment(e.target.checked)}
                    id="auto-payment"
                  />
                  <label htmlFor="auto-payment" className={`${isMobile ? 'text-xs' : 'text-sm'}`}>Enable automatic payments</label>
                </div>
                <p className={`${isMobile ? 'text-xs' : 'text-xs'} text-muted-foreground ${isMobile ? 'ml-4' : 'ml-6'}`}>
                  {autoPayment
                    ? "When enabled, you will automatically receive referral payments when they are due."
                    : "When disabled, you will not receive any referral payments until this is enabled again."}
                </p>
              </div>
              <div className={`flex ${isMobile ? 'flex-col gap-2' : 'gap-2'}`}>
                <Button onClick={updateProfile} size={isMobile ? "sm" : "default"} className={isMobile ? 'text-sm' : ''}>Save Settings</Button>
                <Button variant="outline" onClick={() => setShowSettings(false)} size={isMobile ? "sm" : "default"} className={isMobile ? 'text-sm' : ''}>Cancel</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Overview Cards */}
      <div className={`grid ${isMobile ? 'grid-cols-1 gap-3' : 'grid-cols-1 md:grid-cols-4 gap-4'}`}>
        <Card>
          <CardContent className={`${isMobile ? 'p-4' : 'p-6'}`}>
            <div className="flex items-center gap-2">
              <Users className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} text-blue-500`} />
              <h3 className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-muted-foreground`}>Total Referrals</h3>
            </div>
            <p className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold`}>{profile?.total_referrals || 0}</p>
            <p className="text-xs text-muted-foreground">
              {profile?.active_referrals || 0} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className={`${isMobile ? 'p-4' : 'p-6'}`}>
            <div className="flex items-center gap-2">
              <DollarSign className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} text-green-500`} />
              <h3 className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-muted-foreground`}>Total Earnings</h3>
            </div>
            <p className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold`}>${profile?.total_earnings.toFixed(2) || '0.00'}</p>
            <p className="text-xs text-muted-foreground">
              ${profile?.total_paid.toFixed(2) || '0.00'} paid
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className={`${isMobile ? 'p-4' : 'p-6'}`}>
            <div className="flex items-center gap-2">
              <Wallet className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} text-orange-500`} />
              <h3 className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-muted-foreground`}>Pending Payout</h3>
            </div>
            <p className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold`}>${profile?.pending_payout.toFixed(2) || '0.00'}</p>
            {profile?.eligible_for_payout && (
              <Button size="sm" className={`${isMobile ? 'mt-2 text-xs' : 'mt-2'}`} onClick={requestPayout}>
                Request Payout
              </Button>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className={`${isMobile ? 'p-4' : 'p-6'}`}>
            <div className="flex items-center gap-2">
              <TrendingUp className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} text-purple-500`} />
              <h3 className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-muted-foreground`}>Earning Rate</h3>
            </div>
            <p className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold`}>{currentRate.rate}%</p>
            <p className="text-xs text-muted-foreground">
              {currentRate.name}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Referral Link */}
      <Card>
        <CardContent className={`${isMobile ? 'p-4' : 'p-6'}`}>
          <h3 className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold ${isMobile ? 'mb-3' : 'mb-4'}`}>{t('referrals.referralLink')}</h3>
          <div className={`${isMobile ? 'flex flex-col gap-2' : 'flex gap-2'}`}>
            <input
              type="text"
              value={referralLink}
              readOnly
              className={`${isMobile ? 'w-full' : 'flex-1'} p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 ${isMobile ? 'text-sm' : ''}`}
            />
            <div className={`${isMobile ? 'flex gap-2' : 'contents'}`}>
              <Button onClick={copyReferralLink} size={isMobile ? "sm" : "default"} className={`${isMobile ? 'flex-1 text-xs' : ''}`}>
                <Copy className={`${isMobile ? 'w-3 h-3 mr-1' : 'w-4 h-4 mr-2'}`} />
                Copy
              </Button>
              <Button variant="outline" onClick={generateNewCode} size={isMobile ? "sm" : "default"} className={`${isMobile ? 'flex-1 text-xs' : ''}`}>
                Generate New
              </Button>
            </div>
          </div>
          <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground mt-2`}>
            Share this link to earn {currentRate.rate}% from your referrals' profit share payments.
          </p>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <div className={`grid grid-cols-1 ${isMobile ? 'gap-4' : 'lg:grid-cols-2 gap-6'}`}>
        {/* Recent Referrals */}
        <Card>
          <CardContent className={`${isMobile ? 'p-4' : 'p-6'}`}>
            <h3 className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold ${isMobile ? 'mb-3' : 'mb-4'}`}>Recent Referrals</h3>
            {referrals.length > 0 ? (
              <div className="space-y-3">
                {referrals.slice(0, 5).map((referral) => (
                  <div key={referral.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">{referral.referee_name}</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(referral.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <span className={`px-2 py-1 rounded text-xs ${
                        referral.is_verified 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {referral.is_verified ? 'Verified' : 'Pending'}
                      </span>
                      <p className="text-sm text-muted-foreground mt-1">
                        ${referral.total_earnings.toFixed(2)} earned
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">No referrals yet. Start sharing your link!</p>
            )}
          </CardContent>
        </Card>

        {/* Recent Earnings */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4">Recent Earnings</h3>
            {earnings.length > 0 ? (
              <div className="space-y-3">
                {earnings.slice(0, 5).map((earning) => (
                  <div key={earning.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">${earning.amount.toFixed(2)}</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(earning.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <span className={`px-2 py-1 rounded text-xs ${
                        earning.is_paid 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-orange-100 text-orange-800'
                      }`}>
                        {earning.is_paid ? 'Paid' : 'Pending'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">No earnings yet. Referrals need to start trading!</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
