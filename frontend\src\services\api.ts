// Main API service object
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';

interface RequestOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: string;
}

const api = {
  async request(endpoint: string, options: RequestOptions = {}) {
    const token = localStorage.getItem('access_token');
    
    const config: RequestInit = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      credentials: 'include',
      ...options,
    };

    if (options.body) {
      config.body = options.body;
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response;
  },

  async get(endpoint: string) {
    const response = await this.request(endpoint);
    return {
      data: await response.json()
    };
  },

  async post(endpoint: string, data?: any) {
    const response = await this.request(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
    return {
      data: await response.json()
    };
  },

  async put(endpoint: string, data?: any) {
    const response = await this.request(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
    return {
      data: await response.json()
    };
  },

  async delete(endpoint: string) {
    const response = await this.request(endpoint, {
      method: 'DELETE',
    });
    return {
      data: await response.json()
    };
  }
};

// Additional API functions - Direct fetch to avoid base URL issues
export const getAutoTradingNotifications = async () => {
  const token = localStorage.getItem('access_token');

  // Don't make API call if no token is available
  if (!token) {
    throw new Error('No authentication token available');
  }

  const response = await fetch('http://localhost:5000/api/trading/auto-trade/notifications', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response;
};

export const getAutoTradingStatus = async () => {
  const token = localStorage.getItem('access_token');

  const response = await fetch('http://localhost:5000/api/trading/auto-trade/status', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response;
};

export const toggleAutoTrading = async (enabled: boolean) => {
  const token = localStorage.getItem('access_token');

  const response = await fetch('http://localhost:5000/api/trading/auto-trade/toggle', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    },
    credentials: 'include',
    body: JSON.stringify({ enabled }),
  });

  const data = await response.json();

  if (!response.ok) {
    // Create a custom error with the server response data
    const error = new Error(data.message || `HTTP error! status: ${response.status}`);
    (error as any).errorCode = data.error_code;
    (error as any).currentBalance = data.current_balance;
    (error as any).minimumRequired = data.minimum_required;
    (error as any).responseData = data;
    throw error;
  }

  return data;
};

export default api;
