// NFT and Tier Management Service
import api from './api';

export interface NFTStatusResponse {
  owns_nft: boolean;
  nft_token_id?: string;
  nft_expiry_date?: string;
  days_remaining?: number;
  is_expired: boolean;
}

export interface TierStatusResponse {
  id: number;
  user_id: string;
  tier: number;
  profit_share_owed: number;
  payment_status: string;
  last_daily_payment: string | null;
  profit_share_rate: number;
  effective_profit_share_rate: number;
  updated_at: string;
  monthly_payment_status?: 'active' | 'expired' | 'pending';
  nft_ownership?: NFTStatusResponse;
}

export interface MonthlyPaymentRequest {
  tier: 2 | 3;
  amount: number;
  currency: string;
  paymentMethod: 'card' | 'crypto';
}

export const nftService = {
  // Get NFT ownership status
  async getNFTStatus(): Promise<NFTStatusResponse> {
    const response = await api.get('/api/nft/status');
    return response.data;
  },

  // Validate NFT ownership after purchase
  async validateNFTOwnership(tokenId?: string): Promise<NFTStatusResponse> {
    const response = await api.post('/api/nft/validate', { tokenId });
    return response.data;
  },

  // Get tier status with NFT and payment info
  async getTierStatus(): Promise<TierStatusResponse> {
    const response = await api.get('/api/trading/tier/status');
    return response.data;
  },

  // Update user tier
  async updateTier(tier: number): Promise<void> {
    await api.post('/api/trading/tier/update', { tier });
  },

  // Process monthly payment
  async processMonthlyPayment(payment: MonthlyPaymentRequest): Promise<any> {
    const response = await api.post('/api/payments/monthly', payment);
    return response.data;
  },

  // Get payment history
  async getPaymentHistory(): Promise<any[]> {
    const response = await api.get('/api/payments/history');
    return response.data.payments || [];
  },

  // Check eligibility for tier based on NFT and payment status
  checkTierEligibility(
    tier: number, 
    nftStatus: NFTStatusResponse | null, 
    tierStatus: TierStatusResponse | null
  ): { eligible: boolean; reason?: string } {
    if (tier === 1) {
      return { eligible: true };
    }

    // For Tier 2 and 3, need valid NFT and monthly payment
    const hasValidNFT = nftStatus?.owns_nft && !nftStatus?.is_expired;
    const hasActivePayment = tierStatus?.monthly_payment_status === 'active';

    if (!hasValidNFT && !hasActivePayment) {
      return { 
        eligible: false, 
        reason: "Requires NFT purchase and monthly payment" 
      };
    }
    
    if (!hasValidNFT) {
      return { 
        eligible: false, 
        reason: "Requires valid NFT (not expired)" 
      };
    }
    
    if (!hasActivePayment) {
      return { 
        eligible: false, 
        reason: "Requires active monthly payment" 
      };
    }

    return { eligible: true };
  },

  // Calculate days remaining for NFT
  calculateDaysRemaining(expiryDate: string): number {
    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  },

  // Format time remaining for display
  formatTimeRemaining(days: number): string {
    if (days < 0) return 'Expired';
    if (days === 0) return 'Expires today';
    if (days === 1) return '1 day remaining';
    if (days < 30) return `${days} days remaining`;
    
    const months = Math.floor(days / 30);
    const remainingDays = days % 30;
    
    if (months === 1 && remainingDays === 0) return '1 month remaining';
    if (remainingDays === 0) return `${months} months remaining`;
    return `${months}m ${remainingDays}d remaining`;
  }
};