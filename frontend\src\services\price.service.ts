/**
 * Centralized Price Service for DeepTrade Frontend
 * Handles real-time price updates via Server-Sent Events (SSE)
 */

export interface PriceData {
  symbol: string;
  price: number;
  timestamp: number;
  last_updated: string;
  change_24h?: number;
  change_percent_24h?: number;
  high_24h?: number;
  low_24h?: number;
  volume_24h?: number;
}

export interface PriceUpdate {
  type: 'connected' | 'price_update' | 'heartbeat' | 'error';
  symbol?: string;
  data?: PriceData;
  current_prices?: { [symbol: string]: PriceData };
  timestamp: string;
  message?: string;
}

export type PriceUpdateCallback = (symbol: string, priceData: PriceData, previousPrice?: number) => void;

export class PriceService {
  private eventSource: EventSource | null = null;
  private subscribers: Map<string, PriceUpdateCallback[]> = new Map();
  private currentPrices: Map<string, PriceData> = new Map();
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second

  constructor() {}

  /**
   * Start the price streaming connection
   */
  connect(): void {
    if (this.isConnected) {
      return;
    }

    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error('No auth token found for price streaming');
        return;
      }

      // Close existing connection if any
      this.disconnect();

      this.eventSource = new EventSource(`/api/realtime/price-stream?token=${token}`);

      this.eventSource.onopen = () => {
        console.log('Price streaming connection established');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;
      };

      this.eventSource.onmessage = (event) => {
        try {
          const update: PriceUpdate = JSON.parse(event.data);
          this.handlePriceUpdate(update);
        } catch (error) {
          console.error('Error parsing price update:', error);
        }
      };

      this.eventSource.onerror = (error) => {
        console.error('Price streaming error:', error);
        this.isConnected = false;
        
        // Attempt to reconnect with exponential backoff
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++;
          const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
          
          console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
          
          setTimeout(() => {
            this.connect();
          }, delay);
        } else {
          console.error('Max reconnection attempts reached. Price streaming disabled.');
        }
      };

    } catch (error) {
      console.error('Error establishing price streaming connection:', error);
    }
  }

  /**
   * Disconnect from price streaming
   */
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.isConnected = false;
  }

  /**
   * Handle incoming price updates
   */
  private handlePriceUpdate(update: PriceUpdate): void {
    switch (update.type) {
      case 'connected':
        console.log('Price streaming connected');
        // Load initial prices
        if (update.current_prices) {
          Object.entries(update.current_prices).forEach(([symbol, priceData]) => {
            this.currentPrices.set(symbol, priceData);
          });
        }
        break;

      case 'price_update':
        if (update.symbol && update.data) {
          const previousPrice = this.currentPrices.get(update.symbol)?.price;
          this.currentPrices.set(update.symbol, update.data);
          this.notifySubscribers(update.symbol, update.data, previousPrice);
        }
        break;

      case 'heartbeat':
        // Keep connection alive
        break;

      case 'error':
        console.error('Price streaming error:', update.message);
        break;

      default:
        console.warn('Unknown price update type:', update.type);
    }
  }

  /**
   * Notify subscribers of price updates
   */
  private notifySubscribers(symbol: string, priceData: PriceData, previousPrice?: number): void {
    const callbacks = this.subscribers.get(symbol) || [];
    callbacks.forEach(callback => {
      try {
        callback(symbol, priceData, previousPrice);
      } catch (error) {
        console.error('Error in price update callback:', error);
      }
    });
  }

  /**
   * Subscribe to price updates for a specific symbol
   */
  subscribe(symbol: string, callback: PriceUpdateCallback): () => void {
    const upperSymbol = symbol.toUpperCase();
    
    if (!this.subscribers.has(upperSymbol)) {
      this.subscribers.set(upperSymbol, []);
    }
    
    this.subscribers.get(upperSymbol)!.push(callback);
    
    // Start connection if not already connected
    if (!this.isConnected) {
      this.connect();
    }

    // Send current price if available
    const currentPrice = this.currentPrices.get(upperSymbol);
    if (currentPrice) {
      setTimeout(() => callback(upperSymbol, currentPrice), 0);
    }

    // Return unsubscribe function
    return () => {
      const callbacks = this.subscribers.get(upperSymbol);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
        
        // Remove symbol if no more subscribers
        if (callbacks.length === 0) {
          this.subscribers.delete(upperSymbol);
        }
      }

      // Disconnect if no more subscribers
      if (this.subscribers.size === 0) {
        this.disconnect();
      }
    };
  }

  /**
   * Get current price for a symbol
   */
  getCurrentPrice(symbol: string): PriceData | null {
    return this.currentPrices.get(symbol.toUpperCase()) || null;
  }

  /**
   * Get all current prices
   */
  getAllPrices(): { [symbol: string]: PriceData } {
    const prices: { [symbol: string]: PriceData } = {};
    this.currentPrices.forEach((priceData, symbol) => {
      prices[symbol] = priceData;
    });
    return prices;
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  /**
   * Force reconnection
   */
  reconnect(): void {
    this.disconnect();
    this.reconnectAttempts = 0;
    this.connect();
  }
}

// Global price service instance
export const priceService = new PriceService();
