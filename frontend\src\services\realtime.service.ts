/**
 * Real-time service for handling Server-Sent Events (SSE) connections
 */

export interface RealTimeData {
  timestamp: string;
  balance?: number;
  tier_status?: {
    tier: number;
    profit_share_owed: number;
    payment_status: string;
    effective_profit_share_rate: number;
  };
  app_positions?: any[];
  external_positions?: any[];
  position_count?: {
    app: number;
    external: number;
  };
}

export class RealTimeService {
  private eventSource: EventSource | null = null;
  private listeners: ((data: RealTimeData) => void)[] = [];
  private isConnected = false;

  constructor() {}

  /**
   * Start the real-time connection
   */
  connect(): void {
    if (this.isConnected) {
      return;
    }

    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error('No auth token found for real-time connection');
        return;
      }

      this.eventSource = new EventSource('/api/realtime/stream', {
        withCredentials: true
      });

      this.eventSource.onopen = () => {
        console.log('Real-time connection established');
        this.isConnected = true;
      };

      this.eventSource.onmessage = (event) => {
        try {
          const data: RealTimeData = JSON.parse(event.data);
          this.notifyListeners(data);
        } catch (error) {
          console.error('Error parsing real-time data:', error);
        }
      };

      this.eventSource.onerror = (error) => {
        console.error('Real-time connection error:', error);
        this.isConnected = false;
        
        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          if (!this.isConnected) {
            this.reconnect();
          }
        }, 5000);
      };

    } catch (error) {
      console.error('Failed to establish real-time connection:', error);
    }
  }

  /**
   * Disconnect the real-time connection
   */
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.isConnected = false;
  }

  /**
   * Reconnect the real-time connection
   */
  reconnect(): void {
    this.disconnect();
    setTimeout(() => this.connect(), 1000);
  }

  /**
   * Subscribe to real-time updates
   */
  subscribe(listener: (data: RealTimeData) => void): () => void {
    this.listeners.push(listener);
    
    // Start connection if not already connected
    if (!this.isConnected) {
      this.connect();
    }

    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }

      // Disconnect if no more listeners
      if (this.listeners.length === 0) {
        this.disconnect();
      }
    };
  }

  /**
   * Get current connection status
   */
  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  /**
   * Get a snapshot of current data
   */
  async getSnapshot(): Promise<RealTimeData | null> {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/realtime/snapshot', {
        credentials: 'include',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        return await response.json();
      } else {
        console.error('Failed to fetch snapshot');
        return null;
      }
    } catch (error) {
      console.error('Error fetching snapshot:', error);
      return null;
    }
  }

  /**
   * Notify all listeners of new data
   */
  private notifyListeners(data: RealTimeData): void {
    this.listeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('Error in real-time listener:', error);
      }
    });
  }
}

// Export a singleton instance
export const realTimeService = new RealTimeService();