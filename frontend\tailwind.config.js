/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      screens: {
        'xxs': '320px',
        'xs': '475px',
        'portrait': { 'raw': '(orientation: portrait)' },
        'landscape': { 'raw': '(orientation: landscape)' },
        'iphone-se': { 'raw': '(max-width: 375px)' },
        'iphone-se-landscape': { 'raw': '(max-width: 667px) and (orientation: landscape)' },
      },
      borderColor: {
        DEFAULT: 'hsl(var(--border) / 1)',
      },
      colors: {
        border: 'hsl(var(--border) / 1)',
        input: 'hsl(var(--input) / 1)',
        ring: 'hsl(var(--ring) / 1)',
        background: 'hsl(var(--background) / 1)',
        foreground: 'hsl(var(--foreground) / 1)',
        primary: {
          DEFAULT: 'hsl(var(--primary) / 1)',
          foreground: 'hsl(var(--primary-foreground) / 1)',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary) / 1)',
          foreground: 'hsl(var(--secondary-foreground) / 1)',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive) / 1)',
          foreground: 'hsl(var(--destructive-foreground) / 1)',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted) / 1)',
          foreground: 'hsl(var(--muted-foreground) / 1)',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent) / 1)',
          foreground: 'hsl(var(--accent-foreground) / 1)',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover) / 1)',
          foreground: 'hsl(var(--popover-foreground) / 1)',
        },
        card: {
          DEFAULT: 'hsl(var(--card) / 1)',
          foreground: 'hsl(var(--card-foreground) / 1)',
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}