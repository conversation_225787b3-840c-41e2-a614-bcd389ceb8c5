import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { fileURLToPath } from 'url';
import spaRouting from './vite-plugin-spa-routing';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    spaRouting() // Add our custom SPA routing plugin
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  optimizeDeps: {
    include: ['@radix-ui/react-slot', 'class-variance-authority', 'clsx', 'tailwind-merge'],
  },
  server: {
    port: 5173,
    open: true,
    // Handle SPA fallback for client-side routing
    fs: {
      strict: false,
    },
    // Proxy API requests to the backend
    proxy: {
      '^/api/.*': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false,
        ws: true,
      },
    },
  },
  // This is important for client-side routing in production
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'index.html'),
      },
    },
  },
  // This ensures that the server always returns the index.html for any route
  // when in production mode
  appType: 'spa'
});
