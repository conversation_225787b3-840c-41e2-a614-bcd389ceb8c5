2025-07-18 12:18:02 ERROR: Error fetching futures klines for BTCUSDT: 451 Client Error:  for url: https://fapi.binance.com/fapi/v1/klines?symbol=BTCUSDT&interval=1h&limit=1000 [in C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade\backend\app\services\market_data.py:91]
2025-07-18 12:18:02 WARNING: Insufficient futures data for BTCUSDT, trying spot API [in C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade\backend\app\services\market_data.py:415]
2025-07-18 12:18:02 ERROR: Error fetching current price for BTCUSDT: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT [in C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade\backend\app\services\market_data.py:203]
2025-07-18 12:18:02 WARNING: Price service is already running [in C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade\backend\app\services\price_service.py:159]
2025-07-18 12:18:02 WARNING: Price service is already running [in C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade\backend\app\services\price_service.py:159]
2025-07-18 12:18:02 WARNING: Forecast service is already running [in C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade\backend\app\services\forecast_service.py:30]
2025-07-18 12:18:02 WARNING: Forecast service is already running [in C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade\backend\app\services\forecast_service.py:30]
2025-07-18 12:29:34 ERROR: Failed to send payday warning <NAME_EMAIL>: LocalizedEmailService.render_template() got multiple values for argument 'language' [in C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade\backend\app\services\localized_email_service.py:196]
2025-07-27 16:55:25 ERROR: Error creating default admin: The current Flask app is not registered with this 'SQLAlchemy' instance. Did you forget to call 'init_app', or did you create multiple 'SQLAlchemy' instances? [in C:\Users\<USER>\OneDrive\Desktop\DeepTrade\DeepTrade\backend\app\__init__.py:139]
