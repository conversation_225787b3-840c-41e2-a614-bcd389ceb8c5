#!/usr/bin/env python3
"""
ML Timeframe Analysis Script
Analyzes the performance of the current ML model across different timeframes
to validate whether 1H is indeed the most precise timeframe.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import requests
import time
from datetime import datetime, timedelta
import hmac
import hashlib
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# Binance API configuration
BASE_URL = 'https://fapi.binance.com'
API_KEY = os.getenv('BINANCE_API_KEY', '')
API_SECRET = os.getenv('BINANCE_SECRET_KEY', '')

def fetch_binance_data(symbol: str, interval: str, limit: int = 1000) -> pd.DataFrame:
    """Fetch historical data from Binance"""
    try:
        endpoint = '/fapi/v1/klines'
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        response = requests.get(BASE_URL + endpoint, params=params)
        data = response.json()
        
        if isinstance(data, list) and len(data) > 0:
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert to proper data types
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df = df.set_index('timestamp')
            
            return df[['open', 'high', 'low', 'close', 'volume']]
        else:
            print(f"Error fetching data: {data}")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"Error fetching Binance data: {e}")
        return pd.DataFrame()

def prepare_ml_data(data: pd.DataFrame, time_steps: int) -> Tuple[np.ndarray, np.ndarray]:
    """Prepare data for ML model training (same as current system)"""
    scaler = RobustScaler()
    close_prices = np.array(data['close']).reshape(-1, 1)
    scaled_data = scaler.fit_transform(close_prices)
    
    X, y = [], []
    for i in range(len(scaled_data) - time_steps):
        X.append(scaled_data[i:(i + time_steps)].flatten())
        y.append(scaled_data[i + time_steps][0])
    
    return np.array(X), np.array(y), scaler

def train_ensemble_model(X_train: np.ndarray, y_train: np.ndarray, X_test: np.ndarray, y_test: np.ndarray) -> Dict:
    """Train the ensemble model (same as current system)"""
    # Initialize models with same parameters as current system
    rf_model = RandomForestRegressor(n_estimators=200, random_state=42, n_jobs=-1)
    gb_model = GradientBoostingRegressor(n_estimators=100, learning_rate=0.1, random_state=42)
    lr_model = LinearRegression(n_jobs=-1)
    
    # Train models
    models = [rf_model, gb_model, lr_model]
    for model in models:
        model.fit(X_train, y_train)
    
    # Calculate model weights based on validation performance
    rf_pred_val = rf_model.predict(X_test)
    gb_pred_val = gb_model.predict(X_test)
    lr_pred_val = lr_model.predict(X_test)
    
    rf_score = 1 / (mean_squared_error(y_test, rf_pred_val) + 1e-6)
    gb_score = 1 / (mean_squared_error(y_test, gb_pred_val) + 1e-6)
    lr_score = 1 / (mean_squared_error(y_test, lr_pred_val) + 1e-6)
    
    total_score = rf_score + gb_score + lr_score
    rf_weight = rf_score / total_score
    gb_weight = gb_score / total_score
    lr_weight = lr_score / total_score
    
    # Calculate ensemble predictions
    ensemble_pred = (rf_weight * rf_pred_val + gb_weight * gb_pred_val + lr_weight * lr_pred_val)
    
    # Calculate metrics
    mse = mean_squared_error(y_test, ensemble_pred)
    mae = mean_absolute_error(y_test, ensemble_pred)
    r2 = r2_score(y_test, ensemble_pred)
    
    return {
        'models': {'rf': rf_model, 'gb': gb_model, 'lr': lr_model},
        'weights': {'rf': rf_weight, 'gb': gb_weight, 'lr': lr_weight},
        'metrics': {
            'mse': mse,
            'mae': mae,
            'r2_score': r2,
            'rmse': np.sqrt(mse)
        },
        'predictions': ensemble_pred,
        'actual': y_test
    }

def analyze_timeframe_performance(symbol: str = 'BTCUSDT') -> Dict:
    """Analyze ML model performance across different timeframes"""
    
    # Timeframes to test (same intervals as Binance API)
    timeframes = {
        '15m': {'interval': '15m', 'time_steps': 288, 'name': '15 Minutes'},
        '1h': {'interval': '1h', 'time_steps': 288, 'name': '1 Hour'},
        '4h': {'interval': '4h', 'time_steps': 72, 'name': '4 Hours'},
        '1d': {'interval': '1d', 'time_steps': 30, 'name': '1 Day'}
    }
    
    results = {}
    
    print("=" * 80)
    print("🔍 ANALYZING ML MODEL PERFORMANCE ACROSS TIMEFRAMES")
    print("=" * 80)
    print(f"Symbol: {symbol}")
    print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    for tf_key, tf_config in timeframes.items():
        print(f"📊 Testing {tf_config['name']} ({tf_key}) timeframe...")
        
        try:
            # Fetch data for this timeframe
            data = fetch_binance_data(symbol, tf_config['interval'], limit=1000)
            
            if data.empty or len(data) < tf_config['time_steps'] + 100:
                print(f"❌ Insufficient data for {tf_key}: {len(data)} records")
                continue
            
            # Prepare ML data
            X, y, scaler = prepare_ml_data(data, tf_config['time_steps'])
            
            if len(X) < 50:
                print(f"❌ Insufficient samples for {tf_key}: {len(X)} samples")
                continue
            
            # Split data (80% train, 20% test)
            split_ratio = 0.8
            split_index = max(1, int(split_ratio * len(X)))
            X_train, X_test = X[:split_index], X[split_index:]
            y_train, y_test = y[:split_index], y[split_index:]
            
            # Train ensemble model
            model_results = train_ensemble_model(X_train, y_train, X_test, y_test)
            
            # Calculate additional metrics
            price_data = data['close'].values
            actual_prices = price_data[-len(y_test):]
            predicted_scaled = model_results['predictions']
            
            # Convert back to actual prices for better interpretation
            predicted_prices = scaler.inverse_transform(predicted_scaled.reshape(-1, 1)).flatten()
            actual_test_prices = scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
            
            # Calculate price-based metrics
            price_mse = mean_squared_error(actual_test_prices, predicted_prices)
            price_mae = mean_absolute_error(actual_test_prices, predicted_prices)
            price_mape = np.mean(np.abs((actual_test_prices - predicted_prices) / actual_test_prices)) * 100
            
            # Direction accuracy (most important for trading)
            actual_directions = np.diff(actual_test_prices) > 0
            predicted_directions = np.diff(predicted_prices) > 0
            direction_accuracy = np.mean(actual_directions == predicted_directions) * 100
            
            results[tf_key] = {
                'timeframe': tf_config['name'],
                'data_points': len(data),
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'model_weights': model_results['weights'],
                'scaled_metrics': model_results['metrics'],
                'price_metrics': {
                    'mse': price_mse,
                    'mae': price_mae,
                    'rmse': np.sqrt(price_mse),
                    'mape': price_mape,
                    'direction_accuracy': direction_accuracy
                },
                'data_quality': {
                    'time_steps': tf_config['time_steps'],
                    'data_coverage_days': len(data) * {'15m': 0.25, '1h': 1, '4h': 4, '1d': 24}[tf_key] / 24,
                    'avg_price': np.mean(actual_test_prices),
                    'price_volatility': np.std(actual_test_prices)
                }
            }
            
            print(f"✅ {tf_config['name']} Results:")
            print(f"   📈 Direction Accuracy: {direction_accuracy:.2f}%")
            print(f"   📊 MAPE: {price_mape:.4f}%")
            print(f"   🎯 R² Score: {model_results['metrics']['r2']:.4f}")
            print(f"   📉 RMSE: ${np.sqrt(price_mse):.2f}")
            print()
            
        except Exception as e:
            print(f"❌ Error analyzing {tf_key}: {str(e)}")
            continue
    
    return results

def generate_analysis_report(results: Dict) -> None:
    """Generate comprehensive analysis report"""
    
    print("=" * 80)
    print("📋 COMPREHENSIVE TIMEFRAME ANALYSIS REPORT")
    print("=" * 80)
    
    if not results:
        print("❌ No results to analyze. Check data availability and API connection.")
        return
    
    # Sort by direction accuracy (most important for trading)
    sorted_results = sorted(results.items(), 
                          key=lambda x: x[1]['price_metrics']['direction_accuracy'], 
                          reverse=True)
    
    print("\n🏆 RANKING BY DIRECTION ACCURACY (Most Important for Trading):")
    print("-" * 60)
    for i, (tf_key, data) in enumerate(sorted_results, 1):
        direction_acc = data['price_metrics']['direction_accuracy']
        mape = data['price_metrics']['mape']
        r2 = data['scaled_metrics']['r2_score']
        
        print(f"{i}. {data['timeframe']:12} | Direction: {direction_acc:6.2f}% | MAPE: {mape:7.4f}% | R²: {r2:6.4f}")
    
    print("\n📊 DETAILED METRICS COMPARISON:")
    print("-" * 80)
    print(f"{'Timeframe':<12} {'Dir.Acc':<8} {'MAPE':<8} {'R²':<8} {'RMSE':<10} {'Samples':<8}")
    print("-" * 80)
    
    for tf_key, data in sorted_results:
        direction_acc = data['price_metrics']['direction_accuracy']
        mape = data['price_metrics']['mape']
        r2 = data['scaled_metrics']['r2_score']
        rmse = data['price_metrics']['rmse']
        samples = data['test_samples']
        
        print(f"{data['timeframe']:<12} {direction_acc:6.2f}% {mape:7.4f}% {r2:7.4f} ${rmse:8.2f} {samples:7d}")
    
    # Analysis conclusions
    best_tf = sorted_results[0]
    print(f"\n🎯 ANALYSIS CONCLUSIONS:")
    print("-" * 40)
    print(f"Best Performing Timeframe: {best_tf[1]['timeframe']} ({best_tf[0]})")
    print(f"Direction Accuracy: {best_tf[1]['price_metrics']['direction_accuracy']:.2f}%")
    print(f"Price Prediction Error (MAPE): {best_tf[1]['price_metrics']['mape']:.4f}%")
    
    # Check if 1H is indeed the best
    h1_performance = results.get('1h', {})
    if h1_performance:
        h1_direction_acc = h1_performance['price_metrics']['direction_accuracy']
        best_direction_acc = best_tf[1]['price_metrics']['direction_accuracy']
        
        if best_tf[0] == '1h':
            print(f"\n✅ VALIDATION: 1H timeframe IS the most precise!")
            print(f"   Your observation is CORRECT.")
        else:
            diff = best_direction_acc - h1_direction_acc
            print(f"\n⚠️  VALIDATION: 1H timeframe is NOT the most precise.")
            print(f"   {best_tf[1]['timeframe']} outperforms 1H by {diff:.2f}% in direction accuracy.")
            print(f"   However, the difference might not be statistically significant.")

if __name__ == "__main__":
    print("Starting ML Timeframe Analysis...")
    
    # Run the analysis
    results = analyze_timeframe_performance('BTCUSDT')
    
    # Generate report
    generate_analysis_report(results)
    
    print(f"\n🏁 Analysis completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
