#!/usr/bin/env python3
"""
Test the fixed 2FA reset endpoint
"""
import requests
import json
import time

def test_2fa_endpoint():
    """Test the 2FA reset endpoint with fixed admin credentials"""
    
    # Wait a moment for rate limiting to reset
    print('⏳ Waiting for rate limit to reset...')
    time.sleep(10)
    
    BASE_URL = 'http://127.0.0.1:5000'
    ADMIN_USERNAME = 'test_admin'
    ADMIN_PASSWORD = 'admin123'
    
    print(f'🔐 Testing admin login: {ADMIN_USERNAME}')
    
    try:
        # Get admin token
        response = requests.post(f'{BASE_URL}/api/admin/login', json={
            'username': ADMIN_USERNAME,
            'password': ADMIN_PASSWORD
        })
        
        print(f'Login Status: {response.status_code}')
        
        if response.status_code == 200:
            token = response.json()['access_token']
            print('✅ Admin login successful!')
            
            # Test the 2FA endpoint
            print('\n🔍 Testing 2FA reset request endpoint...')
            request_id = '2fedf66a-16c4-497c-80f5-38b40f84a333'
            
            endpoint_response = requests.get(
                f'{BASE_URL}/api/admin/2fa-reset-requests/{request_id}',
                headers={'Authorization': f'Bearer {token}'}
            )
            
            print(f'2FA Endpoint Status: {endpoint_response.status_code}')
            if endpoint_response.status_code == 200:
                print('✅ 2FA endpoint working!')
                data = endpoint_response.json()
                print(f'Response keys: {list(data.keys())}')
                print(f'Request ID: {data.get("id")}')
                print(f'User Email: {data.get("email_provided")}')
                print(f'Status: {data.get("status")}')
                print(f'Risk Level: {data.get("risk_level")}')
                
                # Check if verification data exists
                if 'verification_data' in data:
                    print('✅ Verification data included')
                    verification = data['verification_data']
                    if 'user_actual_info' in verification:
                        actual_info = verification['user_actual_info']
                        print(f'Actual user email: {actual_info.get("email")}')
                        print(f'Actual user name: {actual_info.get("full_name")}')
                else:
                    print('❌ No verification data found')
                    
                return True
            else:
                print(f'❌ 2FA endpoint failed: {endpoint_response.text}')
                return False
                
        else:
            print(f'❌ Admin login failed: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ Error: {e}')
        return False

if __name__ == "__main__":
    print("🚀 Testing fixed 2FA reset endpoint...")
    success = test_2fa_endpoint()
    
    if success:
        print("\n🎉 SUCCESS! The 2FA reset endpoint is now working!")
        print("You can now use the 'View' button in the admin dashboard.")
    else:
        print("\n❌ The endpoint is still not working.")
