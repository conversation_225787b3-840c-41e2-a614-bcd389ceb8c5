#!/usr/bin/env python3
"""
Test script for 2FA Reset functionality
Tests the complete flow from request submission to admin dashboard display
"""

import requests
import json
import sys
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"
TEST_EMAIL = "<EMAIL>"
TEST_USER_DATA = {
    "email": TEST_EMAIL,
    "full_name": "Test User 2FA",
    "reason": "Lost phone device during testing",
    "last_login_date": "January 15, 2025",
    "account_creation_date": "December 2024",
    "recent_activity_description": "Recent trading activity on BTC/USDT pair",
    "security_question_1": "When was your last successful trade happened?",
    "security_answer_1": "January 10, 2025",
    "security_question_2": "What tier are you currently on (1, 2, or 3)?",
    "security_answer_2": "Tier 2",
    "security_question_3": "What exchange do you primarily use for trading?",
    "security_answer_3": "Binance"
}

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_step(step_num, description):
    """Print a formatted step"""
    print(f"\n[STEP {step_num}] {description}")
    print("-" * 40)

def test_api_endpoint(url, method="GET", data=None, headers=None):
    """Test an API endpoint and return response"""
    try:
        if method == "POST":
            response = requests.post(url, json=data, headers=headers)
        else:
            response = requests.get(url, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2)}")
            return response, response_data
        except:
            print(f"Response Text: {response.text}")
            return response, response.text
            
    except Exception as e:
        print(f"ERROR: {str(e)}")
        return None, None

def check_database_direct():
    """Check database directly for 2FA reset requests"""
    print_step("DB", "Checking database directly")
    
    try:
        # Try to import and check database
        import sqlite3
        import os
        
        # Look for database file
        db_paths = [
            "backend/instance/deeptrade.db",
            "instance/deeptrade.db",
            "deeptrade.db"
        ]
        
        db_path = None
        for path in db_paths:
            if os.path.exists(path):
                db_path = path
                break
        
        if not db_path:
            print("Database file not found in expected locations")
            return
        
        print(f"Found database at: {db_path}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='twofa_reset_requests';")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("Table 'twofa_reset_requests' does not exist!")
            # Show all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"Available tables: {[t[0] for t in tables]}")
        else:
            print("Table 'twofa_reset_requests' exists")
            
            # Get table schema
            cursor.execute("PRAGMA table_info(twofa_reset_requests);")
            schema = cursor.fetchall()
            print("Table schema:")
            for col in schema:
                print(f"  {col[1]} ({col[2]})")
            
            # Get all records
            cursor.execute("SELECT * FROM twofa_reset_requests ORDER BY created_at DESC;")
            records = cursor.fetchall()
            print(f"\nFound {len(records)} records:")
            for record in records:
                print(f"  {record}")
        
        conn.close()
        
    except ImportError:
        print("sqlite3 not available for direct database check")
    except Exception as e:
        print(f"Database check error: {str(e)}")

def main():
    """Main test function"""
    print_header("2FA Reset Functionality Test")
    print(f"Testing at: {BASE_URL}")
    print(f"Test email: {TEST_EMAIL}")
    print(f"Timestamp: {datetime.now()}")
    
    # Step 1: Test server connectivity
    print_step(1, "Testing server connectivity")
    response, data = test_api_endpoint(f"{BASE_URL}/")
    if not response or response.status_code >= 500:
        print("❌ Server not accessible!")
        return
    else:
        print("✅ Server is running")
    
    # Step 2: Submit 2FA reset request
    print_step(2, "Submitting 2FA reset request")
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    response, data = test_api_endpoint(
        f"{BASE_URL}/api/auth/2fa/reset-request",
        method="POST",
        data=TEST_USER_DATA,
        headers=headers
    )
    
    if not response:
        print("❌ Failed to submit request!")
        return
    
    if response.status_code == 200:
        print("✅ Request submitted successfully!")
        request_id = data.get('request_id') if isinstance(data, dict) else None
        print(f"Request ID: {request_id}")
    else:
        print(f"❌ Request failed with status {response.status_code}")
        print(f"Error: {data}")
    
    # Step 3: Check admin API endpoint
    print_step(3, "Checking admin 2FA reset requests endpoint")
    response, data = test_api_endpoint(f"{BASE_URL}/api/admin/2fa-reset-requests")
    
    if response and response.status_code == 200:
        print("✅ Admin endpoint accessible!")
        if isinstance(data, dict) and 'requests' in data:
            requests_list = data['requests']
            print(f"Found {len(requests_list)} requests in admin API")
            for req in requests_list:
                if req.get('email') == TEST_EMAIL:
                    print(f"✅ Found our test request: {req}")
                    break
            else:
                print("❌ Our test request not found in admin API")
        else:
            print(f"❌ Unexpected response format: {data}")
    else:
        print(f"❌ Admin endpoint failed: {response.status_code if response else 'No response'}")
    
    # Step 4: Check database directly
    check_database_direct()
    
    # Step 5: Test admin dashboard page
    print_step(4, "Testing admin dashboard access")
    try:
        response = requests.get(f"{BASE_URL}/admin/dashboard")
        print(f"Admin dashboard status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Admin dashboard accessible")
            # Check if 2FA reset section is present
            if "2FA Reset Requests" in response.text:
                print("✅ 2FA Reset section found in dashboard")
            else:
                print("❌ 2FA Reset section not found in dashboard")
        else:
            print(f"❌ Admin dashboard not accessible: {response.status_code}")
    except Exception as e:
        print(f"❌ Admin dashboard test failed: {str(e)}")
    
    # Step 6: Summary
    print_header("TEST SUMMARY")
    print("1. Check if backend server is running")
    print("2. Check if database table 'twofa_reset_requests' exists")
    print("3. Check if admin API endpoint '/api/admin/2fa-reset-requests' works")
    print("4. Check if admin dashboard displays the requests")
    print("5. Check browser network tab for any JavaScript errors")
    
    print("\n🔍 DEBUGGING TIPS:")
    print("- Check backend logs for any errors during request processing")
    print("- Verify database connection and table creation")
    print("- Check if admin authentication is required")
    print("- Inspect browser developer tools for frontend errors")

if __name__ == "__main__":
    main()
