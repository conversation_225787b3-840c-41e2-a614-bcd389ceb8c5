#!/usr/bin/env python3
"""
Test script to fix admin credentials and test the 2FA reset endpoint
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app import create_app
from backend.app.models.admin import AdminUser
from backend.app import db
from werkzeug.security import generate_password_hash
import requests
import json

def fix_admin_credentials():
    """Fix admin credentials"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔧 Fixing admin credentials...")
            
            # Check if super admin exists
            super_admin = AdminUser.query.filter_by(username='admin').first()
            
            if super_admin:
                print(f"Found existing super admin: {super_admin.username}")
                # Update password to admin123
                super_admin.password_hash = generate_password_hash('admin123')
                super_admin.is_super_admin = True
                super_admin.is_active = True
                db.session.commit()
                print("✅ Updated super admin password to 'admin123'")
            else:
                print("Creating new super admin...")
                super_admin = AdminUser(
                    username='admin',
                    password='admin123',
                    is_super_admin=True
                )
                super_admin.is_active = True
                db.session.add(super_admin)
                db.session.commit()
                print("✅ Created new super admin: admin/admin123")
                
            return True
            
        except Exception as e:
            print(f"❌ Error fixing admin credentials: {e}")
            return False

def test_admin_endpoint():
    """Test the admin 2FA reset endpoint"""
    BASE_URL = 'http://127.0.0.1:5000'
    ADMIN_USERNAME = 'admin'
    ADMIN_PASSWORD = 'admin123'
    
    try:
        # Get admin token
        print("\n🔐 Testing admin login...")
        response = requests.post(f'{BASE_URL}/api/admin/login', json={
            'username': ADMIN_USERNAME,
            'password': ADMIN_PASSWORD
        })
        
        if response.status_code == 200:
            token = response.json()['access_token']
            print('✅ Admin login successful')
            
            # Test the failing endpoint
            print("\n🔍 Testing 2FA reset request endpoint...")
            request_id = '2fedf66a-16c4-497c-80f5-38b40f84a333'  # From our test
            response = requests.get(f'{BASE_URL}/api/admin/2fa-reset-requests/{request_id}', 
                                   headers={'Authorization': f'Bearer {token}'})
            
            print(f'Status Code: {response.status_code}')
            if response.status_code == 200:
                print('✅ Endpoint working!')
                data = response.json()
                print(f"Request details: {json.dumps(data, indent=2)}")
            else:
                print(f'❌ Endpoint failed: {response.text}')
                
        else:
            print(f'❌ Admin login failed: {response.status_code}')
            print(f'Response: {response.text}')
            
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")

if __name__ == "__main__":
    print("🚀 Starting admin endpoint test...")
    
    # Fix admin credentials first
    if fix_admin_credentials():
        # Test the endpoint
        test_admin_endpoint()
    else:
        print("❌ Failed to fix admin credentials")
