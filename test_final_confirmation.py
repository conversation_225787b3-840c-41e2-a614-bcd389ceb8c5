#!/usr/bin/env python3
"""
Final confirmation test for 2FA reset view button
"""
import requests
import json

def test_2fa_view():
    BASE_URL = 'http://127.0.0.1:5000'
    ADMIN_USERNAME = 'admin'
    ADMIN_PASSWORD = '12345678'

    print('🔐 Testing admin login and 2FA view...')

    # Get admin token
    response = requests.post(f'{BASE_URL}/api/admin/login', json={
        'username': ADMIN_USERNAME,
        'password': ADMIN_PASSWORD
    })

    if response.status_code == 200:
        token = response.json()['access_token']
        print('✅ Admin login successful!')
        
        # Get 2FA requests list
        list_response = requests.get(
            f'{BASE_URL}/api/admin/2fa-reset-requests',
            headers={'Authorization': f'Bearer {token}'}
        )
        
        if list_response.status_code == 200:
            requests_list = list_response.json().get('requests', [])
            if requests_list:
                request_id = requests_list[0]['id']
                
                # Test view endpoint
                view_response = requests.get(
                    f'{BASE_URL}/api/admin/2fa-reset-requests/{request_id}',
                    headers={'Authorization': f'Bearer {token}'}
                )
                
                if view_response.status_code == 200:
                    print('🎉 SUCCESS! 2FA reset view button is working!')
                    data = view_response.json()
                    print(f'✅ Request ID: {data.get("id")}')
                    print(f'✅ User: {data.get("email_provided")}')
                    print(f'✅ Status: {data.get("status")}')
                    print(f'✅ Verification data: {"verification_data" in data}')
                    return True
                else:
                    print(f'❌ View failed: {view_response.status_code}')
                    return False
            else:
                print('❌ No requests found')
                return False
        else:
            print(f'❌ List failed: {list_response.status_code}')
            return False
    else:
        print(f'❌ Login failed: {response.status_code}')
        return False

if __name__ == "__main__":
    success = test_2fa_view()
    if success:
        print("\n🎯 READY FOR BROWSER TESTING!")
        print("The admin dashboard 'View' button should now work.")
    else:
        print("\n❌ Still not working.")
