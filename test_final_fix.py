#!/usr/bin/env python3
"""
Test the final fix for 2FA reset view button
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app import create_app
from backend.app import db
import requests
import json
import time

def clear_rate_limiting():
    """Clear rate limiting from database"""
    app = create_app()
    
    with app.app_context():
        try:
            # Clear IP rate limiting records
            from backend.app.models.ip_tracking import IPRateLimit
            IPRateLimit.query.delete()
            db.session.commit()
            print("✅ Cleared IP rate limiting records")
            return True
        except Exception as e:
            print(f"❌ Error clearing rate limits: {e}")
            return False

def test_view_endpoint():
    """Test the view endpoint after clearing rate limits"""
    
    BASE_URL = 'http://127.0.0.1:5000'
    ADMIN_USERNAME = 'admin'
    ADMIN_PASSWORD = '12345678'
    
    print(f'🔐 Testing admin login: {ADMIN_USERNAME}')
    
    try:
        # Get admin token
        response = requests.post(f'{BASE_URL}/api/admin/login', json={
            'username': ADMIN_USERNAME,
            'password': ADMIN_PASSWORD
        })
        
        print(f'Login Status: {response.status_code}')
        
        if response.status_code == 200:
            token = response.json()['access_token']
            print('✅ Admin login successful!')
            
            # Get list of 2FA reset requests
            print('\n📋 Getting list of 2FA reset requests...')
            list_response = requests.get(
                f'{BASE_URL}/api/admin/2fa-reset-requests',
                headers={'Authorization': f'Bearer {token}'}
            )
            
            print(f'List Status: {list_response.status_code}')
            if list_response.status_code == 200:
                list_data = list_response.json()
                requests_list = list_data.get('requests', [])
                print(f'Found {len(requests_list)} requests')
                
                if requests_list:
                    # Use the first request ID
                    request_id = requests_list[0]['id']
                    print(f'Testing with request ID: {request_id}')
                    
                    # Test the view endpoint
                    print('\n🔍 Testing 2FA reset request view endpoint...')
                    view_response = requests.get(
                        f'{BASE_URL}/api/admin/2fa-reset-requests/{request_id}',
                        headers={'Authorization': f'Bearer {token}'}
                    )
                    
                    print(f'View Endpoint Status: {view_response.status_code}')
                    if view_response.status_code == 200:
                        print('🎉 SUCCESS! View endpoint is working!')
                        data = view_response.json()
                        
                        print(f'\n📊 Response Summary:')
                        print(f'  Request ID: {data.get("id")}')
                        print(f'  User Email: {data.get("email_provided")}')
                        print(f'  Status: {data.get("status")}')
                        print(f'  Risk Level: {data.get("risk_level")}')
                        
                        # Check verification data
                        if 'verification_data' in data:
                            print('  ✅ Verification data included')
                            verification = data['verification_data']
                            if 'user_actual_info' in verification:
                                actual_info = verification['user_actual_info']
                                print(f'  Actual user email: {actual_info.get("email")}')
                                print(f'  Actual user name: {actual_info.get("full_name")}')
                        else:
                            print('  ❌ No verification data found')
                            
                        return True
                    else:
                        print(f'❌ View endpoint failed: {view_response.text}')
                        
                        # Try to parse the error
                        try:
                            error_data = view_response.json()
                            print(f'Error details: {json.dumps(error_data, indent=2)}')
                        except:
                            print(f'Raw error response: {view_response.text}')
                        
                        return False
                else:
                    print('❌ No 2FA reset requests found to test with')
                    return False
            else:
                print(f'❌ Failed to get requests list: {list_response.text}')
                return False
                
        else:
            print(f'❌ Admin login failed: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Testing final fix for 2FA reset view button...")
    print("=" * 60)
    
    # Clear rate limiting first
    print("🧹 Clearing rate limiting...")
    if clear_rate_limiting():
        print("✅ Rate limiting cleared")
        
        # Wait a moment
        time.sleep(2)
        
        # Test the endpoint
        success = test_view_endpoint()
        
        print("=" * 60)
        if success:
            print("🎉 SUCCESS! The view button functionality is now working!")
            print("You can now use the admin dashboard to view 2FA reset requests.")
        else:
            print("❌ FAILED! The view button is still not working.")
    else:
        print("❌ Failed to clear rate limiting")
