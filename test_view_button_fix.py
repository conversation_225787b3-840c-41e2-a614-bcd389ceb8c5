#!/usr/bin/env python3
"""
Test the 2FA reset view button functionality with correct admin credentials
"""
import requests
import json
import time

def test_admin_login_and_view():
    """Test admin login and 2FA view endpoint"""
    
    BASE_URL = 'http://127.0.0.1:5000'
    ADMIN_USERNAME = 'admin'
    ADMIN_PASSWORD = '12345678'
    
    print(f'🔐 Testing admin login: {ADMIN_USERNAME}')
    
    try:
        # Get admin token
        response = requests.post(f'{BASE_URL}/api/admin/login', json={
            'username': ADMIN_USERNAME,
            'password': ADMIN_PASSWORD
        })
        
        print(f'Login Status: {response.status_code}')
        
        if response.status_code == 200:
            token = response.json()['access_token']
            print('✅ Admin login successful!')
            
            # First, get the list of 2FA reset requests to find a valid ID
            print('\n📋 Getting list of 2FA reset requests...')
            list_response = requests.get(
                f'{BASE_URL}/api/admin/2fa-reset-requests',
                headers={'Authorization': f'Bearer {token}'}
            )
            
            print(f'List Status: {list_response.status_code}')
            if list_response.status_code == 200:
                list_data = list_response.json()
                requests_list = list_data.get('requests', [])
                print(f'Found {len(requests_list)} requests')
                
                if requests_list:
                    # Use the first request ID
                    request_id = requests_list[0]['id']
                    print(f'Testing with request ID: {request_id}')
                    
                    # Test the view endpoint
                    print('\n🔍 Testing 2FA reset request view endpoint...')
                    view_response = requests.get(
                        f'{BASE_URL}/api/admin/2fa-reset-requests/{request_id}',
                        headers={'Authorization': f'Bearer {token}'}
                    )
                    
                    print(f'View Endpoint Status: {view_response.status_code}')
                    if view_response.status_code == 200:
                        print('✅ View endpoint working!')
                        data = view_response.json()
                        print(f'Response keys: {list(data.keys())}')
                        print(f'Request ID: {data.get("id")}')
                        print(f'User Email: {data.get("email_provided")}')
                        print(f'Status: {data.get("status")}')
                        print(f'Risk Level: {data.get("risk_level")}')
                        
                        # Check verification data
                        if 'verification_data' in data:
                            print('✅ Verification data included')
                        else:
                            print('❌ No verification data found')
                            
                        return True
                    else:
                        print(f'❌ View endpoint failed: {view_response.text}')
                        
                        # Try to parse the error
                        try:
                            error_data = view_response.json()
                            print(f'Error details: {json.dumps(error_data, indent=2)}')
                        except:
                            print(f'Raw error response: {view_response.text}')
                        
                        return False
                else:
                    print('❌ No 2FA reset requests found to test with')
                    return False
            else:
                print(f'❌ Failed to get requests list: {list_response.text}')
                return False
                
        else:
            print(f'❌ Admin login failed: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Testing 2FA reset view button functionality...")
    print("=" * 60)
    
    success = test_admin_login_and_view()
    
    print("=" * 60)
    if success:
        print("🎉 SUCCESS! The view button functionality is working!")
        print("The admin dashboard should now work properly.")
    else:
        print("❌ FAILED! The view button is still not working.")
        print("Need to investigate further.")
