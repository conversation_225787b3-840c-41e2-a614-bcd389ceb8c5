#!/usr/bin/env python3
"""
Ultra High Accuracy ML System for 90%+ Direction Accuracy
Focus on high-confidence signals with market regime detection
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.cluster import KMeans
import requests
import time
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# Binance API configuration
BASE_URL = 'https://fapi.binance.com'

class MarketRegimeDetector:
    """Detect different market regimes for regime-specific models"""
    
    def __init__(self):
        self.regime_model = KMeans(n_clusters=4, random_state=42)
        self.regime_names = {0: 'Bull_Trending', 1: 'Bear_Trending', 2: 'Sideways_Low_Vol', 3: 'Sideways_High_Vol'}
    
    def detect_regimes(self, df: pd.DataFrame) -> pd.Series:
        """Detect market regimes based on volatility and trend"""
        close = df['close']
        
        # Calculate regime features
        returns = close.pct_change(24)  # 24-hour returns
        volatility = returns.rolling(168).std()  # 7-day volatility (168 hours)
        trend = (close / close.rolling(168).mean() - 1)  # 7-day trend
        volume_trend = (df['volume'] / df['volume'].rolling(168).mean() - 1)
        
        # Create regime features matrix
        regime_features = pd.DataFrame({
            'returns': returns,
            'volatility': volatility,
            'trend': trend,
            'volume_trend': volume_trend
        }).fillna(0)
        
        # Fit regime model
        regimes = self.regime_model.fit_predict(regime_features)
        
        return pd.Series(regimes, index=df.index)

class HighConfidenceFeatureEngineering:
    """Feature engineering focused on high-confidence signals"""
    
    def create_high_confidence_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create features optimized for high-confidence predictions"""
        features_df = df.copy()
        
        # 1. MOMENTUM CONVERGENCE/DIVERGENCE
        self._add_momentum_convergence(features_df)
        
        # 2. VOLUME CONFIRMATION SIGNALS
        self._add_volume_confirmation(features_df)
        
        # 3. MULTI-TIMEFRAME TREND ALIGNMENT
        self._add_trend_alignment(features_df)
        
        # 4. SUPPORT/RESISTANCE BREAKOUTS
        self._add_breakout_signals(features_df)
        
        # 5. VOLATILITY REGIME SIGNALS
        self._add_volatility_signals(features_df)
        
        # 6. MARKET STRUCTURE SIGNALS
        self._add_market_structure(features_df)
        
        return features_df
    
    def _add_momentum_convergence(self, df: pd.DataFrame):
        """Add momentum convergence/divergence signals"""
        close = df['close']
        
        # Multiple RSI periods
        for period in [7, 14, 21]:
            delta = close.diff()
            gain = delta.clip(lower=0)
            loss = (-delta).clip(lower=0)
            avg_gain = gain.rolling(window=period).mean()
            avg_loss = loss.rolling(window=period).mean()
            rs = avg_gain / (avg_loss + 1e-10)
            df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        
        # RSI convergence signal
        df['rsi_convergence'] = (
            (df['rsi_7'] > 50) & (df['rsi_14'] > 50) & (df['rsi_21'] > 50)
        ).astype(int) - (
            (df['rsi_7'] < 50) & (df['rsi_14'] < 50) & (df['rsi_21'] < 50)
        ).astype(int)
        
        # MACD convergence
        ema12 = close.ewm(span=12).mean()
        ema26 = close.ewm(span=26).mean()
        macd = ema12 - ema26
        macd_signal = macd.ewm(span=9).mean()
        df['macd_bullish'] = (macd > macd_signal).astype(int)
        df['macd_bearish'] = (macd < macd_signal).astype(int)
    
    def _add_volume_confirmation(self, df: pd.DataFrame):
        """Add volume confirmation signals"""
        volume = df['volume']
        close = df['close']
        
        # Volume surge detection
        vol_ma = volume.rolling(20).mean()
        df['volume_surge'] = (volume > vol_ma * 1.5).astype(int)
        
        # Price-volume confirmation
        price_up = (close > close.shift(1)).astype(int)
        price_down = (close < close.shift(1)).astype(int)
        
        df['bullish_volume_confirm'] = (price_up & df['volume_surge']).astype(int)
        df['bearish_volume_confirm'] = (price_down & df['volume_surge']).astype(int)
        
        # On Balance Volume trend
        obv = (volume * np.where(close > close.shift(1), 1, 
                               np.where(close < close.shift(1), -1, 0))).cumsum()
        df['obv_trend'] = (obv > obv.rolling(20).mean()).astype(int)
    
    def _add_trend_alignment(self, df: pd.DataFrame):
        """Add multi-timeframe trend alignment"""
        close = df['close']
        
        # Multiple moving averages
        for period in [10, 20, 50, 100]:
            df[f'ma_{period}'] = close.rolling(period).mean()
        
        # Trend alignment signals
        df['bullish_alignment'] = (
            (close > df['ma_10']) & 
            (df['ma_10'] > df['ma_20']) & 
            (df['ma_20'] > df['ma_50']) &
            (df['ma_50'] > df['ma_100'])
        ).astype(int)
        
        df['bearish_alignment'] = (
            (close < df['ma_10']) & 
            (df['ma_10'] < df['ma_20']) & 
            (df['ma_20'] < df['ma_50']) &
            (df['ma_50'] < df['ma_100'])
        ).astype(int)
        
        # Trend strength
        df['trend_strength'] = (
            df['bullish_alignment'] - df['bearish_alignment']
        )
    
    def _add_breakout_signals(self, df: pd.DataFrame):
        """Add support/resistance breakout signals"""
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']
        
        # Dynamic support/resistance
        resistance = high.rolling(20).max()
        support = low.rolling(20).min()
        
        # Breakout signals with volume confirmation
        vol_ma = volume.rolling(20).mean()
        
        df['bullish_breakout'] = (
            (close > resistance.shift(1)) & 
            (volume > vol_ma * 1.2)
        ).astype(int)
        
        df['bearish_breakdown'] = (
            (close < support.shift(1)) & 
            (volume > vol_ma * 1.2)
        ).astype(int)
        
        # Distance from support/resistance
        df['dist_from_resistance'] = (resistance - close) / close
        df['dist_from_support'] = (close - support) / close
    
    def _add_volatility_signals(self, df: pd.DataFrame):
        """Add volatility-based signals"""
        close = df['close']
        high = df['high']
        low = df['low']
        
        # Average True Range
        prev_close = close.shift(1)
        tr = np.maximum(high - low, np.maximum(abs(high - prev_close), abs(low - prev_close)))
        atr = tr.rolling(14).mean()
        
        # Volatility expansion/contraction
        df['volatility_expansion'] = (atr > atr.rolling(20).mean() * 1.2).astype(int)
        df['volatility_contraction'] = (atr < atr.rolling(20).mean() * 0.8).astype(int)
        
        # Bollinger Bands squeeze
        bb_middle = close.rolling(20).mean()
        bb_std = close.rolling(20).std()
        bb_upper = bb_middle + (bb_std * 2)
        bb_lower = bb_middle - (bb_std * 2)
        
        df['bb_squeeze'] = ((bb_upper - bb_lower) / bb_middle < 0.1).astype(int)
        df['bb_expansion'] = ((bb_upper - bb_lower) / bb_middle > 0.2).astype(int)
    
    def _add_market_structure(self, df: pd.DataFrame):
        """Add market structure signals"""
        close = df['close']
        high = df['high']
        low = df['low']
        
        # Higher highs, higher lows (bullish structure)
        df['higher_high'] = (high > high.shift(1)).astype(int)
        df['higher_low'] = (low > low.shift(1)).astype(int)
        df['bullish_structure'] = (df['higher_high'] & df['higher_low']).astype(int)
        
        # Lower highs, lower lows (bearish structure)
        df['lower_high'] = (high < high.shift(1)).astype(int)
        df['lower_low'] = (low < low.shift(1)).astype(int)
        df['bearish_structure'] = (df['lower_high'] & df['lower_low']).astype(int)

class UltraHighAccuracyPredictor:
    """Ultra high accuracy predictor with regime-specific models"""
    
    def __init__(self):
        self.regime_detector = MarketRegimeDetector()
        self.feature_engineer = HighConfidenceFeatureEngineering()
        self.regime_models = {}
        self.scalers = {}
        self.confidence_threshold = 0.8  # Only make predictions with >80% confidence
        
    def prepare_regime_specific_data(self, df: pd.DataFrame) -> Dict:
        """Prepare data split by market regimes"""
        
        # Create features
        features_df = self.feature_engineer.create_high_confidence_features(df)
        
        # Detect regimes
        regimes = self.regime_detector.detect_regimes(df)
        features_df['regime'] = regimes
        
        # Create target (direction with higher threshold for noise reduction)
        future_return = features_df['close'].shift(-1) / features_df['close'] - 1
        target = (future_return > 0.002).astype(int)  # 0.2% threshold
        
        # Remove rows with NaN values
        valid_idx = ~(features_df.isnull().any(axis=1) | target.isnull())
        features_df = features_df[valid_idx]
        target = target[valid_idx]
        
        # Select numeric features
        numeric_features = features_df.select_dtypes(include=[np.number]).columns
        numeric_features = [col for col in numeric_features if col != 'regime']
        
        # Split by regime
        regime_data = {}
        for regime in range(4):
            regime_mask = features_df['regime'] == regime
            if regime_mask.sum() > 50:  # Minimum samples per regime
                regime_data[regime] = {
                    'X': features_df[regime_mask][numeric_features],
                    'y': target[regime_mask],
                    'regime_name': self.regime_detector.regime_names[regime]
                }
        
        return regime_data
    
    def train_regime_models(self, regime_data: Dict) -> Dict:
        """Train separate models for each market regime"""
        results = {}
        
        for regime, data in regime_data.items():
            print(f"Training model for {data['regime_name']} regime...")
            
            X, y = data['X'], data['y']
            
            # Remove infinite values and fill NaN
            X = X.replace([np.inf, -np.inf], np.nan).fillna(0)
            
            # Split data
            split_idx = int(0.8 * len(X))
            X_train, X_val = X.iloc[:split_idx], X.iloc[split_idx:]
            y_train, y_val = y.iloc[:split_idx], y.iloc[split_idx:]
            
            if len(X_train) < 20 or len(X_val) < 10:
                print(f"Insufficient data for {data['regime_name']} regime")
                continue
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)
            
            # Train ensemble for this regime
            models = {
                'rf': RandomForestClassifier(n_estimators=200, max_depth=10, random_state=42),
                'gb': GradientBoostingClassifier(n_estimators=200, max_depth=6, random_state=42),
                'svm': SVC(probability=True, random_state=42),
                'lr': LogisticRegression(random_state=42, max_iter=1000)
            }
            
            regime_models = {}
            scores = {}
            
            for name, model in models.items():
                if name in ['svm', 'lr']:
                    model.fit(X_train_scaled, y_train)
                    pred = model.predict(X_val_scaled)
                    prob = model.predict_proba(X_val_scaled)[:, 1]
                else:
                    model.fit(X_train, y_train)
                    pred = model.predict(X_val)
                    prob = model.predict_proba(X_val)[:, 1]
                
                # Only consider high-confidence predictions
                high_conf_mask = (prob > self.confidence_threshold) | (prob < (1 - self.confidence_threshold))
                if high_conf_mask.sum() > 0:
                    high_conf_accuracy = accuracy_score(y_val[high_conf_mask], pred[high_conf_mask])
                    scores[name] = high_conf_accuracy
                    regime_models[name] = model
                    print(f"  {name} high-confidence accuracy: {high_conf_accuracy:.4f} ({high_conf_mask.sum()} samples)")
                else:
                    scores[name] = 0.5
                    regime_models[name] = model
            
            # Store best model for this regime
            best_model_name = max(scores, key=scores.get)
            
            results[regime] = {
                'models': regime_models,
                'best_model': best_model_name,
                'scaler': scaler,
                'scores': scores,
                'regime_name': data['regime_name']
            }
            
            self.regime_models[regime] = regime_models
            self.scalers[regime] = scaler
        
        return results
    
    def predict_high_confidence(self, X: pd.DataFrame, regime: int) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Make high-confidence predictions for specific regime"""
        if regime not in self.regime_models:
            return np.array([]), np.array([]), np.array([])
        
        X_clean = X.replace([np.inf, -np.inf], np.nan).fillna(0)
        
        # Get predictions from all models in this regime
        predictions = []
        probabilities = []
        
        for name, model in self.regime_models[regime].items():
            if name in ['svm', 'lr']:
                X_scaled = self.scalers[regime].transform(X_clean)
                prob = model.predict_proba(X_scaled)[:, 1]
            else:
                prob = model.predict_proba(X_clean)[:, 1]
            
            probabilities.append(prob)
        
        # Ensemble probability
        ensemble_prob = np.mean(probabilities, axis=0)
        ensemble_pred = (ensemble_prob > 0.5).astype(int)
        
        # High confidence mask
        high_conf_mask = (ensemble_prob > self.confidence_threshold) | (ensemble_prob < (1 - self.confidence_threshold))
        
        return ensemble_pred, ensemble_prob, high_conf_mask

def fetch_binance_data(symbol: str, interval: str, limit: int = 1500) -> pd.DataFrame:
    """Fetch historical data from Binance"""
    try:
        endpoint = '/fapi/v1/klines'
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': min(limit, 1500)
        }
        
        response = requests.get(BASE_URL + endpoint, params=params)
        data = response.json()
        
        if isinstance(data, list) and len(data) > 0:
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert to proper data types
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            return df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
        else:
            print(f"Error fetching data: {data}")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"Error fetching Binance data: {e}")
        return pd.DataFrame()

def test_ultra_high_accuracy_system():
    """Test the ultra high accuracy ML system"""
    print("=" * 80)
    print("🎯 TESTING ULTRA HIGH ACCURACY ML SYSTEM (90%+ TARGET)")
    print("=" * 80)
    
    # Fetch data
    print("📊 Fetching BTC/USDT 1H data...")
    df = fetch_binance_data('BTCUSDT', '1h', 1500)
    
    if df.empty:
        print("❌ Failed to fetch data")
        return 0.0
    
    print(f"✅ Fetched {len(df)} data points")
    
    # Initialize ML system
    ml_system = UltraHighAccuracyPredictor()
    
    # Prepare regime-specific data
    print("🔧 Preparing regime-specific data...")
    regime_data = ml_system.prepare_regime_specific_data(df)
    
    print(f"✅ Detected {len(regime_data)} market regimes")
    for regime, data in regime_data.items():
        regime_name = ml_system.regime_detector.regime_names[regime]
        print(f"   {regime_name}: {len(data['X'])} samples")
    
    # Train regime-specific models
    print("\n🤖 Training regime-specific models...")
    results = ml_system.train_regime_models(regime_data)
    
    # Test overall performance
    print("\n🎯 Testing overall high-confidence performance...")
    
    all_predictions = []
    all_actuals = []
    all_high_conf = []
    
    for regime, data in regime_data.items():
        if regime in results:
            X, y = data['X'], data['y']
            split_idx = int(0.8 * len(X))
            X_val, y_val = X.iloc[split_idx:], y.iloc[split_idx:]
            
            pred, prob, high_conf_mask = ml_system.predict_high_confidence(X_val, regime)
            
            if len(pred) > 0:
                all_predictions.extend(pred[high_conf_mask])
                all_actuals.extend(y_val[high_conf_mask])
                all_high_conf.extend([True] * high_conf_mask.sum())
    
    if len(all_predictions) > 0:
        final_accuracy = accuracy_score(all_actuals, all_predictions)
        print(f"\n🏆 HIGH-CONFIDENCE ACCURACY: {final_accuracy:.4f} ({final_accuracy*100:.2f}%)")
        print(f"📊 High-confidence predictions: {len(all_predictions)} out of total validation samples")
        
        if final_accuracy >= 0.90:
            print(f"\n🎉 SUCCESS! Achieved {final_accuracy*100:.2f}% accuracy (≥90%)")
        else:
            print(f"\n⚠️  Still need improvement: {final_accuracy*100:.2f}% accuracy (<90%)")
        
        return final_accuracy
    else:
        print("❌ No high-confidence predictions generated")
        return 0.0

if __name__ == "__main__":
    accuracy = test_ultra_high_accuracy_system()
    print(f"\n🏁 Test completed. Final high-confidence accuracy: {accuracy*100:.2f}%")
